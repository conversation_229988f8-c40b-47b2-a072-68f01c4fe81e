<?xml version='1.0' encoding='UTF-8' standalone='yes'?>
<w:styles xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" mc:Ignorable="w14">
  <w:docDefaults>
    <w:rPrDefault>
      <w:rPr>
        <w:rFonts w:asciiTheme="minorHAnsi" w:eastAsiaTheme="minorEastAsia" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/>
        <w:sz w:val="22"/>
        <w:szCs w:val="22"/>
        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
      </w:rPr>
    </w:rPrDefault>
    <w:pPrDefault>
      <w:pPr>
        <w:spacing w:after="200" w:line="276" w:lineRule="auto"/>
      </w:pPr>
    </w:pPrDefault>
  </w:docDefaults>
  <w:latentStyles w:defLockedState="0" w:defUIPriority="99" w:defSemiHidden="1" w:defUnhideWhenUsed="1" w:defQFormat="0" w:count="276">
    <w:lsdException w:name="Normal" w:semiHidden="0" w:uiPriority="0" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="heading 1" w:semiHidden="0" w:uiPriority="9" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="heading 2" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 3" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 4" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 5" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 6" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 7" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 8" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="heading 9" w:uiPriority="9" w:qFormat="1"/>
    <w:lsdException w:name="toc 1" w:uiPriority="39"/>
    <w:lsdException w:name="toc 2" w:uiPriority="39"/>
    <w:lsdException w:name="toc 3" w:uiPriority="39"/>
    <w:lsdException w:name="toc 4" w:uiPriority="39"/>
    <w:lsdException w:name="toc 5" w:uiPriority="39"/>
    <w:lsdException w:name="toc 6" w:uiPriority="39"/>
    <w:lsdException w:name="toc 7" w:uiPriority="39"/>
    <w:lsdException w:name="toc 8" w:uiPriority="39"/>
    <w:lsdException w:name="toc 9" w:uiPriority="39"/>
    <w:lsdException w:name="caption" w:uiPriority="35" w:qFormat="1"/>
    <w:lsdException w:name="Title" w:semiHidden="0" w:uiPriority="10" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Default Paragraph Font" w:uiPriority="1"/>
    <w:lsdException w:name="Subtitle" w:semiHidden="0" w:uiPriority="11" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Strong" w:semiHidden="0" w:uiPriority="22" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Emphasis" w:semiHidden="0" w:uiPriority="20" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Table Grid" w:semiHidden="0" w:uiPriority="59" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Placeholder Text" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="No Spacing" w:semiHidden="0" w:uiPriority="1" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Light Shading" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 2" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Shading Accent 1" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List Accent 1" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid Accent 1" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1 Accent 1" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2 Accent 1" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1 Accent 1" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Revision" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="List Paragraph" w:semiHidden="0" w:uiPriority="34" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Quote" w:semiHidden="0" w:uiPriority="29" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Intense Quote" w:semiHidden="0" w:uiPriority="30" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Medium List 2 Accent 1" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1 Accent 1" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2 Accent 1" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3 Accent 1" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List Accent 1" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading Accent 1" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List Accent 1" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid Accent 1" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Shading Accent 2" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List Accent 2" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid Accent 2" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1 Accent 2" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2 Accent 2" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1 Accent 2" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 2 Accent 2" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1 Accent 2" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2 Accent 2" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3 Accent 2" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List Accent 2" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading Accent 2" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List Accent 2" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid Accent 2" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Shading Accent 3" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List Accent 3" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid Accent 3" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1 Accent 3" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2 Accent 3" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1 Accent 3" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 2 Accent 3" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1 Accent 3" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2 Accent 3" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3 Accent 3" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List Accent 3" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading Accent 3" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List Accent 3" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid Accent 3" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Shading Accent 4" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List Accent 4" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid Accent 4" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1 Accent 4" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2 Accent 4" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1 Accent 4" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 2 Accent 4" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1 Accent 4" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2 Accent 4" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3 Accent 4" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List Accent 4" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading Accent 4" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List Accent 4" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid Accent 4" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Shading Accent 5" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List Accent 5" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid Accent 5" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1 Accent 5" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2 Accent 5" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1 Accent 5" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 2 Accent 5" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1 Accent 5" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2 Accent 5" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3 Accent 5" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List Accent 5" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading Accent 5" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List Accent 5" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid Accent 5" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Shading Accent 6" w:semiHidden="0" w:uiPriority="60" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light List Accent 6" w:semiHidden="0" w:uiPriority="61" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Light Grid Accent 6" w:semiHidden="0" w:uiPriority="62" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 1 Accent 6" w:semiHidden="0" w:uiPriority="63" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Shading 2 Accent 6" w:semiHidden="0" w:uiPriority="64" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 1 Accent 6" w:semiHidden="0" w:uiPriority="65" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium List 2 Accent 6" w:semiHidden="0" w:uiPriority="66" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 1 Accent 6" w:semiHidden="0" w:uiPriority="67" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 2 Accent 6" w:semiHidden="0" w:uiPriority="68" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Medium Grid 3 Accent 6" w:semiHidden="0" w:uiPriority="69" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Dark List Accent 6" w:semiHidden="0" w:uiPriority="70" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Shading Accent 6" w:semiHidden="0" w:uiPriority="71" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful List Accent 6" w:semiHidden="0" w:uiPriority="72" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Colorful Grid Accent 6" w:semiHidden="0" w:uiPriority="73" w:unhideWhenUsed="0"/>
    <w:lsdException w:name="Subtle Emphasis" w:semiHidden="0" w:uiPriority="19" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Intense Emphasis" w:semiHidden="0" w:uiPriority="21" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Subtle Reference" w:semiHidden="0" w:uiPriority="31" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Intense Reference" w:semiHidden="0" w:uiPriority="32" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Book Title" w:semiHidden="0" w:uiPriority="33" w:unhideWhenUsed="0" w:qFormat="1"/>
    <w:lsdException w:name="Bibliography" w:uiPriority="37"/>
    <w:lsdException w:name="TOC Heading" w:uiPriority="39" w:qFormat="1"/>
  </w:latentStyles>
  <w:style w:type="paragraph" w:default="1" w:styleId="Normal">
    <w:name w:val="Normal"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Header">
    <w:name w:val="header"/>
    <w:basedOn w:val="Normal"/>
    <w:link w:val="HeaderChar"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00E618BF"/>
    <w:pPr>
      <w:tabs>
        <w:tab w:val="center" w:pos="4680"/>
        <w:tab w:val="right" w:pos="9360"/>
      </w:tabs>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="HeaderChar">
    <w:name w:val="Header Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Header"/>
    <w:uiPriority w:val="99"/>
    <w:rsid w:val="00E618BF"/>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Footer">
    <w:name w:val="footer"/>
    <w:basedOn w:val="Normal"/>
    <w:link w:val="FooterChar"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00E618BF"/>
    <w:pPr>
      <w:tabs>
        <w:tab w:val="center" w:pos="4680"/>
        <w:tab w:val="right" w:pos="9360"/>
      </w:tabs>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="FooterChar">
    <w:name w:val="Footer Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Footer"/>
    <w:uiPriority w:val="99"/>
    <w:rsid w:val="00E618BF"/>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading1">
    <w:name w:val="heading 1"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading1Char"/>
    <w:uiPriority w:val="9"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="480" w:after="0"/>
      <w:outlineLvl w:val="0"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:color w:val="365F91" w:themeColor="accent1" w:themeShade="BF"/>
      <w:sz w:val="28"/>
      <w:szCs w:val="28"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading2">
    <w:name w:val="heading 2"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading2Char"/>
    <w:uiPriority w:val="9"/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="1"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:sz w:val="26"/>
      <w:szCs w:val="26"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading3">
    <w:name w:val="heading 3"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading3Char"/>
    <w:uiPriority w:val="9"/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="2"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading4">
    <w:name w:val="heading 4"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading4Char"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="3"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading5">
    <w:name w:val="heading 5"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading5Char"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="4"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="243F60" w:themeColor="accent1" w:themeShade="7F"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading6">
    <w:name w:val="heading 6"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading6Char"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="5"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="243F60" w:themeColor="accent1" w:themeShade="7F"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading7">
    <w:name w:val="heading 7"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading7Char"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="6"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="404040" w:themeColor="text1" w:themeTint="BF"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading8">
    <w:name w:val="heading 8"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading8Char"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="7"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:sz w:val="20"/>
      <w:szCs w:val="20"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading9">
    <w:name w:val="heading 9"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="Heading9Char"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:keepNext/>
      <w:keepLines/>
      <w:spacing w:before="200" w:after="0"/>
      <w:outlineLvl w:val="8"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="404040" w:themeColor="text1" w:themeTint="BF"/>
      <w:sz w:val="20"/>
      <w:szCs w:val="20"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:default="1" w:styleId="DefaultParagraphFont">
    <w:name w:val="Default Paragraph Font"/>
    <w:uiPriority w:val="1"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
  </w:style>
  <w:style w:type="table" w:default="1" w:styleId="TableNormal">
    <w:name w:val="Normal Table"/>
    <w:uiPriority w:val="99"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:tblPr>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
  </w:style>
  <w:style w:type="numbering" w:default="1" w:styleId="NoList">
    <w:name w:val="No List"/>
    <w:uiPriority w:val="99"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
  </w:style>
  <w:style w:type="paragraph" w:styleId="NoSpacing">
    <w:name w:val="No Spacing"/>
    <w:uiPriority w:val="1"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading1Char">
    <w:name w:val="Heading 1 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading1"/>
    <w:uiPriority w:val="9"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:color w:val="365F91" w:themeColor="accent1" w:themeShade="BF"/>
      <w:sz w:val="28"/>
      <w:szCs w:val="28"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading2Char">
    <w:name w:val="Heading 2 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading2"/>
    <w:uiPriority w:val="9"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:sz w:val="26"/>
      <w:szCs w:val="26"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading3Char">
    <w:name w:val="Heading 3 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading3"/>
    <w:uiPriority w:val="9"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Title">
    <w:name w:val="Title"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="TitleChar"/>
    <w:uiPriority w:val="10"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:pBdr>
        <w:bottom w:val="single" w:sz="8" w:space="4" w:color="4F81BD" w:themeColor="accent1"/>
      </w:pBdr>
      <w:spacing w:after="300" w:line="240" w:lineRule="auto"/>
      <w:contextualSpacing/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="17365D" w:themeColor="text2" w:themeShade="BF"/>
      <w:spacing w:val="5"/>
      <w:kern w:val="28"/>
      <w:sz w:val="52"/>
      <w:szCs w:val="52"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="TitleChar">
    <w:name w:val="Title Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Title"/>
    <w:uiPriority w:val="10"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="17365D" w:themeColor="text2" w:themeShade="BF"/>
      <w:spacing w:val="5"/>
      <w:kern w:val="28"/>
      <w:sz w:val="52"/>
      <w:szCs w:val="52"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Subtitle">
    <w:name w:val="Subtitle"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="SubtitleChar"/>
    <w:uiPriority w:val="11"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:numPr>
        <w:ilvl w:val="1"/>
      </w:numPr>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:spacing w:val="15"/>
      <w:sz w:val="24"/>
      <w:szCs w:val="24"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="SubtitleChar">
    <w:name w:val="Subtitle Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Subtitle"/>
    <w:uiPriority w:val="11"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:spacing w:val="15"/>
      <w:sz w:val="24"/>
      <w:szCs w:val="24"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListParagraph">
    <w:name w:val="List Paragraph"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="34"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:ind w:left="720"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="BodyText">
    <w:name w:val="Body Text"/>
    <w:basedOn w:val="Normal"/>
    <w:link w:val="BodyTextChar"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00AA1D8D"/>
    <w:pPr>
      <w:spacing w:after="120"/>
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="BodyTextChar">
    <w:name w:val="Body Text Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="BodyText"/>
    <w:uiPriority w:val="99"/>
    <w:rsid w:val="00AA1D8D"/>
  </w:style>
  <w:style w:type="paragraph" w:styleId="BodyText2">
    <w:name w:val="Body Text 2"/>
    <w:basedOn w:val="Normal"/>
    <w:link w:val="BodyText2Char"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00AA1D8D"/>
    <w:pPr>
      <w:spacing w:after="120" w:line="480" w:lineRule="auto"/>
    </w:pPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="BodyText2Char">
    <w:name w:val="Body Text 2 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="BodyText2"/>
    <w:uiPriority w:val="99"/>
    <w:rsid w:val="00AA1D8D"/>
  </w:style>
  <w:style w:type="paragraph" w:styleId="BodyText3">
    <w:name w:val="Body Text 3"/>
    <w:basedOn w:val="Normal"/>
    <w:link w:val="BodyText3Char"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00AA1D8D"/>
    <w:pPr>
      <w:spacing w:after="120"/>
    </w:pPr>
    <w:rPr>
      <w:sz w:val="16"/>
      <w:szCs w:val="16"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="BodyText3Char">
    <w:name w:val="Body Text 3 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="BodyText3"/>
    <w:uiPriority w:val="99"/>
    <w:rsid w:val="00AA1D8D"/>
    <w:rPr>
      <w:sz w:val="16"/>
      <w:szCs w:val="16"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="List">
    <w:name w:val="List"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00AA1D8D"/>
    <w:pPr>
      <w:ind w:left="360" w:hanging="360"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="List2">
    <w:name w:val="List 2"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00326F90"/>
    <w:pPr>
      <w:ind w:left="720" w:hanging="360"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="List3">
    <w:name w:val="List 3"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00326F90"/>
    <w:pPr>
      <w:ind w:left="1080" w:hanging="360"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListBullet">
    <w:name w:val="List Bullet"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00326F90"/>
    <w:pPr>
      <w:numPr>
        <w:numId w:val="1"/>
      </w:numPr>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListBullet2">
    <w:name w:val="List Bullet 2"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00326F90"/>
    <w:pPr>
      <w:numPr>
        <w:numId w:val="2"/>
      </w:numPr>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListBullet3">
    <w:name w:val="List Bullet 3"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00326F90"/>
    <w:pPr>
      <w:numPr>
        <w:numId w:val="3"/>
      </w:numPr>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListNumber">
    <w:name w:val="List Number"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="00326F90"/>
    <w:pPr>
      <w:numPr>
        <w:numId w:val="5"/>
      </w:numPr>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListNumber2">
    <w:name w:val="List Number 2"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="0029639D"/>
    <w:pPr>
      <w:numPr>
        <w:numId w:val="6"/>
      </w:numPr>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListNumber3">
    <w:name w:val="List Number 3"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="0029639D"/>
    <w:pPr>
      <w:numPr>
        <w:numId w:val="7"/>
      </w:numPr>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListContinue">
    <w:name w:val="List Continue"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="0029639D"/>
    <w:pPr>
      <w:spacing w:after="120"/>
      <w:ind w:left="360"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListContinue2">
    <w:name w:val="List Continue 2"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="0029639D"/>
    <w:pPr>
      <w:spacing w:after="120"/>
      <w:ind w:left="720"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="ListContinue3">
    <w:name w:val="List Continue 3"/>
    <w:basedOn w:val="Normal"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="0029639D"/>
    <w:pPr>
      <w:spacing w:after="120"/>
      <w:ind w:left="1080"/>
      <w:contextualSpacing/>
    </w:pPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="MacroText">
    <w:name w:val="macro"/>
    <w:link w:val="MacroTextChar"/>
    <w:uiPriority w:val="99"/>
    <w:unhideWhenUsed/>
    <w:rsid w:val="0029639D"/>
    <w:pPr>
      <w:tabs>
        <w:tab w:val="left" w:pos="576"/>
        <w:tab w:val="left" w:pos="1152"/>
        <w:tab w:val="left" w:pos="1728"/>
        <w:tab w:val="left" w:pos="2304"/>
        <w:tab w:val="left" w:pos="2880"/>
        <w:tab w:val="left" w:pos="3456"/>
        <w:tab w:val="left" w:pos="4032"/>
      </w:tabs>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:ascii="Courier" w:hAnsi="Courier"/>
      <w:sz w:val="20"/>
      <w:szCs w:val="20"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="MacroTextChar">
    <w:name w:val="Macro Text Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="MacroText"/>
    <w:uiPriority w:val="99"/>
    <w:rsid w:val="0029639D"/>
    <w:rPr>
      <w:rFonts w:ascii="Courier" w:hAnsi="Courier"/>
      <w:sz w:val="20"/>
      <w:szCs w:val="20"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Quote">
    <w:name w:val="Quote"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="QuoteChar"/>
    <w:uiPriority w:val="29"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:i/>
      <w:iCs/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="QuoteChar">
    <w:name w:val="Quote Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Quote"/>
    <w:uiPriority w:val="29"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:i/>
      <w:iCs/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading4Char">
    <w:name w:val="Heading 4 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading4"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:b/>
      <w:bCs/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading5Char">
    <w:name w:val="Heading 5 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading5"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="243F60" w:themeColor="accent1" w:themeShade="7F"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading6Char">
    <w:name w:val="Heading 6 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading6"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="243F60" w:themeColor="accent1" w:themeShade="7F"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading7Char">
    <w:name w:val="Heading 7 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading7"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="404040" w:themeColor="text1" w:themeTint="BF"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading8Char">
    <w:name w:val="Heading 8 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading8"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:sz w:val="20"/>
      <w:szCs w:val="20"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="Heading9Char">
    <w:name w:val="Heading 9 Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="Heading9"/>
    <w:uiPriority w:val="9"/>
    <w:semiHidden/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="404040" w:themeColor="text1" w:themeTint="BF"/>
      <w:sz w:val="20"/>
      <w:szCs w:val="20"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Caption">
    <w:name w:val="caption"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:uiPriority w:val="35"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:b/>
      <w:bCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
      <w:sz w:val="18"/>
      <w:szCs w:val="18"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="Strong">
    <w:name w:val="Strong"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="22"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:b/>
      <w:bCs/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="Emphasis">
    <w:name w:val="Emphasis"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="20"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:i/>
      <w:iCs/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="IntenseQuote">
    <w:name w:val="Intense Quote"/>
    <w:basedOn w:val="Normal"/>
    <w:next w:val="Normal"/>
    <w:link w:val="IntenseQuoteChar"/>
    <w:uiPriority w:val="30"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:pBdr>
        <w:bottom w:val="single" w:sz="4" w:space="4" w:color="4F81BD" w:themeColor="accent1"/>
      </w:pBdr>
      <w:spacing w:before="200" w:after="280"/>
      <w:ind w:left="936" w:right="936"/>
    </w:pPr>
    <w:rPr>
      <w:b/>
      <w:bCs/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:customStyle="1" w:styleId="IntenseQuoteChar">
    <w:name w:val="Intense Quote Char"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:link w:val="IntenseQuote"/>
    <w:uiPriority w:val="30"/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:b/>
      <w:bCs/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="SubtleEmphasis">
    <w:name w:val="Subtle Emphasis"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="19"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:i/>
      <w:iCs/>
      <w:color w:val="808080" w:themeColor="text1" w:themeTint="7F"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="IntenseEmphasis">
    <w:name w:val="Intense Emphasis"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="21"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:b/>
      <w:bCs/>
      <w:i/>
      <w:iCs/>
      <w:color w:val="4F81BD" w:themeColor="accent1"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="SubtleReference">
    <w:name w:val="Subtle Reference"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="31"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:smallCaps/>
      <w:color w:val="C0504D" w:themeColor="accent2"/>
      <w:u w:val="single"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="IntenseReference">
    <w:name w:val="Intense Reference"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="32"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:b/>
      <w:bCs/>
      <w:smallCaps/>
      <w:color w:val="C0504D" w:themeColor="accent2"/>
      <w:spacing w:val="5"/>
      <w:u w:val="single"/>
    </w:rPr>
  </w:style>
  <w:style w:type="character" w:styleId="BookTitle">
    <w:name w:val="Book Title"/>
    <w:basedOn w:val="DefaultParagraphFont"/>
    <w:uiPriority w:val="33"/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:rPr>
      <w:b/>
      <w:bCs/>
      <w:smallCaps/>
      <w:spacing w:val="5"/>
    </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="TOCHeading">
    <w:name w:val="TOC Heading"/>
    <w:basedOn w:val="Heading1"/>
    <w:next w:val="Normal"/>
    <w:uiPriority w:val="39"/>
    <w:semiHidden/>
    <w:unhideWhenUsed/>
    <w:qFormat/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:outlineLvl w:val="9"/>
    </w:pPr>
  </w:style>
  <w:style w:type="table" w:styleId="TableGrid">
    <w:name w:val="Table Grid"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="59"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="4" w:space="0" w:color="auto"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="auto"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="auto"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="auto"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading">
    <w:name w:val="Light Shading"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading-Accent1">
    <w:name w:val="Light Shading Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="365F91" w:themeColor="accent1" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading-Accent2">
    <w:name w:val="Light Shading Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="943634" w:themeColor="accent2" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading-Accent3">
    <w:name w:val="Light Shading Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="76923C" w:themeColor="accent3" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading-Accent4">
    <w:name w:val="Light Shading Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="5F497A" w:themeColor="accent4" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading-Accent5">
    <w:name w:val="Light Shading Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="31849B" w:themeColor="accent5" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightShading-Accent6">
    <w:name w:val="Light Shading Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="60"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="E36C0A" w:themeColor="accent6" w:themeShade="BF"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList">
    <w:name w:val="Light List"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList-Accent1">
    <w:name w:val="Light List Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00FC693F"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList-Accent2">
    <w:name w:val="Light List Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList-Accent3">
    <w:name w:val="Light List Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList-Accent4">
    <w:name w:val="Light List Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList-Accent5">
    <w:name w:val="Light List Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightList-Accent6">
    <w:name w:val="Light List Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="61"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid">
    <w:name w:val="Light Grid"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid-Accent1">
    <w:name w:val="Light Grid Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid-Accent2">
    <w:name w:val="Light Grid Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid-Accent3">
    <w:name w:val="Light Grid Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid-Accent4">
    <w:name w:val="Light Grid Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid-Accent5">
    <w:name w:val="Light Grid Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="LightGrid-Accent6">
    <w:name w:val="Light Grid Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="62"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1">
    <w:name w:val="Medium Shading 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1-Accent1">
    <w:name w:val="Medium Shading 1 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1-Accent2">
    <w:name w:val="Medium Shading 1 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1-Accent3">
    <w:name w:val="Medium Shading 1 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1-Accent4">
    <w:name w:val="Medium Shading 1 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1-Accent5">
    <w:name w:val="Medium Shading 1 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading1-Accent6">
    <w:name w:val="Medium Shading 1 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="63"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band2Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2">
    <w:name w:val="Medium Shading 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2-Accent1">
    <w:name w:val="Medium Shading 2 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2-Accent2">
    <w:name w:val="Medium Shading 2 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2-Accent3">
    <w:name w:val="Medium Shading 2 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2-Accent4">
    <w:name w:val="Medium Shading 2 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2-Accent5">
    <w:name w:val="Medium Shading 2 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumShading2-Accent6">
    <w:name w:val="Medium Shading 2 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="64"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
        <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:pPr>
        <w:spacing w:before="0" w:after="0" w:line="240" w:lineRule="auto"/>
      </w:pPr>
      <w:rPr>
        <w:color w:val="auto"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="double" w:sz="6" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D8D8D8" w:themeFill="background1" w:themeFillShade="D8"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="auto"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1">
    <w:name w:val="Medium List 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1-Accent1">
    <w:name w:val="Medium List 1 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1-Accent2">
    <w:name w:val="Medium List 1 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1-Accent3">
    <w:name w:val="Medium List 1 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1-Accent4">
    <w:name w:val="Medium List 1 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1-Accent5">
    <w:name w:val="Medium List 1 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList1-Accent6">
    <w:name w:val="Medium List 1 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="65"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="1F497D" w:themeColor="text2"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2">
    <w:name w:val="Medium List 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2-Accent1">
    <w:name w:val="Medium List 2 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2-Accent2">
    <w:name w:val="Medium List 2 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2-Accent3">
    <w:name w:val="Medium List 2 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2-Accent4">
    <w:name w:val="Medium List 2 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2-Accent5">
    <w:name w:val="Medium List 2 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumList2-Accent6">
    <w:name w:val="Medium List 2 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="66"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:sz w:val="24"/>
        <w:szCs w:val="24"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="swCell">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1">
    <w:name w:val="Medium Grid 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="404040" w:themeColor="text1" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1-Accent1">
    <w:name w:val="Medium Grid 1 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="7BA0CD" w:themeColor="accent1" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1-Accent2">
    <w:name w:val="Medium Grid 1 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="CF7B79" w:themeColor="accent2" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1-Accent3">
    <w:name w:val="Medium Grid 1 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="B3CC82" w:themeColor="accent3" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1-Accent4">
    <w:name w:val="Medium Grid 1 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="9F8AB9" w:themeColor="accent4" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1-Accent5">
    <w:name w:val="Medium Grid 1 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="78C0D4" w:themeColor="accent5" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid1-Accent6">
    <w:name w:val="Medium Grid 1 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="67"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="F9B074" w:themeColor="accent6" w:themeTint="BF"/>
        </w:tcBorders>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2">
    <w:name w:val="Medium Grid 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="000000" w:themeColor="text1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E6E6E6" w:themeFill="text1" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="CCCCCC" w:themeFill="text1" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2-Accent1">
    <w:name w:val="Medium Grid 2 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="EDF2F8" w:themeFill="accent1" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DBE5F1" w:themeFill="accent1" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2-Accent2">
    <w:name w:val="Medium Grid 2 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="F8EDED" w:themeFill="accent2" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F2DBDB" w:themeFill="accent2" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2-Accent3">
    <w:name w:val="Medium Grid 2 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="F5F8EE" w:themeFill="accent3" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EAF1DD" w:themeFill="accent3" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2-Accent4">
    <w:name w:val="Medium Grid 2 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="F2EFF6" w:themeFill="accent4" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E5DFEC" w:themeFill="accent4" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2-Accent5">
    <w:name w:val="Medium Grid 2 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="EDF6F9" w:themeFill="accent5" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DAEEF3" w:themeFill="accent5" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid2-Accent6">
    <w:name w:val="Medium Grid 2 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="68"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:rFonts w:asciiTheme="majorHAnsi" w:eastAsiaTheme="majorEastAsia" w:hAnsiTheme="majorHAnsi" w:cstheme="majorBidi"/>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:insideH w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:insideV w:val="single" w:sz="8" w:space="0" w:color="F79646" w:themeColor="accent6"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FEF4EC" w:themeFill="accent6" w:themeFillTint="19"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b w:val="0"/>
        <w:bCs w:val="0"/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE9D9" w:themeFill="accent6" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:insideH w:val="single" w:sz="6" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:insideV w:val="single" w:sz="6" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3">
    <w:name w:val="Medium Grid 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3-Accent1">
    <w:name w:val="Medium Grid 3 Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3-Accent2">
    <w:name w:val="Medium Grid 3 Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3-Accent3">
    <w:name w:val="Medium Grid 3 Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3-Accent4">
    <w:name w:val="Medium Grid 3 Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3-Accent5">
    <w:name w:val="Medium Grid 3 Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="MediumGrid3-Accent6">
    <w:name w:val="Medium Grid 3 Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="69"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideH w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:i w:val="0"/>
        <w:iCs w:val="0"/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="24" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideV w:val="single" w:sz="8" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList">
    <w:name w:val="Dark List"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList-Accent1">
    <w:name w:val="Dark List Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="4F81BD" w:themeFill="accent1"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="243F60" w:themeFill="accent1" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="365F91" w:themeFill="accent1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="365F91" w:themeFill="accent1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="365F91" w:themeFill="accent1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="365F91" w:themeFill="accent1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList-Accent2">
    <w:name w:val="Dark List Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="C0504D" w:themeFill="accent2"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="622423" w:themeFill="accent2" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="943634" w:themeFill="accent2" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="943634" w:themeFill="accent2" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="943634" w:themeFill="accent2" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="943634" w:themeFill="accent2" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList-Accent3">
    <w:name w:val="Dark List Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="9BBB59" w:themeFill="accent3"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4E6128" w:themeFill="accent3" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="76923C" w:themeFill="accent3" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="76923C" w:themeFill="accent3" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="76923C" w:themeFill="accent3" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="76923C" w:themeFill="accent3" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList-Accent4">
    <w:name w:val="Dark List Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="8064A2" w:themeFill="accent4"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="3F3151" w:themeFill="accent4" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5F497A" w:themeFill="accent4" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5F497A" w:themeFill="accent4" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5F497A" w:themeFill="accent4" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5F497A" w:themeFill="accent4" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList-Accent5">
    <w:name w:val="Dark List Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="4BACC6" w:themeFill="accent5"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="205867" w:themeFill="accent5" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="31849B" w:themeFill="accent5" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="31849B" w:themeFill="accent5" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="31849B" w:themeFill="accent5" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="31849B" w:themeFill="accent5" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="DarkList-Accent6">
    <w:name w:val="Dark List Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="70"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="FFFFFF" w:themeColor="background1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F79646" w:themeFill="accent6"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="974706" w:themeFill="accent6" w:themeFillShade="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E36C0A" w:themeFill="accent6" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="single" w:sz="18" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E36C0A" w:themeFill="accent6" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E36C0A" w:themeFill="accent6" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E36C0A" w:themeFill="accent6" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading">
    <w:name w:val="Colorful Shading"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="000000" w:themeColor="text1"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="E6E6E6" w:themeFill="text1" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="000000" w:themeColor="text1" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="999999" w:themeFill="text1" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading-Accent1">
    <w:name w:val="Colorful Shading Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="4F81BD" w:themeColor="accent1"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EDF2F8" w:themeFill="accent1" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="2C4C74" w:themeFill="accent1" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="2C4C74" w:themeColor="accent1" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="2C4C74" w:themeFill="accent1" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="2C4C74" w:themeFill="accent1" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="B8CCE4" w:themeFill="accent1" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading-Accent2">
    <w:name w:val="Colorful Shading Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F8EDED" w:themeFill="accent2" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="C0504D" w:themeColor="accent2"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="772C2A" w:themeFill="accent2" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="772C2A" w:themeColor="accent2" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="772C2A" w:themeFill="accent2" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="772C2A" w:themeFill="accent2" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E5B8B7" w:themeFill="accent2" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading-Accent3">
    <w:name w:val="Colorful Shading Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F5F8EE" w:themeFill="accent3" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5E7530" w:themeFill="accent3" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="5E7530" w:themeColor="accent3" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5E7530" w:themeFill="accent3" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="5E7530" w:themeFill="accent3" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D6E3BC" w:themeFill="accent3" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading-Accent4">
    <w:name w:val="Colorful Shading Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="8064A2" w:themeColor="accent4"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F2EFF6" w:themeFill="accent4" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="9BBB59" w:themeColor="accent3"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4C3B62" w:themeFill="accent4" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="4C3B62" w:themeColor="accent4" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4C3B62" w:themeFill="accent4" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="4C3B62" w:themeFill="accent4" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CCC0D9" w:themeFill="accent4" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading-Accent5">
    <w:name w:val="Colorful Shading Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EDF6F9" w:themeFill="accent5" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="F79646" w:themeColor="accent6"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="276A7C" w:themeFill="accent5" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="276A7C" w:themeColor="accent5" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="276A7C" w:themeFill="accent5" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="276A7C" w:themeFill="accent5" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="B6DDE8" w:themeFill="accent5" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulShading-Accent6">
    <w:name w:val="Colorful Shading Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="71"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:top w:val="single" w:sz="24" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
        <w:left w:val="single" w:sz="4" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:right w:val="single" w:sz="4" w:space="0" w:color="F79646" w:themeColor="accent6"/>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        <w:insideV w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="FEF4EC" w:themeFill="accent6" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="single" w:sz="24" w:space="0" w:color="4BACC6" w:themeColor="accent5"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="6" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="B65608" w:themeFill="accent6" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="single" w:sz="4" w:space="0" w:color="B65608" w:themeColor="accent6" w:themeShade="99"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="B65608" w:themeFill="accent6" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="B65608" w:themeFill="accent6" w:themeFillShade="99"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBD4B4" w:themeFill="accent6" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="neCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="nwCell">
      <w:rPr>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList">
    <w:name w:val="Colorful List"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="E6E6E6" w:themeFill="text1" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9E3A38" w:themeFill="accent2" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="9E3A38" w:themeColor="accent2" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="C0C0C0" w:themeFill="text1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CCCCCC" w:themeFill="text1" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList-Accent1">
    <w:name w:val="Colorful List Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EDF2F8" w:themeFill="accent1" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9E3A38" w:themeFill="accent2" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="9E3A38" w:themeColor="accent2" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D3DFEE" w:themeFill="accent1" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DBE5F1" w:themeFill="accent1" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList-Accent2">
    <w:name w:val="Colorful List Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F8EDED" w:themeFill="accent2" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="9E3A38" w:themeFill="accent2" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="9E3A38" w:themeColor="accent2" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="EFD3D2" w:themeFill="accent2" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="F2DBDB" w:themeFill="accent2" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList-Accent3">
    <w:name w:val="Colorful List Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F5F8EE" w:themeFill="accent3" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="664E82" w:themeFill="accent4" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="664E82" w:themeColor="accent4" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="E6EED5" w:themeFill="accent3" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="EAF1DD" w:themeFill="accent3" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList-Accent4">
    <w:name w:val="Colorful List Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F2EFF6" w:themeFill="accent4" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="7E9C40" w:themeFill="accent3" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="7E9C40" w:themeColor="accent3" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="DFD8E8" w:themeFill="accent4" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E5DFEC" w:themeFill="accent4" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList-Accent5">
    <w:name w:val="Colorful List Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EDF6F9" w:themeFill="accent5" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="F2730A" w:themeFill="accent6" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="F2730A" w:themeColor="accent6" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="D2EAF1" w:themeFill="accent5" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DAEEF3" w:themeFill="accent5" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulList-Accent6">
    <w:name w:val="Colorful List Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="72"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="FEF4EC" w:themeFill="accent6" w:themeFillTint="19"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:bottom w:val="single" w:sz="12" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="348DA5" w:themeFill="accent5" w:themeFillShade="CC"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="348DA5" w:themeColor="accent5" w:themeShade="CC"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="single" w:sz="12" w:space="0" w:color="000000" w:themeColor="text1"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF" w:themeFill="background1"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:tcBorders>
          <w:top w:val="nil"/>
          <w:left w:val="nil"/>
          <w:bottom w:val="nil"/>
          <w:right w:val="nil"/>
          <w:insideH w:val="nil"/>
          <w:insideV w:val="nil"/>
        </w:tcBorders>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE4D0" w:themeFill="accent6" w:themeFillTint="3F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FDE9D9" w:themeFill="accent6" w:themeFillTint="33"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid">
    <w:name w:val="Colorful Grid"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="CCCCCC" w:themeFill="text1" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="999999" w:themeFill="text1" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="999999" w:themeFill="text1" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="000000" w:themeFill="text1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="808080" w:themeFill="text1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid-Accent1">
    <w:name w:val="Colorful Grid Accent 1"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="DBE5F1" w:themeFill="accent1" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="B8CCE4" w:themeFill="accent1" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="B8CCE4" w:themeFill="accent1" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="365F91" w:themeFill="accent1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="365F91" w:themeFill="accent1" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A7BFDE" w:themeFill="accent1" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid-Accent2">
    <w:name w:val="Colorful Grid Accent 2"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="F2DBDB" w:themeFill="accent2" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E5B8B7" w:themeFill="accent2" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E5B8B7" w:themeFill="accent2" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="943634" w:themeFill="accent2" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="943634" w:themeFill="accent2" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="DFA7A6" w:themeFill="accent2" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid-Accent3">
    <w:name w:val="Colorful Grid Accent 3"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="EAF1DD" w:themeFill="accent3" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D6E3BC" w:themeFill="accent3" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="D6E3BC" w:themeFill="accent3" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="76923C" w:themeFill="accent3" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="76923C" w:themeFill="accent3" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CDDDAC" w:themeFill="accent3" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid-Accent4">
    <w:name w:val="Colorful Grid Accent 4"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="E5DFEC" w:themeFill="accent4" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CCC0D9" w:themeFill="accent4" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="CCC0D9" w:themeFill="accent4" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="5F497A" w:themeFill="accent4" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="5F497A" w:themeFill="accent4" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="BFB1D0" w:themeFill="accent4" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid-Accent5">
    <w:name w:val="Colorful Grid Accent 5"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="DAEEF3" w:themeFill="accent5" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="B6DDE8" w:themeFill="accent5" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="B6DDE8" w:themeFill="accent5" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="31849B" w:themeFill="accent5" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="31849B" w:themeFill="accent5" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="A5D5E2" w:themeFill="accent5" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
  <w:style w:type="table" w:styleId="ColorfulGrid-Accent6">
    <w:name w:val="Colorful Grid Accent 6"/>
    <w:basedOn w:val="TableNormal"/>
    <w:uiPriority w:val="73"/>
    <w:rsid w:val="00CB0664"/>
    <w:pPr>
      <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
    </w:pPr>
    <w:rPr>
      <w:color w:val="000000" w:themeColor="text1"/>
    </w:rPr>
    <w:tblPr>
      <w:tblStyleRowBandSize w:val="1"/>
      <w:tblStyleColBandSize w:val="1"/>
      <w:tblInd w:w="0" w:type="dxa"/>
      <w:tblBorders>
        <w:insideH w:val="single" w:sz="4" w:space="0" w:color="FFFFFF" w:themeColor="background1"/>
      </w:tblBorders>
      <w:tblCellMar>
        <w:top w:w="0" w:type="dxa"/>
        <w:left w:w="108" w:type="dxa"/>
        <w:bottom w:w="0" w:type="dxa"/>
        <w:right w:w="108" w:type="dxa"/>
      </w:tblCellMar>
    </w:tblPr>
    <w:tcPr>
      <w:shd w:val="clear" w:color="auto" w:fill="FDE9D9" w:themeFill="accent6" w:themeFillTint="33"/>
    </w:tcPr>
    <w:tblStylePr w:type="firstRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBD4B4" w:themeFill="accent6" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastRow">
      <w:rPr>
        <w:b/>
        <w:bCs/>
        <w:color w:val="000000" w:themeColor="text1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBD4B4" w:themeFill="accent6" w:themeFillTint="66"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="firstCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E36C0A" w:themeFill="accent6" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="lastCol">
      <w:rPr>
        <w:color w:val="FFFFFF" w:themeColor="background1"/>
      </w:rPr>
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="E36C0A" w:themeFill="accent6" w:themeFillShade="BF"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Vert">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
    <w:tblStylePr w:type="band1Horz">
      <w:tblPr/>
      <w:tcPr>
        <w:shd w:val="clear" w:color="auto" w:fill="FBCAA2" w:themeFill="accent6" w:themeFillTint="7F"/>
      </w:tcPr>
    </w:tblStylePr>
  </w:style>
</w:styles>
