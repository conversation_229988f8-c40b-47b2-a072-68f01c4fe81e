Canonical name: PidLid<PERSON><PERSON>ressBookProviderArrayType
Description: Specifies the state of the electronic addresses of the contact and represents a set of bit flags.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008029
Data type: PtypInteger32, 0x0003
Area: Contact Properties

Canonical name: PidLidAddressBookProviderEmailList
Description: Specifies which electronic address properties are set on the Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008028
Data type: PtypMultipleInteger32, 0x1003
Area: Contact Properties

Canonical name: PidLidAddressCountryCode
Description: Specifies the country code portion of the mailing address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080DD
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidAgingDontAgeMe
Description: Specifies whether to automatically archive the message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000850E
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidLidAllAttendeesString
Description: Specifies a list of all the attendees except for the organizer, including resources and
unsendable attendees.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008238
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidAllowExternalCheck
Description: This property is set to TRUE.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008246
Data type: PtypBoolean, 0x000B
Area: Conferencing

Canonical name: PidLidAnniversaryEventEntryId
Description: Specifies the EntryID of the Appointment object that represents an anniversary of
the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000804E
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidAppointmentAuxiliaryFlags
Description: Specifies a bit field that describes the auxiliary state of the object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008207
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidAppointmentColor
Description: Specifies the color to be used when displaying the Calendar object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008214
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidAppointmentCounterProposal
Description: Indicates whether a Meeting Response object is a counter proposal.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008257
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidAppointmentDuration
Description: Specifies the length of the event, in minutes.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008213
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidAppointmentEndDate
Description: Indicates the date that the appointment ends.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008211
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidAppointmentEndTime
Description: Indicates the time that the appointment ends.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008210
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidAppointmentEndWhole
Description: Specifies the end date and time for the event.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000820E
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidAppointmentLastSequence
Description: Indicates to the organizer the last sequence number that was sent to any attendee.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008203
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidAppointmentMessageClass
Description: Indicates the message class of the Meeting object to be generated from the Meeting
Request object.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000024
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidAppointmentNotAllowPropose
Description: Indicates whether attendees are not allowed to propose a new date and/or time for the
meeting.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000825A
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidAppointmentProposalNumber
Description: Specifies the number of attendees who have sent counter proposals that have not
been accepted or rejected by the organizer.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008259
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidAppointmentProposedDuration
Description: Indicates the proposed value for the PidLidAppointmentDuration property (section
2.11) for a counter proposal.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008256
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidAppointmentProposedEndWhole
Description: Specifies the proposed value for the PidLidAppointmentEndWhole property (section
2.14) for a counter proposal.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008251
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidAppointmentProposedStartWhole
Description: Specifies the proposed value for the PidLidAppointmentStartWhole property (section
2.29) for a counter proposal.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008250
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidAppointmentRecur
Description: Specifies the dates and times when a recurring series occurs.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008216
Data type: PtypBinary, 0x0102
Area: Calendar

Canonical name: PidLidAppointmentReplyName
Description: Specifies the user who last replied to the meeting request or meeting update.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008230
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidAppointmentReplyTime
Description: Specifies the date and time at which the attendee responded to a received meeting
request or Meeting Update object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008220
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidAppointmentSequence
Description: Specifies the sequence number of a Meeting object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008201
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidAppointmentSequenceTime
Description: Indicates the date and time at which the PidLidAppointmentSequence property
(section 2.25) was last modified.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008202
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidAppointmentStartDate
Description: Identifies the date that the appointment starts.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008212
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidAppointmentStartTime
Description: Identifies the time that the appointment starts.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000820F
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidAppointmentStartWhole
Description: Specifies the start date and time of the appointment.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000820D
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidAppointmentStateFlags
Description: Specifies a bit field that describes the state of the object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008217
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidAppointmentSubType
Description: Specifies whether the event is an all-day event.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008215
Data type: PtypBoolean, 0x000B
Area: Calendar

Canonical name: PidLidAppointmentTimeZoneDefinitionEndDisplay
Description: Specifies time zone information that indicates the time zone of the
PidLidAppointmentEndWhole property (section 2.14).
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000825F
Data type: PtypBinary, 0x0102
Area: Calendar

Canonical name: PidLidAppointmentTimeZoneDefinitionRecur
Description: Specifies time zone information that describes how to convert the meeting date and
time on a recurring series to and from UTC.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008260
Data type: PtypBinary, 0x0102
Area: Calendar

Canonical name: PidLidAppointmentTimeZoneDefinitionStartDisplay
Description: Specifies time zone information that indicates the time zone of the
PidLidAppointmentStartWhole property (section 2.29).
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000825E
Data type: PtypBinary, 0x0102
Area: Calendar

Canonical name: PidLidAppointmentUnsendableRecipients
Description: Contains a list of unsendable attendees.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000825D
Data type: PtypBinary, 0x0102
Area: Meetings

Canonical name: PidLidAppointmentUpdateTime
Description: Indicates the time at which the appointment was last updated.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008226
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidAttendeeCriticalChange
Description: Specifies the date and time at which the meeting-related object was sent.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000001
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidAutoFillLocation
Description: Indicates whether the value of the PidLidLocation property (section 2.159) is set to the PidTagDisplayName property (section 2.670).
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000823A
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidAutoLog
Description: Specifies to the application whether to create a Journal object for each action
associated with this Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008025
Data type: PtypBoolean, 0x000B
Area: Contact Properties

Canonical name: PidLidAutoProcessState
Description: Specifies the options used in the automatic processing of email messages.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000851A
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidLidAutoStartCheck
Description: Specifies whether to automatically start the conferencing application when a reminder
for the start of a meeting is executed.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008244
Data type: PtypBoolean, 0x000B
Area: Conferencing

Canonical name: PidLidBilling
Description: Specifies billing information for the contact.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008535
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidBirthdayEventEntryId
Description: Specifies the EntryID of an optional Appointment object that represents the birthday
of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000804D
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidBirthdayLocal
Description: Specifies the birthday of a contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080DE
Data type: PtypTime, 0x0040
Area: Contact Properties

Canonical name: PidLidBusinessCardCardPicture
Description: Contains the image to be used on a business card.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008041
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidBusinessCardDisplayDefinition
Description: Contains user customization details for displaying a contact as a business card.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008040
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidBusyStatus
Description: Specifies the availability of a user for the event described by the object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008205
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidCalendarType
Description: Contains the value of the CalendarType field from the PidLidAppointmentRecur
property (section 2.22).
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000001C
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidCategories
Description: Contains the array of text labels assigned to this Message object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property long ID (LID): 0x00009000
Data type: PtypMultipleString, 0x101F
Area: Common

Canonical name: PidLidCcAttendeesString
Description: Contains a list of all the sendable attendees who are also optional attendees.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000823C
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidChangeHighlight
Description: Specifies a bit field that indicates how the Meeting object has changed.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008204
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidClassification
Description: Contains a list of the classification categories to which the associated Message object
has been assigned.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085B6
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidClassificationDescription
Description: Contains a human-readable summary of each of the classification categories included in
the PidLidClassification property (section 2.53).
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085B7
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidClassificationGuid
Description: Contains the GUID that identifies the list of email classification categories used by a
Message object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085B8
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidClassificationKeep
Description: Indicates whether the message uses any classification categories.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085BA
Data type: PtypBoolean, 0x000B
Area: General Message Properties

Canonical name: PidLidClassified
Description: Indicates whether the contents of this message are regarded as classified information.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085B5
Data type: PtypBoolean, 0x000B
Area: General Message Properties

Canonical name: PidLidCleanGlobalObjectId
Description: Contains the value of the PidLidGlobalObjectId property (section 2.142) for an object
that represents an Exception object to a recurring series, where the Year, Month, and Day fields
are all zero.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000023
Data type: PtypBinary, 0x0102
Area: Meetings

Canonical name: PidLidClientIntent
Description: Indicates what actions the user has taken on this Meeting object.
Property set: PSETID_CalendarAssistant {11000E07-B51B-40D6-AF21-CAA85EDAB1D0}
Property long ID (LID): 0x00000015
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidClipEnd
Description: Specifies the end date and time of the event in UTC.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008236
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidClipStart
Description: Specifies the start date and time of the event in UTC.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008235
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidCollaborateDoc
Description: Specifies the document to be launched when the user joins the meeting.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008247
Data type: PtypString, 0x001F
Area: Conferencing

Canonical name: PidLidCommonEnd
Description: Indicates the end time for the Message object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008517
Data type: PtypTime, 0x0040
Area: General Message Properties

Canonical name: PidLidCommonStart
Description: Indicates the start time for the Message object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008516
Data type: PtypTime, 0x0040
Area: General Message Properties

Canonical name: PidLidCompanies
Description: Contains a list of company names, each of which is associated with a contact that is specified in the PidLidContacts property ( section ********.2).
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008539
Data type: PtypMultipleString, 0x101F
Area: General Message Properties

Canonical name: PidLidConferencingCheck
Description:
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008240
Data type: PtypBoolean, 0x000B
Area: Conferencing

Canonical name: PidLidConferencingType
Description: Specifies the type of the meeting.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008241
Data type: PtypInteger32, 0x0003
Area: Conferencing

Canonical name: PidLidContactCharacterSet
Description: Specifies the character set used for a Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008023
Data type: PtypInteger32, 0x0003
Area: Contact Properties

Canonical name: PidLidContactItemData
Description: Specifies the visible fields in the application's user interface that are used to help display
the contact information.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008007
Data type: PtypMultipleInteger32, 0x1003
Area: Contact Properties

Canonical name: PidLidContactLinkedGlobalAddressListEntryId
Description: Specifies the EntryID of the GAL contact to which the duplicate contact is linked.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080E2
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidContactLinkEntry
Description: Contains the elements of the PidLidContacts property (section 2.77).
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008585
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidContactLinkGlobalAddressListLinkId
Description: Specifies the GUID of the GAL contact to which the duplicate contact is linked.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080E8
Data type: PtypGuid, 0x0048
Area: Contact Properties

Canonical name: PidLidContactLinkGlobalAddressListLinkState
Description: Specifies the state of the linking between the GAL contact and the duplicate contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080E6
Data type: PtypInteger32, 0x0003
Area: Contact Properties

Canonical name: PidLidContactLinkLinkRejectHistory
Description: Contains a list of GAL contacts that were previously rejected for linking with the
duplicate contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080E5
Data type: PtypMultipleBinary, 0x1102
Area: Contact Properties

Canonical name: PidLidContactLinkName
Description:
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008586
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidContactLinkSearchKey
Description: Contains the list of SearchKeys for a Contact object linked to by the Message object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008584
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidContactLinkSMTPAddressCache
Description: Contains a list of the SMTP addresses that are used by the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080E3
Data type: PtypMultipleString, 0x101F
Area: Contact Properties

Canonical name: PidLidContacts
Description: Contains the PidTagDisplayName property (section 2.670) of each Address Book EntryID referenced in the value of the PidLidContactLinkEntry property (section 2.70).
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000853A
Data type: PtypMultipleString, 0x101F
Area: General Message Properties

Canonical name: PidLidContactUserField1
Description: Contains text used to add custom text to a business card representation of a Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000804F
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidContactUserField2
Description: Contains text used to add custom text to a business card representation of a Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008050
Data type: PtypString, 0x001F
Area: Contact Properties


Canonical name: PidLidContactUserField3
Description: Contains text used to add custom text to a business card representation of a Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008051
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidContactUserField4
Description: Contains text used to add custom text to a business card representation of a Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008052
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidConversationActionLastAppliedTime
Description: Contains the time, in UTC, that an Email object was last received in the conversation, or the last time that the user modified the conversation action, whichever occurs later.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085CA
Data type: PtypTime, 0x0040
Area: Conversation Actions

Canonical name: PidLidConversationActionMaxDeliveryTime
Description: Contains the maximum value of the PidTagMessageDeliveryTime property (section2.783) of all of the Email objects modified in response to the last time that the user changed a conversation action on the client.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085C8
Data type: PtypTime, 0x0040
Area: Conversation Actions

Canonical name: PidLidConversationActionMoveFolderEid
Description: Contains the EntryID for the destination folder.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085C6
Data type: PtypBinary, 0x0102
Area: Conversation Actions

Canonical name: PidLidConversationActionMoveStoreEid
Description: Contains the EntryID for a move to a folder in a different message store.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085C7
Data type: PtypBinary, 0x0102
Area: Conversation Actions

Canonical name: PidLidConversationActionVersion
Description: Contains the version of the conversation action FAI message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085CB
Data type: PtypInteger32, 0x0003
Area: Conversation Actions

Canonical name: PidLidConversationProcessed
Description: Specifies a sequential number to be used in the processing of a conversation action.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085C9
Data type: PtypInteger32, 0x0003
Area: Conversation Actions

Canonical name: PidLidCurrentVersion
Description: Specifies the build number of the client application that sent the message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008552
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidLidCurrentVersionName
Description: Specifies the name of the client application that sent the message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008554
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidDayInterval
Description: Identifies the day interval for the recurrence pattern.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000011
Data type: PtypInteger16, 0x0002
Area: Meetings

Canonical name: PidLidDayOfMonth
Description: Identifies the day of the month for the appointment or meeting.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00001000
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidDelegateMail
Description: Indicates whether a delegate responded to the meeting request.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000009
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidDepartment
Description: This property is ignored by the server and is set to an empty string by the client.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008010
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidDirectory
Description: Specifies the directory server to be used.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008242
Data type: PtypString, 0x001F
Area: Conferencing

Canonical name: PidLidDistributionListChecksum
Description: Specifies the 32-bit cyclic redundancy check (CRC) polynomial checksum, as specified in [ISO/IEC8802-3], calculated on the value of the PidLidDistributionListMembers
property (section 2.96).
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000804C
Data type: PtypInteger32, 0x0003
Area: Contact Properties

Canonical name: PidLidDistributionListMembers
Description: Specifies the list of EntryIDs of the objects corresponding to the members of the
personal distribution list.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008055
Data type: PtypMultipleBinary, 0x1102
Area: Contact Properties

Canonical name: PidLidDistributionListName
Description: Specifies the name of the personal distribution list.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008053
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidDistributionListOneOffMembers
Description: Specifies the list of one-off EntryIDs corresponding to the members of the personal distribution list.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008054
Data type: PtypMultipleBinary, 0x1102
Area: Contact Properties

Canonical name: PidLidDistributionListStream
Description: Specifies the list of EntryIDs and one-off EntryIDs corresponding to the members of
the personal distribution list.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008064
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidEmail1AddressType
Description: Specifies the address type of an electronic address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008082
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail1DisplayName
Description: Specifies the user-readable display name for the email address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008080
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail1EmailAddress
Description: Specifies the email address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008083
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail1OriginalDisplayName
Description: Specifies the SMTP email address that corresponds to the email address for the Contact object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008084
Data type: PtypString, 0x001F
Area: Contact Properties
EXSCHEMA_MAPI_EMAIL1ORIGINALDISPLAYNAME

Canonical name: PidLidEmail1OriginalEntryId
Description: Specifies the EntryID of the object corresponding to this electronic address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008085
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidEmail2AddressType
Description: Specifies the address type of the electronic address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008092
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail2DisplayName
Description: Specifies the user-readable display name for the email address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008090
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail2EmailAddress
Description: Specifies the email address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008093
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail2OriginalDisplayName
Description: Specifies the SMTP email address that corresponds to the email address for the Contact
object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008094
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail2OriginalEntryId
Description: Specifies the EntryID of the object that corresponds to this electronic address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008095
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidEmail3AddressType
Description: Specifies the address type of the electronic address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080A2
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail3DisplayName
Description: Specifies the user-readable display name for the email address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080A0
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail3EmailAddress
Description: Specifies the email address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080A3
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail3OriginalDisplayName
Description: Specifies the SMTP email address that corresponds to the email address for the Contact
object.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080A4
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidEmail3OriginalEntryId
Description: Specifies the EntryID of the object that corresponds to this electronic address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080A5
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidEndRecurrenceDate
Description: Identifies the end date of the recurrence range.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000000F
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidEndRecurrenceTime
Description: Identifies the end time of the recurrence range.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000010
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidExceptionReplaceTime
Description: Specifies the date and time, in UTC, within a recurrence pattern that an exception will
replace.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008228
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidLidFax1AddressType
Description: Contains the string value "FAX".
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080B2
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax1EmailAddress
Description: Contains a user-readable display name, followed by the "@" character, followed by a
fax number.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080B3
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax1OriginalDisplayName
Description: Contains the same value as the PidTagNormalizedSubject property (section 2.806).
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080B4
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax1OriginalEntryId
Description: Specifies a one-off EntryID that corresponds to this fax address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080B5
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidFax2AddressType
Description: Contains the string value "FAX".
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080C2
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax2EmailAddress
Description: Contains a user-readable display name, followed by the "@" character, followed by a
fax number.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080C3
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax2OriginalDisplayName
Description: Contains the same value as the PidTagNormalizedSubject property (section 2.806).
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080C4
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax2OriginalEntryId
Description: Specifies a one-off EntryID corresponding to this fax address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080C5
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidFax3AddressType
Description: Contains the string value "FAX".
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080D2
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax3EmailAddress
Description: Contains a user-readable display name, followed by the "@" character, followed by a
fax number.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080D3
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax3OriginalDisplayName
Description: Contains the same value as the PidTagNormalizedSubject property (section 2.806).
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080D4
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFax3OriginalEntryId
Description: Specifies a one-off EntryID that corresponds to this fax address.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080D5
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidFExceptionalAttendees
Description: Indicates that the object is a Recurring Calendar object with one or more exceptions,
and that at least one of the Exception Embedded Message objects has at least one RecipientRow
structure, as described in  section 2.8.3.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000822B
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidFExceptionalBody
Description: Indicates that the Exception Embedded Message object has a body that differs from
the Recurring Calendar object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008206
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidFileUnder
Description: Specifies the name under which to file a contact when displaying a list of contacts.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008005
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidFileUnderId
Description: Specifies how to generate and recompute the value of the PidLidFileUnder property
(section 2.132) when other contact name properties change.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008006
Data type: PtypInteger32, 0x0003
Area: Contact Properties

Canonical name: PidLidFileUnderList
Description: Specifies a list of possible values for the PidLidFileUnderId property (section 2.133).
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008026
Data type: PtypMultipleInteger32, 0x1003
Area: Contact Properties

Canonical name: PidLidFInvited
Description: Indicates whether invitations have been sent for the meeting that this Meeting object
represents.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008229
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidFlagRequest
Description: Contains user-specifiable text to be associated with the flag.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008530
Data type: PtypString, 0x001F
Area: Flagging

Canonical name: PidLidFlagString
Description: Contains an index identifying one of a set of pre-defined text strings to be associated
with the flag.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085C0
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidForwardInstance
Description: Indicates whether the Meeting Request object represents an exception to a
recurring series, and whether it was forwarded (even when forwarded by the organizer) rather
than being an invitation sent by the organizer.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000820A
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidForwardNotificationRecipients
Description: Contains a list of RecipientRow structures, as described in  section
2.8.3, that indicate the recipients of a meeting forward.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008261
Data type: PtypBinary, 0x0102
Area: Meetings

Canonical name: PidLidFOthersAppointment
Description: Indicates whether the Calendar folder from which the meeting was opened is another
user's calendar.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000822F
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidFreeBusyLocation
Description: Specifies a URL path from which a client can retrieve free/busy status information for the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080D8
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidGlobalObjectId
Description: Contains an ID for an object that represents an exception to a recurring series.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000003
Data type: PtypBinary, 0x0102
Area: Meetings

Canonical name: PidLidHasPicture
Description: Specifies whether the attachment has a picture.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008015
Data type: PtypBoolean, 0x000B
Area: Contact Properties

Canonical name: PidLidHomeAddress
Description: Specifies the complete address of the home address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000801A
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidHomeAddressCountryCode
Description: Specifies the country code portion of the home address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080DA
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidHtml
Description: Specifies the business webpage URL of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000802B
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidICalendarDayOfWeekMask
Description: Identifies the day of the week for the appointment or meeting.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00001001
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidInboundICalStream
Description: Contains the contents of the iCalendar MIME part of the original MIME message.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000827A
Data type: PtypBinary, 0x0102
Area: Calendar

Canonical name: PidLidInfoPathFormName
Description: Contains the name of the form associated with this message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085B1
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidLidInstantMessagingAddress
Description: Specifies the instant messaging address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008062
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidIntendedBusyStatus
Description: Contains the value of the PidLidBusyStatus property (section 2.47) on the Meeting
object in the organizer's calendar at the time that the Meeting Request object or Meeting
Update object was sent.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008224
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidInternetAccountName
Description: Specifies the user-visible email account name through which the email message is sent.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008580
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidInternetAccountStamp
Description: Specifies the email account ID through which the email message is sent.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008581
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidIsContactLinked
Description: Specifies whether the contact is linked to other contacts.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080E0
Data type: PtypBoolean, 0x000B
Area: Contact Properties

Canonical name: PidLidIsException
Description: Indicates whether the object represents an exception (including an orphan instance).
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000000A
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidIsRecurring
Description: Specifies whether the object is associated with a recurring series.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000005
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidIsSilent
Description: Indicates whether the user did not include any text in the body of the Meeting
Response object.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000004
Data type: PtypBoolean, 0x000B
Area: Meetings

Canonical name: PidLidLinkedTaskItems
Description: Indicates whether the user did not include any text in the body of the Meeting
Response object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000820C
Data type: PtypMultipleBinary, 0x1102
Area: Tasks

Canonical name: PidLidLocation
Description: Specifies the location of the event.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008208
Data type: PtypString, 0x001F
Area: Calendar

Canonical name: PidLidLogDocumentPosted
Description: Indicates whether the document was sent by email or posted to a server folder during
journaling.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008711
Data type: PtypBoolean, 0x000B
Area: Journal

Canonical name: PidLidLogDocumentPrinted
Description: Indicates whether the document was printed during journaling.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x0000870E
Data type: PtypBoolean, 0x000B
Area: Journal

Canonical name: PidLidLogDocumentRouted
Description: Indicates whether the document was sent to a routing recipient during journaling.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008710
Data type: PtypBoolean, 0x000B
Area: Journal

Canonical name: PidLidLogDocumentSaved
Description: Indicates whether the document was saved during journaling.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x0000870F
Data type: PtypBoolean, 0x000B
Area: Journal

Canonical name: PidLidLogDuration
Description: Contains the duration, in minutes, of the activity.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008707
Data type: PtypInteger32, 0x0003
Area: Journal

Canonical name: PidLidLogEnd
Description: Contains the time, in UTC, at which the activity ended.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008708
Data type: PtypTime, 0x0040
Area: Journal

Canonical name: PidLidLogFlags
Description: Contains metadata about the Journal object.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x0000870C
Data type: PtypInteger32, 0x0003
Area: Journal

Canonical name: PidLidLogStart
Description: Contains the time, in UTC, at which the activity began.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008706
Data type: PtypTime, 0x0040
Area: Journal

Canonical name: PidLidLogType
Description: Briefly describes the journal activity that is being recorded.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008700
Data type: PtypString, 0x001F
Area: Journal

Canonical name: PidLidLogTypeDesc
Description: Contains an expanded description of the journal activity that is being recorded.
Property set: PSETID_Log {0006200A-0000-0000-C000-************}
Property long ID (LID): 0x00008712
Data type: PtypString, 0x001F
Area: Journal

Canonical name: PidLidMeetingType
Description: Indicates the type of Meeting Request object or Meeting Update object.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000026
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidMeetingWorkspaceUrl
Description: Specifies the URL of the Meeting Workspace that is associated with a Calendar
object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008209
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidMonthInterval
Description: Indicates the monthly interval of the appointment or meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000013
Data type: PtypInteger16, 0x0002
Area: Meetings

Canonical name: PidLidMonthOfYear
Description: Indicates the month of the year in which the appointment or meeting occurs.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00001006
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidMonthOfYearMask
Description: Indicates the calculated month of the year in which the appointment or meeting occurs.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000017
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidNetShowUrl
Description: Specifies the URL to be launched when the user joins the meeting.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008248
Data type: PtypString, 0x001F
Area: Conferencing

Canonical name: PidLidNoEndDateFlag
Description: Indicates whether the recurrence pattern has an end date.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000100B
Data type: PtypBoolean, 0x000B
Area: Calendar

Canonical name: PidLidNonSendableBcc
Description: Contains a list of all of the unsendable attendees who are also resources.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008538
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidNonSendableCc
Description: Contains a list of all of the unsendable attendees who are also optional attendees.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008537
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidNonSendableTo
Description: Contains a list of all of the unsendable attendees who are also required attendees.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008536
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidNonSendBccTrackStatus
Description: Contains the value from the response table.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008545
Data type: PtypMultipleInteger32, 0x1003
Area: General Message Properties

Canonical name: PidLidNonSendCcTrackStatus
Description: Contains the value from the response table.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008544
Data type: PtypMultipleInteger32, 0x1003
Area: General Message Properties

Canonical name: PidLidNonSendToTrackStatus
Description: Contains the value from the response table.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008543
Data type: PtypMultipleInteger32, 0x1003
Area: General Message Properties

Canonical name: PidLidNoteColor
Description: Specifies the suggested background color of the Note object.
Property set: PSETID_Note {0006200E-0000-0000-C000-************}
Property long ID (LID): 0x00008B00
Data type: PtypInteger32, 0x0003
Area: Sticky Notes

Canonical name: PidLidNoteHeight
Description: Specifies the height of the visible message window in pixels.
Property set: PSETID_Note {0006200E-0000-0000-C000-************}
Property long ID (LID): 0x00008B03
Data type: PtypInteger32, 0x0003
Area: Sticky Notes

Canonical name: PidLidNoteWidth
Description: Specifies the width of the visible message window in pixels.
Property set: PSETID_Note {0006200E-0000-0000-C000-************}
Property long ID (LID): 0x00008B02
Data type: PtypInteger32, 0x0003
Area: Sticky Notes

Canonical name: PidLidNoteX
Description: Specifies the distance, in pixels, from the left edge of the screen that a user interface
displays a Note object.
Property set: PSETID_Note {0006200E-0000-0000-C000-************}
Property long ID (LID): 0x00008B04
Data type: PtypInteger32, 0x0003
Area: Sticky Notes

Canonical name: PidLidNoteY
Description: Specifies the distance, in pixels, from the top edge of the screen that a user interface
displays a Note object.
Property set: PSETID_Note {0006200E-0000-0000-C000-************}
Property long ID (LID): 0x00008B05
Data type: PtypInteger32, 0x0003
Area: Sticky Notes

Canonical name: PidLidOccurrences
Description: Indicates the number of occurrences in the recurring appointment or meeting.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00001005
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidOldLocation
Description: Indicates the original value of the PidLidLocation property (section 2.159) before a
meeting update.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000028
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidOldRecurrenceType
Description: Indicates the recurrence pattern for the appointment or meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000018
Data type: PtypInteger16, 0x0002
Area: Meetings

Canonical name: PidLidOldWhenEndWhole
Description: Indicates the original value of the PidLidAppointmentEndWhole property (section
2.14) before a meeting update.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000002A
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidOldWhenStartWhole
Description: Indicates the original value of the PidLidAppointmentStartWhole property (section
2.29) before a meeting update.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000029
Data type: PtypTime, 0x0040
Area: Meetings

Canonical name: PidLidOnlinePassword
Description: Specifies the password for a meeting on which the PidLidConferencingType property
(section 2.66) has the value 0x00000002.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008249
Data type: PtypString, 0x001F
Area: Conferencing

Canonical name: PidLidOptionalAttendees
Description: Specifies optional attendees.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000007
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidOrganizerAlias
Description: Specifies the email address of the organizer.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008243
Data type: PtypString, 0x001F
Area: Conferencing

Canonical name: PidLidOriginalStoreEntryId
Description: Specifies the EntryID of the delegator’s message store.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008237
Data type: PtypBinary, 0x0102
Area: Meetings

Canonical name: PidLidOtherAddress
Description: Specifies the complete address of the other address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000801C
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidOtherAddressCountryCode
Description: Specifies the country code portion of the other address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080DC
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidOwnerCriticalChange
Description: Specifies the date and time at which a Meeting Request object was sent by the
organizer.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000001A
Data type: PtypTime, 0x0040
http://schemas.microsoft.com/mapi/owner_critical_change
Area: Meetings

Canonical name: PidLidOwnerName
Description: Indicates the name of the owner of the mailbox.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000822E
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidPendingStateForSiteMailboxDocument
Description: Specifies the synchronization state of the Document object that is in the Document
Libraries folder of the site mailbox.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085E0
Data type: PtypInteger32, 0x0003
Area: Site Mailbox

Canonical name: PidLidPercentComplete
Description: Indicates whether a time-flagged Message object is complete.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008102
Data type: PtypFloating64, 0x0005
Area: Tasks

Canonical name: PidLidPostalAddressId
Description: Specifies which physical address is the mailing address for this contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008022
Data type: PtypInteger32, 0x0003
Area: Contact Properties

Canonical name: PidLidPostRssChannel
Description: Contains the contents of the title field from the XML of the Atom feed or RSS channel.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008904
Data type: PtypString, 0x001F
Area: RSS

Canonical name: PidLidPostRssChannelLink
Description: Contains the URL of the RSS or Atom feed from which the XML file came.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008900
Data type: PtypString, 0x001F
Area: RSS

Canonical name: PidLidPostRssItemGuid
Description: Contains a unique identifier for the RSS object.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008903
Data type: PtypString, 0x001F
Area: RSS

Canonical name: PidLidPostRssItemHash
Description: Contains a hash of the feed XML computed by using an implementation-dependent
algorithm.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008902
Data type: PtypInteger32, 0x0003
Area: RSS

Canonical name: PidLidPostRssItemLink
Description: Contains the URL of the link from an RSS or Atom item.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008901
Data type: PtypString, 0x001F
Area: RSS

Canonical name: PidLidPostRssItemXml
Description: Contains the item element and all of its sub-elements from an RSS feed, or the entry
element and all of its sub-elements from an Atom feed.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008905
Data type: PtypString, 0x001F
Area: RSS

Canonical name: PidLidPostRssSubscription
Description: Contains the user's preferred name for the RSS or Atom subscription.
Property set: PSETID_PostRss {00062041-0000-0000-C000-************}
Property long ID (LID): 0x00008906
Data type: PtypString, 0x001F
Area: RSS

Canonical name: PidLidPrivate
Description: Indicates whether the end user wishes for this Message object to be hidden from other
users who have access to the Message object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008506
Data type: PtypBoolean, 0x000B
Area: General Message Properties

Canonical name: PidLidPromptSendUpdate
Description: Indicates that the Meeting Response object was out-of-date when it was received.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008045
Data type: PtypBoolean, 0x000B
Area: Meeting Response

Canonical name: PidLidRecurrenceDuration
Description: Identifies the length, in minutes, of the appointment or meeting.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000100D
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidRecurrencePattern
Description: Specifies a description of the recurrence pattern of the Calendar object.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008232
Data type: PtypString, 0x001F
Area: Calendar

Canonical name: PidLidRecurrenceType
Description: Specifies the recurrence type of the recurring series.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008231
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidRecurring
Description: Specifies whether the object represents a recurring series.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008223
Data type: PtypBoolean, 0x000B
Area: Calendar

Canonical name: PidLidReferenceEntryId
Description: Specifies the value of the EntryID of the Contact object unless the Contact object is a
copy of an earlier original.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085BD
Data type: PtypBinary, 0x0102
Area: Contact Properties

Canonical name: PidLidReminderDelta
Description: Specifies the interval, in minutes, between the time at which the reminder first
becomes overdue and the start time of the Calendar object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008501
Data type: PtypInteger32, 0x0003
Area: Reminders

Canonical name: PidLidReminderFileParameter
Description: Specifies the filename of the sound that a client is to play when the reminder for that
object becomes overdue.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000851F
Data type: PtypString, 0x001F
Area: Reminders

Canonical name: PidLidReminderOverride
Description: Specifies whether the client is to respect the current values of the
PidLidReminderPlaySound property (section 2.221) and the PidLidReminderFileParameter
property (section 2.219), or use the default values for those properties.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000851C
Data type: PtypBoolean, 0x000B
Area: Reminders

Canonical name: PidLidReminderPlaySound
Description: Specifies whether the client is to play a sound when the reminder becomes overdue.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000851E
Data type: PtypBoolean, 0x000B
Area: Reminders

Canonical name: PidLidReminderSet
Description: Specifies whether a reminder is set on the object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008503
Data type: PtypBoolean, 0x000B
Area: Reminders

Canonical name: PidLidReminderSignalTime
Description: Specifies the point in time when a reminder transitions from pending to overdue.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008560
Data type: PtypTime, 0x0040
Area: Reminders

Canonical name: PidLidReminderTime
Description: Specifies the initial signal time for objects that are not Calendar objects.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008502
Data type: PtypTime, 0x0040
Area: Reminders

Canonical name: PidLidReminderTimeDate
Description: Indicates the time and date of the reminder for the appointment or meeting.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008505
Data type: PtypTime, 0x0040
Area: Reminders

Canonical name: PidLidReminderTimeTime
Description: Indicates the time of the reminder for the appointment or meeting.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008504
Data type: PtypTime, 0x0040
Area: Reminders

Canonical name: PidLidReminderType
Description: This property is not set and, if set, is ignored.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000851D
Data type: PtypInteger32, 0x0003
Area: Reminders

Canonical name: PidLidRemoteStatus
Description: Indicates the remote status of the calendar item.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008511
Data type: PtypInteger32, 0x0003
Area: Run-time configuration

Canonical name: PidLidRequiredAttendees
Description: Identifies required attendees for the appointment or meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000006
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidResourceAttendees
Description: Identifies resource attendees for the appointment or meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000008
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidResponseStatus
Description: Specifies the response status of an attendee.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008218
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidServerProcessed
Description: Indicates whether the Meeting Request object or Meeting Update object has been processed.
Property set: PSETID_CalendarAssistant {11000E07-B51B-40D6-AF21-CAA85EDAB1D0}
Property long ID (LID): 0x000085CC
Data type: PtypBoolean, 0x000B
Area: Calendar

Canonical name: PidLidServerProcessingActions
Description: Indicates what processing actions have been taken on this Meeting Request object or Meeting Update object.
Property set: PSETID_CalendarAssistant {11000E07-B51B-40D6-AF21-CAA85EDAB1D0}
Property long ID (LID): 0x000085CD
Data type: PtypInteger32, 0x0003
Area: Calendar

Canonical name: PidLidSharingAnonymity
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A19
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingBindingEntryId
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A2D
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingBrowseUrl
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A51
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingCapabilities
Description: Indicates that the Message object relates to a special folder.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A17
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingConfigurationUrl
Description: Contains a zero-length string.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A24
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingDataRangeEnd
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A45
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingDataRangeStart
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A44
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingDetail
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A2B
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingExtensionXml
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A21
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingFilter
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A13
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingFlags
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A0A
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingFlavor
Description: Indicates the type of Sharing Message object.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A18
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingFolderEntryId
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A15
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingIndexEntryId
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A2E
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingInitiatorEntryId
Description: Contains the value of the PidTagEntryId property (section 2.677) for the Address Book object of the currently logged-on user.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A09
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingInitiatorName
Description: Contains the value of the PidTagDisplayName property (section 2.670) from the
Address Book object identified by the PidLidSharingInitiatorEntryId property (section 2.248).
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A07
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingInitiatorSmtp
Description: Contains the value of the PidTagSmtpAddress property (section 2.1014) from the
Address Book object identified by the PidLidSharingInitiatorEntryId property (section 2.248).
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A08
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingInstanceGuid
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A1C
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingLastAutoSyncTime
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A55
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingLastSyncTime
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A1F
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingLocalComment
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A4D
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingLocalLastModificationTime
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A23
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingLocalName
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A0F
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingLocalPath
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A0E
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingLocalStoreUid
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A49
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingLocalType
Description: Contains the value of the PidTagContainerClass property (section 2.636) of the folder being shared.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A14
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingLocalUid
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A10
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingOriginalMessageEntryId
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A29
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingParentBindingEntryId
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A5C
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingParticipants
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A1E
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingPermissions
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A1B
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingProviderExtension
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A0B
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingProviderGuid
Description: Contains the value "%xAE.F0.***********.00.00.C0.***********.00.00.46".
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A01
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSharingProviderName
Description: Contains a user-displayable name of the sharing provider identified by the PidLidSharingProviderGuid property (section 2.266).
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A02
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingProviderUrl
Description: Contains a URL related to the sharing provider identified by the PidLidSharingProviderGuid property (section 2.266).
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A03
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRangeEnd
Description: Contains a value that is ignored by the server no matter what value is generated by the client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A47
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingRangeStart
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A46
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingReciprocation
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A1A
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingRemoteByteSize
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A4B
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingRemoteComment
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A2F
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemoteCrc
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A4C
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingRemoteLastModificationTime
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A22
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingRemoteMessageCount
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A4F
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingRemoteName
Description: Contains the value of the PidTagDisplayName property (section 2.670) on the folder
being shared.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A05
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemotePass
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A0D
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemotePath
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A04
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemoteStoreUid
Description: Contains a hexadecimal string representation of the value of the PidTagStoreEntryId
property (section 2.1022) on the folder being shared.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A48
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemoteType
Description: Contains the same value as the PidLidSharingLocalType property (section 2.259).
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A1D
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemoteUid
Description: Contains the EntryID of the folder being shared.
Property set: PSTID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A06
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemoteUser
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A0C
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingRemoteVersion
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A5B
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidLidSharingResponseTime
Description: Contains the time at which the recipient of the sharing request sent a sharing
response.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A28
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingResponseType
Description: Contains the type of response with which the recipient of the sharing request
responded.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A27
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingRoamLog
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A4E
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingStart
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A25
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingStatus
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A00
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingStop
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A26
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingSyncFlags
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A60
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingSyncInterval
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A2A
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingTimeToLive
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A2C
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingTimeToLiveAuto
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A56
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingWorkingHoursDays
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A42
Data type: PtypInteger32, 0x0003
Area: Sharing

Canonical name: PidLidSharingWorkingHoursEnd
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A41
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingWorkingHoursStart
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A40
Data type: PtypTime, 0x0040
Area: Sharing

Canonical name: PidLidSharingWorkingHoursTimeZone
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PSETID_Sharing {00062040-0000-0000-C000-************}
Property long ID (LID): 0x00008A43
Data type: PtypBinary, 0x0102
Area: Sharing

Canonical name: PidLidSideEffects
Description: Specifies how a Message object is handled by the client in relation to certain user
interface actions by the user, such as deleting a message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008510
Data type: PtypInteger32, 0x0003
Area: Run-time configuration



Canonical name: PidLidSingleBodyICal
Description: Indicates that the original MIME message contained a single MIME part.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000827B
Data type: PtypBoolean, 0x000B
Area: Calendar

Canonical name: PidLidSmartNoAttach
Description: Indicates whether the Message object has no end-user visible attachments.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008514
Data type: PtypBoolean, 0x000B
Area: Run-time configuration

Canonical name: PidLidSpamOriginalFolder
Description: Specifies which folder a message was in before it was filtered into the Junk Email folder.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x0000859C
Data type: PtypBinary, 0x0102
Area: Spam

Canonical name: PidLidStartRecurrenceDate
Description: Identifies the start date of the recurrence pattern.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000000D
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidStartRecurrenceTime
Description: Identifies the start time of the recurrence pattern.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000000E
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidTaskAcceptanceState
Description: Indicates the acceptance state of the task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000812A
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskAccepted
Description: Indicates whether a task assignee has replied to a task request for this Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008108
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskActualEffort
Description: Indicates the number of minutes that the user actually spent working on a task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008110
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskAssigner
Description: Specifies the name of the user that last assigned the task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008121
Data type: PtypString, 0x001F
Area: Tasks

Canonical name: PidLidTaskAssigners
Description: Contains a stack of entries, each of which represents a task assigner.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008117
Data type: PtypBinary, 0x0102
Area: Tasks

Canonical name: PidLidTaskComplete
Description: Indicates that the task is complete.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000811C
Data type: PtypBoolean, 0x000B
Area: Tasks


Canonical name: PidLidTaskCustomFlags
Description: The client can set this property, but it has no impact on the Task-Related Objects
Protocol and is ignored by the server.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008139
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskDateCompleted
Description: Specifies the date when the user completed work on the task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000810F
Data type: PtypTime, 0x0040
Area: Tasks

Canonical name: PidLidTaskDeadOccurrence
Description: Indicates whether new occurrences remain to be generated.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008109
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskDueDate
Description: Specifies the date by which the user expects work on the task to be complete.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008105
Data type: PtypTime, 0x0040
Area: Tasks

Canonical name: PidLidTaskEstimatedEffort
Description: Indicates the number of minutes that the user expects to work on a task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008111
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskFCreator
Description: Indicates that the Task object was originally created by the action of the current user
or user agent instead of by the processing of a task request.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000811E
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskFFixOffline
Description: Indicates the accuracy of the PidLidTaskOwner property (section 2.328).
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000812C
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskFRecurring
Description: Indicates whether the task includes a recurrence pattern.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008126
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskGlobalId
Description: Contains a unique GUID for this task, which is used to locate an existing task upon
receipt of a task response or task update.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008519
Data type: PtypBinary, 0x0102
Area: Tasks

Canonical name: PidLidTaskHistory
Description: Indicates the type of change that was last made to the Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000811A
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskLastDelegate
Description: Contains the name of the user who most recently assigned the task, or the user to
whom it was most recently assigned.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008125
Data type: PtypString, 0x001F
Area: Tasks

Canonical name: PidLidTaskLastUpdate
Description: Contains the date and time of the most recent change made to the Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008115
Data type: PtypTime, 0x0040
Area: Tasks

Canonical name: PidLidTaskLastUser
Description: Contains the name of the most recent user to have been the owner of the task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008122
Data type: PtypString, 0x001F
Area: Tasks

Canonical name: PidLidTaskMode
Description: Specifies the assignment status of the embedded Task object.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008518
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskMultipleRecipients
Description: Provides optimization hints about the recipients of a Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008120
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidLidTaskNoCompute
Description: Not used. The client can set this property, but it has no impact on the Task-Related
Objects Protocol and is ignored by the server.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008124
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskOrdinal
Description: Provides an aid to custom sorting of Task objects.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008123
Data type: PtypInteger32, 0x0003
Area: Tasks


Canonical name: PidLidTaskOwner
Description: Contains the name of the owner of the task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000811F
Data type: PtypString, 0x001F
Area: Tasks


Canonical name: PidLidTaskOwnership
Description: Indicates the role of the current user relative to the Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008129
Data type: PtypInteger32, 0x0003
Area: Tasks


Canonical name: PidLidTaskRecurrence
Description: Contains a RecurrencePattern structure that provides information about recurring
tasks.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008116
Data type: PtypBinary, 0x0102
Area: Tasks

Canonical name: PidLidTaskResetReminder
Description: Indicates whether future instances of recurring tasks need reminders, even though
the value of the PidLidReminderSet property (section 2.222) is 0x00.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008107
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskRole
Description: Not used. The client can set this property, but it has no impact on the Task-Related
Objects Protocol and is ignored by the server.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008127
Data type: PtypString, 0x001F
Area: Tasks


Canonical name: PidLidTaskStartDate
Description: Specifies the date on which the user expects work on the task to begin.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008104
Data type: PtypTime, 0x0040
Area: Tasks

Canonical name: PidLidTaskState
Description: Indicates the current assignment state of the Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008113
Data type: PtypInteger32, 0x0003
Area: Tasks


Canonical name: PidLidTaskStatus
Description: Specifies the status of a task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008101
Data type: PtypInteger32, 0x0003
Area: Tasks


Canonical name: PidLidTaskStatusOnComplete
Description: Indicates whether the task assignee has been requested to send an email message
update upon completion of the assigned task.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008119
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskUpdates
Description: Indicates whether the task assignee has been requested to send a task update when the
assigned Task object changes.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x0000811B
Data type: PtypBoolean, 0x000B
Area: Tasks

Canonical name: PidLidTaskVersion
Description: Indicates which copy is the latest update of a Task object.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008112
Data type: PtypInteger32, 0x0003
Area: Tasks


Canonical name: PidLidTeamTask
Description: This property is set by the client but is ignored by the server.
Property set: PSETID_Task {00062003-0000-0000-C000-************}
Property long ID (LID): 0x00008103
Data type: PtypBoolean, 0x000B
Area: Tasks
Consuming References:

Canonical name: PidLidTimeZone
Description: Specifies information about the time zone of a recurring meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x0000000C
Data type: PtypInteger32, 0x0003
Area: Meetings

Canonical name: PidLidTimeZoneDescription
Description: Specifies a human-readable description of the time zone that is represented by the data
in the PidLidTimeZoneStruct property (section 2.342).
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008234
Data type: PtypString, 0x001F
Area: Calendar

Canonical name: PidLidTimeZoneStruct
Description: Specifies time zone information for a recurring meeting.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x00008233
Data type: PtypBinary, 0x0102
Area: Calendar

Canonical name: PidLidToAttendeesString
Description: Contains a list of all of the sendable attendees who are also required attendees.
Property set: PSETID_Appointment {********-0000-0000-C000-************}
Property long ID (LID): 0x0000823B
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidToDoOrdinalDate
Description: Contains the current time, in UTC, which is used to determine the sort order of objects
in a consolidated to-do list.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085A0
Data type: PtypTime, 0x0040
Area: Tasks

Canonical name: PidLidToDoSubOrdinal
Description: Contains the numerals 0 through 9 that are used to break a tie when the
PidLidToDoOrdinalDate property (section 2.344) is used to perform a sort of objects.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085A1
Data type: PtypString, 0x001F
Area: Tasks

Canonical name: PidLidToDoTitle
Description: Contains user-specifiable text to identify this Message object in a consolidated to-do
list.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085A4
Data type: PtypString, 0x001F
Area: Tasks

Canonical name: PidLidUseTnef
Description: Specifies whether Transport Neutral Encapsulation Format (TNEF) is to be included
on a message when the message is converted from TNEF to MIME or SMTP format.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008582
Data type: PtypBoolean, 0x000B
Area: Run-time configuration

Canonical name: PidLidValidFlagStringProof
Description: Contains the value of the PidTagMessageDeliveryTime property (section 2.783)
when modifying the PidLidFlagRequest property (section 2.136).
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x000085BF
Data type: PtypTime, 0x0040
Area: Tasks


Canonical name: PidLidVerbResponse
Description: Specifies the voting option that a respondent has selected.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008524
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidLidVerbStream
Description: Specifies what voting responses the user can make in response to the message.
Property set: PSETID_Common {********-0000-0000-C000-************}
Property long ID (LID): 0x00008520
Data type: PtypBinary, 0x0102
Area: Run-time configuration

Canonical name: PidLidWeddingAnniversaryLocal
Description: Specifies the wedding anniversary of the contact, at midnight in the client's local time
zone, and is saved without any time zone conversions.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080DF
Data type: PtypTime, 0x0040
Area: Contact Properties

Canonical name: PidLidWeekInterval
Description: Identifies the number of weeks that occur between each meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000012
Data type: PtypInteger16, 0x0002
Area: Meetings

Canonical name: PidLidWhere
Description: Contains the value of the PidLidLocation property (section 2.159) from the associated
Meeting object.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000002
Data type: PtypString, 0x001F
Area: Meetings

Canonical name: PidLidWorkAddress
Description: Specifies the complete address of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000801B
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressCity
Description: Specifies the city or locality portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008046
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressCountry
Description: Specifies the country or region portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008049
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressCountryCode
Description: Specifies the country code portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x000080DB
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressPostalCode
Description: Specifies the postal code (ZIP code) portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008048
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressPostOfficeBox
Description: Specifies the post office box portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000804A
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressState
Description: Specifies the state or province portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008047
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidWorkAddressStreet
Description: Specifies the street portion of the work address of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x00008045
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidYearInterval
Description: Indicates the yearly interval of the appointment or meeting.
Property set: PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}
Property long ID (LID): 0x00000014
Data type: PtypInteger16, 0x0002
Area: Meetings

Canonical name: PidLidYomiCompanyName
Description: Specifies the phonetic pronunciation of the company name of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000802E
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidYomiFirstName
Description: Specifies the phonetic pronunciation of the given name of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000802C
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidLidYomiLastName
Description: Specifies the phonetic pronunciation of the surname of the contact.
Property set: PSETID_Address {********-0000-0000-C000-************}
Property long ID (LID): 0x0000802D
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidNameAcceptLanguage
Description: Contains the value of the MIME Accept-Language header.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: Accept-Language
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameApplicationName
Description: Specifies the application used to open the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: AppName
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameAttachmentMacContentType
Description: Contains the Content-Type of the Mac attachment.
Property set: PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}
Property name: AttachmentMacContentType
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidNameAttachmentMacInfo
Description: Contains the headers and resource fork data associated with the Mac attachment.
Property set: PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}
Property name: AttachmentMacInfo
Data type: PtypBinary, 0x0102
Area: Message Attachment Properties

Canonical name: PidNameAttachmentOriginalPermissionType
Description: Contains the original permission type data associated with a web reference attachment.
Property set: PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}
Property name: AttachmentOriginalPermissionType
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidNameAttachmentPermissionType
Description: Contains the permission type data associated with a web reference attachment.
Property set: PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}
Property name: AttachmentPermissionType
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidNameAttachmentProviderType
Description: Contains the provider type data associated with a web reference attachment.
Property set: PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}
Property name: AttachmentProviderType
Data type: PtypeString, 0x001F
Area: Message Attachment Properties

Canonical name: PidNameAudioNotes
Description: Contains textual annotations to a voice message after it has been delivered to the user's mailbox.
Property set: PSETID_UnifiedMessaging {4442858E-A9E3-4E80-B900-317A210CC15B}
Property name: UMAudioNotes
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidNameAuthor
Description: Specifies the author of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Author
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameAutomaticSpeechRecognitionData
Description: Contains an unprotected voice message.
Property set: PSETID_UnifiedMessaging {4442858E-A9E3-4E80-B900-317A210CC15B}
Property name: AsrData
Data type: PtypBinary, 0x0102
Area: Unified Messaging

Canonical name: PidNameByteCount
Description: Specifies the size, in bytes, of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: ByteCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarAttendeeRole
Description: Specifies the role of the attendee.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarBusystatus
Description: Specifies whether the attendee is busy at the time of an appointment on their
calendar.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarContact
Description: Identifies the name of a contact who is an attendee of a meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarContactUrl
Description: Identifies the URL where you can access contact information in HTML format.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarCreated
Description: Identifies the date and time, in UTC, when the organizer created the appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameCalendarDescriptionUrl
Description: Specifies the URL of a resource that contains a description of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarDuration
Description: Identifies the duration, in seconds, of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarExceptionDate
Description: Identifies a list of dates that are exceptions to a recurring appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypMultipleTime, 0x1040
Area: Common

Canonical name: PidNameCalendarExceptionRule
Description: Specifies an exception rule for a recurring appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypMultipleString, 0x101F
Area: Common

Canonical name: PidNameCalendarGeoLatitude
Description: Specifies the geographical latitude of the location of an appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypFloating64, 0x0005
Area: Common

Canonical name: PidNameCalendarGeoLongitude
Description: Specifies the geographical longitude of the location of an appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypFloating64, 0x0005
Area: Common

Canonical name: PidNameCalendarInstanceType
Description: Specifies the type of an appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarIsOrganizer
Description: Specifies whether an attendee is the organizer of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameCalendarLastModified
Description: Specifies the date and time, in UTC, when an appointment was last modified.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameCalendarLocationUrl
Description: Specifies a URL with location information in HTML format.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarMeetingStatus
Description: Specifies the status of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarMethod
Description: Specifies the iCalendar method that is associated with an Appointment object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarProductId
Description: Identifies the product that created the iCalendar-formatted stream.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarRecurrenceIdRange
Description: Specifies which instances of a recurring appointment are being referred to.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarReminderOffset
Description: Identifies the number of seconds before an appointment starts that a reminder is to be
displayed.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarResources
Description: Identifies a list of resources, such as rooms and video equipment, that are available for an appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarRsvp
Description: Specifies whether the organizer of an appointment or meeting requested a response.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameCalendarSequence
Description: Specifies the sequence number of a version of an appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarTimeZone
Description: Specifies the time zone of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarTimeZoneId
Description: Specifies the time zone identifier of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameCalendarTransparent
Description: Specifies whether an appointment or meeting is visible to busy time searches.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarUid
Description: Specifies the unique identifier of an appointment or meeting.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCalendarVersion
Description: Identifies the version of the iCalendar specification, as specified in
section *******.1.3, that is required to correctly interpret an iCalendar object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCategory
Description: Specifies the category of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Category
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCharacterCount
Description: Specifies the character count of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: CharCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameComments
Description: Specifies the comments of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Comments
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameCompany
Description: Specifies the company for which the file was created.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Company
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameContentBase
Description: Specifies the value of the MIME Content-Base header, which defines the base URI for
resolving relative URLs contained within the message body.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: Content-Base
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameContentClass
Description: Contains a string that identifies the type of content of a Message object.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: Content-Class
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameContentType
Description: Specifies the type of the body part content.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: Content-Type
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameCreateDateTimeReadOnly
Description: Specifies the time, in UTC, that the file was first created.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: CreateDtmRo
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameCrossReference
Description: Contains the name of the host (with domains omitted) and a white-space-separated list
of colon-separated pairs of newsgroup names and message numbers.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: Xref
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameDavId
Description: Specifies a unique ID for the calendar item.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameDavIsCollection
Description: Indicates whether a Calendar object is a collection.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameDavIsStructuredDocument
Description: Indicates whether a Calendar object is a structured document.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameDavParentName
Description: Specifies the name of the Folder object that contains the Calendar object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameDavUid
Description: Specifies the unique identifier for an item.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameDocumentParts
Description: Specifies the title of each part of the document.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: DocParts
Data type: PtypMultipleString, 0x101F
Area: Common

Canonical name: PidNameEditTime
Description: Specifies the time that the file was last edited.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: EditTime
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameExchangeIntendedBusyStatus
Description: Specifies the intended free/busy status of a meeting in a Meeting request.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameExchangeJunkEmailMoveStamp
Description: Indicates that the message is not to be processed by a spam filter.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Secure Messaging Properties

Canonical name: PidNameExchangeModifyExceptionStructure
Description: Specifies a structure that modifies an exception to the recurrence.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypBinary, 0x0102
Area: Common

Canonical name: PidNameExchangeNoModifyExceptions
Description: Indicates whether exceptions to a recurring appointment can be modified.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameExchangePatternEnd
Description: Identifies the maximum time when an instance of a recurring appointment ends.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameExchangePatternStart
Description: Identifies the absolute minimum time when an instance of a recurring appointment starts.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameExchangeReminderInterval
Description: Identifies the time, in seconds, between reminders.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameExchDatabaseSchema
Description: Specifies an array of URLs that identifies other folders within the same message store
that contain schema definition items.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypMultipleString, 0x101F
Area: Common

Canonical name: PidNameExchDataExpectedContentClass
Description: Specifies an array of names that indicates the expected content classes of items within a folder.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypMultipleString, 0x101F
Area: Common

Canonical name: PidNameExchDataSchemaCollectionReference
Description: Specifies an array of names that indicates the expected content classes of items within a folder.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameExtractedAddresses
Description: Contains an XML document with a single AddressSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedAddresses
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameExtractedContacts
Description: Contains an XML document with a single ContactSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedContacts
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameExtractedEmails
Description: Contains an XML document with a single EmailSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedEmails
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameExtractedMeetings
Description: Contains an XML document with a single MeetingSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedMeetings
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameExtractedPhones
Description: Contains an XML document with a single PhoneSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedPhones
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameExtractedTasks
Description: Contains an XML document with a single TaskSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedTasks
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameExtractedUrls
Description: Contains an XML document with a single UrlSet element.
Property set: PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}
Property name: XmlExtractedUrls
Data type: PtypString, 0x001F
Area: Extracted Entities

Canonical name: PidNameFrom
Description: Specifies the SMTP email alias of the organizer of an appointment or meeting.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: From
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameHeadingPairs
Description: Specifies which group of headings are indented in the document.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: HeadingPairs
Data type: PtypBinary, 0x0102
Area: Common

Canonical name: PidNameHiddenCount
Description: Specifies the hidden value of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: HiddenCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameHttpmailCalendar
Description: Specifies the URL for the Calendar folder for a particular user.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameHttpmailHtmlDescription
Description: Specifies the HTML content of the message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameHttpmailSendMessage
Description: Specifies the email submission URI to which outgoing email is submitted.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameICalendarRecurrenceDate
Description: Identifies an array of instances of a recurring appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypMultipleTime, 0x1040
Area: Common

Canonical name: PidNameICalendarRecurrenceRule
Description: Specifies the rule for the pattern that defines a recurring appointment.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypMultipleString, 0x101F
Area: Common

Canonical name: PidNameInternetSubject
Description: Specifies the subject of the message.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: Subject
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameKeywords
Description: Contains keywords or categories for the Message object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Keywords
Data type: PtypMultipleString, 0x101F
Area: General Message Properties

Canonical name: PidNameLastAuthor
Description: Specifies the most recent author of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: LastAuthor
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameLastPrinted
Description: Specifies the time, in UTC, that the file was last printed.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: LastPrinted
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameLastSaveDateTime
Description: Specifies the time, in UTC, that the file was last saved.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: LastSaveDtm
Data type: PtypTime, 0x0040
Area: Common

Canonical name: PidNameLineCount
Description: Specifies the number of lines in the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: LineCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameLinksDirty
Description: Indicates whether the links in the document are up-to-date.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: LinksDirty
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameLocationUrl
Description:
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypString, 0x001F
Area: Calendar
Defining reference:

Canonical name: PidNameManager
Description: Specifies the manager of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Manager
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameMSIPLabels
Description: Contains the string that specifies the CLP label information.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: msip_labels
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidNameMultimediaClipCount
Description: Specifies the number of multimedia clips in the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: MMClipCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameNoteCount
Description: Specifies the number of notes in the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: NoteCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameOMSAccountGuid
Description: Contains the GUID of the SMS account used to deliver the message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: OMSAccountGuid
Data type: PtypString, 0x001F
Area: SMS

Canonical name: PidNameOMSMobileModel
Description: Indicates the model of the mobile device used to send the SMS or MMS message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: OMSMobileModel
Data type: PtypString, 0x001F
Area: SMS

Canonical name: PidNameOMSScheduleTime
Description: Contains the time, in UTC, at which the client requested that the service provider send
the SMS or MMS message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: OMSScheduleTime
Data type: PtypTime, 0x0040
Area: SMS

Canonical name: PidNameOMSServiceType
Description: Contains the type of service used to send an SMS or MMS message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: OMSServiceType
Data type: PtypInteger32, 0x0003
Area: SMS

Canonical name: PidNameOMSSourceType
Description: Contains the source of an SMS or MMS message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: OMSSourceType
Data type: PtypInteger32, 0x0003
Area: SMS

Canonical name: PidNamePageCount
Description: Specifies the page count of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: PageCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameParagraphCount
Description: Specifies the number of paragraphs in the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: ParCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNamePhishingStamp
Description: Indicates whether a message is likely to be phishing.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name:
Data type: PtypInteger32, 0x0003
Area: Secure Messaging Properties

Canonical name: PidNamePresentationFormat
Description: Specifies the presentation format of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: PresFormat
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameQuarantineOriginalSender
Description: Specifies the original sender of a message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: quarantine-original-sender
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameRevisionNumber
Description: Specifies the revision number of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: RevNumber
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameRightsManagementLicense
Description: Specifies the value used to cache the Use License for the rights-managed email
message.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: DRMLicense
Data type: PtypMultipleBinary, 0x1102
Area: Secure Messaging Properties

Canonical name: PidNameScale
Description: Indicates whether the image is to be scaled or cropped.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Scale
Data type: PtypBoolean, 0x000B
Area: Common

Canonical name: PidNameSecurity
Description: Specifies the security level of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Security
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameSlideCount
Description: Specifies the number of slides in the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: SlideCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameSubject
Description: Specifies the subject of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Subject
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameTemplate
Description: Specifies the template of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Template
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameThumbnail
Description: Specifies the data representing the thumbnail image of the document.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Thumbnail
Data type: PtypBinary, 0x0102
Area: Common

Canonical name: PidNameTitle
Description: Specifies the title of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: Title
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidNameWordCount
Description: Specifies the word count of the file attached to the Document object.
Property set: PS_PUBLIC_STRINGS {********-0000-0000-C000-************}
Property name: WordCount
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidNameXCallId
Description: Contains a unique identifier associated with the phone call.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-CallID
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidNameXFaxNumberOfPages
Description: Specifies how many discrete pages are contained within an attachment representing a
facsimile message.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-FaxNumberOfPages
Data type: PtypInteger16, 0x0002
Area: Unified Messaging

Canonical name: PidNameXRequireProtectedPlayOnPhone
Description: Indicates that the client only renders the message on a phone.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-RequireProtectedPlayOnPhone
Data type: PtypBoolean, 0x000B
Area: Unified Messaging

Canonical name: PidNameXSenderTelephoneNumber
Description: Contains the telephone number of the caller associated with a voice mail message.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-CallingTelephoneNumber
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidNameXSharingBrowseUrl
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Browse-Url
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingCapabilities
Description: Contains a string representation of the value of the PidLidSharingCapabilities
property (section 2.237).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Capabilities
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingConfigUrl
Description: Contains the same value as the PidLidSharingConfigurationUrl property (section
2.238).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Config-Url
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingExendedCaps
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Exended-Caps
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingFlavor
Description: Contains a hexadecimal string representation of the value of the PidLidSharingFlavor
property (section 2.245).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Flavor
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingInstanceGuid
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Instance-Guid
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingLocalType
Description: Contains the same value as the PidLidSharingLocalType property (section 2.259).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Local-Type
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingProviderGuid
Description: Contains the hexadecimal string representation of the value of the
PidLidSharingProviderGuid property (section 2.266).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Provider-Guid
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingProviderName
Description: Contains the same value as the PidLidSharingProviderName property (section
2.267).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Provider-Name
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingProviderUrl
Description: Contains the same value as the PidLidSharingProviderUrl property (section 2.268).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Provider-Url
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingRemoteName
Description: Contains the same value as the PidLidSharingRemoteName property (section 2.277).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Remote-Name
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingRemotePath
Description: Contains a value that is ignored by the server no matter what value is generated by the
client.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Remote-Path
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingRemoteStoreUid
Description: Contains the same value as the PidLidSharingRemoteStoreUid property (section
2.282).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Remote-Store-Uid
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingRemoteType
Description: Contains the same value as the PidLidSharingRemoteType property (section 2.281).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Remote-Type
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXSharingRemoteUid
Description: Contains the same value as the PidLidSharingRemoteUid property (section 2.282).
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-Sharing-Remote-Uid
Data type: PtypString, 0x001F
Area: Sharing

Canonical name: PidNameXVoiceMessageAttachmentOrder
Description: Contains the list of names for the audio file attachments that are to be played as part of
a message, in reverse order.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-AttachmentOrder
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidNameXVoiceMessageDuration
Description: Specifies the length of the attached audio message, in seconds.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-VoiceMessageDuration
Data type: PtypInteger16, 0x0002
Area: Unified Messaging

Canonical name: PidNameXVoiceMessageSenderName
Description: Contains the name of the caller who left the attached voice message, as provided by the voice network's caller ID system.
Property set: PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}
Property name: X-VoiceMessageSenderName
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidTagAccess
Description: Indicates the operations available to the client for the object.
Property ID: 0x0FF4
Data type: PtypInteger32, 0x0003
Area: Access Control Properties

Canonical name: PidTagAccessControlListData
Description: Contains a permissions list for a folder.
Property ID: 0x3FE0
Data type: PtypBinary, 0x0102
Area: Access Control Properties

Canonical name: PidTagAccessLevel
Description: Indicates the client's access level to the object.
Property ID: 0x0FF7
Data type: PtypInteger32, 0x0003
Area: Access Control Properties

Canonical name: PidTagAccount
Description: Contains the alias of an Address Book object, which is an alternative name by which
the object can be identified.
Property ID: 0x3A00
Data type: PtypString, 0x001F
Area: Address Book

Canonical name: PidTagAdditionalRenEntryIds
Description: Contains the indexed entry IDs for several special folders related to conflicts, sync
issues, local failures, server failures, junk email and spam.
Property ID: 0x36D8
Data type: PtypMultipleBinary, 0x1102
Area: Outlook Application

Canonical name: PidTagAdditionalRenEntryIdsEx
Description: Contains an array of blocks that specify the EntryIDs of several special folders.
Property ID: 0x36D9
Data type: PtypBinary, 0x0102
Area: Outlook Application

Canonical name: PidTagAddressBookAuthorizedSenders
Description: Indicates whether delivery restrictions exist for a recipient.
Property ID: 0x8CD8
Data type: PtypObject, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookContainerId
Description: Contains the ID of a container on an NSPI server.
Property ID: 0xFFFD
Data type: PtypInteger32, 0x0003
Area: Address Book


Canonical name: PidTagAddressBookDeliveryContentLength
Description: Specifies the maximum size, in bytes, of a message that a recipient can receive.
Property ID: 0x806A
Data type: PtypInteger32, 0x0003
Area: Address Book

Canonical name: PidTagAddressBookDisplayNamePrintable
Description: Contains the printable string version of the display name.
Property ID: 0x39FF
Data type: PtypString, 0x001F
Area: Address Book
,
PR_EMS_AB_DISPLAY_NAME_PRINTABLE_A, PR_EMS_AB_DISPLAY_NAME_PRINTABLE_W,
PR_7BIT_DISPLAY_NAME, PR_7BIT_DISPLAY_NAME_A, PR_7BIT_DISPLAY_NAME_W,
ptagSimpleDisplayName

Canonical name: PidTagAddressBookDisplayTypeExtended
Description: Contains a value that indicates how to display an Address Book object in a table or as
a recipient on a message.
Property ID: 0x8C93
Data type: PtypInteger32, 0x0003
Area: Address Book

Canonical name: PidTagAddressBookDistributionListExternalMemberCount
Description: Contains the number of external recipients in the distribution list.
Property ID: 0x8CE3
Data type: PtypInteger32, 0x0003
Area: Address Book
Defining reference: section ********

Canonical name: PidTagAddressBookDistributionListMemberCount
Description: Contains the total number of recipients in the distribution list.
Property ID: 0x8CE2
Data type: PtypInteger32, 0x0003
Area: Address Book

Canonical name: PidTagAddressBookDistributionListMemberSubmitAccepted
Description: Indicates that delivery restrictions exist for a recipient.
Property ID: 0x8073
Data type: PtypObject, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookDistributionListMemberSubmitRejected
Description: Indicates that delivery restrictions exist for a recipient.
Property ID: 0x8CDA
Data type: PtypObject, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookDistributionListRejectMessagesFromDLMembers
Description: Indicates that delivery restrictions exist for a recipient.
Property ID: 0x8CDB
Data type: PtypObject, 0x000D
Area: Address book

Canonical name: PidTagAddressBookEntryId
Description: Contains the name-service EntryID of a directory object that refers to a public folder.
Property ID: 0x663B
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagAddressBookExtensionAttribute1
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x802D
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_1_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_1_W

Canonical name: PidTagAddressBookExtensionAttribute10
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8036
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_10_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_10_W

Canonical name: PidTagAddressBookExtensionAttribute11
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8C57
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_11_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_11_W

Canonical name: PidTagAddressBookExtensionAttribute12
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8C58
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_12_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_12_W

Canonical name: PidTagAddressBookExtensionAttribute13
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8C59
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_13_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_13_W

Canonical name: PidTagAddressBookExtensionAttribute14
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8C60
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_14_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_14_W

Canonical name: PidTagAddressBookExtensionAttribute15
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8C61
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_15_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_15_W

Canonical name: PidTagAddressBookExtensionAttribute2
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x802E
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_2_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_2_W

Canonical name: PidTagAddressBookExtensionAttribute3
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x802F
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_3_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_3_W

Canonical name: PidTagAddressBookExtensionAttribute4
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8030
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_4_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_4_W

Canonical name: PidTagAddressBookExtensionAttribute5
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8031
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_5_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_5_W

Canonical name: PidTagAddressBookExtensionAttribute6
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8032
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_6_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_6_W

Canonical name: PidTagAddressBookExtensionAttribute7
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8033
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_7_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_7_W

Canonical name: PidTagAddressBookExtensionAttribute8
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8034
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_8_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_8_W

Canonical name: PidTagAddressBookExtensionAttribute9
Description: Contains custom values defined and populated by the organization that modified the
display templates.
Property ID: 0x8035
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_EXTENSION_ATTRIBUTE_9_A, PR_EMS_AB_EXTENSION_ATTRIBUTE_9_W

Canonical name: PidTagAddressBookFolderPathname
Description: This property is deprecated and is to be ignored.
Property ID: 0x8004
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_FOLDER_PATHNAME_W

Canonical name: PidTagAddressBookHierarchicalChildDepartments
Description: Contains the child departments in a hierarchy of departments.
Property ID: 0x8C9A
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookHierarchicalDepartmentMembers
Description: Contains all of the mail users that belong to this department.
Property ID: 0x8C97
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookHierarchicalIsHierarchicalGroup
Description: Indicates whether the distribution list represents a departmental group.
Property ID: 0x8CDD
Data type: PtypBoolean, 0x000B
Area: Address Book

Canonical name: PidTagAddressBookHierarchicalParentDepartment
Description: Contains all of the departments to which this department is a child.
Property ID: 0x8C99
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookHierarchicalRootDepartment
Description: Contains the distinguished name (DN) of either the root Department object or the
root departmental group in the department hierarchy for the organization.
Property ID: 0x8C98
Data type: PtypString8, 0x001E
Area: Address Book

Canonical name: PidTagAddressBookHierarchicalShowInDepartments
Description: Lists all Department objects of which the mail user is a member.
Property ID: 0x8C94
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookHomeMessageDatabase
Description: Contains the DN expressed in the X500 DN format. This property is returned from a
name service provider interface (NSPI) server as a PtypEmbeddedTable. Otherwise, the data
type is PtypString8.
Property ID: 0x8006
Data type: PtypString8, 0x001EPtypEmbeddedTable, 0x000D
Area: Address Book
PR_EMS_AB_HOME_MDB_W

Canonical name: PidTagAddressBookIsMaster
Description: Contains a Boolean value of TRUE if it is possible to create Address Book objects in
that container, and FALSE otherwise.
Property ID: 0xFFFB
Data type: PtypBoolean, 0x000B
Area: Address Book

Canonical name: PidTagAddressBookIsMemberOfDistributionList
Description: Lists all of the distribution lists for which the object is a member. This property is
returned from an NSPI server as a PtypEmbeddedTable. Otherwise, the data type is PtypString8.
Property ID: 0x8008
Data type: PtypString8, 0x001E; PtypEmbeddedTable, 0x000D
Area: Address Book
PR_EMS_AB_IS_MEMBER_OF_DL_W

Canonical name: PidTagAddressBookManageDistributionList
Description: Contains information for use in display templates for distribution lists.
Property ID: 0x6704
Data type: PtypObject, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookManager
Description: Contains one row that references the mail user's manager.
Property ID: 0x8005
Data type: PtypObject, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookManagerDistinguishedName
Description: Contains the DN of the mail user's manager.
Property ID: 0x8005
Data type: PtypString, 0x001F
Area: Address Book

Canonical name: PidTagAddressBookMember
Description: Contains the members of the distribution list.
Property ID: 0x8009
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookMessageId
Description: Contains the Short-term Message ID (MID) ( section *******) of the first
message in the local site's offline address book public folder.
Property ID: 0x674F
Data type: PtypInteger64, 0x0014
Area: ProviderDefinedNonTransmittable

Canonical name: PidTagAddressBookModerationEnabled
Description: Indicates whether moderation is enabled for the mail user or distribution list.
Property ID: 0x8CB5
Data type: PtypBoolean, 0x000B
Area: Address Book

Canonical name: PidTagAddressBookNetworkAddress
Description: Contains a list of names by which a server is known to the various transports in use by
the network.
Property ID: 0x8170
Data type: PtypMultipleString, 0x101F
Area: Address Book
PR_EMS_AB_NETWORK_ADDRESS_W

Canonical name: PidTagAddressBookObjectDistinguishedName
Description: Contains the DN of the Address Book object.
Property ID: 0x803C
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_OBJ_DIST_NAME_W

Canonical name: PidTagAddressBookObjectGuid
Description: Contains a GUID that identifies an Address Book object.
Property ID: 0x8C6D
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagAddressBookOrganizationalUnitRootDistinguishedName
Description: Contains the DN of the Organization object of the mail user's organization.
Property ID: 0x8CA8
Data type: PtypString, 0x001F
Area: Address Book

Canonical name: PidTagAddressBookOwner
Description: Contains one row that references the distribution list's owner.
Property ID: 0x800C
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookOwnerBackLink
Description: Contains a list of the distribution lists owned by a mail user.
Property ID: 0x8024
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookParentEntryId
Description: Contains the EntryID of the parent container in a hierarchy of address book
containers.
Property ID: 0xFFFC
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagAddressBookPhoneticCompanyName
Description: Contains the phonetic representation of the PidTagCompanyName property (section
2.633).
Property ID: 0x8C91
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_PHONETIC_COMPANY_NAME_A, PR_EMS_AB_PHONETIC_COMPANY_NAME_W

Canonical name: PidTagAddressBookPhoneticDepartmentName
Description: Contains the phonetic representation of the PidTagDepartmentName property
(section 2.666).
Property ID: 0x8C90
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_PHONETIC_DEPARTMENT_NAME_A, PR_EMS_AB_PHONETIC_DEPARTMENT_NAME_W

Canonical name: PidTagAddressBookPhoneticDisplayName
Description: Contains the phonetic representation of the PidTagDisplayName property (section
2.670).
Property ID: 0x8C92
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_PHONETIC_DISPLAY_NAME_A, PR_EMS_AB_PHONETIC_DISPLAY_NAME_W

Canonical name: PidTagAddressBookPhoneticGivenName
Description: Contains the phonetic representation of the PidTagGivenName property (section
2.708).
Property ID: 0x8C8E
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_PHONETIC_GIVEN_NAME_W

Canonical name: PidTagAddressBookPhoneticSurname
Description: Contains the phonetic representation of the PidTagSurname property (section 2.1030).
Property ID: 0x8C8F
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_PHONETIC_SURNAME_W

Canonical name: PidTagAddressBookProxyAddresses
Description: Contains alternate email addresses for the Address Book object.
Property ID: 0x800F
Data type: PtypMultipleString, 0x101F
Area: Address Book
PR_EMS_AB_PROXY_ADDRESSES_W

Canonical name: PidTagAddressBookPublicDelegates
Description: Contains a list of mail users who are allowed to send email on behalf of the mailbox
owner.
Property ID: 0x8015
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book
PR_EMS_AB_PUBLIC_DELEGATES_W

Canonical name: PidTagAddressBookReports
Description: Lists all of the mail user’s direct reports.
Property ID: 0x800E
Data type: PtypEmbeddedTable, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookRoomCapacity
Description: Contains the maximum occupancy of the room.
Property ID: 0x0807
Data type: PtypInteger32, 0x0003
Area: Address Book

Canonical name: PidTagAddressBookRoomContainers
Description: Contains a list of DNs that represent the address book containers that hold
Resource objects, such as conference rooms and equipment.
Property ID: 0x8C96
Data type: PtypMultipleString, 0x101F
Area: Address Book
PR_EMS_AB_ROOM_CONTAINERS_W

Canonical name: PidTagAddressBookRoomDescription
Description: Contains a description of the Resource object.
Property ID: 0x0809
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_ROOM_DESCRIPTION_W

Canonical name: PidTagAddressBookSenderHintTranslations
Description: Contains the locale ID and translations of the default mail tip.
Property ID: 0x8CAC
Data type: PtypMultipleString, 0x101F
Area: Address Book

Canonical name: PidTagAddressBookSeniorityIndex
Description: Contains a signed integer that specifies the seniority order of Address Book objects that represent members of a department and are referenced by a Department object or departmental group, with larger values specifying members that are more senior.
Property ID: 0x8CA0
Data type: PtypInteger32, 0x0003
Area: Address Book


Canonical name: PidTagAddressBookTargetAddress
Description: Contains the foreign system email address of an Address Book object.
Property ID: 0x8011
Data type: PtypString, 0x001F
Area: Address Book
PR_EMS_AB_TARGET_ADDRESS_W

Canonical name: PidTagAddressBookUnauthorizedSenders
Description: Indicates whether delivery restrictions exist for a recipient.
Property ID: 0x8CD9
Data type: PtypObject, 0x000D
Area: Address Book

Canonical name: PidTagAddressBookX509Certificate
Description: Contains the ASN_1 DER encoded X.509 certificates for the mail user.
Property ID: 0x8C6A
Data type: PtypMultipleBinary, 0x1102
Area: Address Book

Canonical name: PidTagAddressType
Description: Contains the email address type of a Message object.
Property ID: 0x3002
Data type: PtypString, 0x001F
Area: Address Properties


Canonical name: PidTagAlternateRecipientAllowed
Description: Specifies whether the sender permits the message to be auto-forwarded.
Property ID: 0x0002
Data type: PtypBoolean, 0x000B
Area: Address Properties

Canonical name: PidTagAnr
Description: Contains a filter value used in ambiguous name resolution.
Property ID: 0x360C
Data type: PtypString, 0x001F
Area: Address Book

Canonical name: PidTagArchiveDate
Description: Specifies the date, in UTC, after which a Message object is archived by the server.
Property ID: 0x301F
Data type: PtypTime, 0x0040
Area: Archive

Canonical name: PidTagArchivePeriod
Description: Specifies the number of days that a Message object can remain unarchived.
Property ID: 0x301E
Data type: PtypInteger32, 0x0003
Area: Archive

Canonical name: PidTagArchiveTag
Description: Specifies the GUID of an archive tag.
Property ID: 0x3018
Data type: PtypBinary, 0x0102
Area: Archive

Canonical name: PidTagAssistant
Description: Contains the name of the mail user's administrative assistant.
Property ID: 0x3A30
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagAssistantTelephoneNumber
Description: Contains the telephone number of the mail user's administrative assistant.
Property ID: 0x3A2E
Data type: PtypString, 0x001F
Area: Address Properties
PR_ASSISTANT_TELEPHONE_NUMBER_W

Canonical name: PidTagAssociated
Description: Specifies whether the message being synchronized is an FAI message.
Property ID: 0x67AA
Data type: PtypBoolean, 0x000B
Area: Sync

Canonical name: PidTagAttachAdditionalInformation
Description: Contains attachment encoding information.
Property ID: 0x370F
Data type: PtypBinary, 0x0102
Area: Message Attachment Properties

Canonical name: PidTagAttachContentBase
Description: Contains the base of a relative URI.
Property ID: 0x3711
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachContentId
Description: Contains a content identifier unique to the Message object that matches a corresponding cid URI schema reference in the HTML body of the Message object.
Property ID: 0x3712
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachContentLocation
Description: Contains a relative or full URI that matches a corresponding reference in the HTML
body of a Message object.
Property ID: 0x3713
Data type: PtypString, 0x001F
Area: Message Attachment Properties
PR_ATTACH_CONTENT_LOCATION_W

Canonical name: PidTagAttachDataBinary
Description: Contains the contents of the file to be attached.
Property ID: 0x3701
Data type: PtypBinary, 0x0102
Area: Message Attachment Properties

Canonical name: PidTagAttachDataObject
Description: Contains the binary representation of the Attachment object in an application-specific
format.
Property ID: 0x3701
Data type: PtypObject, 0x000D
Area: Message Attachment Properties

Canonical name: PidTagAttachEncoding
Description: Contains encoding information about the Attachment object.
Property ID: 0x3702
Data type: PtypBinary, 0x0102
Area: Message Attachment Properties

Canonical name: PidTagAttachExtension
Description: Contains a file name extension that indicates the document type of an attachment.
Property ID: 0x3703
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachFilename
Description: Contains the  of the PidTagAttachLongFilename property (section 2.589).
Property ID: 0x3704
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachFlags
Description: Indicates which body formats might reference this attachment when rendering data.
Property ID: 0x3714
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidTagAttachLongFilename
Description: Contains the full filename and extension of the Attachment object.
Property ID: 0x3707
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachLongPathname
Description: Contains the fully-qualified path and file name with extension.
Property ID: 0x370D
Data type: PtypString, 0x001F
Area: Message Attachment Properties
ptagAttachLongPathname, PR_ATTACH_LONG_PATHNAME_W

Canonical name: PidTagAttachmentContactPhoto
Description: Indicates that a contact photo attachment is attached to a Contact object.
Property ID: 0x7FFF
Data type: PtypBoolean, 0x000B
Area: Message Attachment Properties

Canonical name: PidTagAttachmentFlags
Description: Indicates special handling for an Attachment object.
Property ID: 0x7FFD
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidTagAttachmentHidden
Description: Indicates whether an Attachment object is hidden from the end user.
Property ID: 0x7FFE
Data type: PtypBoolean, 0x000B
Area: Message Attachment Properties

Canonical name: PidTagAttachmentLinkId
Description: Contains the type of Message object to which an attachment is linked.
Property ID: 0x7FFA
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidTagAttachMethod
Description: Represents the way the contents of an attachment are accessed.
Property ID: 0x3705
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidTagAttachMimeTag
Description: Contains a content-type MIME header.
Property ID: 0x370E
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachNumber
Description: Identifies the Attachment object within its Message object.
Property ID: 0x0E21
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidTagAttachPathname
Description: Contains the  of the PidTagAttachLongPathname property (section 2.590).
Property ID: 0x3708
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttachPayloadClass
Description: Contains the class name of an object that can display the contents of the message.
Property ID: 0x371A
Data type: PtypString, 0x001F
Area: Outlook Application

Canonical name: PidTagAttachPayloadProviderGuidString
Description: Contains the GUID of the software component that can display the contents of the
message.
Property ID: 0x3719
Data type: PtypString, 0x001F
Area: Outlook Application

Canonical name: PidTagAttachRendering
Description: Contains a Windows Metafile, as specified in  for the Attachment object.
Property ID: 0x3709
Data type: PtypBinary, 0x0102
Area: Message Attachment Properties

Canonical name: PidTagAttachSize
Description: Contains the size, in bytes, consumed by the Attachment object on the server.
Property ID: 0x0E20
Data type: PtypInteger32, 0x0003
Area: Message Attachment Properties

Canonical name: PidTagAttachTag
Description: Contains the identifier information for the application that supplied the Attachment
object data.
Property ID: 0x370A
Data type: PtypBinary, 0x0102
Area: Message Attachment Properties

Canonical name: PidTagAttachTransportName
Description: Contains the name of an attachment file, modified so that it can be correlated with
TNEF messages.
Property ID: 0x370C
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagAttributeHidden
Description: Specifies the hide or show status of a folder.
Property ID: 0x10F4
Data type: PtypBoolean, 0x000B
Area: Access Control Properties

Canonical name: PidTagAttributeReadOnly
Description: Indicates whether an item can be modified or deleted.
Property ID: 0x10F6
Data type: PtypBoolean, 0x000B
Area: Access Control Properties

Canonical name: PidTagAutoForwardComment
Description: Contains text included in an automatically-generated message.
Property ID: 0x0004
Data type: PtypString, 0x001F
Area: General Report Properties

Canonical name: PidTagAutoForwarded
Description: Indicates that a Message object has been automatically generated or automatically
forwarded.
Property ID: 0x0005
Data type: PtypBoolean, 0x000B
Area: General Report Properties

Canonical name: PidTagAutoResponseSuppress
Description: Specifies whether a client or server application will forego sending automated replies in
response to this message.
Property ID: 0x3FDF
Data type: PtypInteger32, 0x0003
Area: Email

Canonical name: PidTagBirthday
Description: Contains the date of the mail user's birthday at midnight.
Property ID: 0x3A42
Data type: PtypTime, 0x0040
Area: Contact Properties

Canonical name: PidTagBlockStatus
Description: Indicates the user's preference for viewing external content (such as links to images on
an HTTP server) in the message body.
Property ID: 0x1096
Data type: PtypInteger32, 0x0003
Area: Secure Messaging Properties

Canonical name: PidTagBody
Description: Contains message body text in plain text format.
Property ID: 0x1000
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagBodyContentId
Description: Contains a GUID that corresponds to the current message body.
Property ID: 0x1015
Data type: PtypString, 0x001F
Area: Exchange

Canonical name: PidTagBodyContentLocation
Description: Contains a globally unique Uniform Resource Identifier (URI) that serves as a label
for the current message body.
Property ID: 0x1014
Data type: PtypString, 0x001F
Area: MIME Properties

Canonical name: PidTagBodyHtml
Description: Contains the HTML body of the Message object.
Property ID: 0x1013
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagBusiness2TelephoneNumber
Description: Contains a secondary telephone number at the mail user's place of business.
Property ID: 0x3A1B
Data type: PtypString, 0x001F
Area: Contact Properties
PR_BUSINESS2_TELEPHONE_NUMBER, PR_BUSINESS2_TELEPHONE_NUMBER_A, PR_BUSINESS2_TELEPHONE_NUMBER_W, PR_OFFICE2_TELEPHONE_NUMBER

Canonical name: PidTagBusiness2TelephoneNumbers
Description: Contains secondary telephone numbers at the mail user's place of business.
Property ID: 0x3A1B
Data type: PtypMultipleString, 0x101F
Area: Contact Properties

Canonical name: PidTagBusinessFaxNumber
Description: Contains the telephone number of the mail user's business fax machine.
Property ID: 0x3A24
Data type: PtypString, 0x001F
Area: Contact Properties
PR_BUSINESS_FAX_NUMBER_W

Canonical name: PidTagBusinessHomePage
Description: Contains the URL of the mail user's business home page.
Property ID: 0x3A51
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagBusinessTelephoneNumber
Description: Contains the primary telephone number of the mail user's place of business.
Property ID: 0x3A08
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagCallbackTelephoneNumber
Description: Contains a telephone number to reach the mail user.
Property ID: 0x3A02
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagCallId
Description: Contains a unique identifier associated with the phone call.
Property ID: 0x6806
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidTagCarTelephoneNumber
Description: Contains the mail user's car telephone number.
Property ID: 0x3A1E
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagCdoRecurrenceid
Description: Identifies a specific instance of a recurring appointment.
Property ID: 0x10C5
Data type: PtypTime, 0x0040
Area: Exchange

Canonical name: PidTagChangeKey
Description: Contains a structure that identifies the last change to the object.
Property ID: 0x65E2
Data type: PtypBinary, 0x0102
Area: History Properties

Canonical name: PidTagChangeNumber
Description: Contains a structure that identifies the last change to the message or folder that is
currently being synchronized.
Property ID: 0x67A4
Data type: PtypInteger64, 0x0014
Area: Sync

Canonical name: PidTagChildrensNames
Description: Specifies the names of the children of the contact.
Property ID: 0x3A58
Data type: PtypMultipleString, 0x101F
Area: Contact Properties

Canonical name: PidTagClientActions
Description: Specifies the actions the client is required to take on the message.
Property ID: 0x6645
Data type: PtypBinary, 0x0102
Area: Server-side Rules Properties

Canonical name: PidTagClientSubmitTime
Description: Contains the current time, in UTC, when the email message is submitted.
Property ID: 0x0039
Data type: PtypTime, 0x0040
Area: Message Time Properties

Canonical name: PidTagCodePageId
Description: Contains the identifier for the client code page used for Unicode to double-byte
character set (DBCS) string conversion.
Property ID: 0x66C3
Data type: PtypInteger32, 0x0003
Area: Exchange Profile Configuration

Canonical name: PidTagComment
Description: Contains a comment about the purpose or content of the Address Book object.
Property ID: 0x3004
Data type: PtypString, 0x001F
Area: Common

Canonical name: PidTagCompanyMainTelephoneNumber
Description: Contains the main telephone number of the mail user's company.
Property ID: 0x3A57
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagCompanyName
Description: Contains the mail user's company name.
Property ID: 0x3A16
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagComputerNetworkName
Description: Contains the name of the mail user's computer network.
Property ID: 0x3A49
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagConflictEntryId
Description: Contains the EntryID of the conflict resolve message.
Property ID: 0x3FF0
Data type: PtypBinary, 0x0102
Area: ICS

Canonical name: PidTagContainerClass
Description: Contains a string value that describes the type of Message object that a folder contains.
Property ID: 0x3613
Data type: PtypString, 0x001F
Area: Container Properties

Canonical name: PidTagContainerContents
Description: Empty. An NSPI server defines this value for distribution lists and it is not present for
other objects.
Property ID: 0x360F
Data type: PtypEmbeddedTable, 0x000D
Area: Container Properties

Canonical name: PidTagContainerFlags
Description: Contains a bitmask of flags that describe capabilities of an address book container.
Property ID: 0x3600
Data type: PtypInteger32, 0x0003
Area: Address Book

Canonical name: PidTagContainerHierarchy
Description: Identifies all of the subfolders of the current folder.
Property ID: 0x360E
Data type: PtypObject, 0x000D
Area: Container Properties

Canonical name: PidTagContentCount
Description: Specifies the number of rows under the header row.
Property ID: 0x3602
Data type: PtypInteger32, 0x0003
Area: Folder Properties

Canonical name: PidTagContentFilterSpamConfidenceLevel
Description: Indicates a confidence level that the message is spam.
Property ID: 0x4076
Data type: PtypInteger32, 0x0003
Area: Secure Messaging Properties

Canonical name: PidTagContentUnreadCount
Description: Specifies the number of rows under the header row that have the PidTagRead property(section 2.872) set to FALSE.
Property ID: 0x3603
Data type: PtypInteger32, 0x0003
Area: Folder Properties

Canonical name: PidTagConversationId
Description: Contains a computed value derived from other conversation-related properties.
Property ID: 0x3013
Data type: PtypBinary, 0x0102
Area: Conversations

Canonical name: PidTagConversationIndex
Description: Indicates the relative position of this message within a conversation thread.
Property ID: 0x0071
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagConversationIndexTracking
Description: Indicates whether the GUID portion of the PidTagConversationIndex property(section 2.644) is to be used to compute the PidTagConversationId property (section 2.643).
Property ID: 0x3016
Data type: PtypBoolean, 0x000B
Area: Conversations

Canonical name: PidTagConversationTopic
Description: Contains an unchanging copy of the original subject.
Property ID: 0x0070
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagCountry
Description: Contains the name of the mail user's country/region.
Property ID: 0x3A26
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagCreationTime
Description: Contains the time, in UTC, that the object was created.
Property ID: 0x3007
Data type: PtypTime, 0x0040
Area: Message Time Properties

Canonical name: PidTagCreatorEntryId
Description: Specifies the original author of the message according to their Address Book EntryID.
Property ID: 0x3FF9
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagCreatorName
Description: Contains the name of the creator of a Message object.
Property ID: 0x3FF8
Data type: PtypString, 0x001F
Area: General Message Properties
PR_CREATOR_NAME_W

Canonical name: PidTagCustomerId
Description: Contains the mail user's customer identification number.
Property ID: 0x3A4A
Data type: PtypString, 0x001F
Area: Contact Properties

Canonical name: PidTagDamBackPatched
Description: Indicates whether the Deferred Action Message (DAM) was updated by the server.
Property ID: 0x6647
Data type: PtypBoolean, 0x000B
Area: Server-side Rules Properties

Canonical name: PidTagDamOriginalEntryId
Description: Contains the EntryID of the delivered message that the client has to process.
Property ID: 0x6646
Data type: PtypBinary, 0x0102
Area: Server-side Rules Properties

Canonical name: PidTagDefaultPostMessageClass
Description: Contains the message class of the object.
Property ID: 0x36E5
Data type: PtypString, 0x001F
Area: MapiContainer

Canonical name: PidTagDeferredActionMessageOriginalEntryId
Description: Contains the server EntryID for the DAM.
Property ID: 0x6741
Data type: PtypServerId, 0x00FB
Area: Server-side Rules Properties

Canonical name: PidTagDeferredDeliveryTime
Description: Contains the date and time, in UTC, at which the sender prefers that the message be delivered.
Property ID: 0x000F
Data type: PtypTime, 0x0040
Area: MapiEnvelope

Canonical name: PidTagDeferredSendNumber
Description: Contains a number used in the calculation of how long to defer sending a message.
Property ID: 0x3FEB
Data type: PtypInteger32, 0x0003
Area: MapiStatus

Canonical name: PidTagDeferredSendTime
Description: Contains the amount of time after which a client would like to defer sending the message.
Property ID: 0x3FEF
Data type: PtypTime, 0x0040
Area: MapiStatus

Canonical name: PidTagDeferredSendUnits
Description: Specifies the unit of time used as a multiplier with the PidTagDeferredSendNumber
property (section 2.657) value.
Property ID: 0x3FEC
Data type: PtypInteger32, 0x0003
Area: MapiStatus

Canonical name: PidTagDelegatedByRule
Description: Specifies whether the message was forwarded due to the triggering of a delegate
forward rule.
Property ID: 0x3FE3
Data type: PtypBoolean, 0x000B
Area: MapiStatus

Canonical name: PidTagDelegateFlags
Description: Indicates whether delegates can view Message objects that are marked as private.
Property ID: 0x686B
Data type: PtypMultipleInteger32, 0x1003
Area: MessageClassDefinedTransmittable

Canonical name: PidTagDeleteAfterSubmit
Description: Indicates that the original message is to be deleted after it is sent.
Property ID: 0x0E01
Data type: PtypBoolean, 0x000B
Area: MapiNonTransmittable

Canonical name: PidTagDeletedCountTotal
Description: Contains the total count of messages that have been deleted from a folder, excluding
messages deleted within subfolders.
Property ID: 0x670B
Data type: PtypInteger32, 0x0003
Area: Server

Canonical name: PidTagDeletedOn
Description: Specifies the time, in UTC, when the item or folder was soft deleted.
Property ID: 0x668F
Data type: PtypTime, 0x0040
Area: ExchangeFolder

Canonical name: PidTagDeliverTime
Description: Contains the delivery time for a delivery status notification, as specified  or a
message disposition notification, as specified in .
Property ID: 0x0010
Data type: PtypTime, 0x0040
Area: Email

Canonical name: PidTagDepartmentName
Description: Contains a name for the department in which the mail user works.
Property ID: 0x3A18
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagDepth
Description: Specifies the number of nested categories in which a given row is contained.
Property ID: 0x3005
Data type: PtypInteger32, 0x0003
Area: MapiCommon

Canonical name: PidTagDisplayBcc
Description: Contains a list of blind carbon copy (Bcc) recipient display names.
Property ID: 0x0E02
Data type: PtypString, 0x001F
Area: Message Properties

Canonical name: PidTagDisplayCc
Description: Contains a list of carbon copy (Cc) recipient display names.
Property ID: 0x0E03
Data type: PtypString, 0x001F
Area: Message Properties

Canonical name: PidTagDisplayName
Description: Contains the display name of the folder.
Property ID: 0x3001
Data type: PtypString, 0x001F
Area: MapiCommon

Canonical name: PidTagDisplayNamePrefix
Description: Contains the mail user's honorific title.
Property ID: 0x3A45
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagDisplayTo
Description: Contains a list of the primary recipient display names, separated by semicolons,
when an email message has primary recipients .
Property ID: 0x0E04
Data type: PtypString, 0x001F
Area: Message Properties

Canonical name: PidTagDisplayType
Description: Contains an integer value that indicates how to display an Address Book object in a
table or as an addressee on a message.
Property ID: 0x3900
Data type: PtypInteger32, 0x0003
Area: MapiAddressBook
Defining reference: section ********

Canonical name: PidTagDisplayTypeEx
Description: Contains an integer value that indicates how to display an Address Book object in a
table or as a recipient on a message.
Property ID: 0x3905
Data type: PtypInteger32, 0x0003
Area: MapiAddressBook

Canonical name: PidTagEmailAddress
Description: Contains the email address of a Message object.
Property ID: 0x3003
Data type: PtypString, 0x001F
Area: MapiCommon

Canonical name: PidTagEndDate
Description: Contains the value of the PidLidAppointmentEndWhole property (section 2.14).
Property ID: 0x0061
Data type: PtypTime, 0x0040
Area: MapiEnvelope Property set

Canonical name: PidTagEntryId
Description: Contains the information to identify many different types of messaging objects.
Property ID: 0x0FFF
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagExceptionEndTime
Description: Contains the end date and time of the exception in the local time zone of the computer
when the exception is created.
Property ID: 0x7FFC
Data type: PtypTime, 0x0040
Area: MessageClassDefinedNonTransmittable

Canonical name: PidTagExceptionReplaceTime
Description: Indicates the original date and time, in UTC, at which the instance in the recurrence
pattern would have occurred if it were not an exception.
Property ID: 0x7FF9
Data type: PtypTime, 0x0040
Area: MessageClassDefinedNonTransmittable

Canonical name: PidTagExceptionStartTime
Description: Contains the start date and time of the exception in the local time zone of the computer
when the exception is created.
Property ID: 0x7FFB
Data type: PtypTime, 0x0040
Area: MessageClassDefinedNonTransmittable

Canonical name: PidTagExchangeNTSecurityDescriptor
Description: Contains the calculated security descriptor for the item.
Property ID: 0x0E84
Data type: PtypBinary, 0x0102
Area: Calendar Document

Canonical name: PidTagExpiryNumber
Description: Contains an integer value that is used along with the PidTagExpiryUnits property(section 2.684) to define the expiry send time.
Property ID: 0x3FED
Data type: PtypInteger32, 0x0003
Area: MapiStatus
Defining reference: section *******

Canonical name: PidTagExpiryTime
Description: Contains the time, in UTC, after which a client wants to receive an expiry event if the message arrives late.
Property ID: 0x0015
Data type: PtypTime, 0x0040
Area: MapiEnvelope

Canonical name: PidTagExpiryUnits
Description: Contains the unit of time that the value of the PidTagExpiryNumber property (section
2.682) multiplies.
Property ID: 0x3FEE
Data type: PtypInteger32, 0x0003
Area: MapiStatus

Canonical name: PidTagExtendedFolderFlags
Description: Contains encoded sub-properties for a folder.
Property ID: 0x36DA
Data type: PtypBinary, 0x0102
Area: MapiContainer

Canonical name: PidTagExtendedRuleMessageActions
Description: Contains action information about named properties used in the rule.
Property ID: 0x0E99
Data type: PtypBinary, 0x0102
Area: Rules

Canonical name: PidTagExtendedRuleMessageCondition
Description: Contains condition information about named properties used in the rule.
Property ID: 0x0E9A
Data type: PtypBinary, 0x0102
Area: Rules

Canonical name: PidTagExtendedRuleSizeLimit
Description: Contains the maximum size, in bytes, that the user is allowed to accumulate for a single extended rule.
Property ID: 0x0E9B
Data type: PtypInteger32, 0x0003
Area: Rules

Canonical name: PidTagFaxNumberOfPages
Description: Contains the number of pages in a Fax object.
Property ID: 0x6804
Data type: PtypInteger32, 0x0003
Area: Unified Messaging

Canonical name: PidTagFlagCompleteTime
Description: Specifies the date and time, in UTC, that the Message object was flagged as complete.
Property ID: 0x1091
Data type: PtypTime, 0x0040
Area: Miscellaneous Properties

Canonical name: PidTagFlagStatus
Description: Specifies the flag state of the Message object.
Property ID: 0x1090
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties

Canonical name: PidTagFlatUrlName
Description: Contains a unique identifier for an item across the message store.
Property ID: 0x670E
Data type: PtypString, 0x001F
Area: ExchangeAdministrative
ptagFlatURLName,

Canonical name: PidTagFolderAssociatedContents
Description: Identifies all FAI messages in the current folder.
Property ID: 0x3610
Data type: PtypObject, 0x000D
Area: MapiContainer

Canonical name: PidTagFolderId
Description: Contains the Folder ID (FID) ( section *******) of the folder.
Property ID: 0x6748
Data type: PtypInteger64, 0x0014
Area: ID Properties

Canonical name: PidTagFolderFlags
Description: Contains a computed value to specify the type or state of a folder.
Property ID: 0x66A8
Data type: PtypInteger32, 0x0003
Area: ExchangeAdministrative

Canonical name: PidTagFolderType
Description: Specifies the type of a folder that includes the Root folder, Generic folder, and Search
folder.
Property ID: 0x3601
Data type: PtypInteger32, 0x0003
Area: MapiContainer

Canonical name: PidTagFollowupIcon
Description: Specifies the flag color of the Message object.
Property ID: 0x1095
Data type: PtypInteger32, 0x0003
Area: RenMessageFolder

Canonical name: PidTagFreeBusyCountMonths
Description: Contains an integer value used to calculate the start and end dates of the range of
free/busy data to be published to the public folders.
Property ID: 0x6869
Data type: PtypInteger32, 0x0003
Area: MessageClassDefinedTransmittable

Canonical name: PidTagFreeBusyEntryIds
Description: Contains EntryIDs of the Delegate Information object, the free/busy message of the
logged on user, and the folder with the PidTagDisplayName property (section 2.670) value of
"Freebusy Data".
Property ID: 0x36E4
Data type: PtypMultipleBinary, 0x1102
Area: MapiContainer

Canonical name: PidTagFreeBusyMessageEmailAddress
Description: Specifies the email address of the user or resource to whom this free/busy message
applies.
Property ID: 0x6849
Data type: PtypString, 0x001F
Area: MessageClassDefinedTransmittable

Canonical name: PidTagFreeBusyPublishEnd
Description: Specifies the end time, in UTC, of the publishing range.
Property ID: 0x6848
Data type: PtypInteger32, 0x0003
Area: Free/Busy Properties

Canonical name: PidTagFreeBusyPublishStart
Description: Specifies the start time, in UTC, of the publishing range.
Property ID: 0x6847
Data type: PtypInteger32, 0x0003
Area: Free/Busy Properties

Canonical name: PidTagFreeBusyRangeTimestamp
Description: Specifies the time, in UTC, that the data was published.
Property ID: 0x6868
Data type: PtypTime, 0x0040
Area: Free/Busy Properties

Canonical name: PidTagFtpSite
Description: Contains the File Transfer Protocol (FTP) site address of the mail user.
Property ID: 0x3A4C
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagGatewayNeedsToRefresh
Description: This property is deprecated and SHOULD NOT be used.
Property ID: 0x6846
Data type: PtypBoolean, 0x000B
Area: MessageClassDefinedTransmittable

Canonical name: PidTagGender
Description: Contains a value that represents the mail user's gender.
Property ID: 0x3A4D
Data type: PtypInteger16, 0x0002
Area: MapiMailUser

Canonical name: PidTagGeneration
Description: Contains a generational abbreviation that follows the full name of the mail user.
Property ID: 0x3A05
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagGivenName
Description: Contains the mail user's given name.
Property ID: 0x3A06
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagGovernmentIdNumber
Description: Contains a government identifier for the mail user.
Property ID: 0x3A07
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHasAttachments
Description: Indicates whether the Message object contains at least one attachment.
Property ID: 0x0E1B
Data type: PtypBoolean, 0x000B
Area: Message Attachment Properties Property set

Canonical name: PidTagHasDeferredActionMessages
Description: Indicates whether a Message object has a deferred action message associated with it.
Property ID: 0x3FEA
Data type: PtypBoolean, 0x000B
Area: Rules

Canonical name: PidTagHasNamedProperties
Description: Indicates whether the Message object has a named property.
Property ID: 0x664A
Data type: PtypBoolean, 0x000B
Area: ExchangeMessageReadOnly

Canonical name: PidTagHasRules
Description: Indicates whether a Folder object has rules.
Property ID: 0x663A
Data type: PtypBoolean, 0x000B
Area: ExchangeFolder

Canonical name: PidTagHierarchyChangeNumber
Description: Contains a number that monotonically increases every time a subfolder is added to, or
deleted from, this folder.
Property ID: 0x663E
Data type: PtypInteger32, 0x0003
Area: ExchangeFolder

Canonical name: PidTagHierRev
Description: Specifies the time, in UTC, to trigger the client in cached mode to synchronize the folder
hierarchy.
Property ID: 0x4082
Data type: PtypTime, 0x0040
Area: TransportEnvelope

Canonical name: PidTagHobbies
Description: Contains the names of the mail user's hobbies.
Property ID: 0x3A43
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHome2TelephoneNumber
Description: Contains a secondary telephone number at the mail user's home.
Property ID: 0x3A2F
Data type: PtypString, 0x001F
Area: MapiMailUser
Defining reference: section ********

Canonical name: PidTagHome2TelephoneNumbers
Description: Contains secondary telephone numbers at the mail user's home.
Property ID: 0x3A2F
Data type: PtypMultipleString, 0x101F
Area: MapiMailUser

Canonical name: PidTagHomeAddressCity
Description: Contains the name of the mail user's home locality, such as the town or city.
Property ID: 0x3A59
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHomeAddressCountry
Description: Contains the name of the mail user's home country/region.
Property ID: 0x3A5A
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHomeAddressPostalCode
Description: Contains the postal code for the mail user's home postal address.
Property ID: 0x3A5B
Data type: PtypString, 0x001F
Area: MapiMailUser
PR_HOME_ADDRESS_POSTAL_CODE_W

Canonical name: PidTagHomeAddressPostOfficeBox
Description: Contains the number or identifier of the mail user's home post office box.
Property ID: 0x3A5E
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHomeAddressStateOrProvince
Description: Contains the name of the mail user's home state or province.
Property ID: 0x3A5C
Data type: PtypString, 0x001F
Area: MapiMailUser
PR_HOME_ADDRESS_STATE_OR_PROVINCE_A, PR_HOME_ADDRESS_STATE_OR_PROVINCE_W

Canonical name: PidTagHomeAddressStreet
Description: Contains the mail user's home street address.
Property ID: 0x3A5D
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHomeFaxNumber
Description: Contains the telephone number of the mail user's home fax machine.
Property ID: 0x3A25
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHomeTelephoneNumber
Description: Contains the primary telephone number of the mail user's home.
Property ID: 0x3A09
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagHtml
Description: Contains message body text in HTML format.
Property ID: 0x1013
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagICalendarEndTime
Description: Contains the date and time, in UTC, when an appointment or meeting ends.
Property ID: 0x10C4
Data type: PtypTime, 0x0040
Area: Calendar
Defining reference: section ********

Canonical name: PidTagICalendarReminderNextTime
Description: Contains the date and time, in UTC, for the activation of the next reminder.
Property ID: 0x10CA
Data type: PtypTime, 0x0040
Area: Calendar

Canonical name: PidTagICalendarStartTime
Description: Contains the date and time, in UTC, when the appointment or meeting starts.
Property ID: 0x10C3
Data type: PtypTime, 0x0040
Area: Calendar Property set

Canonical name: PidTagIconIndex
Description: Specifies which icon is to be used by a user interface when displaying a group of
Message objects.
Property ID: 0x1080
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagImportance
Description: Indicates the level of importance assigned by the end user to the Message object.
Property ID: 0x0017
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagInConflict
Description: Specifies whether the attachment represents an alternate replica.
Property ID: 0x666C
Data type: PtypBoolean, 0x000B
Area: Conflict Note

Canonical name: PidTagInitialDetailsPane
Description: Indicates which page of a display template to display first.
Property ID: 0x3F08
Data type: PtypInteger32, 0x0003
Area: MAPI Display Tables

Canonical name: PidTagInitials
Description: Contains the initials for parts of the full name of the mail user.
Property ID: 0x3A0A
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagInReplyToId
Description: Contains the value of the original message's PidTagInternetMessageId property (section 2.742) value.
Property ID: 0x1042
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagInstanceKey
Description: Contains an object on an NSPI server.
Property ID: 0x0FF6
Data type: PtypBinary, 0x0102
Area: Table Properties

Canonical name: PidTagInstanceNum
Description: Contains an identifier for a single instance of a row in the table.
Property ID: 0x674E
Data type: PtypInteger32, 0x0003
Area: ProviderDefinedNonTransmittable

Canonical name: PidTagInstID
Description: Contains an identifier for all instances of a row in the table.
Property ID: 0x674D
Data type: PtypInteger64, 0x0014
Area: ProviderDefinedNonTransmittable

Canonical name: PidTagInternetCodepage
Description: Indicates the code page used for the PidTagBody property (section 2.612) or the
PidTagBodyHtml property (section 2.615).
Property ID: 0x3FDE
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties

Canonical name: PidTagInternetMailOverrideFormat
Description: Indicates the encoding method and HTML inclusion for attachments.
Property ID: 0x5902
Data type: PtypInteger32, 0x0003
Area: MIME Properties

Canonical name: PidTagInternetMessageId
Description: Corresponds to the message-id field.
Property ID: 0x1035
Data type: PtypString, 0x001F
Area: MIME Properties

Canonical name: PidTagInternetReferences
Description: Contains a list of message IDs that specify the messages to which this reply is related.
Property ID: 0x1039
Data type: PtypString, 0x001F
Area: MIME Properties

Canonical name: PidTagIpmAppointmentEntryId
Description: Contains the EntryID of the Calendar folder.
Property ID: 0x36D0
Data type: PtypBinary, 0x0102
Area: Folder Properties

Canonical name: PidTagIpmContactEntryId
Description: Contains the EntryID of the Contacts folder.
Property ID: 0x36D1
Data type: PtypBinary, 0x0102
Area: Folder Properties

Canonical name: PidTagIpmDraftsEntryId
Description: Contains the EntryID of the Drafts folder.
Property ID: 0x36D7
Data type: PtypBinary, 0x0102
Area: Folder Properties

Canonical name: PidTagIpmJournalEntryId
Description: Contains the EntryID of the Journal folder.
Property ID: 0x36D2
Data type: PtypBinary, 0x0102
Area: Folder Properties

Canonical name: PidTagIpmNoteEntryId
Description: Contains the EntryID of the Notes folder.
Property ID: 0x36D3
Data type: PtypBinary, 0x0102
Area: Folder Properties

Canonical name: PidTagIpmTaskEntryId
Description: Contains the EntryID of the Tasks folder.
Property ID: 0x36D4
Data type: PtypBinary, 0x0102
Area: Folder Properties

Canonical name: PidTagIsdnNumber
Description: Contains the Integrated Services Digital Network (ISDN) telephone number of the
mail user.
Property ID: 0x3A2D
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagJunkAddRecipientsToSafeSendersList
Description: Indicates whether email recipients are to be added to the safe senders list.
Property ID: 0x6103
Data type: PtypInteger32, 0x0003
Area: Spam

Canonical name: PidTagJunkIncludeContacts
Description: Indicates whether email addresses of the contacts in the Contacts folder are treated in
a special way with respect to the spam filter.
Property ID: 0x6100
Data type: PtypInteger32, 0x0003
Area: Spam

Canonical name: PidTagJunkPermanentlyDelete
Description: Indicates whether messages identified as spam can be permanently deleted.
Property ID: 0x6102
Data type: PtypInteger32, 0x0003
Area: Spam

Canonical name: PidTagJunkPhishingEnableLinks
Description: Indicated whether the phishing stamp on a message is to be ignored.
Property ID: 0x6107
Data type: PtypBoolean, 0x000B
Area: Spam

Canonical name: PidTagJunkThreshold
Description: Indicates how aggressively incoming email is to be sent to the Junk Email folder.
Property ID: 0x6101
Data type: PtypInteger32, 0x0003
Area: Spam

Canonical name: PidTagKeyword
Description: Contains a keyword that identifies the mail user to the mail user's system
administrator.
Property ID: 0x3A0B
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagLanguage
Description: Contains a value that indicates the language in which the messaging user is writing
messages.
Property ID: 0x3A0C
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagLastModificationTime
Description: Contains the time, in UTC, of the last modification to the object.
Property ID: 0x3008
Data type: PtypTime, 0x0040
Area: Message Time Properties

Canonical name: PidTagLastModifierEntryId
Description: Specifies the Address Book EntryID of the last user to modify the contents of the
message.
Property ID: 0x3FFB
Data type: PtypBinary, 0x0102
Area: History Properties

Canonical name: PidTagLastModifierName
Description: Contains the name of the last mail user to change the Message object.
Property ID: 0x3FFA
Data type: PtypString, 0x001F
Area: History Properties
PR_LAST_MODIFIER_NAME_W

Canonical name: PidTagLastVerbExecuted
Description: Specifies the last verb executed for the message item to which it is related.
Property ID: 0x1081
Data type: PtypInteger32, 0x0003
Area: History Properties

Canonical name: PidTagLastVerbExecutionTime
Description: Contains the date and time, in UTC, during which the operation represented in the
PidTagLastVerbExecuted property (section 2.761) took place.
Property ID: 0x1082
Data type: PtypTime, 0x0040
Area: History Properties

Canonical name: PidTagListHelp
Description: Contains a URI that provides detailed help information for the mailing list from which an
email message was sent.
Property ID: 0x1043
Data type: PtypString, 0x001F
Area: Miscellaneous Properties

Canonical name: PidTagListSubscribe
Description: Contains the URI that subscribes a recipient to a message’s associated mailing list.
Property ID: 0x1044
Data type: PtypString, 0x001F
Area: Miscellaneous Properties

Canonical name: PidTagListUnsubscribe
Description: Contains the URI that unsubscribes a recipient from a message’s associated mailing
list.
Property ID: 0x1045
Data type: PtypString, 0x001F
Area: Miscellaneous Properties

Canonical name: PidTagLocalCommitTime
Description: Specifies the time, in UTC, that a Message object or Folder object was last changed.
Property ID: 0x6709
Data type: PtypTime, 0x0040
Area: Server
Defining references:  section ********;  section 2.2.*******3

Canonical name: PidTagLocalCommitTimeMax
Description: Contains the time of the most recent message change within the folder container,
excluding messages changed within subfolders.
Property ID: 0x670A
Data type: PtypTime, 0x0040
Area: Server

Canonical name: PidTagLocaleId
Description: Contains the Logon object LocaleID.
Property ID: 0x66A1
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties

Canonical name: PidTagLocality
Description: Contains the name of the mail user's locality, such as the town or city.
Property ID: 0x3A27
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagLocation
Description: Contains the location of the mail user.
Property ID: 0x3A0D
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagMailboxOwnerEntryId
Description: Contains the EntryID in the Global Address List (GAL) of the owner of the mailbox.
Property ID: 0x661B
Data type: PtypBinary, 0x0102
Area: Message Store Properties

Canonical name: PidTagMailboxOwnerName
Description: Contains the display name of the owner of the mailbox.
Property ID: 0x661C
Data type: PtypString, 0x001F
Area: Message Store Properties
ptagMailboxOwnerName, PR_MAILBOX_OWNER_NAME_W

Canonical name: PidTagManagerName
Description: Contains the name of the mail user's manager.
Property ID: 0x3A4E
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagMappingSignature
Description: A 16-byte constant that is present on all Address Book objects, but is not present on
objects in an offline address book.
Property ID: 0x0FF8
Data type: PtypBinary, 0x0102
Area: Miscellaneous Properties

Canonical name: PidTagMaximumSubmitMessageSize
Description: Maximum size, in kilobytes, of a message that a user is allowed to submit for
transmission to another user.
Property ID: 0x666D
Data type: PtypInteger32, 0x0003
Area: Message Store Properties

Canonical name: PidTagMemberId
Description: Contains a unique identifier that the messaging server generates for each user.
Property ID: 0x6671
Data type: PtypInteger64, 0x0014
Area: Access Control Properties

Canonical name: PidTagMemberName
Description: Contains the user-readable name of the user.
Property ID: 0x6672
Data type: PtypString, 0x001F
Area: Access Control Properties

Canonical name: PidTagMemberRights
Description: Contains the permissions for the specified user.
Property ID: 0x6673
Data type: PtypInteger32, 0x0003
Area: Access Control Properties

Canonical name: PidTagMessageAttachments
Description: Identifies all attachments to the current message.
Property ID: 0x0E13
Data type: PtypObject, 0x000D
Area: Message Attachment Properties

Canonical name: PidTagMessageCcMe
Descripton: Indicates that the receiving mailbox owner is a carbon copy (Cc) recipient of this
email message.
Property ID: 0x0058
Data type: PtypBoolean, 0x000B
Area: General Message Properties

Canonical name: PidTagMessageClass
Description: Denotes the specific type of the Message object.
Property ID: 0x001A
Data type: PtypString, 0x001F
Area: Common Property set

Canonical name: PidTagMessageCodepage
Description: Specifies the code page used to encode the non-Unicode string properties on this
Message object.
Property ID: 0x3FFD
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidTagMessageDeliveryTime
Description: Specifies the time (in UTC) when the server received the message.
Property ID: 0x0E06
Data type: PtypTime, 0x0040
Area: Message Time Properties

Canonical name: PidTagMessageEditorFormat
Description: Specifies the format that an email editor can use for editing the message body.
Property ID: 0x5909
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties

Canonical name: PidTagMessageFlags
Description: Specifies the status of the Message object.
Property ID: 0x0E07
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagMessageHandlingSystemCommonName
Description: Contains the common name of a messaging user for use in a message header.
Property ID: 0x3A0F
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagMessageLocaleId
Description: Contains the Windows Locale ID of the end-user who created this message.
Property ID: 0x3FF1
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties

Canonical name: PidTagMessageRecipientMe
Description: Indicates that the receiving mailbox owner is a primary or a carbon copy (Cc)
recipient of this email message.
Property ID: 0x0059
Data type: PtypBoolean, 0x000B
Area: General Message Properties

Canonical name: PidTagMessageRecipients
Description: Identifies all of the recipients of the current message.
Property ID: 0x0E12
Data type: PtypObject, 0x000D
Area: Address Properties

Canonical name: PidTagMessageSize
Description: Contains the size, in bytes, consumed by the Message object on the server.
Property ID: 0x0E08
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagMessageSizeExtended
Description: Specifies the 64-bit version of the PidTagMessageSize property (section 2.790).
Property ID: 0x0E08
Data type: PtypInteger64, 0x0014
Area: General Message Properties

Canonical name: PidTagMessageStatus
Description: Specifies the status of a message in a contents table.
Property ID: 0x0E17
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagMessageSubmissionId
Description: Contains a message identifier assigned by a message transfer agent.
Property ID: 0x0047
Data type: PtypBinary, 0x0102
Area: Email

Canonical name: PidTagMessageToMe
Description: Indicates that the receiving mailbox owner is one of the primary recipients of this
email message.
Property ID: 0x0057
Data type: PtypBoolean, 0x000B
Area: General Message Properties

Canonical name: PidTagMid
Description: Contains a value that contains the MID of the message currently being synchronized.
Property ID: 0x674A
Data type: PtypInteger64, 0x0014
Area: ID Properties

Canonical name: PidTagMiddleName
Description: Specifies the middle name(s) of the contact.
Property ID: 0x3A44
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagMimeSkeleton
Description: Contains the top-level MIME message headers, all MIME message body part headers,
and body part content that is not already converted to Message object properties, including
attachments.
Property ID: 0x64F0
Data type: PtypBinary, 0x0102
Area: MIME properties

Canonical name: PidTagMobileTelephoneNumber
Description: Contains the mail user's cellular telephone number.
Property ID: 0x3A1C
Data type: PtypString, 0x001F
Area: Address Properties
PR_MOBILE_TELEPHONE_NUMBER_W

Canonical name: PidTagNativeBody
Description: Indicates the best available format for storing the message body.
Property ID: 0x1016
Data type: PtypInteger32, 0x0003
Area: BestBody

Canonical name: PidTagNextSendAcct
Description: Specifies the server that a client is currently attempting to use to send email.
Property ID: 0x0E29
Data type: PtypString, 0x001F
Area: Outlook Application

Canonical name: PidTagNickname
Description: Contains the mail user's nickname.
Property ID: 0x3A4F
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagNonDeliveryReportDiagCode
Description: Contains the diagnostic code for a delivery status notification, as specified in .
Property ID: 0x0C05
Data type: PtypInteger32, 0x0003
Area: Email

Canonical name: PidTagNonDeliveryReportReasonCode
Description: Contains an integer value that indicates a reason for delivery failure.
Property ID: 0x0C04
Data type: PtypInteger32, 0x0003
Area: Email

Canonical name: PidTagNonDeliveryReportStatusCode
Description: Contains the value of the Status field for a delivery status notification, as specified in
Property ID: 0x0C20
Data type: PtypInteger32, 0x0003
Area: Email
Canonical Name: PidTagNonReceiptNotificationRequested
Description: Specifies whether the client sends a non-read receipt.
Property ID: 0x0C06
Data type: PtypBoolean, 0x000B
Area: Email

Canonical name: PidTagNormalizedSubject
Description: Contains the normalized subject of the message.
Property ID: 0x0E1D
Data type: PtypString, 0x001F
Area: Email
ptagNormalizedSubject, PR_NORMALIZED_SUBJECT_W

Canonical name: PidTagObjectType
Description: Indicates the type of Server object.
Property ID: 0x0FFE
Data type: PtypInteger32, 0x0003
Area: Common

Canonical name: PidTagOfficeLocation
Description: Contains the mail user's office location.
Property ID: 0x3A19
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagOfflineAddressBookContainerGuid
Description: A string-formatted GUID that represents the address list container object.
Property ID: 0x6802
Data type: PtypString8, 0x001E
Area: Offline Address Book Properties

Canonical name: PidTagOfflineAddressBookDistinguishedName
Description: Contains the DN of the address list that is contained in the OAB message.
Property ID: 0x6804
Data type: PtypString8, 0x001E
Area: Offline Address Book Properties

Canonical name: PidTagOfflineAddressBookMessageClass
Description: Contains the message class for full OAB messages.
Property ID: 0x6803
Data type: PtypInteger32, 0x0003
Area: Offline Address Book Properties

Canonical name: PidTagOfflineAddressBookName
Description: Contains the display name of the address list.
Property ID: 0x6800
Data type: PtypString, 0x001F
Area: Offline Address Book Properties

Canonical name: PidTagOfflineAddressBookSequence
Description: Contains the sequence number of the OAB.
Property ID: 0x6801
Data type: PtypInteger32, 0x0003
Area: Offline Address Book Properties

Canonical name: PidTagOfflineAddressBookTruncatedProperties
Description: Contains a list of the property tags that have been truncated or limited by the server.
Property ID: 0x6805
Data type: PtypMultipleInteger32, 0x1003
Area: Offline Address Book Properties

Canonical name: PidTagOrdinalMost
Description: Contains a positive number whose negative is less than or equal to the value of the
PidLidTaskOrdinal property (section 2.327) of all of the Task objects in the folder.
Property ID: 0x36E2
Data type: PtypInteger32, 0x0003
Area: Tasks

Canonical name: PidTagOrganizationalIdNumber
Description: Contains an identifier for the mail user used within the mail user's organization.
Property ID: 0x3A10
Data type: PtypString, 0x001F
Area: Address Properties
PR_ORGANIZATIONAL_ID_NUMBER_W

Canonical name: PidTagOriginalAuthorEntryId
Description: Contains an address book EntryID structure ( section *******) and is
defined in report messages to identify the user who sent the original message.
Property ID: 0x004C
Data type: PtypBinary, 0x0102
Area: Email

Canonical name: PidTagOriginalAuthorName
Description: Contains the display name of the sender of the original message referenced by a report
message.
Property ID: 0x004D
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagOriginalDeliveryTime
Description: Contains the delivery time, in UTC, from the original message.
Property ID: 0x0055
Data type: PtypTime, 0x0040
Area: General Message Properties

Canonical name: PidTagOriginalDisplayBcc
Description: Contains the value of the PidTagDisplayBcc property (section 2.668) from the original message.
Property ID: 0x0072
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalDisplayCc
Description: Contains the value of the PidTagDisplayCc property(section 2.669) from the original message.
Property ID: 0x0073
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalDisplayTo
Description: Contains the value of the PidTagDisplayTo property (section 2.672) from the original message.
Property ID: 0x0074
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalEntryId
Description: Contains the original EntryID of an object.
Property ID: 0x3A12
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagOriginalMessageClass
Description: Designates the PidTagMessageClass property ( section *******) from the original message.
Property ID: 0x004B
Data type: PtypString, 0x001F
Area: Secure Messaging Properties

Canonical name: PidTagOriginalMessageId
Description: Contains the message ID of the original message included in replies or resent messages.
Property ID: 0x1046
Data type: PtypString, 0x001F
Area: Mail

Canonical name: PidTagOriginalSenderAddressType
Description: Contains the value of the original message sender's PidTagSenderAddressType property (section 2.994).
Property ID: 0x0066
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSenderEmailAddress
Description: Contains the value of the original message sender's PidTagSenderEmailAddress
property (section 2.995).
Property ID: 0x0067
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSenderEntryId
Description: Contains an address book EntryID that is set on delivery report messages.
Property ID: 0x005B
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagOriginalSenderName
Description: Contains the value of the original message sender's PidTagSenderName property(section 2.998), and is set on delivery report messages.
Property ID: 0x005A
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSenderSearchKey
Description: Contains an address book search key set on the original email message.
Property ID: 0x005C
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagOriginalSensitivity
Description: Contains the sensitivity value of the original email message.
Property ID: 0x002E
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagOriginalSentRepresentingAddressType
Description: Contains the address type of the end user who is represented by the original email
message sender.
Property ID: 0x0068
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSentRepresentingEmailAddress
Description: Contains the email address of the end user who is represented by the original email
message sender.
Property ID: 0x0069
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSentRepresentingEntryId
Description: Identifies an address book EntryID that contains the entry identifier of the end user
who is represented by the original message sender.
Property ID: 0x005E
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagOriginalSentRepresentingName
Description: Contains the display name of the end user who is represented by the original email
message sender.
Property ID: 0x005D
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSentRepresentingSearchKey
Description: Identifies an address book search key that contains the SearchKey of the end user who
is represented by the original message sender.
Property ID: 0x005F
Data type: PtypBinary, 0x0102
Area: General Message Properties

Canonical name: PidTagOriginalSubject
Description: Specifies the subject of the original message.
Property ID: 0x0049
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagOriginalSubmitTime
Description: Specifies the original email message's submission date and time, in UTC.
Property ID: 0x004E
Data type: PtypTime, 0x0040
Area: General Message Properties

Canonical name: PidTagOriginatorDeliveryReportRequested
Description: Indicates whether an email sender requests an email delivery receipt from the messaging system.
Property ID: 0x0023
Data type: PtypBoolean, 0x000B
Area: MIME Properties

Canonical name: PidTagOriginatorNonDeliveryReportRequested
Description: Specifies whether an email sender requests suppression of nondelivery receipts.
Property ID: 0x0C08
Data type: PtypBoolean, 0x000B
Area: MIME Properties

Canonical name: PidTagOscSyncEnabled
Description: Specifies whether contact synchronization with an external source is handled by the
server.
Property ID: 0x7C24
Data type: PtypBoolean, 0x000B
Area: Contact Properties

Canonical name: PidTagOtherAddressCity
Description: Contains the name of the mail user's other locality, such as the town or city.
Property ID: 0x3A5F
Data type: PtypString, 0x001F
Area: Address Properties
PR_OTHER_ADDRESS_CITY_W

Canonical name: PidTagOtherAddressCountry
Description: Contains the name of the mail user's other country/region.
Property ID: 0x3A60
Data type: PtypString, 0x001F
Area: Address Properties
PR_OTHER_ADDRESS_COUNTRY_W

Canonical name: PidTagOtherAddressPostalCode
Description: Contains the postal code for the mail user's other postal address.
Property ID: 0x3A61
Data type: PtypString, 0x001F
Area: Address Properties
PR_OTHER_ADDRESS_POSTAL_CODE_W

Canonical name: PidTagOtherAddressPostOfficeBox
Description: Contains the number or identifier of the mail user's other post office box.
Property ID: 0x3A64
Data type: PtypString, 0x001F
Area: Address Properties

PR_OTHER_ADDRESS_POST_OFFICE_BOX_A, PR_OTHER_ADDRESS_POST_OFFICE_BOX_W,

Canonical name: PidTagOtherAddressStateOrProvince
Description: Contains the name of the mail user's other state or province.
Property ID: 0x3A62
Data type: PtypString, 0x001F
Area: Address Properties
PR_OTHER_ADDRESS_STATE_OR_PROVINCE_A, PR_OTHER_ADDRESS_STATE_OR_PROVINCE_W,

Canonical name: PidTagOtherAddressStreet
Description: Contains the mail user's other street address.
Property ID: 0x3A63
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagOtherTelephoneNumber
Description: Contains an alternate telephone number for the mail user.
Property ID: 0x3A1F
Data type: PtypString, 0x001F
Area: Address Properties


Canonical name: PidTagOutOfOfficeState
Description: Indicates whether the user is OOF.
Property ID: 0x661D
Data type: PtypBoolean, 0x000B
Area: Message Store Properties

Canonical name: PidTagOwnerAppointmentId
Description: Specifies a quasi-unique value among all of the Calendar objects in a user's mailbox.
Property ID: 0x0062
Data type: PtypInteger32, 0x0003
Area: Appointment
Defining reference: section *******9

Canonical name: PidTagPagerTelephoneNumber
Description: Contains the mail user's pager telephone number.
Property ID: 0x3A21
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagParentEntryId
Description: Contains the EntryID of the folder where messages or subfolders reside.
Property ID: 0x0E09
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagParentFolderId
Description: Contains a value that contains the Folder ID (FID), as specified in
section *******, that identifies the parent folder of the messaging object being synchronized.
Property ID: 0x6749
Data type: PtypInteger64, 0x0014
Area: ID Properties

Canonical name: PidTagParentKey
Description: Contains the search key that is used to correlate the original message and the reports
about the original message.
Property ID: 0x0025
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagParentSourceKey
Description: Contains a value on a folder that contains the PidTagSourceKey property (section
2.1016) of the parent folder.
Property ID: 0x65E1
Data type: PtypBinary, 0x0102
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagPersonalHomePage
Description: Contains the URL of the mail user's personal home page.
Property ID: 0x3A50
Data type: PtypString, 0x001F
Area: MapiMailUser
PR_PERSONAL_HOME_PAGE_W

Canonical name: PidTagPolicyTag
Description: Specifies the GUID of a retention tag.
Property ID: 0x3019
Data type: PtypBinary, 0x0102
Area: Archive

Canonical name: PidTagPostalAddress
Description: Contains the mail user's postal address.
Property ID: 0x3A15
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagPostalCode
Description: Contains the postal code for the mail user's postal address.
Property ID: 0x3A2A
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagPostOfficeBox
Description: Contains the number or identifier of the mail user's post office box.
Property ID: 0x3A2B
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagPredecessorChangeList
Description: Contains a value that contains a serialized representation of a PredecessorChangeList structure.
Property ID: 0x65E3
Data type: PtypBinary, 0x0102
Area: Sync

Canonical name: PidTagPrimaryFaxNumber
Description: Contains the telephone number of the mail user's primary fax machine.
Property ID: 0x3A23
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagPrimarySendAccount
Description: Specifies the first server that a client is to use to send the email with.
Property ID: 0x0E28
Data type: PtypString, 0x001F
Area: MapiNonTransmittable

Canonical name: PidTagPrimaryTelephoneNumber
Description: Contains the mail user's primary telephone number.
Property ID: 0x3A1A
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagPriority
Description: Indicates the client's request for the priority with which the message is to be sent by the
messaging system.
Property ID: 0x0026
Data type: PtypInteger32, 0x0003
Area: Email

Canonical name: PidTagProcessed
Description: Indicates whether a client has already processed a received task communication.
Property ID: 0x7D01
Data type: PtypBoolean, 0x000B
Area: Calendar

Canonical name: PidTagProfession
Description: Contains the name of the mail user's line of business.
Property ID: 0x3A46
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagProhibitReceiveQuota
Description: Maximum size, in kilobytes, that a user is allowed to accumulate in their mailbox
before no further email will be delivered to their mailbox.
Property ID: 0x666A
Data type: PtypInteger32, 0x0003
Area: Exchange Administrative

Canonical name: PidTagProhibitSendQuota
Description: Maximum size, in kilobytes, that a user is allowed to accumulate in their mailbox
before the user can no longer send any more email.
Property ID: 0x666E
Data type: PtypInteger32, 0x0003
Area: ExchangeAdministrative

Canonical name: PidTagPurportedSenderDomain
Description: Contains the domain responsible for transmitting the current message.
Property ID: 0x4083
Data type: PtypString, 0x001F
Area: TransportEnvelope

Canonical name: PidTagRadioTelephoneNumber
Description: Contains the mail user's radio telephone number.
Property ID: 0x3A1D
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagRead
Description: Indicates whether a message has been read.
Property ID: 0x0E69
Data type: PtypBoolean, 0x000B
Area: MapiNonTransmittable Property set

Canonical name: PidTagReadReceiptAddressType
Description: Contains the address type of the end user to whom a read receipt is directed.
Property ID: 0x4029
Data type: PtypString, 0x001F
Area: Transport Envelope

Canonical name: PidTagReadReceiptEmailAddress
Description: Contains the email address of the end user to whom a read receipt is directed.
Property ID: 0x402A
Data type: PtypString, 0x001F
Area: Transport Envelope

Canonical name: PidTagReadReceiptEntryId
Description: Contains an address book EntryID.
Property ID: 0x0046
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagReadReceiptName
Description: Contains the display name for the end user to whom a read receipt is directed.
Property ID: 0x402B
Data type: PtypString, 0x001F
Area: Transport Envelope

Canonical name: PidTagReadReceiptRequested
Description: Specifies whether the email sender requests a read receipt from all recipients when this email message is read or opened.
Property ID: 0x0029
Data type: PtypBoolean, 0x000B
Area: Email


Canonical name: PidTagReadReceiptSearchKey
Description: Contains an address book search key.
Property ID: 0x0053
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagReadReceiptSmtpAddress
Description: Contains the SMTP email address of the user to whom a read receipt is directed.
Property ID: 0x5D05
Data type: PtypString, 0x001F
Area: Mail

Canonical name: PidTagReceiptTime
Description: Contains the sent time for a message disposition notification, as specified in .
Property ID: 0x002A
Data type: PtypTime, 0x0040
Area: Email

Canonical name: PidTagReceivedByAddressType
Description: Contains the email message receiver's email address type.
Property ID: 0x0075
Data type: PtypString, 0x001F
Area: MapiEnvelope

Canonical name: PidTagReceivedByEmailAddress
Description: Contains the email message receiver's email address.
Property ID: 0x0076
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagReceivedByEntryId
Description: Contains the address book EntryID of the mailbox receiving the Email object.
Property ID: 0x003F
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagReceivedByName
Description: Contains the email message receiver's display name.
Property ID: 0x0040
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagReceivedBySearchKey
Description: Identifies an address book search key that contains a binary-comparable key that is
used to identify correlated objects for a search.
Property ID: 0x0051
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagReceivedBySmtpAddress
Description: Contains the email message receiver's SMTP email address.
Property ID: 0x5D07
Data type: PtypString, 0x001F
Area: Mail

Canonical name: PidTagReceivedRepresentingAddressType
Description: Contains the email address type for the end user represented by the receiving mailbox owner.
Property ID: 0x0077
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagReceivedRepresentingEmailAddress
Description: Contains the email address for the end user represented by the receiving mailbox owner.
Property ID: 0x0078
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagReceivedRepresentingEntryId
Description: Contains an address book EntryID that identifies the end user represented by the
receiving mailbox owner.
Property ID: 0x0043
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagReceivedRepresentingName
Description: Contains the display name for the end user represented by the receiving mailbox owner.
Property ID: 0x0044
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagReceivedRepresentingSearchKey
Description: Identifies an address book search key that contains a binary-comparable key of the end user represented by the receiving mailbox owner.
Property ID: 0x0052
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagReceivedRepresentingSmtpAddress
Description: Contains the SMTP email address of the user represented by the receiving mailbox owner.
Property ID: 0x5D08
Data type: PtypString, 0x001F
Area: Mail

Canonical name: PidTagRecipientDisplayName
Description: Specifies the display name of the recipient.
Property ID: 0x5FF6
Data type: PtypString, 0x001F
Area: TransportRecipient

Canonical name: PidTagRecipientEntryId
Description: Identifies an Address Book object that specifies the recipient.
Property ID: 0x5FF7
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagRecipientFlags
Description: Specifies a bit field that describes the recipient status.
Property ID: 0x5FFD
Data type: PtypInteger32, 0x0003
Area: TransportRecipient

Canonical name: PidTagRecipientOrder
Description: Specifies the location of the current recipient in the recipient table.
Property ID: 0x5FDF
Data type: PtypInteger32, 0x0003
Area: TransportRecipient

Canonical name: PidTagRecipientProposed
Description: Indicates that the attendee proposed a new date and/or time.
Property ID: 0x5FE1
Data type: PtypBoolean, 0x000B
Area: TransportRecipient

Canonical name: PidTagRecipientProposedEndTime
Description: Indicates the meeting end time requested by the attendee in a counter proposal.
Property ID: 0x5FE4
Data type: PtypTime, 0x0040
Area: TransportRecipient

Canonical name: PidTagRecipientProposedStartTime
Description: Indicates the meeting start time requested by the attendee in a counter proposal.
Property ID: 0x5FE3
Data type: PtypTime, 0x0040
Area: TransportRecipient

Canonical name: PidTagRecipientReassignmentProhibited
Description: Specifies whether adding additional or different recipients is prohibited for the email
message when forwarding the email message.
Property ID: 0x002B
Data type: PtypBoolean, 0x000B
Area: MapiEnvelope

Canonical name: PidTagRecipientTrackStatus
Description: Indicates the response status that is returned by the attendee.
Property ID: 0x5FFF
Data type: PtypInteger32, 0x0003
Area: TransportRecipient

Canonical name: PidTagRecipientTrackStatusTime
Description: Indicates the date and time at which the attendee responded.
Property ID: 0x5FFB
Data type: PtypTime, 0x0040
Area: TransportRecipient

Canonical name: PidTagRecipientType
Description: Represents the recipient type of a recipient on the message.
Property ID: 0x0C15
Data type: PtypInteger32, 0x0003
Area: MapiRecipient


Canonical name: PidTagRecordKey
Description: Contains a unique binary-comparable identifier for a specific object.
Property ID: 0x0FF9
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagReferredByName
Description: Contains the name of the mail user's referral.
Property ID: 0x3A47
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagRemindersOnlineEntryId
Description: Contains an EntryID for the Reminders folder.
Property ID: 0x36D5
Data type: PtypBinary, 0x0102
Area: MapiContainer

Canonical name: PidTagRemoteMessageTransferAgent
Description: Contains the value of the Remote-MTA field for a delivery status notification, as specified in.
Property ID: 0x0C21
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagRenderingPosition
Description: Represents an offset, in rendered characters, to use when rendering an attachment within the main message text.
Property ID: 0x370B
Data type: PtypInteger32, 0x0003
Area: MapiAttachment

Canonical name: PidTagReplyRecipientEntries
Description: Identifies a FlatEntryList structure ( section 2.3.3) of address book EntryIDs for recipients that are to receive a reply.
Property ID: 0x004F
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagReplyRecipientNames
Description: Contains a list of display names for recipients that are to receive a reply.
Property ID: 0x0050
Data type: PtypString, 0x001F
Area: MapiEnvelope

Canonical name: PidTagReplyRequested
Description: Indicates whether a reply is requested to a Message object.
Property ID: 0x0C17
Data type: PtypBoolean, 0x000B
Area: MapiRecipient


Canonical name: PidTagReplyTemplateId
Description: Contains the value of the GUID that points to a Reply template.
Property ID: 0x65C2
Data type: PtypBinary, 0x0102
Area: Rules

Canonical name: PidTagReplyTime
Description: Specifies the time, in UTC, that the sender has designated for an associated work item to be due.
Property ID: 0x0030
Data type: PtypTime, 0x0040
Area: MapiEnvelope

Canonical name: PidTagReportDisposition
Description: Contains a string indicating whether the original message was displayed to the user or deleted (report messages only).
Property ID: 0x0080
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagReportDispositionMode
Description: Contains a description of the action that a client has performed on behalf of a user(report messages only).
Property ID: 0x0081
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagReportEntryId
Description: Specifies an entry ID that identifies the application that generated a report message.
Property ID: 0x0045
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagReportingMessageTransferAgent
Description: Contains the value of the Reporting-MTA field for a delivery status notification, as specified in .
Property ID: 0x6820
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagReportName
Description: Contains the display name for the entity (usually a server agent) that generated the report message.
Property ID: 0x003A
Data type: PtypString, 0x001F
Area: MapiEnvelope

Canonical name: PidTagReportSearchKey
Description: Contains an address book search key representing the entity (usually a server agent) that generated the report message.
Property ID: 0x0054
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagReportTag
Description: Contains the data that is used to correlate the report and the original message.
Property ID: 0x0031
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagReportText
Description: Contains the optional text for a report message.
Property ID: 0x1001
Data type: PtypString, 0x001F
Area: MapiMessage

Canonical name: PidTagReportTime
Description: Indicates the last time that the contact list that is controlled by thePidTagJunkIncludeContacts property (section 2.752) was updated.
Property ID: 0x0032
Data type: PtypTime, 0x0040
Area: MapiEnvelope Property set

Canonical name: PidTagResolveMethod
Description: Specifies how to resolve any conflicts with the message.
Property ID: 0x3FE7
Data type: PtypInteger32, 0x0003
Area: MapiStatus

Canonical name: PidTagResponseRequested
Description: Indicates whether a response is requested to a Message object.
Property ID: 0x0063
Data type: PtypBoolean, 0x000B
Area: MapiEnvelope Property set


Canonical name: PidTagResponsibility
Description: Specifies whether another mail agent has ensured that the message will be delivered.
Property ID: 0x0E0F
Data type: PtypBoolean, 0x000B
Area: MapiNonTransmittable

Canonical name: PidTagRetentionDate
Description: Specifies the date, in UTC, after which a Message object is expired by the server.
Property ID: 0x301C
Data type: PtypTime, 0x0040
Area: Archive

Canonical name: PidTagRetentionFlags
Description: Contains flags that specify the status or nature of an item's retention tag or archive tag.
Property ID: 0x301D
Data type: PtypInteger32, 0x0003
Area: Archive

Canonical name: PidTagRetentionPeriod
Description: Specifies the number of days that a Message object can remain unarchived.
Property ID: 0x301A
Data type: PtypInteger32, 0x0003
Area: Archive

Canonical name: PidTagRights
Description: Specifies a user's folder permissions.
Property ID: 0x6639
Data type: PtypInteger32, 0x0003
Area: ExchangeFolder

Canonical name: PidTagRoamingDatatypes
Description: Contains a bitmask that indicates which stream properties exist on the message.
Property ID: 0x7C06
Data type: PtypInteger32, 0x0003
Area: Configuration

Canonical name: PidTagRoamingDictionary
Description: Contains a dictionary stream, as specified in  section *******.
Property ID: 0x7C07
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagRoamingXmlStream
Description: Contains an XML stream, as specified in  section *******.
Property ID: 0x7C08
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagRowid
Description: Contains a unique identifier for a recipient in a message's recipient table.
Property ID: 0x3000
Data type: PtypInteger32, 0x0003
Area: MapiCommon

Canonical name: PidTagRowType
Description: Identifies the type of the row.
Property ID: 0x0FF5
Data type: PtypInteger32, 0x0003
Area: MapiNonTransmittable

Canonical name: PidTagRtfCompressed
Description: Contains message body text in compressed RTF format.
Property ID: 0x1009
Data type: PtypBinary, 0x0102
Area: Email


Canonical name: PidTagRtfInSync
Description: Indicates whether the PidTagBody property (section 2.612) and the
PidTagRtfCompressed property (section 2.935) contain the same text (ignoring formatting).
Property ID: 0x0E1F
Data type: PtypBoolean, 0x000B
Area: Email


Canonical name: PidTagRuleActionNumber
Description: Contains the index of a rule action that failed.
Property ID: 0x6650
Data type: PtypInteger32, 0x0003
Area: ExchangeMessageReadOnly

Canonical name: PidTagRuleActions
Description: Contains the set of actions associated with the rule.
Property ID: 0x6680
Data type: PtypRuleAction, 0x00FE
Area: Server-Side Rules Properties

Canonical name: PidTagRuleActionType
Description: Contains the ActionType field ( section *******) of a rule that failed.
Property ID: 0x6649
Data type: PtypInteger32, 0x0003
Area: ExchangeMessageReadOnly

Canonical name: PidTagRuleCondition
Description: Defines the conditions under which a rule action is to be executed.
Property ID: 0x6679
Data type: PtypRestriction, 0x00FD
Area: Server-Side Rules Properties

Canonical name: PidTagRuleError
Description: Contains the error code that indicates the cause of an error encountered during the execution of the rule.
Property ID: 0x6648
Data type: PtypInteger32, 0x0003
Area: ExchangeMessageReadOnly

Canonical name: PidTagRuleFolderEntryId
Description: Contains the EntryID of the folder where the rule that triggered the generation of a DAM is stored.
Property ID: 0x6651
Data type: PtypBinary, 0x0102
Area: ExchangeMessageReadOnly

Canonical name: PidTagRuleId
Description: Specifies a unique identifier that is generated by the messaging server for each rule
when the rule is first created.
Property ID: 0x6674
Data type: PtypInteger64, 0x0014
Area: Server-Side Rules Properties

Canonical name: PidTagRuleIds
Description: Contains a buffer that is obtained by concatenating the PidTagRuleId property.
Property ID: 0x6675
Data type: PtypBinary, 0x0102
Area: Server-Side Rules Properties

Canonical name: PidTagRuleLevel
Description: Contains 0x00000000. This property is not used.
Property ID: 0x6683
Data type: PtypInteger32, 0x0003
Area: Server-Side Rules Properties

Canonical name: PidTagRuleMessageLevel
Description: Contains 0x00000000. Set on the FAI message.
Property ID: 0x65ED
Data type: PtypInteger32, 0x0003
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleMessageName
Description: Specifies the name of the rule. Set on the FAI message.
Property ID: 0x65EC
Data type: PtypString, 0x001F
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleMessageProvider
Description: Identifies the client application that owns the rule. Set on the FAI message.
Property ID: 0x65EB
Data type: PtypString, 0x001F
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleMessageProviderData
Description: Contains opaque data set by the client for the exclusive use of the client. Set on the FAI message.
Property ID: 0x65EE
Data type: PtypBinary, 0x0102
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleMessageSequence
Description: Contains a value used to determine the order in which rules are evaluated and executed. Set on the FAI message.
Property ID: 0x65F3
Data type: PtypInteger32, 0x0003
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleMessageState
Description: Contains flags that specify the state of the rule. Set on the FAI message.
Property ID: 0x65E9
Data type: PtypInteger32, 0x0003
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleMessageUserFlags
Description: Contains an opaque property that the client sets for the exclusive use of the client. Set on the FAI message.
Property ID: 0x65EA
Data type: PtypInteger32, 0x0003
Area: ExchangeNonTransmittableReserved

Canonical name: PidTagRuleName
Description: Specifies the name of the rule.
Property ID: 0x6682
Data type: PtypString, 0x001F
Area: Server-Side Rules Properties

Canonical name: PidTagRuleProvider
Description: A string identifying the client application that owns a rule.
Property ID: 0x6681
Data type: PtypString, 0x001F
Area: Server-Side Rules Properties

Canonical name: PidTagRuleProviderData
Description: Contains opaque data set by the client for the exclusive use of the client.
Property ID: 0x6684
Data type: PtypBinary, 0x0102
Area: Server-Side Rules Properties

Canonical name: PidTagRuleSequence
Description: Contains a value used to determine the order in which rules are evaluated and executed.
Property ID: 0x6676
Data type: PtypInteger32, 0x0003
Area: Server-Side Rules Properties

Canonical name: PidTagRuleState
Description: Contains flags that specify the state of the rule.
Property ID: 0x6677
Data type: PtypInteger32, 0x0003
Area: Server-Side Rules Properties

Canonical name: PidTagRuleUserFlags
Description: Contains an opaque property that the client sets for the exclusive use of the client.
Property ID: 0x6678
Data type: PtypInteger32, 0x0003
Area: Server-Side Rules Properties

Canonical name: PidTagRwRulesStream
Description: Contains additional rule data about the Rule FAI message.
Property ID: 0x6802
Data type: PtypBinary, 0x0102
Area: Message Class Defined Transmittable

Canonical name: PidTagScheduleInfoAppointmentTombstone
Description: Contains a list of tombstones, where each tombstone represents a Meeting object that has been declined.
Property ID: 0x686A
Data type: PtypBinary, 0x0102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoAutoAcceptAppointments
Description: Indicates whether a client or server is to automatically respond to all meeting requests for the attendee or resource.
Property ID: 0x686D
Data type: PtypBoolean, 0x000B
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDelegateEntryIds
Description: Specifies the EntryIDs of the delegates.
Property ID: 0x6845
Data type: PtypMultipleBinary, 0x1102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDelegateNames
Description: Specifies the names of the delegates.
Property ID: 0x6844
Data type: PtypMultipleString, 0x101F
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDelegateNamesW
Description: Specifies the names of the delegates in Unicode.
Property ID: 0x684A
Data type: PtypMultipleString, 0x101F
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDelegatorWantsCopy
Description: Indicates whether the delegator wants to receive copies of the meeting-related objects that are sent to the delegate.
Property ID: 0x6842
Data type: PtypBoolean, 0x000B
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDelegatorWantsInfo
Description: Indicates whether the delegator wants to receive informational updates.
Property ID: 0x684B
Data type: PtypBoolean, 0x000B
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDisallowOverlappingAppts
Description: Indicates whether a client or server, when automatically responding to meeting requests, is to decline Meeting Request objects that overlap with previously scheduled events.
Property ID: 0x686F
Data type: PtypBoolean, 0x000B
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDisallowRecurringAppts
Description: Indicates whether a client or server, when automatically responding to meeting requests, is to decline Meeting Request objects that represent a recurring series.
Property ID: 0x686E
Data type: PtypBoolean, 0x000B
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoDontMailDelegates
Description: Contains a value set to TRUE by the client, regardless of user input.
Property ID: 0x6843
Data type: PtypBoolean, 0x000B
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoFreeBusy
Description: This property is deprecated and is not to be used.
Property ID: 0x686C
Data type: PtypBinary, 0x0102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoFreeBusyAway
Description: Specifies the times for which the free/busy status is set a value of OOF.
Property ID: 0x6856
Data type: PtypMultipleBinary, 0x1102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoFreeBusyBusy
Description: Specifies the blocks of time for which the free/busy status is set to a value of busy.
Property ID: 0x6854
Data type: PtypMultipleBinary, 0x1102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoFreeBusyMerged
Description: Specifies the blocks for which free/busy data of type busy or OOF is present in the free/busy message.
Property ID: 0x6850
Data type: PtypMultipleBinary, 0x1102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoFreeBusyTentative
Description: Specifies the blocks of times for which the free/busy status is set to a value of tentative.
Property ID: 0x6852
Data type: PtypMultipleBinary, 0x1102
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoMonthsAway
Description: Specifies the months for which free/busy data of type OOF is present in the free/busy message.
Property ID: 0x6855
Data type: PtypMultipleInteger32, 0x1003
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoMonthsBusy
Description: Specifies the months for which free/busy data of type busy is present in the free/busy message.
Property ID: 0x6853
Data type: PtypMultipleInteger32, 0x1003
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoMonthsMerged
Description: Specifies the months for which free/busy data of type busy or OOF is present in the free/busy message.
Property ID: 0x684F
Data type: PtypMultipleInteger32, 0x1003
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoMonthsTentative
Description: Specifies the months for which free/busy data of type tentative is present in the free/busy message.
Property ID: 0x6851
Data type: PtypMultipleInteger32, 0x1003
Area: Free/Busy Properties

Canonical name: PidTagScheduleInfoResourceType
Description: Set to 0x00000000 when sending and is ignored on receipt.
Property ID: 0x6841
Data type: PtypInteger32, 0x0003
Area: Free/Busy Properties

Canonical name: PidTagSchedulePlusFreeBusyEntryId
Description: Contains the EntryID of the folder named "SCHEDULE+ FREE BUSY" under the non-IPM subtree of the public folder message store.
Property ID: 0x6622
Data type: PtypBinary, 0x0102
Area: ExchangeMessageStore

Canonical name: PidTagScriptData
Description: Contains a series of instructions that can be executed to format an address and the data that is needed to execute those instructions.
Property ID: 0x0004
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagSearchFolderDefinition
Description: Specifies the search criteria and search options.
Property ID: 0x6845
Data type: PtypBinary, 0x0102
Area: Search

Canonical name: PidTagSearchFolderEfpFlags
Description: Specifies flags that control how a folder is displayed.
Property ID: 0x6848
Data type: PtypInteger32, 0x0003
Area: Search

Canonical name: PidTagSearchFolderExpiration
Description: Contains the time, in UTC, at which the search folder container will be stale and has to be updated or recreated.
Property ID: 0x683A
Data type: PtypInteger32, 0x0003
Area: Search

Canonical name: PidTagSearchFolderId
Description: Contains a GUID that identifies the search folder.
Property ID: 0x6842
Data type: PtypBinary, 0x0102
Area: Search

Canonical name: PidTagSearchFolderLastUsed
Description: Contains the last time, in UTC, that the folder was accessed.
Property ID: 0x6834
Data type: PtypInteger32, 0x0003
Area: Search

Canonical name: PidTagSearchFolderRecreateInfo
Description: This property is not to be used.
Property ID: 0x6844
Data type: PtypBinary, 0x0102
Area: Search

Canonical name: PidTagSearchFolderStorageType
Description: Contains flags that specify the binary large object (BLOB) data that appears in the PidTagSearchFolderDefinition property.
Property ID: 0x6846
Data type: PtypInteger32, 0x0003
Area: Search

Canonical name: PidTagSearchFolderTag
Description: Contains the value of the SearchFolderTag sub-property of the PidTagExtendedFolderFlags (section 2.685) property of the search folder container.
Property ID: 0x6847
Data type: PtypInteger32, 0x0003
Area: Search

Canonical name: PidTagSearchFolderTemplateId
Description: Contains the ID of the template that is being used for the search.
Property ID: 0x6841
Data type: PtypInteger32, 0x0003
Area: Search

Canonical name: PidTagSearchKey
Description: Contains a unique binary-comparable key that identifies an object for a search.
Property ID: 0x300B
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagSecurityDescriptorAsXml
Description: Contains security attributes in XML.
Property ID: 0x0E6A
Data type: PtypString, 0x001F
Area: Access Control Properties

Canonical name: PidTagSelectable
Description: This property is not set and, if set, is ignored.
Property ID: 0x3609
Data type: PtypBoolean, 0x000B
Area: AB Container

Canonical name: PidTagSenderAddressType
Description: Contains the email address type of the sending mailbox owner.
Property ID: 0x0C1E
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSenderEmailAddress
Description: Contains the email address of the sending mailbox owner.
Property ID: 0x0C1F
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSenderEntryId
Description: Identifies an address book EntryID that contains the address book EntryID of the sending mailbox owner.
Property ID: 0x0C19
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagSenderIdStatus
Description: Reports the results of a Sender-ID check.
Property ID: 0x4079
Data type: PtypInteger32, 0x0003
Area: Secure Messaging Properties

Canonical name: PidTagSenderName
Description: Contains the display name of the sending mailbox owner.
Property ID: 0x0C1A
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSenderSearchKey
Description: Identifies an address book search key.
Property ID: 0x0C1D
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagSenderSmtpAddress
Description: Contains the SMTP email address format of the e–mail address of the sending mailbox owner.
Property ID: 0x5D01
Data type: PtypString, 0x001F
Area: Mail

Canonical name: PidTagSenderTelephoneNumber
Description: Contains the telephone number of the caller associated with a voice mail message.
Property ID: 0x6802
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidTagSendInternetEncoding
Description: Contains a bitmask of message encoding preferences for email sent to an email-enabled entity that is represented by this Address Book object.
Property ID: 0x3A71
Data type: PtypInteger32, 0x0003
Area: Address Properties

Canonical name: PidTagSendRichInfo
Description: Indicates whether the email-enabled entity represented by the Address Book object can receive all message content, including Rich Text Format (RTF) and other embedded objects.
Property ID: 0x3A40
Data type: PtypBoolean, 0x000B
Area: Address Properties

Canonical name: PidTagSensitivity
Description: Indicates the sender's assessment of the sensitivity of the Message object.
Property ID: 0x0036
Data type: PtypInteger32, 0x0003
Area: General Message Properties

Canonical name: PidTagSentMailSvrEID
Description: Contains an EntryID that represents the Sent Items folder for the message.
Property ID: 0x6740
Data type: PtypServerId, 0x00FB
Area: ProviderDefinedNonTransmittable

Canonical name: PidTagSentRepresentingAddressType
Description: Contains an email address type.
Property ID: 0x0064
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSentRepresentingEmailAddress
Description: Contains an email address for the end user who is represented by the sending mailbox owner.
Property ID: 0x0065
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSentRepresentingEntryId
Description: Contains the identifier of the end user who is represented by the sending mailbox owner.
Property ID: 0x0041
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagSentRepresentingFlags
Description:
Property ID: 0x401A
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties
Defining reference:

Canonical name: PidTagSentRepresentingName
Description: Contains the display name for the end user who is represented by the sending mailbox owner.
Property ID: 0x0042
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSentRepresentingSearchKey
Description: Contains a binary-comparable key that represents the end user who is represented by
the sending mailbox owner.
Property ID: 0x003B
Data type: PtypBinary, 0x0102
Area: Address Properties

Canonical name: PidTagSentRepresentingSmtpAddress
Description: Contains the SMTP email address of the end user who is represented by the sending mailbox owner.
Property ID: 0x5D02
Data type: PtypString, 0x001F
Area: Mail

Canonical name: PidTagSerializedReplidGuidMap
Description: Contains a serialized list of REPLID and REPLGUID pairs which represent all or part of the REPLID / REPLGUID mapping of the associated Logon object.
Property ID: 0x6638
Data type: PtypBinary, 0x0102
Area: Logon Properties

Canonical name: PidTagSmtpAddress
Description: Contains the SMTP address of the Message object.
Property ID: 0x39FE
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagSortLocaleId
Description: Contains the locale identifier.
Property ID: 0x6705
Data type: PtypInteger32, 0x0003
Area: ExchangeAdministrative

Canonical name: PidTagSourceKey
Description: Contains a value that contains an internal global identifier (GID) for this folder or
message.
Property ID: 0x65E0
Data type: PtypBinary, 0x0102
Area: Sync

Canonical name: PidTagSpokenName
Description: Contains a recording of the mail user's name pronunciation.
Property ID: 0x8CC2
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagSpouseName
Description: Contains the name of the mail user's spouse/partner.
Property ID: 0x3A48
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagStartDate
Description: Contains the value of the PidLidAppointmentStartWhole property (section 2.29).
Property ID: 0x0060
Data type: PtypTime, 0x0040
Area: MapiEnvelope

Canonical name: PidTagStartDateEtc
Description: Contains the default retention period, and the start date from which the age of a
Message object is calculated.
Property ID: 0x301B
Data type: PtypBinary, 0x0102
Area: Archive

Canonical name: PidTagStateOrProvince
Description: Contains the name of the mail user's state or province.
Property ID: 0x3A28
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagStoreEntryId
Description: Contains the unique EntryID of the message store where an object resides.
Property ID: 0x0FFB
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagStoreState
Description: Indicates whether a mailbox has any active Search folders.
Property ID: 0x340E
Data type: PtypInteger32, 0x0003
Area: MapiMessageStore

Canonical name: PidTagStoreSupportMask
Description: Indicates whether string properties within the .msg file are Unicode-encoded.
Property ID: 0x340D
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties

Canonical name: PidTagStreetAddress
Description: Contains the mail user's street address.
Property ID: 0x3A29
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagSubfolders
Description: Specifies whether a folder has subfolders.
Property ID: 0x360A
Data type: PtypBoolean, 0x000B
Area: MapiContainer

Canonical name: PidTagSubject
Description: Contains the subject of the email message.
Property ID: 0x0037
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagSubjectPrefix
Description: Contains the prefix for the subject of the message.
Property ID: 0x003D
Data type: PtypString, 0x001F
Area: General Message Properties

Canonical name: PidTagSupplementaryInfo
Description: Contains supplementary information about a delivery status notification, as specified in
Property ID: 0x0C1B
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagSurname
Description: Contains the mail user's family name.
Property ID: 0x3A11
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagSwappedToDoData
Description: Contains a secondary storage location for flags when sender flags or sender reminders are supported.
Property ID: 0x0E2D
Data type: PtypBinary, 0x0102
Area: MapiNonTransmittable

Canonical name: PidTagSwappedToDoStore
Description: Contains the value of the PidTagStoreEntryId property.
Property ID: 0x0E2C
Data type: PtypBinary, 0x0102
Area: MapiNonTransmittable

Canonical name: PidTagTargetEntryId
Description: Contains the message ID of a Message object being submitted for optimization ( section *******).
Property ID: 0x3010
Data type: PtypBinary, 0x0102
Area: ID Properties

Canonical name: PidTagTelecommunicationsDeviceForDeafTelephoneNumber
Description: Contains the mail user's telecommunication device for the deaf (TTY/TDD) telephone number.
Property ID: 0x3A4B
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagTelexNumber
Description: Contains the mail user's telex number. This property is returned from an NSPI server as a PtypMultipleBinary. Otherwise, the data type is PtypString.
Property ID: 0x3A2C
Data type: PtypString, 0x001F; PtypMultipleBinary, 0x1102
Area: MapiMailUser

Canonical name: PidTagTemplateData
Description: Describes the controls used in the template that is used to retrieve address book information.
Property ID: 0x0001
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagTemplateid
Description: Contains the value of the PidTagEntryId property (section 2.677), expressed as a Permanent Entry ID format.
Property ID: 0x3902
Data type: PtypBinary, 0x0102
Area: MapiAddressBook

Canonical name: PidTagTextAttachmentCharset
Description: Specifies the character set of an attachment received via MIME with the content-type of text.
Property ID: 0x371B
Data type: PtypString, 0x001F
Area: Message Attachment Properties

Canonical name: PidTagThumbnailPhoto
Description: Contains the mail user's photo in .jpg format.
Property ID: 0x8C9E
Data type: PtypBinary, 0x0102
Area: Address Book

Canonical name: PidTagTitle
Description: Contains the mail user's job title.
Property ID: 0x3A17
Data type: PtypString, 0x001F
Area: MapiMailUser

Canonical name: PidTagTnefCorrelationKey
Description: Contains a value that correlates a Transport Neutral Encapsulation Format (TNEF) attachment with a message.
Property ID: 0x007F
Data type: PtypBinary, 0x0102
Area: MapiEnvelope

Canonical name: PidTagToDoItemFlags
Description: Contains flags associated with objects.
Property ID: 0x0E2B
Data type: PtypInteger32, 0x0003
Area: MapiNonTransmittable

Canonical name: PidTagTransmittableDisplayName
Description: Contains an Address Book object's display name that is transmitted with the message.
Property ID: 0x3A20
Data type: PtypString, 0x001F
Area: Address Properties

Canonical name: PidTagTransportMessageHeaders
Description: Contains transport-specific message envelope information for email.
Property ID: 0x007D
Data type: PtypString, 0x001F
Area: Email

Canonical name: PidTagTrustSender
Description: Specifies whether the associated message was delivered through a trusted transport
channel.
Property ID: 0x0E79
Data type: PtypInteger32, 0x0003
Area: MapiNonTransmittable

Canonical name: PidTagUserCertificate
Description: Contains an ASN.1 authentication certificate for a messaging user.
Property ID: 0x3A22
Data type: PtypBinary, 0x0102
Area: MapiMailUser

Canonical name: PidTagUserEntryId
Description: Address book EntryID of the user logged on to the public folders.
Property ID: 0x6619
Data type: PtypBinary, 0x0102
Area: ExchangeMessageStore

Canonical name: PidTagUserX509Certificate
Description: Contains a list of certificates for the mail user.
Property ID: 0x3A70
Data type: PtypMultipleBinary, 0x1102
Area: MapiMailUser

Canonical name: PidTagViewDescriptorBinary
Description: Contains view definitions.
Property ID: 0x7001
Data type: PtypBinary, 0x0102
Area: MessageClassDefinedTransmittable

Canonical name: PidTagViewDescriptorName
Description:
Property ID: 0x7006
Data type: PtypString, 0x001F
Area: MessageClassDefinedTransmittable
Defining reference:

Canonical name: PidTagViewDescriptorStrings
Description: Contains view definitions in string format.
Property ID: 0x7002
Data type: PtypString, 0x001F
Area: MessageClassDefinedTransmittable

Canonical name: PidTagViewDescriptorVersion
Description: Contains the View Descriptor version.
Property ID: 0x7007
Data type: PtypInteger32, 0x0003
Area: Miscellaneous Properties
Defining reference:

Canonical name: PidTagVoiceMessageAttachmentOrder
Description: Contains a list of file names for the audio file attachments that are to be played as part of a message.
Property ID: 0x6805
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidTagVoiceMessageDuration
Description: Specifies the length of the attached audio message, in seconds.
Property ID: 0x6801
Data type: PtypInteger32, 0x0003
Area: Unified Messaging

Canonical name: PidTagVoiceMessageSenderName
Description: Specifies the name of the caller who left the attached voice message, as provided by the voice network's caller ID system.
Property ID: 0x6803
Data type: PtypString, 0x001F
Area: Unified Messaging

Canonical name: PidTagWeddingAnniversary
Description: Contains the date of the mail user's wedding anniversary.
Property ID: 0x3A41
Data type: PtypTime, 0x0040
Area: MapiMailUser

Canonical name: PidTagWlinkAddressBookEID
Description: Specifies the value of the PidTagEntryId property (section 2.677) of the user to whom the folder belongs.
Property ID: 0x6854
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkAddressBookStoreEID
Description: Specifies the value of the PidTagStoreEntryId property (section 2.1022) of the current user (not the owner of the folder).
Property ID: 0x6891
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkCalendarColor
Description: Specifies the background color of the calendar.
Property ID: 0x6853
Data type: PtypInteger32, 0x0003
Area: Configuration

Canonical name: PidTagWlinkClientID
Description: Specifies the Client ID that allows the client to determine whether the shortcut was created on the current machine/user via an equality test.
Property ID: 0x6890
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkEntryId
Description: Specifies the EntryID of the folder pointed to by the shortcut.
Property ID: 0x684C
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkFlags
Description: Specifies conditions associated with the shortcut.
Property ID: 0x684A
Data type: PtypInteger32, 0x0003
Area: Configuration

Canonical name: PidTagWlinkFolderType
Description: Specifies the type of folder pointed to by the shortcut.
Property ID: 0x684F
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkGroupClsid
Description: Specifies the value of the PidTagWlinkGroupHeaderID property of the group header associated with the shortcut.
Property ID: 0x6850
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkGroupHeaderID
Description: Specifies the ID of the navigation shortcut that groups other navigation shortcuts.
Property ID: 0x6842
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkGroupName
Description: Specifies the value of the PidTagNormalizedSubject (section 2.806) of the group header associated with the shortcut.
Property ID: 0x6851
Data type: PtypString, 0x001F
Area: Configuration

Canonical name: PidTagWlinkOrdinal
Description: Specifies a variable-length binary property to be used to sort shortcuts lexicographically.
Property ID: 0x684B
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkRecordKey
Description: Specifies the value of PidTagRecordKey property (section 2.904) of the folder pointed to by the shortcut.
Property ID: 0x684D
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkROGroupType
Description: Specifies the type of group header.
Property ID: 0x6892
Data type: PtypInteger32, 0x0003
Area: Configuration

Canonical name: PidTagWlinkSaveStamp
Description: Specifies an integer that allows a client to identify with a high probability whether the navigation shortcut was saved by the current client session.
Property ID: 0x6847
Data type: PtypInteger32, 0x0003
Area: Configuration

Canonical name: PidTagWlinkSection
Description: Specifies the section where the shortcut will be grouped.
Property ID: 0x6852
Data type: PtypInteger32, 0x0003
Area: Configuration

Canonical name: PidTagWlinkStoreEntryId
Description: Specifies the value of the PidTagStoreEntryId property (section 2.1022) of the folder pointed to by the shortcut.
Property ID: 0x684E
Data type: PtypBinary, 0x0102
Area: Configuration

Canonical name: PidTagWlinkType
Description: Specifies the type of navigation shortcut.
Property ID: 0x6849
Data type: PtypInteger32, 0x0003
Area: Configuration
