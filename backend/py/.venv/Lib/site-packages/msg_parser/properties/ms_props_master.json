{"properties_list": [{"name": "AddressBookProviderArrayType", "data_type": "0x0003", "area": "Contact Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAddressBookProviderArrayType", "property_long_id": "0x00008029", "description": "Specifies the state of the electronic addresses of the contact and represents a set of bit flags."}, {"name": "AddressBookProviderEmailList", "data_type": "0x1003", "area": "Contact Properties", "data_type_name": "PtypMultipleInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAddressBookProviderEmailList", "property_long_id": "0x00008028", "description": "Specifies which electronic address properties are set on the Contact object."}, {"name": "AddressCountryCode", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAddressCountryCode", "property_long_id": "0x000080DD", "description": "Specifies the country code portion of the mailing address of the contact."}, {"name": "AgingDontAgeMe", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAgingDontAgeMe", "property_long_id": "0x0000850E", "description": "Specifies whether to automatically archive the message."}, {"name": "AllAttendeesString", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAllAttendeesString", "property_long_id": "0x00008238", "description": "Specifies a list of all the attendees except for the organizer, including resources and"}, {"name": "AllowExternalCheck", "data_type": "0x000B", "area": "Conferencing", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAllowExternalCheck", "property_long_id": "0x00008246", "description": "This property is set to TRUE."}, {"name": "AnniversaryEventEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAnniversaryEventEntryId", "property_long_id": "0x0000804E", "description": "Specifies the EntryID of the Appointment object that represents an anniversary of"}, {"name": "AppointmentAuxiliaryFlags", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentAuxiliaryFlags", "property_long_id": "0x00008207", "description": "Specifies a bit field that describes the auxiliary state of the object."}, {"name": "AppointmentColor", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentColor", "property_long_id": "0x00008214", "description": "Specifies the color to be used when displaying the Calendar object."}, {"name": "AppointmentCounterProposal", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentCounterProposal", "property_long_id": "0x00008257", "description": "Indicates whether a Meeting Response object is a counter proposal."}, {"name": "AppointmentDuration", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentDuration", "property_long_id": "0x00008213", "description": "Specifies the length of the event, in minutes."}, {"name": "AppointmentEndDate", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentEndDate", "property_long_id": "0x00008211", "description": "Indicates the date that the appointment ends."}, {"name": "AppointmentEndTime", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentEndTime", "property_long_id": "0x00008210", "description": "Indicates the time that the appointment ends."}, {"name": "AppointmentEndWhole", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentEndWhole", "property_long_id": "0x0000820E", "description": "Specifies the end date and time for the event."}, {"name": "AppointmentLastSequence", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentLastSequence", "property_long_id": "0x00008203", "description": "Indicates to the organizer the last sequence number that was sent to any attendee."}, {"name": "AppointmentMessageClass", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentMessageClass", "property_long_id": "0x00000024", "description": "Indicates the message class of the Meeting object to be generated from the Meeting"}, {"name": "AppointmentNotAllowPropose", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentNotAllowPropose", "property_long_id": "0x0000825A", "description": "Indicates whether attendees are not allowed to propose a new date and/or time for the"}, {"name": "AppointmentProposalNumber", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentProposalNumber", "property_long_id": "0x00008259", "description": "Specifies the number of attendees who have sent counter proposals that have not"}, {"name": "AppointmentProposedDuration", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentProposedDuration", "property_long_id": "0x00008256", "description": "Indicates the proposed value for the PidLidAppointmentDuration property (section"}, {"name": "AppointmentProposedEndWhole", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentProposedEndWhole", "property_long_id": "0x00008251", "description": "Specifies the proposed value for the PidLidAppointmentEndWhole property (section"}, {"name": "AppointmentProposedStartWhole", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentProposedStartWhole", "property_long_id": "0x00008250", "description": "Specifies the proposed value for the PidLidAppointmentStartWhole property (section"}, {"name": "Appointment<PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "Calendar", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentRecur", "property_long_id": "0x00008216", "description": "Specifies the dates and times when a recurring series occurs."}, {"name": "Appointment<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentReplyName", "property_long_id": "0x00008230", "description": "Specifies the user who last replied to the meeting request or meeting update."}, {"name": "AppointmentReplyTime", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentReplyTime", "property_long_id": "0x00008220", "description": "Specifies the date and time at which the attendee responded to a received meeting"}, {"name": "AppointmentSequence", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentSequence", "property_long_id": "0x00008201", "description": "Specifies the sequence number of a Meeting object."}, {"name": "AppointmentSequenceTime", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentSequenceTime", "property_long_id": "0x00008202", "description": "Indicates the date and time at which the PidLidAppointmentSequence property"}, {"name": "AppointmentStartDate", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentStartDate", "property_long_id": "0x00008212", "description": "Identifies the date that the appointment starts."}, {"name": "AppointmentStartTime", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentStartTime", "property_long_id": "0x0000820F", "description": "Identifies the time that the appointment starts."}, {"name": "AppointmentStartWhole", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentStartWhole", "property_long_id": "0x0000820D", "description": "Specifies the start date and time of the appointment."}, {"name": "AppointmentStateFlags", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentStateFlags", "property_long_id": "0x00008217", "description": "Specifies a bit field that describes the state of the object."}, {"name": "AppointmentSubType", "data_type": "0x000B", "area": "Calendar", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentSubType", "property_long_id": "0x00008215", "description": "Specifies whether the event is an all-day event."}, {"name": "AppointmentTimeZoneDefinitionEndDisplay", "data_type": "0x0102", "area": "Calendar", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentTimeZoneDefinitionEndDisplay", "property_long_id": "0x0000825F", "description": "Specifies time zone information that indicates the time zone of the"}, {"name": "AppointmentTimeZoneDefinitionRecur", "data_type": "0x0102", "area": "Calendar", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentTimeZoneDefinitionRecur", "property_long_id": "0x00008260", "description": "Specifies time zone information that describes how to convert the meeting date and"}, {"name": "AppointmentTimeZoneDefinitionStartDisplay", "data_type": "0x0102", "area": "Calendar", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentTimeZoneDefinitionStartDisplay", "property_long_id": "0x0000825E", "description": "Specifies time zone information that indicates the time zone of the"}, {"name": "AppointmentUnsendableRecipients", "data_type": "0x0102", "area": "Meetings", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentUnsendableRecipients", "property_long_id": "0x0000825D", "description": "Contains a list of unsendable attendees."}, {"name": "AppointmentUpdateTime", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAppointmentUpdateTime", "property_long_id": "0x00008226", "description": "Indicates the time at which the appointment was last updated."}, {"name": "AttendeeCriticalChange", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidAttendeeCriticalChange", "property_long_id": "0x00000001", "description": "Specifies the date and time at which the meeting-related object was sent."}, {"name": "AutoFillLocation", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAutoFillLocation", "property_long_id": "0x0000823A", "description": "Indicates whether the value of the PidLidLocation property (section 2.159) is set to the PidTagDisplayName property (section 2.670)."}, {"name": "AutoLog", "data_type": "0x000B", "area": "Contact Properties", "data_type_name": "PtypBoolean", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAutoLog", "property_long_id": "0x00008025", "description": "Specifies to the application whether to create a Journal object for each action"}, {"name": "AutoProcessState", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAutoProcessState", "property_long_id": "0x0000851A", "description": "Specifies the options used in the automatic processing of email messages."}, {"name": "AutoStartCheck", "data_type": "0x000B", "area": "Conferencing", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidAutoStartCheck", "property_long_id": "0x00008244", "description": "Specifies whether to automatically start the conferencing application when a reminder"}, {"name": "Billing", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidBilling", "property_long_id": "0x00008535", "description": "Specifies billing information for the contact."}, {"name": "BirthdayEventEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidBirthdayEventEntryId", "property_long_id": "0x0000804D", "description": "Specifies the EntryID of an optional Appointment object that represents the birthday"}, {"name": "BirthdayLocal", "data_type": "0x0040", "area": "Contact Properties", "data_type_name": "PtypTime", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidBirthdayLocal", "property_long_id": "0x000080DE", "description": "Specifies the birthday of a contact."}, {"name": "BusinessCardCardPicture", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidBusinessCardCardPicture", "property_long_id": "0x00008041", "description": "Contains the image to be used on a business card."}, {"name": "BusinessCardDisplayDefinition", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidBusinessCardDisplayDefinition", "property_long_id": "0x00008040", "description": "Contains user customization details for displaying a contact as a business card."}, {"name": "BusyStatus", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidBusyStatus", "property_long_id": "0x00008205", "description": "Specifies the availability of a user for the event described by the object."}, {"name": "CalendarType", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidCalendarType", "property_long_id": "0x0000001C", "description": "Contains the value of the CalendarType field from the PidLidAppointmentRecur"}, {"name": "Categories", "data_type": "0x101F", "area": "Common", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCategories", "property_long_id": "0x00009000", "description": "Contains the array of text labels assigned to this Message object."}, {"name": "CcAttendeesString", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCcAttendeesString", "property_long_id": "0x0000823C", "description": "Contains a list of all the sendable attendees who are also optional attendees."}, {"name": "ChangeHighlight", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidChangeHighlight", "property_long_id": "0x00008204", "description": "Specifies a bit field that indicates how the Meeting object has changed."}, {"name": "Classification", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClassification", "property_long_id": "0x000085B6", "description": "Contains a list of the classification categories to which the associated Message object"}, {"name": "ClassificationDescription", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClassificationDescription", "property_long_id": "0x000085B7", "description": "Contains a human-readable summary of each of the classification categories included in"}, {"name": "ClassificationGuid", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClassificationGuid", "property_long_id": "0x000085B8", "description": "Contains the GUID that identifies the list of email classification categories used by a"}, {"name": "ClassificationKeep", "data_type": "0x000B", "area": "General Message Properties", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClassificationKeep", "property_long_id": "0x000085BA", "description": "Indicates whether the message uses any classification categories."}, {"name": "Classified", "data_type": "0x000B", "area": "General Message Properties", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClassified", "property_long_id": "0x000085B5", "description": "Indicates whether the contents of this message are regarded as classified information."}, {"name": "CleanGlobalObjectId", "data_type": "0x0102", "area": "Meetings", "data_type_name": "PtypBinary", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidCleanGlobalObjectId", "property_long_id": "0x00000023", "description": "Contains the value of the PidLidGlobalObjectId property (section 2.142) for an object"}, {"name": "ClientIntent", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_CalendarAssistant {11000E07-B51B-40D6-AF21-CAA85EDAB1D0}", "canonical_type": "PidLid", "canonical_name": "PidLidClientIntent", "property_long_id": "0x00000015", "description": "Indicates what actions the user has taken on this Meeting object."}, {"name": "ClipEnd", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClipEnd", "property_long_id": "0x00008236", "description": "Specifies the end date and time of the event in UTC."}, {"name": "ClipStart", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidClipStart", "property_long_id": "0x00008235", "description": "Specifies the start date and time of the event in UTC."}, {"name": "CollaborateDoc", "data_type": "0x001F", "area": "Conferencing", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCollaborateDoc", "property_long_id": "0x00008247", "description": "Specifies the document to be launched when the user joins the meeting."}, {"name": "CommonEnd", "data_type": "0x0040", "area": "General Message Properties", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCommonEnd", "property_long_id": "0x00008517", "description": "Indicates the end time for the Message object."}, {"name": "CommonStart", "data_type": "0x0040", "area": "General Message Properties", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCommonStart", "property_long_id": "0x00008516", "description": "Indicates the start time for the Message object."}, {"name": "Companies", "data_type": "0x101F", "area": "General Message Properties", "data_type_name": "PtypMultipleString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCompanies", "property_long_id": "0x00008539", "description": "Contains a list of company names, each of which is associated with a contact that is specified in the PidLidContacts property ( section ********.2)."}, {"name": "ConferencingCheck", "data_type": "0x000B", "area": "Conferencing", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConferencingCheck", "property_long_id": "0x00008240", "description": ""}, {"name": "ConferencingType", "data_type": "0x0003", "area": "Conferencing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConferencingType", "property_long_id": "0x00008241", "description": "Specifies the type of the meeting."}, {"name": "ContactCharacterSet", "data_type": "0x0003", "area": "Contact Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactCharacterSet", "property_long_id": "0x00008023", "description": "Specifies the character set used for a Contact object."}, {"name": "ContactItemData", "data_type": "0x1003", "area": "Contact Properties", "data_type_name": "PtypMultipleInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactItemData", "property_long_id": "0x00008007", "description": "Specifies the visible fields in the application's user interface that are used to help display"}, {"name": "ContactLinkedGlobalAddressListEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkedGlobalAddressListEntryId", "property_long_id": "0x000080E2", "description": "Specifies the EntryID of the GAL contact to which the duplicate contact is linked."}, {"name": "ContactLinkEntry", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkEntry", "property_long_id": "0x00008585", "description": "Contains the elements of the PidLidContacts property (section 2.77)."}, {"name": "ContactLinkGlobalAddressListLinkId", "data_type": "0x0048", "area": "Contact Properties", "data_type_name": "PtypGuid", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkGlobalAddressListLinkId", "property_long_id": "0x000080E8", "description": "Specifies the GUID of the GAL contact to which the duplicate contact is linked."}, {"name": "ContactLinkGlobalAddressListLinkState", "data_type": "0x0003", "area": "Contact Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkGlobalAddressListLinkState", "property_long_id": "0x000080E6", "description": "Specifies the state of the linking between the GAL contact and the duplicate contact."}, {"name": "ContactLinkLinkRejectHistory", "data_type": "0x1102", "area": "Contact Properties", "data_type_name": "PtypMultipleBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkLinkRejectHistory", "property_long_id": "0x000080E5", "description": "Contains a list of GAL contacts that were previously rejected for linking with the"}, {"name": "ContactLinkName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkName", "property_long_id": "0x00008586", "description": ""}, {"name": "ContactLinkSearchKey", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkSearchKey", "property_long_id": "0x00008584", "description": "Contains the list of SearchKeys for a Contact object linked to by the Message object."}, {"name": "ContactLinkSMTPAddressCache", "data_type": "0x101F", "area": "Contact Properties", "data_type_name": "PtypMultipleString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactLinkSMTPAddressCache", "property_long_id": "0x000080E3", "description": "Contains a list of the SMTP addresses that are used by the contact."}, {"name": "Contacts", "data_type": "0x101F", "area": "General Message Properties", "data_type_name": "PtypMultipleString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContacts", "property_long_id": "0x0000853A", "description": "Contains the PidTagDisplayName property (section 2.670) of each Address Book EntryID referenced in the value of the PidLidContactLinkEntry property (section 2.70)."}, {"name": "ContactUserField1", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactUserField1", "property_long_id": "0x0000804F", "description": "Contains text used to add custom text to a business card representation of a Contact object."}, {"name": "ContactUserField2", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactUserField2", "property_long_id": "0x00008050", "description": "Contains text used to add custom text to a business card representation of a Contact object."}, {"name": "ContactUserField3", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactUserField3", "property_long_id": "0x00008051", "description": "Contains text used to add custom text to a business card representation of a Contact object."}, {"name": "ContactUserField4", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidContactUserField4", "property_long_id": "0x00008052", "description": "Contains text used to add custom text to a business card representation of a Contact object."}, {"name": "ConversationActionLastAppliedTime", "data_type": "0x0040", "area": "Conversation Actions", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConversationActionLastAppliedTime", "property_long_id": "0x000085CA", "description": "Contains the time, in UTC, that an Email object was last received in the conversation, or the last time that the user modified the conversation action, whichever occurs later."}, {"name": "ConversationActionMaxDeliveryTime", "data_type": "0x0040", "area": "Conversation Actions", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConversationActionMaxDeliveryTime", "property_long_id": "0x000085C8", "description": "Contains the maximum value of the PidTagMessageDeliveryTime property (section2.783) of all of the Email objects modified in response to the last time that the user changed a conversation action on the client."}, {"name": "ConversationActionMoveFolderEid", "data_type": "0x0102", "area": "Conversation Actions", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConversationActionMoveFolderEid", "property_long_id": "0x000085C6", "description": "Contains the EntryID for the destination folder."}, {"name": "ConversationActionMoveStoreEid", "data_type": "0x0102", "area": "Conversation Actions", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConversationActionMoveStoreEid", "property_long_id": "0x000085C7", "description": "Contains the EntryID for a move to a folder in a different message store."}, {"name": "ConversationActionVersion", "data_type": "0x0003", "area": "Conversation Actions", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConversationActionVersion", "property_long_id": "0x000085CB", "description": "Contains the version of the conversation action FAI message."}, {"name": "ConversationProcessed", "data_type": "0x0003", "area": "Conversation Actions", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidConversationProcessed", "property_long_id": "0x000085C9", "description": "Specifies a sequential number to be used in the processing of a conversation action."}, {"name": "CurrentVersion", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCurrentVersion", "property_long_id": "0x00008552", "description": "Specifies the build number of the client application that sent the message."}, {"name": "CurrentVersionName", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidCurrentVersionName", "property_long_id": "0x00008554", "description": "Specifies the name of the client application that sent the message."}, {"name": "DayInterval", "data_type": "0x0002", "area": "Meetings", "data_type_name": "PtypInteger16", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidDayInterval", "property_long_id": "0x00000011", "description": "Identifies the day interval for the recurrence pattern."}, {"name": "DayOfMonth", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDayOfMonth", "property_long_id": "0x00001000", "description": "Identifies the day of the month for the appointment or meeting."}, {"name": "DelegateMail", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidDelegateMail", "property_long_id": "0x00000009", "description": "Indicates whether a delegate responded to the meeting request."}, {"name": "Department", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDepartment", "property_long_id": "0x00008010", "description": "This property is ignored by the server and is set to an empty string by the client."}, {"name": "Directory", "data_type": "0x001F", "area": "Conferencing", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDirectory", "property_long_id": "0x00008242", "description": "Specifies the directory server to be used."}, {"name": "DistributionListChecksum", "data_type": "0x0003", "area": "Contact Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDistributionListChecksum", "property_long_id": "0x0000804C", "description": "Specifies the 32-bit cyclic redundancy check (CRC) polynomial checksum, as specified in [ISO/IEC8802-3], calculated on the value of the PidLidDistributionListMembers"}, {"name": "DistributionListMembers", "data_type": "0x1102", "area": "Contact Properties", "data_type_name": "PtypMultipleBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDistributionListMembers", "property_long_id": "0x00008055", "description": "Specifies the list of EntryIDs of the objects corresponding to the members of the"}, {"name": "DistributionListName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDistributionListName", "property_long_id": "0x00008053", "description": "Specifies the name of the personal distribution list."}, {"name": "DistributionListOneOffMembers", "data_type": "0x1102", "area": "Contact Properties", "data_type_name": "PtypMultipleBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDistributionListOneOffMembers", "property_long_id": "0x00008054", "description": "Specifies the list of one-off EntryIDs corresponding to the members of the personal distribution list."}, {"name": "DistributionListStream", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidDistributionListStream", "property_long_id": "0x00008064", "description": "Specifies the list of EntryIDs and one-off EntryIDs corresponding to the members of"}, {"name": "Email1AddressType", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail1AddressType", "property_long_id": "0x00008082", "description": "Specifies the address type of an electronic address."}, {"name": "Email1DisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail1DisplayName", "property_long_id": "0x00008080", "description": "Specifies the user-readable display name for the email address."}, {"name": "Email1EmailAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail1EmailAddress", "property_long_id": "0x00008083", "description": "Specifies the email address of the contact."}, {"name": "Email1OriginalDisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail1OriginalDisplayName", "property_long_id": "0x00008084", "description": "Specifies the SMTP email address that corresponds to the email address for the Contact object."}, {"name": "Email1OriginalEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail1OriginalEntryId", "property_long_id": "0x00008085", "description": "Specifies the EntryID of the object corresponding to this electronic address."}, {"name": "Email2AddressType", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail2AddressType", "property_long_id": "0x00008092", "description": "Specifies the address type of the electronic address."}, {"name": "Email2DisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail2DisplayName", "property_long_id": "0x00008090", "description": "Specifies the user-readable display name for the email address."}, {"name": "Email2EmailAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail2EmailAddress", "property_long_id": "0x00008093", "description": "Specifies the email address of the contact."}, {"name": "Email2OriginalDisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail2OriginalDisplayName", "property_long_id": "0x00008094", "description": "Specifies the SMTP email address that corresponds to the email address for the Contact"}, {"name": "Email2OriginalEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail2OriginalEntryId", "property_long_id": "0x00008095", "description": "Specifies the EntryID of the object that corresponds to this electronic address."}, {"name": "Email3AddressType", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail3AddressType", "property_long_id": "0x000080A2", "description": "Specifies the address type of the electronic address."}, {"name": "Email3DisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail3DisplayName", "property_long_id": "0x000080A0", "description": "Specifies the user-readable display name for the email address."}, {"name": "Email3EmailAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail3EmailAddress", "property_long_id": "0x000080A3", "description": "Specifies the email address of the contact."}, {"name": "Email3OriginalDisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail3OriginalDisplayName", "property_long_id": "0x000080A4", "description": "Specifies the SMTP email address that corresponds to the email address for the Contact"}, {"name": "Email3OriginalEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidEmail3OriginalEntryId", "property_long_id": "0x000080A5", "description": "Specifies the EntryID of the object that corresponds to this electronic address."}, {"name": "EndRecurrenceDate", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidEndRecurrenceDate", "property_long_id": "0x0000000F", "description": "Identifies the end date of the recurrence range."}, {"name": "EndRecurrenceTime", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidEndRecurrenceTime", "property_long_id": "0x00000010", "description": "Identifies the end time of the recurrence range."}, {"name": "ExceptionReplaceTime", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidExceptionReplaceTime", "property_long_id": "0x00008228", "description": "Specifies the date and time, in UTC, within a recurrence pattern that an exception will"}, {"name": "Fax1AddressType", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax1AddressType", "property_long_id": "0x000080B2", "description": "Contains the string value \"FAX\"."}, {"name": "Fax1EmailAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax1EmailAddress", "property_long_id": "0x000080B3", "description": "Contains a user-readable display name, followed by the \"@\" character, followed by a"}, {"name": "Fax1OriginalDisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax1OriginalDisplayName", "property_long_id": "0x000080B4", "description": "Contains the same value as the PidTagNormalizedSubject property (section 2.806)."}, {"name": "Fax1OriginalEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax1OriginalEntryId", "property_long_id": "0x000080B5", "description": "Specifies a one-off EntryID that corresponds to this fax address."}, {"name": "Fax2AddressType", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax2AddressType", "property_long_id": "0x000080C2", "description": "Contains the string value \"FAX\"."}, {"name": "Fax2EmailAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax2EmailAddress", "property_long_id": "0x000080C3", "description": "Contains a user-readable display name, followed by the \"@\" character, followed by a"}, {"name": "Fax2OriginalDisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax2OriginalDisplayName", "property_long_id": "0x000080C4", "description": "Contains the same value as the PidTagNormalizedSubject property (section 2.806)."}, {"name": "Fax2OriginalEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax2OriginalEntryId", "property_long_id": "0x000080C5", "description": "Specifies a one-off EntryID corresponding to this fax address."}, {"name": "Fax3AddressType", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax3AddressType", "property_long_id": "0x000080D2", "description": "Contains the string value \"FAX\"."}, {"name": "Fax3EmailAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax3EmailAddress", "property_long_id": "0x000080D3", "description": "Contains a user-readable display name, followed by the \"@\" character, followed by a"}, {"name": "Fax3OriginalDisplayName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax3OriginalDisplayName", "property_long_id": "0x000080D4", "description": "Contains the same value as the PidTagNormalizedSubject property (section 2.806)."}, {"name": "Fax3OriginalEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFax3OriginalEntryId", "property_long_id": "0x000080D5", "description": "Specifies a one-off EntryID that corresponds to this fax address."}, {"name": "FExceptionalAttendees", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFExceptionalAttendees", "property_long_id": "0x0000822B", "description": "Indicates that the object is a Recurring Calendar object with one or more exceptions,"}, {"name": "FExceptionalBody", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFExceptionalBody", "property_long_id": "0x00008206", "description": "Indicates that the Exception Embedded Message object has a body that differs from"}, {"name": "FileUnder", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFileUnder", "property_long_id": "0x00008005", "description": "Specifies the name under which to file a contact when displaying a list of contacts."}, {"name": "FileUnderId", "data_type": "0x0003", "area": "Contact Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFileUnderId", "property_long_id": "0x00008006", "description": "Specifies how to generate and recompute the value of the PidLidFileUnder property"}, {"name": "FileUnderList", "data_type": "0x1003", "area": "Contact Properties", "data_type_name": "PtypMultipleInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFileUnderList", "property_long_id": "0x00008026", "description": "Specifies a list of possible values for the PidLidFileUnderId property (section 2.133)."}, {"name": "FInvited", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFInvited", "property_long_id": "0x00008229", "description": "Indicates whether invitations have been sent for the meeting that this Meeting object"}, {"name": "FlagRequest", "data_type": "0x001F", "area": "Flagging", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFlagRequest", "property_long_id": "0x00008530", "description": "Contains user-specifiable text to be associated with the flag."}, {"name": "FlagString", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFlagString", "property_long_id": "0x000085C0", "description": "Contains an index identifying one of a set of pre-defined text strings to be associated"}, {"name": "ForwardInstance", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidForwardInstance", "property_long_id": "0x0000820A", "description": "Indicates whether the Meeting Request object represents an exception to a"}, {"name": "ForwardNotificationRecipients", "data_type": "0x0102", "area": "Meetings", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidForwardNotificationRecipients", "property_long_id": "0x00008261", "description": "Contains a list of RecipientRow structures, as described in  section"}, {"name": "FOthersAppointment", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFOthersAppointment", "property_long_id": "0x0000822F", "description": "Indicates whether the Calendar folder from which the meeting was opened is another"}, {"name": "FreeBusyLocation", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidFreeBusyLocation", "property_long_id": "0x000080D8", "description": "Specifies a URL path from which a client can retrieve free/busy status information for the contact."}, {"name": "GlobalObjectId", "data_type": "0x0102", "area": "Meetings", "data_type_name": "PtypBinary", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidGlobalObjectId", "property_long_id": "0x00000003", "description": "Contains an ID for an object that represents an exception to a recurring series."}, {"name": "HasPicture", "data_type": "0x000B", "area": "Contact Properties", "data_type_name": "PtypBoolean", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidHasPicture", "property_long_id": "0x00008015", "description": "Specifies whether the attachment has a picture."}, {"name": "HomeAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidHomeAddress", "property_long_id": "0x0000801A", "description": "Specifies the complete address of the home address of the contact."}, {"name": "HomeAddressCountryCode", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidHomeAddressCountryCode", "property_long_id": "0x000080DA", "description": "Specifies the country code portion of the home address of the contact."}, {"name": "Html", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidHtml", "property_long_id": "0x0000802B", "description": "Specifies the business webpage URL of the contact."}, {"name": "ICalendarDayOfWeekMask", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidICalendarDayOfWeekMask", "property_long_id": "0x00001001", "description": "Identifies the day of the week for the appointment or meeting."}, {"name": "InboundICalStream", "data_type": "0x0102", "area": "Calendar", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidInboundICalStream", "property_long_id": "0x0000827A", "description": "Contains the contents of the iCalendar MIME part of the original MIME message."}, {"name": "InfoPathFormName", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidInfoPathFormName", "property_long_id": "0x000085B1", "description": "Contains the name of the form associated with this message."}, {"name": "InstantMessagingAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidInstantMessagingAddress", "property_long_id": "0x00008062", "description": "Specifies the instant messaging address of the contact."}, {"name": "IntendedBusyStatus", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidIntendedBusyStatus", "property_long_id": "0x00008224", "description": "Contains the value of the PidLidBusyStatus property (section 2.47) on the Meeting"}, {"name": "InternetAccountName", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidInternetAccountName", "property_long_id": "0x00008580", "description": "Specifies the user-visible email account name through which the email message is sent."}, {"name": "InternetAccountStamp", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidInternetAccountStamp", "property_long_id": "0x00008581", "description": "Specifies the email account ID through which the email message is sent."}, {"name": "IsContactLinked", "data_type": "0x000B", "area": "Contact Properties", "data_type_name": "PtypBoolean", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidIsContactLinked", "property_long_id": "0x000080E0", "description": "Specifies whether the contact is linked to other contacts."}, {"name": "IsException", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidIsException", "property_long_id": "0x0000000A", "description": "Indicates whether the object represents an exception (including an orphan instance)."}, {"name": "IsRecurring", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidIsRecurring", "property_long_id": "0x00000005", "description": "Specifies whether the object is associated with a recurring series."}, {"name": "IsSilent", "data_type": "0x000B", "area": "Meetings", "data_type_name": "PtypBoolean", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidIsSilent", "property_long_id": "0x00000004", "description": "Indicates whether the user did not include any text in the body of the Meeting"}, {"name": "LinkedTaskItems", "data_type": "0x1102", "area": "Tasks", "data_type_name": "PtypMultipleBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLinkedTaskItems", "property_long_id": "0x0000820C", "description": "Indicates whether the user did not include any text in the body of the Meeting"}, {"name": "Location", "data_type": "0x001F", "area": "Calendar", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLocation", "property_long_id": "0x00008208", "description": "Specifies the location of the event."}, {"name": "LogDocumentPosted", "data_type": "0x000B", "area": "Journal", "data_type_name": "PtypBoolean", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogDocumentPosted", "property_long_id": "0x00008711", "description": "Indicates whether the document was sent by email or posted to a server folder during"}, {"name": "LogDocumentPrinted", "data_type": "0x000B", "area": "Journal", "data_type_name": "PtypBoolean", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogDocumentPrinted", "property_long_id": "0x0000870E", "description": "Indicates whether the document was printed during journaling."}, {"name": "LogDocumentRouted", "data_type": "0x000B", "area": "Journal", "data_type_name": "PtypBoolean", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogDocumentRouted", "property_long_id": "0x00008710", "description": "Indicates whether the document was sent to a routing recipient during journaling."}, {"name": "LogDocumentSaved", "data_type": "0x000B", "area": "Journal", "data_type_name": "PtypBoolean", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogDocumentSaved", "property_long_id": "0x0000870F", "description": "Indicates whether the document was saved during journaling."}, {"name": "LogDuration", "data_type": "0x0003", "area": "Journal", "data_type_name": "PtypInteger32", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogDuration", "property_long_id": "0x00008707", "description": "Contains the duration, in minutes, of the activity."}, {"name": "LogEnd", "data_type": "0x0040", "area": "Journal", "data_type_name": "PtypTime", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogEnd", "property_long_id": "0x00008708", "description": "Contains the time, in UTC, at which the activity ended."}, {"name": "LogFlags", "data_type": "0x0003", "area": "Journal", "data_type_name": "PtypInteger32", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogFlags", "property_long_id": "0x0000870C", "description": "Contains metadata about the Journal object."}, {"name": "LogStart", "data_type": "0x0040", "area": "Journal", "data_type_name": "PtypTime", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogStart", "property_long_id": "0x00008706", "description": "Contains the time, in UTC, at which the activity began."}, {"name": "LogType", "data_type": "0x001F", "area": "Journal", "data_type_name": "PtypString", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogType", "property_long_id": "0x00008700", "description": "Briefly describes the journal activity that is being recorded."}, {"name": "LogTypeDesc", "data_type": "0x001F", "area": "Journal", "data_type_name": "PtypString", "property_set": "PSETID_Log {0006200A-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidLogTypeDesc", "property_long_id": "0x00008712", "description": "Contains an expanded description of the journal activity that is being recorded."}, {"name": "MeetingType", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidMeetingType", "property_long_id": "0x00000026", "description": "Indicates the type of Meeting Request object or Meeting Update object."}, {"name": "MeetingWorkspaceUrl", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidMeetingWorkspaceUrl", "property_long_id": "0x00008209", "description": "Specifies the URL of the Meeting Workspace that is associated with a Calendar"}, {"name": "MonthInterval", "data_type": "0x0002", "area": "Meetings", "data_type_name": "PtypInteger16", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidMonthInterval", "property_long_id": "0x00000013", "description": "Indicates the monthly interval of the appointment or meeting."}, {"name": "MonthOfYear", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidMonthOfYear", "property_long_id": "0x00001006", "description": "Indicates the month of the year in which the appointment or meeting occurs."}, {"name": "MonthOfYearMask", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidMonthOfYearMask", "property_long_id": "0x00000017", "description": "Indicates the calculated month of the year in which the appointment or meeting occurs."}, {"name": "NetShowUrl", "data_type": "0x001F", "area": "Conferencing", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNetShowUrl", "property_long_id": "0x00008248", "description": "Specifies the URL to be launched when the user joins the meeting."}, {"name": "NoEndDateFlag", "data_type": "0x000B", "area": "Calendar", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNoEndDateFlag", "property_long_id": "0x0000100B", "description": "Indicates whether the recurrence pattern has an end date."}, {"name": "NonSendableBcc", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNonSendableBcc", "property_long_id": "0x00008538", "description": "Contains a list of all of the unsendable attendees who are also resources."}, {"name": "NonSendableCc", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNonSendableCc", "property_long_id": "0x00008537", "description": "Contains a list of all of the unsendable attendees who are also optional attendees."}, {"name": "NonSendableTo", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNonSendableTo", "property_long_id": "0x00008536", "description": "Contains a list of all of the unsendable attendees who are also required attendees."}, {"name": "NonSendBccTrackStatus", "data_type": "0x1003", "area": "General Message Properties", "data_type_name": "PtypMultipleInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNonSendBccTrackStatus", "property_long_id": "0x00008545", "description": "Contains the value from the response table."}, {"name": "NonSendCcTrackStatus", "data_type": "0x1003", "area": "General Message Properties", "data_type_name": "PtypMultipleInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNonSendCcTrackStatus", "property_long_id": "0x00008544", "description": "Contains the value from the response table."}, {"name": "NonSendToTrackStatus", "data_type": "0x1003", "area": "General Message Properties", "data_type_name": "PtypMultipleInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNonSendToTrackStatus", "property_long_id": "0x00008543", "description": "Contains the value from the response table."}, {"name": "NoteColor", "data_type": "0x0003", "area": "Sticky Notes", "data_type_name": "PtypInteger32", "property_set": "PSETID_Note {0006200E-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNoteColor", "property_long_id": "0x00008B00", "description": "Specifies the suggested background color of the Note object."}, {"name": "NoteHeight", "data_type": "0x0003", "area": "Sticky Notes", "data_type_name": "PtypInteger32", "property_set": "PSETID_Note {0006200E-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNoteHeight", "property_long_id": "0x00008B03", "description": "Specifies the height of the visible message window in pixels."}, {"name": "Note<PERSON>idth", "data_type": "0x0003", "area": "Sticky Notes", "data_type_name": "PtypInteger32", "property_set": "PSETID_Note {0006200E-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNoteWidth", "property_long_id": "0x00008B02", "description": "Specifies the width of the visible message window in pixels."}, {"name": "NoteX", "data_type": "0x0003", "area": "Sticky Notes", "data_type_name": "PtypInteger32", "property_set": "PSETID_Note {0006200E-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNoteX", "property_long_id": "0x00008B04", "description": "Specifies the distance, in pixels, from the left edge of the screen that a user interface"}, {"name": "NoteY", "data_type": "0x0003", "area": "Sticky Notes", "data_type_name": "PtypInteger32", "property_set": "PSETID_Note {0006200E-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidNoteY", "property_long_id": "0x00008B05", "description": "Specifies the distance, in pixels, from the top edge of the screen that a user interface"}, {"name": "Occurrences", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOccurrences", "property_long_id": "0x00001005", "description": "Indicates the number of occurrences in the recurring appointment or meeting."}, {"name": "OldLocation", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidOldLocation", "property_long_id": "0x00000028", "description": "Indicates the original value of the PidLidLocation property (section 2.159) before a"}, {"name": "OldRecurrenceType", "data_type": "0x0002", "area": "Meetings", "data_type_name": "PtypInteger16", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidOldRecurrenceType", "property_long_id": "0x00000018", "description": "Indicates the recurrence pattern for the appointment or meeting."}, {"name": "OldWhenEndWhole", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidOldWhenEndWhole", "property_long_id": "0x0000002A", "description": "Indicates the original value of the PidLidAppointmentEndWhole property (section"}, {"name": "OldWhenStartWhole", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidOldWhenStartWhole", "property_long_id": "0x00000029", "description": "Indicates the original value of the PidLidAppointmentStartWhole property (section"}, {"name": "OnlinePassword", "data_type": "0x001F", "area": "Conferencing", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOnlinePassword", "property_long_id": "0x00008249", "description": "Specifies the password for a meeting on which the PidLidConferencingType property"}, {"name": "OptionalAttendees", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidOptionalAttendees", "property_long_id": "0x00000007", "description": "Specifies optional attendees."}, {"name": "OrganizerAlias", "data_type": "0x001F", "area": "Conferencing", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOrganizerAlias", "property_long_id": "0x00008243", "description": "Specifies the email address of the organizer."}, {"name": "OriginalStoreEntryId", "data_type": "0x0102", "area": "Meetings", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOriginalStoreEntryId", "property_long_id": "0x00008237", "description": "Specifies the EntryID of the delegator’s message store."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOtherAddress", "property_long_id": "0x0000801C", "description": "Specifies the complete address of the other address of the contact."}, {"name": "OtherAddressCountryCode", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOtherAddressCountryCode", "property_long_id": "0x000080DC", "description": "Specifies the country code portion of the other address of the contact."}, {"http": "//schemas.microsoft.com/mapi/owner_critical_change", "name": "OwnerCriticalChange", "data_type": "0x0040", "area": "Meetings", "data_type_name": "PtypTime", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidOwnerCriticalChange", "property_long_id": "0x0000001A", "description": "Specifies the date and time at which a Meeting Request object was sent by the"}, {"name": "OwnerName", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidOwnerName", "property_long_id": "0x0000822E", "description": "Indicates the name of the owner of the mailbox."}, {"name": "PendingStateForSiteMailboxDocument", "data_type": "0x0003", "area": "Site Mailbox", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPendingStateForSiteMailboxDocument", "property_long_id": "0x000085E0", "description": "Specifies the synchronization state of the Document object that is in the Document"}, {"name": "PercentComplete", "data_type": "0x0005", "area": "Tasks", "data_type_name": "PtypFloating64", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPercentComplete", "property_long_id": "0x00008102", "description": "Indicates whether a time-flagged Message object is complete."}, {"name": "PostalAddressId", "data_type": "0x0003", "area": "Contact Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostalAddressId", "property_long_id": "0x00008022", "description": "Specifies which physical address is the mailing address for this contact."}, {"name": "PostRssChannel", "data_type": "0x001F", "area": "RSS", "data_type_name": "PtypString", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssChannel", "property_long_id": "0x00008904", "description": "Contains the contents of the title field from the XML of the Atom feed or RSS channel."}, {"name": "PostRssChannelLink", "data_type": "0x001F", "area": "RSS", "data_type_name": "PtypString", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssChannelLink", "property_long_id": "0x00008900", "description": "Contains the URL of the RSS or Atom feed from which the XML file came."}, {"name": "PostRssItemGuid", "data_type": "0x001F", "area": "RSS", "data_type_name": "PtypString", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssItemGuid", "property_long_id": "0x00008903", "description": "Contains a unique identifier for the RSS object."}, {"name": "PostRssItemHash", "data_type": "0x0003", "area": "RSS", "data_type_name": "PtypInteger32", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssItemHash", "property_long_id": "0x00008902", "description": "Contains a hash of the feed XML computed by using an implementation-dependent"}, {"name": "PostRssItemLink", "data_type": "0x001F", "area": "RSS", "data_type_name": "PtypString", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssItemLink", "property_long_id": "0x00008901", "description": "Contains the URL of the link from an RSS or Atom item."}, {"name": "PostRssItemXml", "data_type": "0x001F", "area": "RSS", "data_type_name": "PtypString", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssItemXml", "property_long_id": "0x00008905", "description": "Contains the item element and all of its sub-elements from an RSS feed, or the entry"}, {"name": "PostRssSubscription", "data_type": "0x001F", "area": "RSS", "data_type_name": "PtypString", "property_set": "PSETID_PostRss {00062041-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPostRssSubscription", "property_long_id": "0x00008906", "description": "Contains the user's preferred name for the RSS or Atom subscription."}, {"name": "Private", "data_type": "0x000B", "area": "General Message Properties", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPrivate", "property_long_id": "0x00008506", "description": "Indicates whether the end user wishes for this Message object to be hidden from other"}, {"name": "PromptSendUpdate", "data_type": "0x000B", "area": "Meeting Response", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidPromptSendUpdate", "property_long_id": "0x00008045", "description": "Indicates that the Meeting Response object was out-of-date when it was received."}, {"name": "RecurrenceDuration", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidRecurrenceDuration", "property_long_id": "0x0000100D", "description": "Identifies the length, in minutes, of the appointment or meeting."}, {"name": "RecurrencePattern", "data_type": "0x001F", "area": "Calendar", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidRecurrencePattern", "property_long_id": "0x00008232", "description": "Specifies a description of the recurrence pattern of the Calendar object."}, {"name": "RecurrenceType", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidRecurrenceType", "property_long_id": "0x00008231", "description": "Specifies the recurrence type of the recurring series."}, {"name": "Recurring", "data_type": "0x000B", "area": "Calendar", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidRecurring", "property_long_id": "0x00008223", "description": "Specifies whether the object represents a recurring series."}, {"name": "ReferenceEntryId", "data_type": "0x0102", "area": "Contact Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReferenceEntryId", "property_long_id": "0x000085BD", "description": "Specifies the value of the EntryID of the Contact object unless the Contact object is a"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0003", "area": "Reminders", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderDelta", "property_long_id": "0x00008501", "description": "Specifies the interval, in minutes, between the time at which the reminder first"}, {"name": "ReminderFileParameter", "data_type": "0x001F", "area": "Reminders", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderFileParameter", "property_long_id": "0x0000851F", "description": "Specifies the filename of the sound that a client is to play when the reminder for that"}, {"name": "ReminderOverride", "data_type": "0x000B", "area": "Reminders", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderOverride", "property_long_id": "0x0000851C", "description": "Specifies whether the client is to respect the current values of the"}, {"name": "ReminderPlaySound", "data_type": "0x000B", "area": "Reminders", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderPlaySound", "property_long_id": "0x0000851E", "description": "Specifies whether the client is to play a sound when the reminder becomes overdue."}, {"name": "ReminderSet", "data_type": "0x000B", "area": "Reminders", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderSet", "property_long_id": "0x00008503", "description": "Specifies whether a reminder is set on the object."}, {"name": "ReminderSignalTime", "data_type": "0x0040", "area": "Reminders", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderSignalTime", "property_long_id": "0x00008560", "description": "Specifies the point in time when a reminder transitions from pending to overdue."}, {"name": "ReminderTime", "data_type": "0x0040", "area": "Reminders", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderTime", "property_long_id": "0x00008502", "description": "Specifies the initial signal time for objects that are not Calendar objects."}, {"name": "ReminderTimeDate", "data_type": "0x0040", "area": "Reminders", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderTimeDate", "property_long_id": "0x00008505", "description": "Indicates the time and date of the reminder for the appointment or meeting."}, {"name": "ReminderTimeTime", "data_type": "0x0040", "area": "Reminders", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderTimeTime", "property_long_id": "0x00008504", "description": "Indicates the time of the reminder for the appointment or meeting."}, {"name": "ReminderType", "data_type": "0x0003", "area": "Reminders", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidReminderType", "property_long_id": "0x0000851D", "description": "This property is not set and, if set, is ignored."}, {"name": "RemoteStatus", "data_type": "0x0003", "area": "Run-time configuration", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidRemoteStatus", "property_long_id": "0x00008511", "description": "Indicates the remote status of the calendar item."}, {"name": "RequiredAtten<PERSON>es", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidRequiredAttendees", "property_long_id": "0x00000006", "description": "Identifies required attendees for the appointment or meeting."}, {"name": "ResourceAttendees", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidResourceAttendees", "property_long_id": "0x00000008", "description": "Identifies resource attendees for the appointment or meeting."}, {"name": "ResponseStatus", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidResponseStatus", "property_long_id": "0x00008218", "description": "Specifies the response status of an attendee."}, {"name": "ServerProcessed", "data_type": "0x000B", "area": "Calendar", "data_type_name": "PtypBoolean", "property_set": "PSETID_CalendarAssistant {11000E07-B51B-40D6-AF21-CAA85EDAB1D0}", "canonical_type": "PidLid", "canonical_name": "PidLidServerProcessed", "property_long_id": "0x000085CC", "description": "Indicates whether the Meeting Request object or Meeting Update object has been processed."}, {"name": "ServerProcessingActions", "data_type": "0x0003", "area": "Calendar", "data_type_name": "PtypInteger32", "property_set": "PSETID_CalendarAssistant {11000E07-B51B-40D6-AF21-CAA85EDAB1D0}", "canonical_type": "PidLid", "canonical_name": "PidLidServerProcessingActions", "property_long_id": "0x000085CD", "description": "Indicates what processing actions have been taken on this Meeting Request object or Meeting Update object."}, {"name": "SharingAnonymity", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingAnonymity", "property_long_id": "0x00008A19", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingBindingEntryId", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingBindingEntryId", "property_long_id": "0x00008A2D", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingBrowseUrl", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingBrowseUrl", "property_long_id": "0x00008A51", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingCapabilities", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingCapabilities", "property_long_id": "0x00008A17", "description": "Indicates that the Message object relates to a special folder."}, {"name": "SharingConfigurationUrl", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingConfigurationUrl", "property_long_id": "0x00008A24", "description": "Contains a zero-length string."}, {"name": "SharingDataRangeEnd", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingDataRangeEnd", "property_long_id": "0x00008A45", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingDataRangeStart", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingDataRangeStart", "property_long_id": "0x00008A44", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingDetail", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingDetail", "property_long_id": "0x00008A2B", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingExtensionXml", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingExtensionXml", "property_long_id": "0x00008A21", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingF<PERSON>er", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingFilter", "property_long_id": "0x00008A13", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingFlags", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingFlags", "property_long_id": "0x00008A0A", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingFlavor", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingFlavor", "property_long_id": "0x00008A18", "description": "Indicates the type of Sharing Message object."}, {"name": "SharingFolderEntryId", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingFolderEntryId", "property_long_id": "0x00008A15", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingIndexEntryId", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingIndexEntryId", "property_long_id": "0x00008A2E", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingInitiatorEntryId", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingInitiatorEntryId", "property_long_id": "0x00008A09", "description": "Contains the value of the PidTagEntryId property (section 2.677) for the Address Book object of the currently logged-on user."}, {"name": "SharingInitiatorName", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingInitiatorName", "property_long_id": "0x00008A07", "description": "Contains the value of the PidTagDisplayName property (section 2.670) from the"}, {"name": "SharingInitiatorSmtp", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingInitiatorSmtp", "property_long_id": "0x00008A08", "description": "Contains the value of the PidTagSmtpAddress property (section 2.1014) from the"}, {"name": "SharingInstanceGuid", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingInstanceGuid", "property_long_id": "0x00008A1C", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLastAutoSyncTime", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLastAutoSyncTime", "property_long_id": "0x00008A55", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLastSyncTime", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLastSyncTime", "property_long_id": "0x00008A1F", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLocalComment", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalComment", "property_long_id": "0x00008A4D", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLocalLastModificationTime", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalLastModificationTime", "property_long_id": "0x00008A23", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLocalName", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalName", "property_long_id": "0x00008A0F", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLocalPath", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalPath", "property_long_id": "0x00008A0E", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLocalStoreUid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalStoreUid", "property_long_id": "0x00008A49", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingLocalType", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalType", "property_long_id": "0x00008A14", "description": "Contains the value of the PidTagContainerClass property (section 2.636) of the folder being shared."}, {"name": "SharingLocalUid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingLocalUid", "property_long_id": "0x00008A10", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingOriginalMessageEntryId", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingOriginalMessageEntryId", "property_long_id": "0x00008A29", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingParentBindingEntryId", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingParentBindingEntryId", "property_long_id": "0x00008A5C", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingParticipants", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingParticipants", "property_long_id": "0x00008A1E", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingPermissions", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingPermissions", "property_long_id": "0x00008A1B", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingProviderExtension", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingProviderExtension", "property_long_id": "0x00008A0B", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingProviderGuid", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingProviderGuid", "property_long_id": "0x00008A01", "description": "Contains the value \"%xAE.F0.***********.00.00.C0.***********.00.00.46\"."}, {"name": "SharingProviderName", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingProviderName", "property_long_id": "0x00008A02", "description": "Contains a user-displayable name of the sharing provider identified by the PidLidSharingProviderGuid property (section 2.266)."}, {"name": "SharingProviderUrl", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingProviderUrl", "property_long_id": "0x00008A03", "description": "Contains a URL related to the sharing provider identified by the PidLidSharingProviderGuid property (section 2.266)."}, {"name": "SharingRangeEnd", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRangeEnd", "property_long_id": "0x00008A47", "description": "Contains a value that is ignored by the server no matter what value is generated by the client."}, {"name": "SharingRangeStart", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRangeStart", "property_long_id": "0x00008A46", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingReciprocation", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingReciprocation", "property_long_id": "0x00008A1A", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteByteSize", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteByteSize", "property_long_id": "0x00008A4B", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteComment", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteComment", "property_long_id": "0x00008A2F", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteCrc", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteCrc", "property_long_id": "0x00008A4C", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteLastModificationTime", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteLastModificationTime", "property_long_id": "0x00008A22", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteMessageCount", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteMessageCount", "property_long_id": "0x00008A4F", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteName", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteName", "property_long_id": "0x00008A05", "description": "Contains the value of the PidTagDisplayName property (section 2.670) on the folder"}, {"name": "SharingRemotePass", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemotePass", "property_long_id": "0x00008A0D", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemotePath", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemotePath", "property_long_id": "0x00008A04", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteStoreUid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteStoreUid", "property_long_id": "0x00008A48", "description": "Contains a hexadecimal string representation of the value of the PidTagStoreEntryId"}, {"name": "SharingRemoteType", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteType", "property_long_id": "0x00008A1D", "description": "Contains the same value as the PidLidSharingLocalType property (section 2.259)."}, {"name": "SharingRemoteUid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSTID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteUid", "property_long_id": "0x00008A06", "description": "Contains the EntryID of the folder being shared."}, {"name": "SharingRemoteUser", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteUser", "property_long_id": "0x00008A0C", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingRemoteVersion", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRemoteVersion", "property_long_id": "0x00008A5B", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingResponseTime", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingResponseTime", "property_long_id": "0x00008A28", "description": "Contains the time at which the recipient of the sharing request sent a sharing"}, {"name": "SharingResponseType", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingResponseType", "property_long_id": "0x00008A27", "description": "Contains the type of response with which the recipient of the sharing request"}, {"name": "SharingRoamLog", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingRoamLog", "property_long_id": "0x00008A4E", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingStart", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingStart", "property_long_id": "0x00008A25", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingStatus", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingStatus", "property_long_id": "0x00008A00", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingStop", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingStop", "property_long_id": "0x00008A26", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingSyncFlags", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingSyncFlags", "property_long_id": "0x00008A60", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingSyncInterval", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingSyncInterval", "property_long_id": "0x00008A2A", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingTimeToLive", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingTimeToLive", "property_long_id": "0x00008A2C", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingTimeToLiveAuto", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingTimeToLiveAuto", "property_long_id": "0x00008A56", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingWorkingHoursDays", "data_type": "0x0003", "area": "Sharing", "data_type_name": "PtypInteger32", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingWorkingHoursDays", "property_long_id": "0x00008A42", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingWorkingHoursEnd", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingWorkingHoursEnd", "property_long_id": "0x00008A41", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingWorkingHoursStart", "data_type": "0x0040", "area": "Sharing", "data_type_name": "PtypTime", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingWorkingHoursStart", "property_long_id": "0x00008A40", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SharingWorkingHoursTimeZone", "data_type": "0x0102", "area": "Sharing", "data_type_name": "PtypBinary", "property_set": "PSETID_Sharing {00062040-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSharingWorkingHoursTimeZone", "property_long_id": "0x00008A43", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "SideEffects", "data_type": "0x0003", "area": "Run-time configuration", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSideEffects", "property_long_id": "0x00008510", "description": "Specifies how a Message object is handled by the client in relation to certain user"}, {"name": "SingleBodyICal", "data_type": "0x000B", "area": "Calendar", "data_type_name": "PtypBoolean", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSingleBodyICal", "property_long_id": "0x0000827B", "description": "Indicates that the original MIME message contained a single MIME part."}, {"name": "SmartNoAttach", "data_type": "0x000B", "area": "Run-time configuration", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSmartNoAttach", "property_long_id": "0x00008514", "description": "Indicates whether the Message object has no end-user visible attachments."}, {"name": "SpamOriginalFolder", "data_type": "0x0102", "area": "Spam", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidSpamOriginalFolder", "property_long_id": "0x0000859C", "description": "Specifies which folder a message was in before it was filtered into the Junk Email folder."}, {"name": "StartRecurrenceDate", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidStartRecurrenceDate", "property_long_id": "0x0000000D", "description": "Identifies the start date of the recurrence pattern."}, {"name": "StartRecurrenceTime", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidStartRecurrenceTime", "property_long_id": "0x0000000E", "description": "Identifies the start time of the recurrence pattern."}, {"name": "TaskAcceptanceState", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskAcceptanceState", "property_long_id": "0x0000812A", "description": "Indicates the acceptance state of the task."}, {"name": "TaskAccepted", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskAccepted", "property_long_id": "0x00008108", "description": "Indicates whether a task assignee has replied to a task request for this Task object."}, {"name": "TaskActualEffort", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskActualEffort", "property_long_id": "0x00008110", "description": "Indicates the number of minutes that the user actually spent working on a task."}, {"name": "TaskAssigner", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskAssigner", "property_long_id": "0x00008121", "description": "Specifies the name of the user that last assigned the task."}, {"name": "TaskAssigners", "data_type": "0x0102", "area": "Tasks", "data_type_name": "PtypBinary", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskAssigners", "property_long_id": "0x00008117", "description": "Contains a stack of entries, each of which represents a task assigner."}, {"name": "TaskComplete", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskComplete", "property_long_id": "0x0000811C", "description": "Indicates that the task is complete."}, {"name": "TaskCustomFlags", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskCustomFlags", "property_long_id": "0x00008139", "description": "The client can set this property, but it has no impact on the Task-Related Objects"}, {"name": "TaskDateCompleted", "data_type": "0x0040", "area": "Tasks", "data_type_name": "PtypTime", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskDateCompleted", "property_long_id": "0x0000810F", "description": "Specifies the date when the user completed work on the task."}, {"name": "TaskDeadOccurrence", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskDeadOccurrence", "property_long_id": "0x00008109", "description": "Indicates whether new occurrences remain to be generated."}, {"name": "TaskDueDate", "data_type": "0x0040", "area": "Tasks", "data_type_name": "PtypTime", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskDueDate", "property_long_id": "0x00008105", "description": "Specifies the date by which the user expects work on the task to be complete."}, {"name": "TaskEstimatedEffort", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskEstimatedEffort", "property_long_id": "0x00008111", "description": "Indicates the number of minutes that the user expects to work on a task."}, {"name": "TaskFCreator", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskFCreator", "property_long_id": "0x0000811E", "description": "Indicates that the Task object was originally created by the action of the current user"}, {"name": "TaskFFixOffline", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskFFixOffline", "property_long_id": "0x0000812C", "description": "Indicates the accuracy of the PidLidTaskOwner property (section 2.328)."}, {"name": "TaskFRecurring", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskFRecurring", "property_long_id": "0x00008126", "description": "Indicates whether the task includes a recurrence pattern."}, {"name": "TaskGlobalId", "data_type": "0x0102", "area": "Tasks", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskGlobalId", "property_long_id": "0x00008519", "description": "Contains a unique GUID for this task, which is used to locate an existing task upon"}, {"name": "TaskHistory", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskHistory", "property_long_id": "0x0000811A", "description": "Indicates the type of change that was last made to the Task object."}, {"name": "TaskLastDelegate", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskLastDelegate", "property_long_id": "0x00008125", "description": "Contains the name of the user who most recently assigned the task, or the user to"}, {"name": "TaskLastUpdate", "data_type": "0x0040", "area": "Tasks", "data_type_name": "PtypTime", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskLastUpdate", "property_long_id": "0x00008115", "description": "Contains the date and time of the most recent change made to the Task object."}, {"name": "TaskLastUser", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskLastUser", "property_long_id": "0x00008122", "description": "Contains the name of the most recent user to have been the owner of the task."}, {"name": "TaskMode", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskMode", "property_long_id": "0x00008518", "description": "Specifies the assignment status of the embedded Task object."}, {"name": "TaskMultipleRecipients", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskMultipleRecipients", "property_long_id": "0x00008120", "description": "Provides optimization hints about the recipients of a Task object."}, {"name": "TaskNoCompute", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskNoCompute", "property_long_id": "0x00008124", "description": "Not used. The client can set this property, but it has no impact on the Task-Related"}, {"name": "TaskOrdinal", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskOrdinal", "property_long_id": "0x00008123", "description": "Provides an aid to custom sorting of Task objects."}, {"name": "TaskOwner", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskOwner", "property_long_id": "0x0000811F", "description": "Contains the name of the owner of the task."}, {"name": "TaskOwnership", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskOwnership", "property_long_id": "0x00008129", "description": "Indicates the role of the current user relative to the Task object."}, {"name": "TaskRecurrence", "data_type": "0x0102", "area": "Tasks", "data_type_name": "PtypBinary", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskRecurrence", "property_long_id": "0x00008116", "description": "Contains a RecurrencePattern structure that provides information about recurring"}, {"name": "TaskResetReminder", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskResetReminder", "property_long_id": "0x00008107", "description": "Indicates whether future instances of recurring tasks need reminders, even though"}, {"name": "TaskRole", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskRole", "property_long_id": "0x00008127", "description": "Not used. The client can set this property, but it has no impact on the Task-Related"}, {"name": "TaskStartDate", "data_type": "0x0040", "area": "Tasks", "data_type_name": "PtypTime", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskStartDate", "property_long_id": "0x00008104", "description": "Specifies the date on which the user expects work on the task to begin."}, {"name": "TaskState", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskState", "property_long_id": "0x00008113", "description": "Indicates the current assignment state of the Task object."}, {"name": "TaskStatus", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskStatus", "property_long_id": "0x00008101", "description": "Specifies the status of a task."}, {"name": "TaskStatusOnComplete", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskStatusOnComplete", "property_long_id": "0x00008119", "description": "Indicates whether the task assignee has been requested to send an email message"}, {"name": "TaskUpdates", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskUpdates", "property_long_id": "0x0000811B", "description": "Indicates whether the task assignee has been requested to send a task update when the"}, {"name": "TaskVersion", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTaskVersion", "property_long_id": "0x00008112", "description": "Indicates which copy is the latest update of a Task object."}, {"name": "TeamTask", "data_type": "0x000B", "area": "Tasks", "data_type_name": "PtypBoolean", "property_set": "PSETID_Task {00062003-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTeamTask", "property_long_id": "0x00008103", "consuming_references": "", "description": "This property is set by the client but is ignored by the server."}, {"name": "TimeZone", "data_type": "0x0003", "area": "Meetings", "data_type_name": "PtypInteger32", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidTimeZone", "property_long_id": "0x0000000C", "description": "Specifies information about the time zone of a recurring meeting."}, {"name": "TimeZoneDescription", "data_type": "0x001F", "area": "Calendar", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTimeZoneDescription", "property_long_id": "0x00008234", "description": "Specifies a human-readable description of the time zone that is represented by the data"}, {"name": "TimeZoneStruct", "data_type": "0x0102", "area": "Calendar", "data_type_name": "PtypBinary", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidTimeZoneStruct", "property_long_id": "0x00008233", "description": "Specifies time zone information for a recurring meeting."}, {"name": "ToAttendeesString", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Appointment {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidToAttendeesString", "property_long_id": "0x0000823B", "description": "Contains a list of all of the sendable attendees who are also required attendees."}, {"name": "ToDoOrdinalDate", "data_type": "0x0040", "area": "Tasks", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidToDoOrdinalDate", "property_long_id": "0x000085A0", "description": "Contains the current time, in UTC, which is used to determine the sort order of objects"}, {"name": "ToDoSubOrdinal", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidToDoSubOrdinal", "property_long_id": "0x000085A1", "description": "Contains the numerals 0 through 9 that are used to break a tie when the"}, {"name": "ToDoTitle", "data_type": "0x001F", "area": "Tasks", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidToDoTitle", "property_long_id": "0x000085A4", "description": "Contains user-specifiable text to identify this Message object in a consolidated to-do"}, {"name": "UseTnef", "data_type": "0x000B", "area": "Run-time configuration", "data_type_name": "PtypBoolean", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidUseTnef", "property_long_id": "0x00008582", "description": "Specifies whether Transport Neutral Encapsulation Format (TNEF) is to be included"}, {"name": "ValidFlagStringProof", "data_type": "0x0040", "area": "Tasks", "data_type_name": "PtypTime", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidValidFlagStringProof", "property_long_id": "0x000085BF", "description": "Contains the value of the PidTagMessageDeliveryTime property (section 2.783)"}, {"name": "VerbResponse", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidVerbResponse", "property_long_id": "0x00008524", "description": "Specifies the voting option that a respondent has selected."}, {"name": "VerbStream", "data_type": "0x0102", "area": "Run-time configuration", "data_type_name": "PtypBinary", "property_set": "PSETID_Common {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidVerbStream", "property_long_id": "0x00008520", "description": "Specifies what voting responses the user can make in response to the message."}, {"name": "WeddingAnniversaryLocal", "data_type": "0x0040", "area": "Contact Properties", "data_type_name": "PtypTime", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWeddingAnniversaryLocal", "property_long_id": "0x000080DF", "description": "Specifies the wedding anniversary of the contact, at midnight in the client's local time"}, {"name": "WeekInterval", "data_type": "0x0002", "area": "Meetings", "data_type_name": "PtypInteger16", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidWeekInterval", "property_long_id": "0x00000012", "description": "Identifies the number of weeks that occur between each meeting."}, {"name": "Where", "data_type": "0x001F", "area": "Meetings", "data_type_name": "PtypString", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidWhere", "property_long_id": "0x00000002", "description": "Contains the value of the PidLidLocation property (section 2.159) from the associated"}, {"name": "WorkAddress", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddress", "property_long_id": "0x0000801B", "description": "Specifies the complete address of the work address of the contact."}, {"name": "WorkAddressCity", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressCity", "property_long_id": "0x00008046", "description": "Specifies the city or locality portion of the work address of the contact."}, {"name": "WorkAddressCountry", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressCountry", "property_long_id": "0x00008049", "description": "Specifies the country or region portion of the work address of the contact."}, {"name": "WorkAddressCountryCode", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressCountryCode", "property_long_id": "0x000080DB", "description": "Specifies the country code portion of the work address of the contact."}, {"name": "WorkAddressPostalCode", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressPostalCode", "property_long_id": "0x00008048", "description": "Specifies the postal code (ZIP code) portion of the work address of the contact."}, {"name": "WorkAddressPostOfficeBox", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressPostOfficeBox", "property_long_id": "0x0000804A", "description": "Specifies the post office box portion of the work address of the contact."}, {"name": "WorkAddressState", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressState", "property_long_id": "0x00008047", "description": "Specifies the state or province portion of the work address of the contact."}, {"name": "WorkAddressStreet", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidWorkAddressStreet", "property_long_id": "0x00008045", "description": "Specifies the street portion of the work address of the contact."}, {"name": "YearInterval", "data_type": "0x0002", "area": "Meetings", "data_type_name": "PtypInteger16", "property_set": "PSETID_Meeting {6ED8DA90-450B-101B-98DA-00AA003F1305}", "canonical_type": "PidLid", "canonical_name": "PidLidYearInterval", "property_long_id": "0x00000014", "description": "Indicates the yearly interval of the appointment or meeting."}, {"name": "YomiCompanyName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidYomiCompanyName", "property_long_id": "0x0000802E", "description": "Specifies the phonetic pronunciation of the company name of the contact."}, {"name": "YomiFirstName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidYomiFirstName", "property_long_id": "0x0000802C", "description": "Specifies the phonetic pronunciation of the given name of the contact."}, {"name": "YomiLastName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "property_set": "PSETID_Address {********-0000-0000-C000-************}", "canonical_type": "PidLid", "canonical_name": "PidLidYomiLastName", "property_long_id": "0x0000802D", "description": "Specifies the phonetic pronunciation of the surname of the contact."}, {"name": "AcceptLanguage", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameAcceptLanguage", "property_name": "Accept-Language", "description": "Contains the value of the MIME Accept-Language header."}, {"name": "ApplicationName", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameApplicationName", "property_name": "AppName", "description": "Specifies the application used to open the file attached to the Document object."}, {"name": "AttachmentMacContentType", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "property_set": "PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}", "canonical_type": "PidName", "canonical_name": "PidNameAttachmentMacContentType", "property_name": "AttachmentMacContentType", "description": "Contains the Content-Type of the Mac attachment."}, {"name": "AttachmentMacInfo", "data_type": "0x0102", "area": "Message Attachment Properties", "data_type_name": "PtypBinary", "property_set": "PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}", "canonical_type": "PidName", "canonical_name": "PidNameAttachmentMacInfo", "property_name": "AttachmentMacInfo", "description": "Contains the headers and resource fork data associated with the Mac attachment."}, {"name": "AttachmentOriginalPermissionType", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}", "canonical_type": "PidName", "canonical_name": "PidNameAttachmentOriginalPermissionType", "property_name": "AttachmentOriginalPermissionType", "description": "Contains the original permission type data associated with a web reference attachment."}, {"name": "AttachmentPermissionType", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "property_set": "PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}", "canonical_type": "PidName", "canonical_name": "PidNameAttachmentPermissionType", "property_name": "AttachmentPermissionType", "description": "Contains the permission type data associated with a web reference attachment."}, {"name": "AttachmentProviderType", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypeString", "property_set": "PSETID_Attachment {96357F7F-59E1-47D0-99A7-46515C183B54}", "canonical_type": "PidName", "canonical_name": "PidNameAttachmentProviderType", "property_name": "AttachmentProviderType", "description": "Contains the provider type data associated with a web reference attachment."}, {"name": "AudioNotes", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "property_set": "PSETID_UnifiedMessaging {4442858E-A9E3-4E80-B900-317A210CC15B}", "canonical_type": "PidName", "canonical_name": "PidNameAudioNotes", "property_name": "UMAudioNotes", "description": "Contains textual annotations to a voice message after it has been delivered to the user's mailbox."}, {"name": "Author", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameAuthor", "property_name": "Author", "description": "Specifies the author of the file attached to the Document object."}, {"name": "AutomaticSpeechRecognitionData", "data_type": "0x0102", "area": "Unified Messaging", "data_type_name": "PtypBinary", "property_set": "PSETID_UnifiedMessaging {4442858E-A9E3-4E80-B900-317A210CC15B}", "canonical_type": "PidName", "canonical_name": "PidNameAutomaticSpeechRecognitionData", "property_name": "AsrData", "description": "Contains an unprotected voice message."}, {"name": "ByteCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameByteCount", "property_name": "ByteCount", "description": "Specifies the size, in bytes, of the file attached to the Document object."}, {"name": "CalendarAttendeeRole", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarAttendeeRole", "property_name": "", "description": "Specifies the role of the attendee."}, {"name": "CalendarBusystatus", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarBusystatus", "property_name": "", "description": "Specifies whether the attendee is busy at the time of an appointment on their"}, {"name": "CalendarContact", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarContact", "property_name": "", "description": "Identifies the name of a contact who is an attendee of a meeting."}, {"name": "CalendarContactUrl", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarContactUrl", "property_name": "", "description": "Identifies the URL where you can access contact information in HTML format."}, {"name": "CalendarCreated", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarCreated", "property_name": "", "description": "Identifies the date and time, in UTC, when the organizer created the appointment or meeting."}, {"name": "CalendarDescriptionUrl", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarDescriptionUrl", "property_name": "", "description": "Specifies the URL of a resource that contains a description of an appointment or meeting."}, {"name": "CalendarDuration", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarDuration", "property_name": "", "description": "Identifies the duration, in seconds, of an appointment or meeting."}, {"name": "CalendarExceptionDate", "data_type": "0x1040", "area": "Common", "data_type_name": "PtypMultipleTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarExceptionDate", "property_name": "", "description": "Identifies a list of dates that are exceptions to a recurring appointment."}, {"name": "CalendarExceptionRule", "data_type": "0x101F", "area": "Common", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarExceptionRule", "property_name": "", "description": "Specifies an exception rule for a recurring appointment."}, {"name": "CalendarGeoLatitude", "data_type": "0x0005", "area": "Common", "data_type_name": "PtypFloating64", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarGeoLatitude", "property_name": "", "description": "Specifies the geographical latitude of the location of an appointment."}, {"name": "CalendarGeoLongitude", "data_type": "0x0005", "area": "Common", "data_type_name": "PtypFloating64", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarGeoLongitude", "property_name": "", "description": "Specifies the geographical longitude of the location of an appointment."}, {"name": "CalendarInstanceType", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarInstanceType", "property_name": "", "description": "Specifies the type of an appointment."}, {"name": "CalendarIsOrganizer", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarIsOrganizer", "property_name": "", "description": "Specifies whether an attendee is the organizer of an appointment or meeting."}, {"name": "CalendarLastModified", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarLastModified", "property_name": "", "description": "Specifies the date and time, in UTC, when an appointment was last modified."}, {"name": "CalendarLocationUrl", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarLocationUrl", "property_name": "", "description": "Specifies a URL with location information in HTML format."}, {"name": "CalendarMeetingStatus", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarMeetingStatus", "property_name": "", "description": "Specifies the status of an appointment or meeting."}, {"name": "CalendarMethod", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarMethod", "property_name": "", "description": "Specifies the iCalendar method that is associated with an Appointment object."}, {"name": "CalendarProductId", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarProductId", "property_name": "", "description": "Identifies the product that created the iCalendar-formatted stream."}, {"name": "CalendarRecurrenceIdRange", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarRecurrenceIdRange", "property_name": "", "description": "Specifies which instances of a recurring appointment are being referred to."}, {"name": "CalendarReminderOffset", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarReminderOffset", "property_name": "", "description": "Identifies the number of seconds before an appointment starts that a reminder is to be"}, {"name": "CalendarResources", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarResources", "property_name": "", "description": "Identifies a list of resources, such as rooms and video equipment, that are available for an appointment."}, {"name": "CalendarRsvp", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarRsvp", "property_name": "", "description": "Specifies whether the organizer of an appointment or meeting requested a response."}, {"name": "CalendarSequence", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarSequence", "property_name": "", "description": "Specifies the sequence number of a version of an appointment."}, {"name": "CalendarTimeZone", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarTimeZone", "property_name": "", "description": "Specifies the time zone of an appointment or meeting."}, {"name": "CalendarTimeZoneId", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarTimeZoneId", "property_name": "", "description": "Specifies the time zone identifier of an appointment or meeting."}, {"name": "CalendarTransparent", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarTransparent", "property_name": "", "description": "Specifies whether an appointment or meeting is visible to busy time searches."}, {"name": "CalendarUid", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarUid", "property_name": "", "description": "Specifies the unique identifier of an appointment or meeting."}, {"name": "CalendarVersion", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCalendarVersion", "property_name": "", "description": "Identifies the version of the iCalendar specification, as specified in"}, {"name": "Category", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCategory", "property_name": "Category", "description": "Specifies the category of the file attached to the Document object."}, {"name": "CharacterCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCharacterCount", "property_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Specifies the character count of the file attached to the Document object."}, {"name": "Comments", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameComments", "property_name": "Comments", "description": "Specifies the comments of the file attached to the Document object."}, {"name": "Company", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCompany", "property_name": "Company", "description": "Specifies the company for which the file was created."}, {"name": "ContentBase", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameContentBase", "property_name": "Content-Base", "description": "Specifies the value of the MIME Content-Base header, which defines the base URI for"}, {"name": "ContentClass", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameContentClass", "property_name": "Content-Class", "description": "Contains a string that identifies the type of content of a Message object."}, {"name": "ContentType", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameContentType", "property_name": "Content-Type", "description": "Specifies the type of the body part content."}, {"name": "CreateDateTimeReadOnly", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCreateDateTimeReadOnly", "property_name": "CreateDtmRo", "description": "Specifies the time, in UTC, that the file was first created."}, {"name": "CrossReference", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameCrossReference", "property_name": "<PERSON><PERSON><PERSON>", "description": "Contains the name of the host (with domains omitted) and a white-space-separated list"}, {"name": "DavId", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameDavId", "property_name": "", "description": "Specifies a unique ID for the calendar item."}, {"name": "DavIsCollection", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameDavIsCollection", "property_name": "", "description": "Indicates whether a Calendar object is a collection."}, {"name": "DavIsStructuredDocument", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameDavIsStructuredDocument", "property_name": "", "description": "Indicates whether a Calendar object is a structured document."}, {"name": "DavParentName", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameDavParentName", "property_name": "", "description": "Specifies the name of the Folder object that contains the Calendar object."}, {"name": "DavUid", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameDavUid", "property_name": "", "description": "Specifies the unique identifier for an item."}, {"name": "DocumentParts", "data_type": "0x101F", "area": "Common", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameDocumentParts", "property_name": "DocParts", "description": "Specifies the title of each part of the document."}, {"name": "EditTime", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameEditTime", "property_name": "EditTime", "description": "Specifies the time that the file was last edited."}, {"name": "ExchangeIntendedBusyStatus", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangeIntendedBusyStatus", "property_name": "", "description": "Specifies the intended free/busy status of a meeting in a Meeting request."}, {"name": "ExchangeJunkEmailMoveStamp", "data_type": "0x0003", "area": "Secure Messaging Properties", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangeJunkEmailMoveStamp", "property_name": "", "description": "Indicates that the message is not to be processed by a spam filter."}, {"name": "ExchangeModifyExceptionStructure", "data_type": "0x0102", "area": "Common", "data_type_name": "PtypBinary", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangeModifyExceptionStructure", "property_name": "", "description": "Specifies a structure that modifies an exception to the recurrence."}, {"name": "ExchangeNoModifyExceptions", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangeNoModifyExceptions", "property_name": "", "description": "Indicates whether exceptions to a recurring appointment can be modified."}, {"name": "ExchangePatternEnd", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangePatternEnd", "property_name": "", "description": "Identifies the maximum time when an instance of a recurring appointment ends."}, {"name": "ExchangePatternStart", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangePatternStart", "property_name": "", "description": "Identifies the absolute minimum time when an instance of a recurring appointment starts."}, {"name": "ExchangeReminderInterval", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchangeReminderInterval", "property_name": "", "description": "Identifies the time, in seconds, between reminders."}, {"name": "ExchDatabaseSchema", "data_type": "0x101F", "area": "Common", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchDatabaseSchema", "property_name": "", "description": "Specifies an array of URLs that identifies other folders within the same message store"}, {"name": "ExchDataExpectedContentClass", "data_type": "0x101F", "area": "Common", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchDataExpectedContentClass", "property_name": "", "description": "Specifies an array of names that indicates the expected content classes of items within a folder."}, {"name": "ExchDataSchemaCollectionReference", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameExchDataSchemaCollectionReference", "property_name": "", "description": "Specifies an array of names that indicates the expected content classes of items within a folder."}, {"name": "ExtractedAddresses", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedAddresses", "property_name": "XmlExtractedAddresses", "description": "Contains an XML document with a single AddressSet element."}, {"name": "ExtractedContacts", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedContacts", "property_name": "XmlExtractedContacts", "description": "Contains an XML document with a single ContactSet element."}, {"name": "ExtractedEmails", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedEmails", "property_name": "XmlExtractedEmails", "description": "Contains an XML document with a single EmailSet element."}, {"name": "ExtractedMeetings", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedMeetings", "property_name": "XmlExtractedMeetings", "description": "Contains an XML document with a single MeetingSet element."}, {"name": "ExtractedPhones", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedPhones", "property_name": "XmlExtractedPhones", "description": "Contains an XML document with a single PhoneSet element."}, {"name": "ExtractedTasks", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedTasks", "property_name": "XmlExtractedTasks", "description": "Contains an XML document with a single TaskSet element."}, {"name": "ExtractedUrls", "data_type": "0x001F", "area": "Extracted Entities", "data_type_name": "PtypString", "property_set": "PSETID_XmlExtractedEntities {23239608-685D-4732-9C55-4C95CB4E8E33}", "canonical_type": "PidName", "canonical_name": "PidNameExtractedUrls", "property_name": "XmlExtractedUrls", "description": "Contains an XML document with a single UrlSet element."}, {"name": "From", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameFrom", "property_name": "From", "description": "Specifies the SMTP email alias of the organizer of an appointment or meeting."}, {"name": "HeadingPairs", "data_type": "0x0102", "area": "Common", "data_type_name": "PtypBinary", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameHeadingPairs", "property_name": "HeadingPairs", "description": "Specifies which group of headings are indented in the document."}, {"name": "HiddenCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameHiddenCount", "property_name": "HiddenCount", "description": "Specifies the hidden value of the file attached to the Document object."}, {"name": "HttpmailCalendar", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameHttpmailCalendar", "property_name": "", "description": "Specifies the URL for the Calendar folder for a particular user."}, {"name": "HttpmailHtmlDescription", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameHttpmailHtmlDescription", "property_name": "", "description": "Specifies the HTML content of the message."}, {"name": "HttpmailSendMessage", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameHttpmailSendMessage", "property_name": "", "description": "Specifies the email submission URI to which outgoing email is submitted."}, {"name": "ICalendarRecurrenceDate", "data_type": "0x1040", "area": "Common", "data_type_name": "PtypMultipleTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameICalendarRecurrenceDate", "property_name": "", "description": "Identifies an array of instances of a recurring appointment."}, {"name": "ICalendarRecurrenceRule", "data_type": "0x101F", "area": "Common", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameICalendarRecurrenceRule", "property_name": "", "description": "Specifies the rule for the pattern that defines a recurring appointment."}, {"name": "InternetSubject", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameInternetSubject", "property_name": "Subject", "description": "Specifies the subject of the message."}, {"name": "Keywords", "data_type": "0x101F", "area": "General Message Properties", "data_type_name": "PtypMultipleString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameKeywords", "property_name": "Keywords", "description": "Contains keywords or categories for the Message object."}, {"name": "LastAuthor", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameLastAuthor", "property_name": "LastAuthor", "description": "Specifies the most recent author of the file attached to the Document object."}, {"name": "LastPrinted", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameLastPrinted", "property_name": "LastPrinted", "description": "Specifies the time, in UTC, that the file was last printed."}, {"name": "LastSaveDateTime", "data_type": "0x0040", "area": "Common", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameLastSaveDateTime", "property_name": "LastSaveDtm", "description": "Specifies the time, in UTC, that the file was last saved."}, {"name": "LineCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameLineCount", "property_name": "LineCount", "description": "Specifies the number of lines in the file attached to the Document object."}, {"name": "LinksDirty", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameLinksDirty", "property_name": "LinksDirty", "description": "Indicates whether the links in the document are up-to-date."}, {"name": "LocationUrl", "data_type": "0x001F", "area": "Calendar", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "defining_reference": "", "canonical_name": "PidNameLocationUrl", "property_name": "", "description": ""}, {"name": "Manager", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameManager", "property_name": "Manager", "description": "Specifies the manager of the file attached to the Document object."}, {"name": "MSIPLabels", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameMSIPLabels", "property_name": "msip_labels", "description": "Contains the string that specifies the CLP label information."}, {"name": "MultimediaClipCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameMultimediaClipCount", "property_name": "MMClipCount", "description": "Specifies the number of multimedia clips in the file attached to the Document object."}, {"name": "NoteCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameNoteCount", "property_name": "NoteCount", "description": "Specifies the number of notes in the file attached to the Document object."}, {"name": "OMSAccountGuid", "data_type": "0x001F", "area": "SMS", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameOMSAccountGuid", "property_name": "OMSAccountGuid", "description": "Contains the GUID of the SMS account used to deliver the message."}, {"name": "OMSMobileModel", "data_type": "0x001F", "area": "SMS", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameOMSMobileModel", "property_name": "OMSMobileModel", "description": "Indicates the model of the mobile device used to send the SMS or MMS message."}, {"name": "OMSScheduleTime", "data_type": "0x0040", "area": "SMS", "data_type_name": "PtypTime", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameOMSScheduleTime", "property_name": "OMSScheduleTime", "description": "Contains the time, in UTC, at which the client requested that the service provider send"}, {"name": "OMSServiceType", "data_type": "0x0003", "area": "SMS", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameOMSServiceType", "property_name": "OMSServiceType", "description": "Contains the type of service used to send an SMS or MMS message."}, {"name": "OMSSourceType", "data_type": "0x0003", "area": "SMS", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameOMSSourceType", "property_name": "OMSSourceType", "description": "Contains the source of an SMS or MMS message."}, {"name": "PageCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNamePageCount", "property_name": "PageCount", "description": "Specifies the page count of the file attached to the Document object."}, {"name": "ParagraphCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameParagraphCount", "property_name": "Pa<PERSON><PERSON><PERSON>nt", "description": "Specifies the number of paragraphs in the file attached to the Document object."}, {"name": "PhishingStamp", "data_type": "0x0003", "area": "Secure Messaging Properties", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNamePhishingStamp", "property_name": "", "description": "Indicates whether a message is likely to be phishing."}, {"name": "PresentationFormat", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNamePresentationFormat", "property_name": "PresFormat", "description": "Specifies the presentation format of the file attached to the Document object."}, {"name": "QuarantineOriginalSender", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameQuarantineOriginalSender", "property_name": "quarantine-original-sender", "description": "Specifies the original sender of a message."}, {"name": "RevisionNumber", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameRevisionNumber", "property_name": "RevNumber", "description": "Specifies the revision number of the file attached to the Document object."}, {"name": "RightsManagementLicense", "data_type": "0x1102", "area": "Secure Messaging Properties", "data_type_name": "PtypMultipleBinary", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameRightsManagementLicense", "property_name": "DRMLicense", "description": "Specifies the value used to cache the Use License for the rights-managed email"}, {"name": "Scale", "data_type": "0x000B", "area": "Common", "data_type_name": "PtypBoolean", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameScale", "property_name": "Scale", "description": "Indicates whether the image is to be scaled or cropped."}, {"name": "Security", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameSecurity", "property_name": "Security", "description": "Specifies the security level of the file attached to the Document object."}, {"name": "SlideCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameSlideCount", "property_name": "SlideCount", "description": "Specifies the number of slides in the file attached to the Document object."}, {"name": "Subject", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameSubject", "property_name": "Subject", "description": "Specifies the subject of the file attached to the Document object."}, {"name": "Template", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameTemplate", "property_name": "Template", "description": "Specifies the template of the file attached to the Document object."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "Common", "data_type_name": "PtypBinary", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameThumbnail", "property_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Specifies the data representing the thumbnail image of the document."}, {"name": "Title", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameTitle", "property_name": "Title", "description": "Specifies the title of the file attached to the Document object."}, {"name": "WordCount", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "property_set": "PS_PUBLIC_STRINGS {********-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameWordCount", "property_name": "WordCount", "description": "Specifies the word count of the file attached to the Document object."}, {"name": "XCallId", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXCallId", "property_name": "X-CallID", "description": "Contains a unique identifier associated with the phone call."}, {"name": "XFaxNumberOfPages", "data_type": "0x0002", "area": "Unified Messaging", "data_type_name": "PtypInteger16", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXFaxNumberOfPages", "property_name": "X-FaxNumberOfPages", "description": "Specifies how many discrete pages are contained within an attachment representing a"}, {"name": "XRequireProtectedPlayOnPhone", "data_type": "0x000B", "area": "Unified Messaging", "data_type_name": "PtypBoolean", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXRequireProtectedPlayOnPhone", "property_name": "X-RequireProtectedPlayOnPhone", "description": "Indicates that the client only renders the message on a phone."}, {"name": "XSenderTelephoneNumber", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSenderTelephoneNumber", "property_name": "X-CallingTelephoneNumber", "description": "Contains the telephone number of the caller associated with a voice mail message."}, {"name": "XSharingBrowseUrl", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingBrowseUrl", "property_name": "X-Sharing-Browse-Url", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "XSharingCapabilities", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingCapabilities", "property_name": "X-Sharing-Capabilities", "description": "Contains a string representation of the value of the PidLidSharingCapabilities"}, {"name": "XSharingConfigUrl", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingConfigUrl", "property_name": "X-Sharing-Config-Url", "description": "Contains the same value as the PidLidSharingConfigurationUrl property (section"}, {"name": "XSharingExendedCaps", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingExendedCaps", "property_name": "X-Sharing-Exended-Caps", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "XSharingFlavor", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingFlavor", "property_name": "X-Sharing-Flavor", "description": "Contains a hexadecimal string representation of the value of the PidLidSharingFlavor"}, {"name": "XSharingInstanceGuid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingInstanceGuid", "property_name": "X-Sharing-Instance-Guid", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "XSharingLocalType", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingLocalType", "property_name": "X-Sharing-Local-Type", "description": "Contains the same value as the PidLidSharingLocalType property (section 2.259)."}, {"name": "XSharingProviderGuid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingProviderGuid", "property_name": "X-Sharing-Provider-Guid", "description": "Contains the hexadecimal string representation of the value of the"}, {"name": "XSharingProviderName", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingProviderName", "property_name": "X-Sharing-Provider-Name", "description": "Contains the same value as the PidLidSharingProviderName property (section"}, {"name": "XSharingProviderUrl", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingProviderUrl", "property_name": "X-Sharing-Provider-Url", "description": "Contains the same value as the PidLidSharingProviderUrl property (section 2.268)."}, {"name": "XSharingRemoteName", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingRemoteName", "property_name": "X-Sharing-Remote-Name", "description": "Contains the same value as the PidLidSharingRemoteName property (section 2.277)."}, {"name": "XSharingRemotePath", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingRemotePath", "property_name": "X-Sharing-Remote-Path", "description": "Contains a value that is ignored by the server no matter what value is generated by the"}, {"name": "XSharingRemoteStoreUid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingRemoteStoreUid", "property_name": "X-Sharing-Remote-Store-Uid", "description": "Contains the same value as the PidLidSharingRemoteStoreUid property (section"}, {"name": "XSharingRemoteType", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingRemoteType", "property_name": "X-Sharing-Remote-Type", "description": "Contains the same value as the PidLidSharingRemoteType property (section 2.281)."}, {"name": "XSharingRemoteUid", "data_type": "0x001F", "area": "Sharing", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXSharingRemoteUid", "property_name": "X-Sharing-Remote-Uid", "description": "Contains the same value as the PidLidSharingRemoteUid property (section 2.282)."}, {"name": "XVoiceMessageAttachmentOrder", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXVoiceMessageAttachmentOrder", "property_name": "X-AttachmentOrder", "description": "Contains the list of names for the audio file attachments that are to be played as part of"}, {"name": "XVoiceMessageDuration", "data_type": "0x0002", "area": "Unified Messaging", "data_type_name": "PtypInteger16", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXVoiceMessageDuration", "property_name": "X-VoiceMessageDuration", "description": "Specifies the length of the attached audio message, in seconds."}, {"name": "XVoiceMessageSenderName", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "property_set": "PS_INTERNET_HEADERS {00020386-0000-0000-C000-************}", "canonical_type": "PidName", "canonical_name": "PidNameXVoiceMessageSenderName", "property_name": "X-VoiceMessageSenderName", "description": "Contains the name of the caller who left the attached voice message, as provided by the voice network's caller ID system."}, {"name": "Access", "data_type": "0x0003", "area": "Access Control Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAccess", "property_id": "0x0FF4", "description": "Indicates the operations available to the client for the object."}, {"name": "AccessControlListData", "data_type": "0x0102", "area": "Access Control Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAccessControlListData", "property_id": "0x3FE0", "description": "Contains a permissions list for a folder."}, {"name": "AccessLevel", "data_type": "0x0003", "area": "Access Control Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAccessLevel", "property_id": "0x0FF7", "description": "Indicates the client's access level to the object."}, {"name": "Account", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "Pid<PERSON>agAccount", "property_id": "0x3A00", "description": "Contains the alias of an Address Book object, which is an alternative name by which"}, {"name": "AdditionalRenEntryIds", "data_type": "0x1102", "area": "Outlook Application", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAdditionalRenEntryIds", "property_id": "0x36D8", "description": "Contains the indexed entry IDs for several special folders related to conflicts, sync"}, {"name": "AdditionalRenEntryIdsEx", "data_type": "0x0102", "area": "Outlook Application", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAdditionalRenEntryIdsEx", "property_id": "0x36D9", "description": "Contains an array of blocks that specify the EntryIDs of several special folders."}, {"name": "AddressBookAuthorizedSenders", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookAuthorizedSenders", "property_id": "0x8CD8", "description": "Indicates whether delivery restrictions exist for a recipient."}, {"name": "AddressBookContainerId", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookContainerId", "property_id": "0xFFFD", "description": "Contains the ID of a container on an NSPI server."}, {"name": "AddressBookDeliveryContentLength", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDeliveryContentLength", "property_id": "0x806A", "description": "Specifies the maximum size, in bytes, of a message that a recipient can receive."}, {"name": "AddressBookDisplayNamePrintable", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDisplayNamePrintable", "property_id": "0x39FF", "description": "Contains the printable string version of the display name."}, {"name": "AddressBookDisplayTypeExtended", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDisplayTypeExtended", "property_id": "0x8C93", "description": "Contains a value that indicates how to display an Address Book object in a table or as"}, {"name": "AddressBookDistributionListExternalMemberCount", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "defining_reference": "section ********", "canonical_name": "PidTagAddressBookDistributionListExternalMemberCount", "property_id": "0x8CE3", "description": "Contains the number of external recipients in the distribution list."}, {"name": "AddressBookDistributionListMemberCount", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDistributionListMemberCount", "property_id": "0x8CE2", "description": "Contains the total number of recipients in the distribution list."}, {"name": "AddressBookDistributionListMemberSubmitAccepted", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDistributionListMemberSubmitAccepted", "property_id": "0x8073", "description": "Indicates that delivery restrictions exist for a recipient."}, {"name": "AddressBookDistributionListMemberSubmitRejected", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDistributionListMemberSubmitRejected", "property_id": "0x8CDA", "description": "Indicates that delivery restrictions exist for a recipient."}, {"name": "AddressBookDistributionListRejectMessagesFromDLMembers", "data_type": "0x000D", "area": "Address book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookDistributionListRejectMessagesFromDLMembers", "property_id": "0x8CDB", "description": "Indicates that delivery restrictions exist for a recipient."}, {"name": "AddressBookEntryId", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookEntryId", "property_id": "0x663B", "description": "Contains the name-service EntryID of a directory object that refers to a public folder."}, {"name": "AddressBookExtensionAttribute1", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute1", "property_id": "0x802D", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute10", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute10", "property_id": "0x8036", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute11", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute11", "property_id": "0x8C57", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute12", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute12", "property_id": "0x8C58", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute13", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute13", "property_id": "0x8C59", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute14", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute14", "property_id": "0x8C60", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute15", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute15", "property_id": "0x8C61", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute2", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute2", "property_id": "0x802E", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute3", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute3", "property_id": "0x802F", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute4", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute4", "property_id": "0x8030", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute5", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute5", "property_id": "0x8031", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute6", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute6", "property_id": "0x8032", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute7", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute7", "property_id": "0x8033", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute8", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute8", "property_id": "0x8034", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookExtensionAttribute9", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookExtensionAttribute9", "property_id": "0x8035", "description": "Contains custom values defined and populated by the organization that modified the"}, {"name": "AddressBookFolderPathname", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookFolderPathname", "property_id": "0x8004", "description": "This property is deprecated and is to be ignored."}, {"name": "AddressBookHierarchicalChildDepartments", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHierarchicalChildDepartments", "property_id": "0x8C9A", "description": "Contains the child departments in a hierarchy of departments."}, {"name": "AddressBookHierarchicalDepartmentMembers", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHierarchicalDepartmentMembers", "property_id": "0x8C97", "description": "Contains all of the mail users that belong to this department."}, {"name": "AddressBookHierarchicalIsHierarchicalGroup", "data_type": "0x000B", "area": "Address Book", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHierarchicalIsHierarchicalGroup", "property_id": "0x8CDD", "description": "Indicates whether the distribution list represents a departmental group."}, {"name": "AddressBookHierarchicalParentDepartment", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHierarchicalParentDepartment", "property_id": "0x8C99", "description": "Contains all of the departments to which this department is a child."}, {"name": "AddressBookHierarchicalRootDepartment", "data_type": "0x001E", "area": "Address Book", "data_type_name": "PtypString8", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHierarchicalRootDepartment", "property_id": "0x8C98", "description": "Contains the distinguished name (DN) of either the root Department object or the"}, {"name": "AddressBookHierarchicalShowInDepartments", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHierarchicalShowInDepartments", "property_id": "0x8C94", "description": "Lists all Department objects of which the mail user is a member."}, {"name": "AddressBookHomeMessageDatabase", "data_type": "0x001EPtypEmbeddedTable, 0x000D", "area": "Address Book", "data_type_name": "PtypString8", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookHomeMessageDatabase", "property_id": "0x8006", "description": "Contains the DN expressed in the X500 DN format. This property is returned from a"}, {"name": "AddressBookIsMaster", "data_type": "0x000B", "area": "Address Book", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookIsMaster", "property_id": "0xFFFB", "description": "Contains a Boolean value of TRUE if it is possible to create Address Book objects in"}, {"name": "AddressBookIsMemberOfDistributionList", "data_type": "0x001E; PtypEmbeddedTable, 0x000D", "area": "Address Book", "data_type_name": "PtypString8", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookIsMemberOfDistributionList", "property_id": "0x8008", "description": "Lists all of the distribution lists for which the object is a member. This property is"}, {"name": "AddressBookManageDistributionList", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookManageDistributionList", "property_id": "0x6704", "description": "Contains information for use in display templates for distribution lists."}, {"name": "AddressBookManager", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookManager", "property_id": "0x8005", "description": "Contains one row that references the mail user's manager."}, {"name": "AddressBookManagerDistinguishedName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookManagerDistinguishedName", "property_id": "0x8005", "description": "Contains the DN of the mail user's manager."}, {"name": "AddressBookMember", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookMember", "property_id": "0x8009", "description": "Contains the members of the distribution list."}, {"name": "AddressBookMessageId", "data_type": "0x0014", "area": "ProviderDefinedNonTransmittable", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookMessageId", "property_id": "0x674F", "description": "Contains the Short-term Message ID (MID) ( section *******) of the first"}, {"name": "AddressBookModerationEnabled", "data_type": "0x000B", "area": "Address Book", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookModerationEnabled", "property_id": "0x8CB5", "description": "Indicates whether moderation is enabled for the mail user or distribution list."}, {"name": "AddressBookNetworkAddress", "data_type": "0x101F", "area": "Address Book", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookNetworkAddress", "property_id": "0x8170", "description": "Contains a list of names by which a server is known to the various transports in use by"}, {"name": "AddressBookObjectDistinguishedName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookObjectDistinguishedName", "property_id": "0x803C", "description": "Contains the DN of the Address Book object."}, {"name": "AddressBookObjectGuid", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookObjectGuid", "property_id": "0x8C6D", "description": "Contains a GUID that identifies an Address Book object."}, {"name": "AddressBookOrganizationalUnitRootDistinguishedName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookOrganizationalUnitRootDistinguishedName", "property_id": "0x8CA8", "description": "Contains the DN of the Organization object of the mail user's organization."}, {"name": "AddressBookOwner", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookOwner", "property_id": "0x800C", "description": "Contains one row that references the distribution list's owner."}, {"name": "AddressBookOwnerBackLink", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookOwnerBackLink", "property_id": "0x8024", "description": "Contains a list of the distribution lists owned by a mail user."}, {"name": "AddressBookParentEntryId", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookParentEntryId", "property_id": "0xFFFC", "description": "Contains the EntryID of the parent container in a hierarchy of address book"}, {"name": "AddressBookPhoneticCompanyName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookPhoneticCompanyName", "property_id": "0x8C91", "description": "Contains the phonetic representation of the PidTagCompanyName property (section"}, {"name": "AddressBookPhoneticDepartmentName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookPhoneticDepartmentName", "property_id": "0x8C90", "description": "Contains the phonetic representation of the PidTagDepartmentName property"}, {"name": "AddressBookPhoneticDisplayName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookPhoneticDisplayName", "property_id": "0x8C92", "description": "Contains the phonetic representation of the PidTagDisplayName property (section"}, {"name": "AddressBookPhoneticGivenName", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookPhoneticGivenName", "property_id": "0x8C8E", "description": "Contains the phonetic representation of the PidTagGivenName property (section"}, {"name": "AddressBookPhoneticSurname", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookPhoneticSurname", "property_id": "0x8C8F", "description": "Contains the phonetic representation of the PidTagSurname property (section 2.1030)."}, {"name": "AddressBookProxyAddresses", "data_type": "0x101F", "area": "Address Book", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookProxyAddresses", "property_id": "0x800F", "description": "Contains alternate email addresses for the Address Book object."}, {"name": "AddressBookPublicDelegates", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookPublicDelegates", "property_id": "0x8015", "description": "Contains a list of mail users who are allowed to send email on behalf of the mailbox"}, {"name": "AddressBookReports", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookReports", "property_id": "0x800E", "description": "Lists all of the mail user’s direct reports."}, {"name": "AddressBookRoomCapacity", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookRoomCapacity", "property_id": "0x0807", "description": "Contains the maximum occupancy of the room."}, {"name": "AddressBookRoomContainers", "data_type": "0x101F", "area": "Address Book", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookRoomContainers", "property_id": "0x8C96", "description": "Contains a list of DNs that represent the address book containers that hold"}, {"name": "AddressBookRoomDescription", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookRoomDescription", "property_id": "0x0809", "description": "Contains a description of the Resource object."}, {"name": "AddressBookSenderHintTranslations", "data_type": "0x101F", "area": "Address Book", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookSenderHintTranslations", "property_id": "0x8CAC", "description": "Contains the locale ID and translations of the default mail tip."}, {"name": "AddressBookSeniorityIndex", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookSeniorityIndex", "property_id": "0x8CA0", "description": "Contains a signed integer that specifies the seniority order of Address Book objects that represent members of a department and are referenced by a Department object or departmental group, with larger values specifying members that are more senior."}, {"name": "AddressBookTargetAddress", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookTargetAddress", "property_id": "0x8011", "description": "Contains the foreign system email address of an Address Book object."}, {"name": "AddressBookUnauthorizedSenders", "data_type": "0x000D", "area": "Address Book", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookUnauthorizedSenders", "property_id": "0x8CD9", "description": "Indicates whether delivery restrictions exist for a recipient."}, {"name": "AddressBookX509Certificate", "data_type": "0x1102", "area": "Address Book", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAddressBookX509Certificate", "property_id": "0x8C6A", "description": "Contains the ASN_1 DER encoded X.509 certificates for the mail user."}, {"name": "AddressType", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAddressType", "property_id": "0x3002", "description": "Contains the email address type of a Message object."}, {"name": "AlternateRecipientAllowed", "data_type": "0x000B", "area": "Address Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAlternateRecipientAllowed", "property_id": "0x0002", "description": "Specifies whether the sender permits the message to be auto-forwarded."}, {"name": "<PERSON><PERSON>", "data_type": "0x001F", "area": "Address Book", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAnr", "property_id": "0x360C", "description": "Contains a filter value used in ambiguous name resolution."}, {"name": "ArchiveDate", "data_type": "0x0040", "area": "Archive", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagArchiveDate", "property_id": "0x301F", "description": "Specifies the date, in UTC, after which a Message object is archived by the server."}, {"name": "ArchivePeriod", "data_type": "0x0003", "area": "Archive", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagArchivePeriod", "property_id": "0x301E", "description": "Specifies the number of days that a Message object can remain unarchived."}, {"name": "ArchiveTag", "data_type": "0x0102", "area": "Archive", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagArchiveTag", "property_id": "0x3018", "description": "Specifies the GUID of an archive tag."}, {"name": "Assistant", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAssistant", "property_id": "0x3A30", "description": "Contains the name of the mail user's administrative assistant."}, {"name": "AssistantTelephoneNumber", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAssistantTelephoneNumber", "property_id": "0x3A2E", "description": "Contains the telephone number of the mail user's administrative assistant."}, {"name": "Associated", "data_type": "0x000B", "area": "Sync", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAssociated", "property_id": "0x67AA", "description": "Specifies whether the message being synchronized is an FAI message."}, {"name": "AttachAdditionalInformation", "data_type": "0x0102", "area": "Message Attachment Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAttachAdditionalInformation", "property_id": "0x370F", "description": "Contains attachment encoding information."}, {"name": "AttachContentBase", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachContentBase", "property_id": "0x3711", "description": "Contains the base of a relative URI."}, {"name": "AttachContentId", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachContentId", "property_id": "0x3712", "description": "Contains a content identifier unique to the Message object that matches a corresponding cid URI schema reference in the HTML body of the Message object."}, {"name": "AttachContentLocation", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachContentLocation", "property_id": "0x3713", "description": "Contains a relative or full URI that matches a corresponding reference in the HTML"}, {"name": "AttachDataBinary", "data_type": "0x0102", "area": "Message Attachment Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAttachDataBinary", "property_id": "0x3701", "description": "Contains the contents of the file to be attached."}, {"name": "AttachDataObject", "data_type": "0x000D", "area": "Message Attachment Properties", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagAttachDataObject", "property_id": "0x3701", "description": "Contains the binary representation of the Attachment object in an application-specific"}, {"name": "AttachEncoding", "data_type": "0x0102", "area": "Message Attachment Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAttachEncoding", "property_id": "0x3702", "description": "Contains encoding information about the At<PERSON>chment object."}, {"name": "AttachExtension", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachExtension", "property_id": "0x3703", "description": "Contains a file name extension that indicates the document type of an attachment."}, {"name": "AttachFilename", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachFilename", "property_id": "0x3704", "description": "Contains the  of the PidTagAttachLongFilename property (section 2.589)."}, {"name": "AttachFlags", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAttachFlags", "property_id": "0x3714", "description": "Indicates which body formats might reference this attachment when rendering data."}, {"name": "AttachLongFilename", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachLongFilename", "property_id": "0x3707", "description": "Contains the full filename and extension of the Attachment object."}, {"name": "AttachLongPathname", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachLongPathname", "property_id": "0x370D", "description": "Contains the fully-qualified path and file name with extension."}, {"name": "AttachmentContactPhoto", "data_type": "0x000B", "area": "Message Attachment Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAttachmentContactPhoto", "property_id": "0x7FFF", "description": "Indicates that a contact photo attachment is attached to a Contact object."}, {"name": "AttachmentFlags", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAttachmentFlags", "property_id": "0x7FFD", "description": "Indicates special handling for an Attachment object."}, {"name": "AttachmentHidden", "data_type": "0x000B", "area": "Message Attachment Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAttachmentHidden", "property_id": "0x7FFE", "description": "Indicates whether an Attachment object is hidden from the end user."}, {"name": "AttachmentLinkId", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAttachmentLinkId", "property_id": "0x7FFA", "description": "Contains the type of Message object to which an attachment is linked."}, {"name": "AttachMethod", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAttachMethod", "property_id": "0x3705", "description": "Represents the way the contents of an attachment are accessed."}, {"name": "AttachMimeTag", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachMimeTag", "property_id": "0x370E", "description": "Contains a content-type MIME header."}, {"name": "AttachNumber", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAttachNumber", "property_id": "0x0E21", "description": "Identifies the Attachment object within its Message object."}, {"name": "AttachPathname", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachPathname", "property_id": "0x3708", "description": "Contains the  of the PidTagAttachLongPathname property (section 2.590)."}, {"name": "AttachPayloadClass", "data_type": "0x001F", "area": "Outlook Application", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachPayloadClass", "property_id": "0x371A", "description": "Contains the class name of an object that can display the contents of the message."}, {"name": "AttachPayloadProviderGuidString", "data_type": "0x001F", "area": "Outlook Application", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachPayloadProviderGuidString", "property_id": "0x3719", "description": "Contains the GUID of the software component that can display the contents of the"}, {"name": "AttachRendering", "data_type": "0x0102", "area": "Message Attachment Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAttachRendering", "property_id": "0x3709", "description": "Contains a Windows Metafile, as specified in  for the Attachment object."}, {"name": "AttachSize", "data_type": "0x0003", "area": "Message Attachment Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAttachSize", "property_id": "0x0E20", "description": "Contains the size, in bytes, consumed by the Attachment object on the server."}, {"name": "AttachTag", "data_type": "0x0102", "area": "Message Attachment Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagAttachTag", "property_id": "0x370A", "description": "Contains the identifier information for the application that supplied the Attachment"}, {"name": "AttachTransportName", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAttachTransportName", "property_id": "0x370C", "description": "Contains the name of an attachment file, modified so that it can be correlated with"}, {"name": "AttributeHidden", "data_type": "0x000B", "area": "Access Control Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAttributeHidden", "property_id": "0x10F4", "description": "Specifies the hide or show status of a folder."}, {"name": "AttributeReadOnly", "data_type": "0x000B", "area": "Access Control Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAttributeReadOnly", "property_id": "0x10F6", "description": "Indicates whether an item can be modified or deleted."}, {"name": "AutoForwardComment", "data_type": "0x001F", "area": "General Report Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagAutoForwardComment", "property_id": "0x0004", "description": "Contains text included in an automatically-generated message."}, {"name": "AutoForwarded", "data_type": "0x000B", "area": "General Report Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagAutoForwarded", "property_id": "0x0005", "description": "Indicates that a Message object has been automatically generated or automatically"}, {"name": "AutoResponseSuppress", "data_type": "0x0003", "area": "Email", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagAutoResponseSuppress", "property_id": "0x3FDF", "description": "Specifies whether a client or server application will forego sending automated replies in"}, {"name": "Birthday", "data_type": "0x0040", "area": "Contact Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagBirthday", "property_id": "0x3A42", "description": "Contains the date of the mail user's birthday at midnight."}, {"name": "BlockStatus", "data_type": "0x0003", "area": "Secure Messaging Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagBlockStatus", "property_id": "0x1096", "description": "Indicates the user's preference for viewing external content (such as links to images on"}, {"name": "Body", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBody", "property_id": "0x1000", "description": "Contains message body text in plain text format."}, {"name": "BodyContentId", "data_type": "0x001F", "area": "Exchange", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBodyContentId", "property_id": "0x1015", "description": "Contains a GUID that corresponds to the current message body."}, {"name": "BodyContentLocation", "data_type": "0x001F", "area": "MIME Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBodyContentLocation", "property_id": "0x1014", "description": "Contains a globally unique Uniform Resource Identifier (URI) that serves as a label"}, {"name": "BodyHtml", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBodyHtml", "property_id": "0x1013", "description": "Contains the HTML body of the Message object."}, {"name": "Business2TelephoneNumber", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBusiness2TelephoneNumber", "property_id": "0x3A1B", "description": "Contains a secondary telephone number at the mail user's place of business."}, {"name": "Business2TelephoneNumbers", "data_type": "0x101F", "area": "Contact Properties", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagBusiness2TelephoneNumbers", "property_id": "0x3A1B", "description": "Contains secondary telephone numbers at the mail user's place of business."}, {"name": "BusinessFaxNumber", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBusinessFaxNumber", "property_id": "0x3A24", "description": "Contains the telephone number of the mail user's business fax machine."}, {"name": "BusinessHomePage", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBusinessHomePage", "property_id": "0x3A51", "description": "Contains the URL of the mail user's business home page."}, {"name": "BusinessTelephoneNumber", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagBusinessTelephoneNumber", "property_id": "0x3A08", "description": "Contains the primary telephone number of the mail user's place of business."}, {"name": "CallbackTelephoneNumber", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCallbackTelephoneNumber", "property_id": "0x3A02", "description": "Contains a telephone number to reach the mail user."}, {"name": "CallId", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCallId", "property_id": "0x6806", "description": "Contains a unique identifier associated with the phone call."}, {"name": "CarTelephoneNumber", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCarTelephoneNumber", "property_id": "0x3A1E", "description": "Contains the mail user's car telephone number."}, {"name": "CdoRecurrenceid", "data_type": "0x0040", "area": "Exchange", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagCdoRecurrenceid", "property_id": "0x10C5", "description": "Identifies a specific instance of a recurring appointment."}, {"name": "<PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "History Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagChangeKey", "property_id": "0x65E2", "description": "Contains a structure that identifies the last change to the object."}, {"name": "ChangeNumber", "data_type": "0x0014", "area": "Sync", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagChangeNumber", "property_id": "0x67A4", "description": "Contains a structure that identifies the last change to the message or folder that is"}, {"name": "ChildrensNames", "data_type": "0x101F", "area": "Contact Properties", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagChildrensNames", "property_id": "0x3A58", "description": "Specifies the names of the children of the contact."}, {"name": "ClientActions", "data_type": "0x0102", "area": "Server-side Rules Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagClientActions", "property_id": "0x6645", "description": "Specifies the actions the client is required to take on the message."}, {"name": "ClientSubmitTime", "data_type": "0x0040", "area": "Message Time Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagClientSubmitTime", "property_id": "0x0039", "description": "Contains the current time, in UTC, when the email message is submitted."}, {"name": "CodePageId", "data_type": "0x0003", "area": "Exchange Profile Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagCodePageId", "property_id": "0x66C3", "description": "Contains the identifier for the client code page used for Unicode to double-byte"}, {"name": "Comment", "data_type": "0x001F", "area": "Common", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagComment", "property_id": "0x3004", "description": "Contains a comment about the purpose or content of the Address Book object."}, {"name": "CompanyMainTelephoneNumber", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCompanyMainTelephoneNumber", "property_id": "0x3A57", "description": "Contains the main telephone number of the mail user's company."}, {"name": "CompanyName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCompanyName", "property_id": "0x3A16", "description": "Contains the mail user's company name."}, {"name": "ComputerNetworkName", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagComputerNetworkName", "property_id": "0x3A49", "description": "Contains the name of the mail user's computer network."}, {"name": "ConflictEntryId", "data_type": "0x0102", "area": "ICS", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagConflictEntryId", "property_id": "0x3FF0", "description": "Contains the EntryID of the conflict resolve message."}, {"name": "ContainerClass", "data_type": "0x001F", "area": "Container Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagContainerClass", "property_id": "0x3613", "description": "Contains a string value that describes the type of Message object that a folder contains."}, {"name": "ContainerContents", "data_type": "0x000D", "area": "Container Properties", "data_type_name": "PtypEmbeddedTable", "canonical_type": "PidTag", "canonical_name": "PidTagContainerContents", "property_id": "0x360F", "description": "Empty. An NSPI server defines this value for distribution lists and it is not present for"}, {"name": "ContainerFlags", "data_type": "0x0003", "area": "Address Book", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagContainerFlags", "property_id": "0x3600", "description": "Contains a bitmask of flags that describe capabilities of an address book container."}, {"name": "ContainerHierarchy", "data_type": "0x000D", "area": "Container Properties", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagContainerHierarchy", "property_id": "0x360E", "description": "Identifies all of the subfolders of the current folder."}, {"name": "ContentCount", "data_type": "0x0003", "area": "Folder Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagContentCount", "property_id": "0x3602", "description": "Specifies the number of rows under the header row."}, {"name": "ContentFilterSpamConfidenceLevel", "data_type": "0x0003", "area": "Secure Messaging Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagContentFilterSpamConfidenceLevel", "property_id": "0x4076", "description": "Indicates a confidence level that the message is spam."}, {"name": "ContentUnreadCount", "data_type": "0x0003", "area": "Folder Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagContentUnreadCount", "property_id": "0x3603", "description": "Specifies the number of rows under the header row that have the PidTagRead property(section 2.872) set to FALSE."}, {"name": "ConversationId", "data_type": "0x0102", "area": "Conversations", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagConversationId", "property_id": "0x3013", "description": "Contains a computed value derived from other conversation-related properties."}, {"name": "ConversationIndex", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagConversationIndex", "property_id": "0x0071", "description": "Indicates the relative position of this message within a conversation thread."}, {"name": "ConversationIndexTracking", "data_type": "0x000B", "area": "Conversations", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagConversationIndexTracking", "property_id": "0x3016", "description": "Indicates whether the GUID portion of the PidTagConversationIndex property(section 2.644) is to be used to compute the PidTagConversationId property (section 2.643)."}, {"name": "ConversationTopic", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagConversationTopic", "property_id": "0x0070", "description": "Contains an unchanging copy of the original subject."}, {"name": "Country", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCountry", "property_id": "0x3A26", "description": "Contains the name of the mail user's country/region."}, {"name": "CreationTime", "data_type": "0x0040", "area": "Message Time Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagCreationTime", "property_id": "0x3007", "description": "Contains the time, in UTC, that the object was created."}, {"name": "CreatorEntryId", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagCreatorEntryId", "property_id": "0x3FF9", "description": "Specifies the original author of the message according to their Address Book EntryID."}, {"name": "CreatorName", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCreatorName", "property_id": "0x3FF8", "description": "Contains the name of the creator of a Message object."}, {"name": "CustomerId", "data_type": "0x001F", "area": "Contact Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagCustomerId", "property_id": "0x3A4A", "description": "Contains the mail user's customer identification number."}, {"name": "DamBackPatched", "data_type": "0x000B", "area": "Server-side Rules Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagDamBackPatched", "property_id": "0x6647", "description": "Indicates whether the Deferred Action Message (DAM) was updated by the server."}, {"name": "DamOriginalEntryId", "data_type": "0x0102", "area": "Server-side Rules Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagDamOriginalEntryId", "property_id": "0x6646", "description": "Contains the EntryID of the delivered message that the client has to process."}, {"name": "DefaultPostMessageClass", "data_type": "0x001F", "area": "MapiContainer", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDefaultPostMessageClass", "property_id": "0x36E5", "description": "Contains the message class of the object."}, {"name": "DeferredActionMessageOriginalEntryId", "data_type": "0x00FB", "area": "Server-side Rules Properties", "data_type_name": "PtypServerId", "canonical_type": "PidTag", "canonical_name": "PidTagDeferredActionMessageOriginalEntryId", "property_id": "0x6741", "description": "Contains the server EntryID for the DAM."}, {"name": "DeferredDeliveryTime", "data_type": "0x0040", "area": "MapiEnvelope", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagDeferredDeliveryTime", "property_id": "0x000F", "description": "Contains the date and time, in UTC, at which the sender prefers that the message be delivered."}, {"name": "DeferredSendNumber", "data_type": "0x0003", "area": "MapiStatus", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagDeferredSendNumber", "property_id": "0x3FEB", "description": "Contains a number used in the calculation of how long to defer sending a message."}, {"name": "DeferredSendTime", "data_type": "0x0040", "area": "MapiStatus", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagDeferredSendTime", "property_id": "0x3FEF", "description": "Contains the amount of time after which a client would like to defer sending the message."}, {"name": "DeferredSendUnits", "data_type": "0x0003", "area": "MapiStatus", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagDeferredSendUnits", "property_id": "0x3FEC", "description": "Specifies the unit of time used as a multiplier with the PidTagDeferredSendNumber"}, {"name": "DelegatedByRule", "data_type": "0x000B", "area": "MapiStatus", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagDelegatedByRule", "property_id": "0x3FE3", "description": "Specifies whether the message was forwarded due to the triggering of a delegate"}, {"name": "DelegateFlags", "data_type": "0x1003", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypMultipleInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagDelegateFlags", "property_id": "0x686B", "description": "Indicates whether delegates can view Message objects that are marked as private."}, {"name": "DeleteAfterSubmit", "data_type": "0x000B", "area": "MapiNonTransmittable", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagDeleteAfterSubmit", "property_id": "0x0E01", "description": "Indicates that the original message is to be deleted after it is sent."}, {"name": "DeletedCountTotal", "data_type": "0x0003", "area": "Server", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagDeletedCountTotal", "property_id": "0x670B", "description": "Contains the total count of messages that have been deleted from a folder, excluding"}, {"name": "DeletedOn", "data_type": "0x0040", "area": "ExchangeFolder", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagDeletedOn", "property_id": "0x668F", "description": "Specifies the time, in UTC, when the item or folder was soft deleted."}, {"name": "DeliverTime", "data_type": "0x0040", "area": "Email", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagDeliverTime", "property_id": "0x0010", "description": "Contains the delivery time for a delivery status notification, as specified  or a"}, {"name": "DepartmentName", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDepartmentName", "property_id": "0x3A18", "description": "Contains a name for the department in which the mail user works."}, {"name": "De<PERSON><PERSON>", "data_type": "0x0003", "area": "MapiCommon", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagDepth", "property_id": "0x3005", "description": "Specifies the number of nested categories in which a given row is contained."}, {"name": "DisplayBcc", "data_type": "0x001F", "area": "Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDisplayBcc", "property_id": "0x0E02", "description": "Contains a list of blind carbon copy (Bcc) recipient display names."}, {"name": "DisplayCc", "data_type": "0x001F", "area": "Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDisplayCc", "property_id": "0x0E03", "description": "Contains a list of carbon copy (Cc) recipient display names."}, {"name": "DisplayName", "data_type": "0x001F", "area": "MapiCommon", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDisplayName", "property_id": "0x3001", "description": "Contains the display name of the folder."}, {"name": "DisplayNamePrefix", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDisplayNamePrefix", "property_id": "0x3A45", "description": "Contains the mail user's honorific title."}, {"name": "DisplayTo", "data_type": "0x001F", "area": "Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagDisplayTo", "property_id": "0x0E04", "description": "Contains a list of the primary recipient display names, separated by semicolons,"}, {"name": "DisplayType", "data_type": "0x0003", "area": "MapiAddressBook", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "defining_reference": "section ********", "canonical_name": "PidTagDisplayType", "property_id": "0x3900", "description": "Contains an integer value that indicates how to display an Address Book object in a"}, {"name": "DisplayTypeEx", "data_type": "0x0003", "area": "MapiAddressBook", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagDisplayTypeEx", "property_id": "0x3905", "description": "Contains an integer value that indicates how to display an Address Book object in a"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x001F", "area": "MapiCommon", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagEmailAddress", "property_id": "0x3003", "description": "Contains the email address of a Message object."}, {"name": "EndDate", "data_type": "0x0040", "area": "MapiEnvelope Property set", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagEndDate", "property_id": "0x0061", "description": "Contains the value of the PidLidAppointmentEndWhole property (section 2.14)."}, {"name": "EntryId", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagEntryId", "property_id": "0x0FFF", "description": "Contains the information to identify many different types of messaging objects."}, {"name": "ExceptionEndTime", "data_type": "0x0040", "area": "MessageClassDefinedNonTransmittable", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagExceptionEndTime", "property_id": "0x7FFC", "description": "Contains the end date and time of the exception in the local time zone of the computer"}, {"name": "ExceptionReplaceTime", "data_type": "0x0040", "area": "MessageClassDefinedNonTransmittable", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagExceptionReplaceTime", "property_id": "0x7FF9", "description": "Indicates the original date and time, in UTC, at which the instance in the recurrence"}, {"name": "ExceptionStartTime", "data_type": "0x0040", "area": "MessageClassDefinedNonTransmittable", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagExceptionStartTime", "property_id": "0x7FFB", "description": "Contains the start date and time of the exception in the local time zone of the computer"}, {"name": "ExchangeNTSecurityDescriptor", "data_type": "0x0102", "area": "Calendar Document", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagExchangeNTSecurityDescriptor", "property_id": "0x0E84", "description": "Contains the calculated security descriptor for the item."}, {"name": "ExpiryNumber", "data_type": "0x0003", "area": "MapiStatus", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "defining_reference": "section *******", "canonical_name": "PidTagExpiryNumber", "property_id": "0x3FED", "description": "Contains an integer value that is used along with the PidTagExpiryUnits property(section 2.684) to define the expiry send time."}, {"name": "ExpiryTime", "data_type": "0x0040", "area": "MapiEnvelope", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagExpiryTime", "property_id": "0x0015", "description": "Contains the time, in UTC, after which a client wants to receive an expiry event if the message arrives late."}, {"name": "ExpiryUnits", "data_type": "0x0003", "area": "MapiStatus", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagExpiryUnits", "property_id": "0x3FEE", "description": "Contains the unit of time that the value of the PidTagExpiryNumber property (section"}, {"name": "ExtendedFolderFlags", "data_type": "0x0102", "area": "MapiContainer", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagExtendedFolderFlags", "property_id": "0x36DA", "description": "Contains encoded sub-properties for a folder."}, {"name": "ExtendedRuleMessageActions", "data_type": "0x0102", "area": "Rules", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagExtendedRuleMessageActions", "property_id": "0x0E99", "description": "Contains action information about named properties used in the rule."}, {"name": "ExtendedRuleMessageCondition", "data_type": "0x0102", "area": "Rules", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagExtendedRuleMessageCondition", "property_id": "0x0E9A", "description": "Contains condition information about named properties used in the rule."}, {"name": "ExtendedRuleSizeLimit", "data_type": "0x0003", "area": "Rules", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagExtendedRuleSizeLimit", "property_id": "0x0E9B", "description": "Contains the maximum size, in bytes, that the user is allowed to accumulate for a single extended rule."}, {"name": "FaxNumberOfPages", "data_type": "0x0003", "area": "Unified Messaging", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFaxNumberOfPages", "property_id": "0x6804", "description": "Contains the number of pages in a Fax object."}, {"name": "FlagCompleteTime", "data_type": "0x0040", "area": "Miscellaneous Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagFlagCompleteTime", "property_id": "0x1091", "description": "Specifies the date and time, in UTC, that the Message object was flagged as complete."}, {"name": "FlagStatus", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFlagStatus", "property_id": "0x1090", "description": "Specifies the flag state of the Message object."}, {"name": "FlatUrlName", "data_type": "0x001F", "area": "ExchangeAdministrative", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagFlatUrlName", "property_id": "0x670E", "description": "Contains a unique identifier for an item across the message store."}, {"name": "FolderAssociatedContents", "data_type": "0x000D", "area": "MapiContainer", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagFolderAssociatedContents", "property_id": "0x3610", "description": "Identifies all FAI messages in the current folder."}, {"name": "FolderId", "data_type": "0x0014", "area": "ID Properties", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagFolderId", "property_id": "0x6748", "description": "Contains the Folder ID (FID) ( section *******) of the folder."}, {"name": "FolderFlags", "data_type": "0x0003", "area": "ExchangeAdministrative", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFolderFlags", "property_id": "0x66A8", "description": "Contains a computed value to specify the type or state of a folder."}, {"name": "FolderType", "data_type": "0x0003", "area": "MapiContainer", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFolderType", "property_id": "0x3601", "description": "Specifies the type of a folder that includes the Root folder, Generic folder, and Search"}, {"name": "FollowupIcon", "data_type": "0x0003", "area": "RenMessageFolder", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFollowupIcon", "property_id": "0x1095", "description": "Specifies the flag color of the Message object."}, {"name": "FreeBusyCountMonths", "data_type": "0x0003", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFreeBusyCountMonths", "property_id": "0x6869", "description": "Contains an integer value used to calculate the start and end dates of the range of"}, {"name": "FreeBusyEntryIds", "data_type": "0x1102", "area": "MapiContainer", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagFreeBusyEntryIds", "property_id": "0x36E4", "description": "Contains EntryIDs of the Delegate Information object, the free/busy message of the"}, {"name": "FreeBusyMessageEmailAddress", "data_type": "0x001F", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagFreeBusyMessageEmailAddress", "property_id": "0x6849", "description": "Specifies the email address of the user or resource to whom this free/busy message"}, {"name": "FreeBusyPublishEnd", "data_type": "0x0003", "area": "Free/Busy Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFreeBusyPublishEnd", "property_id": "0x6848", "description": "Specifies the end time, in UTC, of the publishing range."}, {"name": "FreeBusyPublishStart", "data_type": "0x0003", "area": "Free/Busy Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagFreeBusyPublishStart", "property_id": "0x6847", "description": "Specifies the start time, in UTC, of the publishing range."}, {"name": "FreeBusyRangeTimestamp", "data_type": "0x0040", "area": "Free/Busy Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagFreeBusyRangeTimestamp", "property_id": "0x6868", "description": "Specifies the time, in UTC, that the data was published."}, {"name": "FtpSite", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagFtpSite", "property_id": "0x3A4C", "description": "Contains the File Transfer Protocol (FTP) site address of the mail user."}, {"name": "GatewayNeedsToRefresh", "data_type": "0x000B", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagGatewayNeedsToRefresh", "property_id": "0x6846", "description": "This property is deprecated and SHOULD NOT be used."}, {"name": "Gender", "data_type": "0x0002", "area": "MapiMailUser", "data_type_name": "PtypInteger16", "canonical_type": "PidTag", "canonical_name": "PidTagGender", "property_id": "0x3A4D", "description": "Contains a value that represents the mail user's gender."}, {"name": "Generation", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagGeneration", "property_id": "0x3A05", "description": "Contains a generational abbreviation that follows the full name of the mail user."}, {"name": "GivenName", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagGivenName", "property_id": "0x3A06", "description": "Contains the mail user's given name."}, {"name": "GovernmentIdNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagGovernmentIdNumber", "property_id": "0x3A07", "description": "Contains a government identifier for the mail user."}, {"name": "HasAttachments", "data_type": "0x000B", "area": "Message Attachment Properties Property set", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagHasAttachments", "property_id": "0x0E1B", "description": "Indicates whether the Message object contains at least one attachment."}, {"name": "HasDeferredActionMessages", "data_type": "0x000B", "area": "Rules", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagHasDeferredActionMessages", "property_id": "0x3FEA", "description": "Indicates whether a Message object has a deferred action message associated with it."}, {"name": "HasNamedProperties", "data_type": "0x000B", "area": "ExchangeMessageReadOnly", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagHasNamedProperties", "property_id": "0x664A", "description": "Indicates whether the Message object has a named property."}, {"name": "HasRules", "data_type": "0x000B", "area": "ExchangeFolder", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagHasRules", "property_id": "0x663A", "description": "Indicates whether a Folder object has rules."}, {"name": "HierarchyChangeNumber", "data_type": "0x0003", "area": "ExchangeFolder", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagHierarchyChangeNumber", "property_id": "0x663E", "description": "Contains a number that monotonically increases every time a subfolder is added to, or"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0040", "area": "TransportEnvelope", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagHierRev", "property_id": "0x4082", "description": "Specifies the time, in UTC, to trigger the client in cached mode to synchronize the folder"}, {"name": "Hobbies", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHobbies", "property_id": "0x3A43", "description": "Contains the names of the mail user's hobbies."}, {"name": "Home2TelephoneNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "defining_reference": "section ********", "canonical_name": "PidTagHome2TelephoneNumber", "property_id": "0x3A2F", "description": "Contains a secondary telephone number at the mail user's home."}, {"name": "Home2TelephoneNumbers", "data_type": "0x101F", "area": "MapiMailUser", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagHome2TelephoneNumbers", "property_id": "0x3A2F", "description": "Contains secondary telephone numbers at the mail user's home."}, {"name": "HomeAddressCity", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeAddressCity", "property_id": "0x3A59", "description": "Contains the name of the mail user's home locality, such as the town or city."}, {"name": "HomeAddressCountry", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeAddressCountry", "property_id": "0x3A5A", "description": "Contains the name of the mail user's home country/region."}, {"name": "HomeAddressPostalCode", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeAddressPostalCode", "property_id": "0x3A5B", "description": "Contains the postal code for the mail user's home postal address."}, {"name": "HomeAddressPostOfficeBox", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeAddressPostOfficeBox", "property_id": "0x3A5E", "description": "Contains the number or identifier of the mail user's home post office box."}, {"name": "HomeAddressStateOrProvince", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeAddressStateOrProvince", "property_id": "0x3A5C", "description": "Contains the name of the mail user's home state or province."}, {"name": "HomeAddressStreet", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeAddressStreet", "property_id": "0x3A5D", "description": "Contains the mail user's home street address."}, {"name": "HomeFaxNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeFaxNumber", "property_id": "0x3A25", "description": "Contains the telephone number of the mail user's home fax machine."}, {"name": "HomeTelephoneNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagHomeTelephoneNumber", "property_id": "0x3A09", "description": "Contains the primary telephone number of the mail user's home."}, {"name": "Html", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagHtml", "property_id": "0x1013", "description": "Contains message body text in HTML format."}, {"name": "ICalendarEndTime", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "canonical_type": "PidTag", "defining_reference": "section ********", "canonical_name": "PidTagICalendarEndTime", "property_id": "0x10C4", "description": "Contains the date and time, in UTC, when an appointment or meeting ends."}, {"name": "ICalendarReminderNextTime", "data_type": "0x0040", "area": "Calendar", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagICalendarReminderNextTime", "property_id": "0x10CA", "description": "Contains the date and time, in UTC, for the activation of the next reminder."}, {"name": "ICalendarStartTime", "data_type": "0x0040", "area": "Calendar Property set", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagICalendarStartTime", "property_id": "0x10C3", "description": "Contains the date and time, in UTC, when the appointment or meeting starts."}, {"name": "IconIndex", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagIconIndex", "property_id": "0x1080", "description": "Specifies which icon is to be used by a user interface when displaying a group of"}, {"name": "Importance", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagImportance", "property_id": "0x0017", "description": "Indicates the level of importance assigned by the end user to the Message object."}, {"name": "InConflict", "data_type": "0x000B", "area": "Conflict Note", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagInConflict", "property_id": "0x666C", "description": "Specifies whether the attachment represents an alternate replica."}, {"name": "InitialDetailsPane", "data_type": "0x0003", "area": "MAPI Display Tables", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagInitialDetailsPane", "property_id": "0x3F08", "description": "Indicates which page of a display template to display first."}, {"name": "Initials", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagInitials", "property_id": "0x3A0A", "description": "Contains the initials for parts of the full name of the mail user."}, {"name": "InReplyToId", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagInReplyToId", "property_id": "0x1042", "description": "Contains the value of the original message's PidTagInternetMessageId property (section 2.742) value."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "Table Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagInstanceKey", "property_id": "0x0FF6", "description": "Contains an object on an NSPI server."}, {"name": "InstanceNum", "data_type": "0x0003", "area": "ProviderDefinedNonTransmittable", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagInstanceNum", "property_id": "0x674E", "description": "Contains an identifier for a single instance of a row in the table."}, {"name": "InstID", "data_type": "0x0014", "area": "ProviderDefinedNonTransmittable", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagInstID", "property_id": "0x674D", "description": "Contains an identifier for all instances of a row in the table."}, {"name": "InternetCodepage", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagInternetCodepage", "property_id": "0x3FDE", "description": "Indicates the code page used for the PidTagBody property (section 2.612) or the"}, {"name": "InternetMailOverrideFormat", "data_type": "0x0003", "area": "MIME Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagInternetMailOverrideFormat", "property_id": "0x5902", "description": "Indicates the encoding method and HTML inclusion for attachments."}, {"name": "InternetMessageId", "data_type": "0x001F", "area": "MIME Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagInternetMessageId", "property_id": "0x1035", "description": "Corresponds to the message-id field."}, {"name": "InternetReferences", "data_type": "0x001F", "area": "MIME Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagInternetReferences", "property_id": "0x1039", "description": "Contains a list of message IDs that specify the messages to which this reply is related."}, {"name": "IpmAppointmentEntryId", "data_type": "0x0102", "area": "Folder Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagIpmAppointmentEntryId", "property_id": "0x36D0", "description": "Contains the EntryID of the Calendar folder."}, {"name": "IpmContactEntryId", "data_type": "0x0102", "area": "Folder Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagIpmContactEntryId", "property_id": "0x36D1", "description": "Contains the EntryID of the Contacts folder."}, {"name": "IpmDraftsEntryId", "data_type": "0x0102", "area": "Folder Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagIpmDraftsEntryId", "property_id": "0x36D7", "description": "Contains the EntryID of the Drafts folder."}, {"name": "IpmJournalEntryId", "data_type": "0x0102", "area": "Folder Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagIpmJournalEntryId", "property_id": "0x36D2", "description": "Contains the EntryID of the Journal folder."}, {"name": "IpmNoteEntryId", "data_type": "0x0102", "area": "Folder Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagIpmNoteEntryId", "property_id": "0x36D3", "description": "Contains the EntryID of the Notes folder."}, {"name": "IpmTaskEntryId", "data_type": "0x0102", "area": "Folder Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagIpmTaskEntryId", "property_id": "0x36D4", "description": "Contains the EntryID of the Tasks folder."}, {"name": "IsdnNumber", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagIsdnNumber", "property_id": "0x3A2D", "description": "Contains the Integrated Services Digital Network (ISDN) telephone number of the"}, {"name": "JunkAddRecipientsToSafeSendersList", "data_type": "0x0003", "area": "Spam", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagJunkAddRecipientsToSafeSendersList", "property_id": "0x6103", "description": "Indicates whether email recipients are to be added to the safe senders list."}, {"name": "JunkIncludeContacts", "data_type": "0x0003", "area": "Spam", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagJunkIncludeContacts", "property_id": "0x6100", "description": "Indicates whether email addresses of the contacts in the Contacts folder are treated in"}, {"name": "JunkPermanentlyDelete", "data_type": "0x0003", "area": "Spam", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagJunkPermanentlyDelete", "property_id": "0x6102", "description": "Indicates whether messages identified as spam can be permanently deleted."}, {"name": "JunkPhishingEnableLinks", "data_type": "0x000B", "area": "Spam", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagJunkPhishingEnableLinks", "property_id": "0x6107", "description": "Indicated whether the phishing stamp on a message is to be ignored."}, {"name": "JunkThreshold", "data_type": "0x0003", "area": "Spam", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagJunkThreshold", "property_id": "0x6101", "description": "Indicates how aggressively incoming email is to be sent to the Junk Email folder."}, {"name": "Keyword", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagKeyword", "property_id": "0x3A0B", "description": "Contains a keyword that identifies the mail user to the mail user's system"}, {"name": "Language", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagLanguage", "property_id": "0x3A0C", "description": "Contains a value that indicates the language in which the messaging user is writing"}, {"name": "LastModificationTime", "data_type": "0x0040", "area": "Message Time Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagLastModificationTime", "property_id": "0x3008", "description": "Contains the time, in UTC, of the last modification to the object."}, {"name": "LastModifierEntryId", "data_type": "0x0102", "area": "History Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagLastModifierEntryId", "property_id": "0x3FFB", "description": "Specifies the Address Book EntryID of the last user to modify the contents of the"}, {"name": "LastModifierName", "data_type": "0x001F", "area": "History Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagLastModifierName", "property_id": "0x3FFA", "description": "Contains the name of the last mail user to change the Message object."}, {"name": "LastVerbExecuted", "data_type": "0x0003", "area": "History Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagLastVerbExecuted", "property_id": "0x1081", "description": "Specifies the last verb executed for the message item to which it is related."}, {"name": "LastVerbExecutionTime", "data_type": "0x0040", "area": "History Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagLastVerbExecutionTime", "property_id": "0x1082", "description": "Contains the date and time, in UTC, during which the operation represented in the"}, {"name": "ListHelp", "data_type": "0x001F", "area": "Miscellaneous Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagListHelp", "property_id": "0x1043", "description": "Contains a URI that provides detailed help information for the mailing list from which an"}, {"name": "ListSubscribe", "data_type": "0x001F", "area": "Miscellaneous Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagListSubscribe", "property_id": "0x1044", "description": "Contains the URI that subscribes a recipient to a message’s associated mailing list."}, {"name": "ListUnsubscribe", "data_type": "0x001F", "area": "Miscellaneous Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagListUnsubscribe", "property_id": "0x1045", "description": "Contains the URI that unsubscribes a recipient from a message’s associated mailing"}, {"name": "LocalCommitTime", "data_type": "0x0040", "area": "Server", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagLocalCommitTime", "defining_references": "section ********;  section *******.1.13", "property_id": "0x6709", "description": "Specifies the time, in UTC, that a Message object or Folder object was last changed."}, {"name": "LocalCommitTimeMax", "data_type": "0x0040", "area": "Server", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagLocalCommitTimeMax", "property_id": "0x670A", "description": "Contains the time of the most recent message change within the folder container,"}, {"name": "LocaleId", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagLocaleId", "property_id": "0x66A1", "description": "Contains the Logon object LocaleID."}, {"name": "Locality", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagLocality", "property_id": "0x3A27", "description": "Contains the name of the mail user's locality, such as the town or city."}, {"name": "Location", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagLocation", "property_id": "0x3A0D", "description": "Contains the location of the mail user."}, {"name": "MailboxOwnerEntryId", "data_type": "0x0102", "area": "Message Store Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagMailboxOwnerEntryId", "property_id": "0x661B", "description": "Contains the EntryID in the Global Address List (GAL) of the owner of the mailbox."}, {"name": "MailboxOwnerName", "data_type": "0x001F", "area": "Message Store Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagMailboxOwnerName", "property_id": "0x661C", "description": "Contains the display name of the owner of the mailbox."}, {"name": "Manager<PERSON>ame", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagManagerName", "property_id": "0x3A4E", "description": "Contains the name of the mail user's manager."}, {"name": "MappingSignature", "data_type": "0x0102", "area": "Miscellaneous Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagMappingSignature", "property_id": "0x0FF8", "description": "A 16-byte constant that is present on all Address Book objects, but is not present on"}, {"name": "MaximumSubmitMessageSize", "data_type": "0x0003", "area": "Message Store Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMaximumSubmitMessageSize", "property_id": "0x666D", "description": "Maximum size, in kilobytes, of a message that a user is allowed to submit for"}, {"name": "MemberId", "data_type": "0x0014", "area": "Access Control Properties", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagMemberId", "property_id": "0x6671", "description": "Contains a unique identifier that the messaging server generates for each user."}, {"name": "MemberName", "data_type": "0x001F", "area": "Access Control Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagMemberName", "property_id": "0x6672", "description": "Contains the user-readable name of the user."}, {"name": "MemberRights", "data_type": "0x0003", "area": "Access Control Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMemberRights", "property_id": "0x6673", "description": "Contains the permissions for the specified user."}, {"name": "MessageAttachments", "data_type": "0x000D", "area": "Message Attachment Properties", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagMessageAttachments", "property_id": "0x0E13", "description": "Identifies all attachments to the current message."}, {"name": "MessageCcMe", "data_type": "0x000B", "area": "General Message Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "descripton": "Indicates that the receiving mailbox owner is a carbon copy (Cc) recipient of this", "canonical_name": "PidTagMessageCcMe", "property_id": "0x0058"}, {"name": "MessageClass", "data_type": "0x001F", "area": "Common Property set", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagMessageClass", "property_id": "0x001A", "description": "Denotes the specific type of the Message object."}, {"name": "MessageCodepage", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMessageCodepage", "property_id": "0x3FFD", "description": "Specifies the code page used to encode the non-Unicode string properties on this"}, {"name": "MessageDeliveryTime", "data_type": "0x0040", "area": "Message Time Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagMessageDeliveryTime", "property_id": "0x0E06", "description": "Specifies the time (in UTC) when the server received the message."}, {"name": "MessageEditorFormat", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMessageEditorFormat", "property_id": "0x5909", "description": "Specifies the format that an email editor can use for editing the message body."}, {"name": "MessageFlags", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMessageFlags", "property_id": "0x0E07", "description": "Specifies the status of the Message object."}, {"name": "MessageHandlingSystemCommonName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagMessageHandlingSystemCommonName", "property_id": "0x3A0F", "description": "Contains the common name of a messaging user for use in a message header."}, {"name": "MessageLocaleId", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMessageLocaleId", "property_id": "0x3FF1", "description": "Contains the Windows Locale ID of the end-user who created this message."}, {"name": "MessageRecipientMe", "data_type": "0x000B", "area": "General Message Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagMessageRecipientMe", "property_id": "0x0059", "description": "Indicates that the receiving mailbox owner is a primary or a carbon copy (Cc)"}, {"name": "MessageRecipients", "data_type": "0x000D", "area": "Address Properties", "data_type_name": "PtypObject", "canonical_type": "PidTag", "canonical_name": "PidTagMessageRecipients", "property_id": "0x0E12", "description": "Identifies all of the recipients of the current message."}, {"name": "MessageSize", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMessageSize", "property_id": "0x0E08", "description": "Contains the size, in bytes, consumed by the Message object on the server."}, {"name": "MessageSizeExtended", "data_type": "0x0014", "area": "General Message Properties", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagMessageSizeExtended", "property_id": "0x0E08", "description": "Specifies the 64-bit version of the PidTagMessageSize property (section 2.790)."}, {"name": "MessageStatus", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagMessageStatus", "property_id": "0x0E17", "description": "Specifies the status of a message in a contents table."}, {"name": "MessageSubmissionId", "data_type": "0x0102", "area": "Email", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagMessageSubmissionId", "property_id": "0x0047", "description": "Contains a message identifier assigned by a message transfer agent."}, {"name": "MessageToMe", "data_type": "0x000B", "area": "General Message Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagMessageToMe", "property_id": "0x0057", "description": "Indicates that the receiving mailbox owner is one of the primary recipients of this"}, {"name": "Mid", "data_type": "0x0014", "area": "ID Properties", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagMid", "property_id": "0x674A", "description": "Contains a value that contains the MID of the message currently being synchronized."}, {"name": "MiddleName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagMiddleName", "property_id": "0x3A44", "description": "Specifies the middle name(s) of the contact."}, {"name": "MimeSkeleton", "data_type": "0x0102", "area": "MIME properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagMimeSkeleton", "property_id": "0x64F0", "description": "Contains the top-level MIME message headers, all MIME message body part headers,"}, {"name": "MobileTelephoneNumber", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagMobileTelephoneNumber", "property_id": "0x3A1C", "description": "Contains the mail user's cellular telephone number."}, {"name": "NativeBody", "data_type": "0x0003", "area": "BestBody", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagNativeBody", "property_id": "0x1016", "description": "Indicates the best available format for storing the message body."}, {"name": "NextSendAcct", "data_type": "0x001F", "area": "Outlook Application", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagNextSendAcct", "property_id": "0x0E29", "description": "Specifies the server that a client is currently attempting to use to send email."}, {"name": "Nickname", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagNickname", "property_id": "0x3A4F", "description": "Contains the mail user's nickname."}, {"name": "NonDeliveryReportDiagCode", "data_type": "0x0003", "area": "Email", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagNonDeliveryReportDiagCode", "property_id": "0x0C05", "description": "Contains the diagnostic code for a delivery status notification, as specified in ."}, {"name": "NonDeliveryReportReasonCode", "data_type": "0x0003", "area": "Email", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagNonDeliveryReportReasonCode", "property_id": "0x0C04", "description": "Contains an integer value that indicates a reason for delivery failure."}, {"name": "NonReceiptNotificationRequested", "data_type": "0x000B", "area": "Email", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagNonReceiptNotificationRequested", "property_id": "0x0C06", "description": "Specifies whether the client sends a non-read receipt."}, {"name": "NormalizedSubject", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagNormalizedSubject", "property_id": "0x0E1D", "description": "Contains the normalized subject of the message."}, {"name": "ObjectType", "data_type": "0x0003", "area": "Common", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagObjectType", "property_id": "0x0FFE", "description": "Indicates the type of Server object."}, {"name": "OfficeLocation", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOfficeLocation", "property_id": "0x3A19", "description": "Contains the mail user's office location."}, {"name": "OfflineAddressBookContainerGuid", "data_type": "0x001E", "area": "Offline Address Book Properties", "data_type_name": "PtypString8", "canonical_type": "PidTag", "canonical_name": "PidTagOfflineAddressBookContainerGuid", "property_id": "0x6802", "description": "A string-formatted GUID that represents the address list container object."}, {"name": "OfflineAddressBookDistinguishedName", "data_type": "0x001E", "area": "Offline Address Book Properties", "data_type_name": "PtypString8", "canonical_type": "PidTag", "canonical_name": "PidTagOfflineAddressBookDistinguishedName", "property_id": "0x6804", "description": "Contains the DN of the address list that is contained in the OAB message."}, {"name": "OfflineAddressBookMessageClass", "data_type": "0x0003", "area": "Offline Address Book Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagOfflineAddressBookMessageClass", "property_id": "0x6803", "description": "Contains the message class for full OAB messages."}, {"name": "OfflineAddressBookName", "data_type": "0x001F", "area": "Offline Address Book Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOfflineAddressBookName", "property_id": "0x6800", "description": "Contains the display name of the address list."}, {"name": "OfflineAddressBookSequence", "data_type": "0x0003", "area": "Offline Address Book Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagOfflineAddressBookSequence", "property_id": "0x6801", "description": "Contains the sequence number of the OAB."}, {"name": "OfflineAddressBookTruncatedProperties", "data_type": "0x1003", "area": "Offline Address Book Properties", "data_type_name": "PtypMultipleInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagOfflineAddressBookTruncatedProperties", "property_id": "0x6805", "description": "Contains a list of the property tags that have been truncated or limited by the server."}, {"name": "OrdinalMost", "data_type": "0x0003", "area": "Tasks", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagOrdinalMost", "property_id": "0x36E2", "description": "Contains a positive number whose negative is less than or equal to the value of the"}, {"name": "OrganizationalIdNumber", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOrganizationalIdNumber", "property_id": "0x3A10", "description": "Contains an identifier for the mail user used within the mail user's organization."}, {"name": "OriginalAuthorEntryId", "data_type": "0x0102", "area": "Email", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalAuthorEntryId", "property_id": "0x004C", "description": "Contains an address book EntryID structure ( section *******) and is"}, {"name": "OriginalAuthorName", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalAuthorName", "property_id": "0x004D", "description": "Contains the display name of the sender of the original message referenced by a report"}, {"name": "OriginalDeliveryTime", "data_type": "0x0040", "area": "General Message Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalDeliveryTime", "property_id": "0x0055", "description": "Contains the delivery time, in UTC, from the original message."}, {"name": "OriginalDisplayBcc", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalDisplayBcc", "property_id": "0x0072", "description": "Contains the value of the PidTagDisplayBcc property (section 2.668) from the original message."}, {"name": "OriginalDisplayCc", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalDisplayCc", "property_id": "0x0073", "description": "Contains the value of the PidTagDisplayCc property(section 2.669) from the original message."}, {"name": "OriginalDisplayTo", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalDisplayTo", "property_id": "0x0074", "description": "Contains the value of the PidTagDisplayTo property (section 2.672) from the original message."}, {"name": "OriginalEntryId", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalEntryId", "property_id": "0x3A12", "description": "Contains the original EntryID of an object."}, {"name": "OriginalMessageClass", "data_type": "0x001F", "area": "Secure Messaging Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalMessageClass", "property_id": "0x004B", "description": "Designates the PidTagMessageClass property ( section *******) from the original message."}, {"name": "OriginalMessageId", "data_type": "0x001F", "area": "Mail", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalMessageId", "property_id": "0x1046", "description": "Contains the message ID of the original message included in replies or resent messages."}, {"name": "OriginalSenderAddressType", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSenderAddressType", "property_id": "0x0066", "description": "Contains the value of the original message sender's PidTagSenderAddressType property (section 2.994)."}, {"name": "OriginalSender<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSenderEmailAddress", "property_id": "0x0067", "description": "Contains the value of the original message sender's PidTagSenderEmailAddress"}, {"name": "OriginalSenderEntryId", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSenderEntryId", "property_id": "0x005B", "description": "Contains an address book EntryID that is set on delivery report messages."}, {"name": "OriginalSenderName", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSenderName", "property_id": "0x005A", "description": "Contains the value of the original message sender's PidTagSenderName property(section 2.998), and is set on delivery report messages."}, {"name": "OriginalSenderSearchKey", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSenderSearchKey", "property_id": "0x005C", "description": "Contains an address book search key set on the original email message."}, {"name": "OriginalSensitivity", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSensitivity", "property_id": "0x002E", "description": "Contains the sensitivity value of the original email message."}, {"name": "OriginalSentRepresentingAddressType", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSentRepresentingAddressType", "property_id": "0x0068", "description": "Contains the address type of the end user who is represented by the original email"}, {"name": "OriginalSentRepresentingEmailAddress", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSentRepresentingEmailAddress", "property_id": "0x0069", "description": "Contains the email address of the end user who is represented by the original email"}, {"name": "OriginalSentRepresentingEntryId", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSentRepresentingEntryId", "property_id": "0x005E", "description": "Identifies an address book EntryID that contains the entry identifier of the end user"}, {"name": "OriginalSentRepresentingName", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSentRepresentingName", "property_id": "0x005D", "description": "Contains the display name of the end user who is represented by the original email"}, {"name": "OriginalSentRepresentingSearchKey", "data_type": "0x0102", "area": "General Message Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSentRepresentingSearchKey", "property_id": "0x005F", "description": "Identifies an address book search key that contains the SearchKey of the end user who"}, {"name": "OriginalSubject", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSubject", "property_id": "0x0049", "description": "Specifies the subject of the original message."}, {"name": "OriginalSubmitTime", "data_type": "0x0040", "area": "General Message Properties", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagOriginalSubmitTime", "property_id": "0x004E", "description": "Specifies the original email message's submission date and time, in UTC."}, {"name": "OriginatorDeliveryReportRequested", "data_type": "0x000B", "area": "MIME Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagOriginatorDeliveryReportRequested", "property_id": "0x0023", "description": "Indicates whether an email sender requests an email delivery receipt from the messaging system."}, {"name": "OriginatorNonDeliveryReportRequested", "data_type": "0x000B", "area": "MIME Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagOriginatorNonDeliveryReportRequested", "property_id": "0x0C08", "description": "Specifies whether an email sender requests suppression of nondelivery receipts."}, {"name": "OscSyncEnabled", "data_type": "0x000B", "area": "Contact Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagOscSyncEnabled", "property_id": "0x7C24", "description": "Specifies whether contact synchronization with an external source is handled by the"}, {"name": "OtherAddressCity", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherAddressCity", "property_id": "0x3A5F", "description": "Contains the name of the mail user's other locality, such as the town or city."}, {"name": "OtherAddressCountry", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherAddressCountry", "property_id": "0x3A60", "description": "Contains the name of the mail user's other country/region."}, {"name": "OtherAddressPostalCode", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherAddressPostalCode", "property_id": "0x3A61", "description": "Contains the postal code for the mail user's other postal address."}, {"name": "OtherAddressPostOfficeBox", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherAddressPostOfficeBox", "property_id": "0x3A64", "description": "Contains the number or identifier of the mail user's other post office box."}, {"name": "OtherAddressStateOrProvince", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherAddressStateOrProvince", "property_id": "0x3A62", "description": "Contains the name of the mail user's other state or province."}, {"name": "OtherAddressStreet", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherAddressStreet", "property_id": "0x3A63", "description": "Contains the mail user's other street address."}, {"name": "OtherTelephoneNumber", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagOtherTelephoneNumber", "property_id": "0x3A1F", "description": "Contains an alternate telephone number for the mail user."}, {"name": "OutOfOfficeState", "data_type": "0x000B", "area": "Message Store Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagOutOfOfficeState", "property_id": "0x661D", "description": "Indicates whether the user is OOF."}, {"name": "OwnerAppointmentId", "data_type": "0x0003", "area": "Appointment", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "defining_reference": "section *******9", "canonical_name": "PidTagOwnerAppointmentId", "property_id": "0x0062", "description": "Specifies a quasi-unique value among all of the Calendar objects in a user's mailbox."}, {"name": "PagerTelephoneNumber", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPagerTelephoneNumber", "property_id": "0x3A21", "description": "Contains the mail user's pager telephone number."}, {"name": "ParentEntryId", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagParentEntryId", "property_id": "0x0E09", "description": "Contains the EntryID of the folder where messages or subfolders reside."}, {"name": "ParentFolderId", "data_type": "0x0014", "area": "ID Properties", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagParentFolderId", "property_id": "0x6749", "description": "Contains a value that contains the Folder ID (FID), as specified in"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagParentKey", "property_id": "0x0025", "description": "Contains the search key that is used to correlate the original message and the reports"}, {"name": "ParentSourceKey", "data_type": "0x0102", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagParentSourceKey", "property_id": "0x65E1", "description": "Contains a value on a folder that contains the PidTagSourceKey property (section"}, {"name": "PersonalHomePage", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPersonalHomePage", "property_id": "0x3A50", "description": "Contains the URL of the mail user's personal home page."}, {"name": "PolicyTag", "data_type": "0x0102", "area": "Archive", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagPolicyTag", "property_id": "0x3019", "description": "Specifies the GUID of a retention tag."}, {"name": "PostalAddress", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPostalAddress", "property_id": "0x3A15", "description": "Contains the mail user's postal address."}, {"name": "PostalCode", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPostalCode", "property_id": "0x3A2A", "description": "Contains the postal code for the mail user's postal address."}, {"name": "PostOfficeBox", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPostOfficeBox", "property_id": "0x3A2B", "description": "Contains the number or identifier of the mail user's post office box."}, {"name": "PredecessorChangeList", "data_type": "0x0102", "area": "Sync", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagPredecessorChangeList", "property_id": "0x65E3", "description": "Contains a value that contains a serialized representation of a PredecessorChangeList structure."}, {"name": "PrimaryFaxNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPrimaryFaxNumber", "property_id": "0x3A23", "description": "Contains the telephone number of the mail user's primary fax machine."}, {"name": "PrimarySendAccount", "data_type": "0x001F", "area": "MapiNonTransmittable", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPrimarySendAccount", "property_id": "0x0E28", "description": "Specifies the first server that a client is to use to send the email with."}, {"name": "PrimaryTelephoneNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPrimaryTelephoneNumber", "property_id": "0x3A1A", "description": "Contains the mail user's primary telephone number."}, {"name": "Priority", "data_type": "0x0003", "area": "Email", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagPriority", "property_id": "0x0026", "description": "Indicates the client's request for the priority with which the message is to be sent by the"}, {"name": "Processed", "data_type": "0x000B", "area": "Calendar", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagProcessed", "property_id": "0x7D01", "description": "Indicates whether a client has already processed a received task communication."}, {"name": "Profession", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagProfession", "property_id": "0x3A46", "description": "Contains the name of the mail user's line of business."}, {"name": "ProhibitReceiveQuota", "data_type": "0x0003", "area": "Exchange Administrative", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagProhibitReceiveQuota", "property_id": "0x666A", "description": "Maximum size, in kilobytes, that a user is allowed to accumulate in their mailbox"}, {"name": "ProhibitSendQuota", "data_type": "0x0003", "area": "ExchangeAdministrative", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagProhibitSendQuota", "property_id": "0x666E", "description": "Maximum size, in kilobytes, that a user is allowed to accumulate in their mailbox"}, {"name": "PurportedSenderDomain", "data_type": "0x001F", "area": "TransportEnvelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagPurportedSenderDomain", "property_id": "0x4083", "description": "Contains the domain responsible for transmitting the current message."}, {"name": "RadioTelephoneNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRadioTelephoneNumber", "property_id": "0x3A1D", "description": "Contains the mail user's radio telephone number."}, {"name": "Read", "data_type": "0x000B", "area": "MapiNonTransmittable Property set", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagRead", "property_id": "0x0E69", "description": "Indicates whether a message has been read."}, {"name": "ReadReceiptAddressType", "data_type": "0x001F", "area": "Transport Envelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptAddressType", "property_id": "0x4029", "description": "Contains the address type of the end user to whom a read receipt is directed."}, {"name": "ReadReceiptEmailAddress", "data_type": "0x001F", "area": "Transport Envelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptEmailAddress", "property_id": "0x402A", "description": "Contains the email address of the end user to whom a read receipt is directed."}, {"name": "ReadReceiptEntryId", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptEntryId", "property_id": "0x0046", "description": "Contains an address book EntryID."}, {"name": "ReadReceiptName", "data_type": "0x001F", "area": "Transport Envelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptName", "property_id": "0x402B", "description": "Contains the display name for the end user to whom a read receipt is directed."}, {"name": "ReadReceiptRequested", "data_type": "0x000B", "area": "Email", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptRequested", "property_id": "0x0029", "description": "Specifies whether the email sender requests a read receipt from all recipients when this email message is read or opened."}, {"name": "ReadReceiptSearchKey", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptSearchKey", "property_id": "0x0053", "description": "Contains an address book search key."}, {"name": "ReadReceiptSmtpAddress", "data_type": "0x001F", "area": "Mail", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReadReceiptSmtpAddress", "property_id": "0x5D05", "description": "Contains the SMTP email address of the user to whom a read receipt is directed."}, {"name": "ReceiptTime", "data_type": "0x0040", "area": "Email", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagReceiptTime", "property_id": "0x002A", "description": "Contains the sent time for a message disposition notification, as specified in ."}, {"name": "ReceivedByAddressType", "data_type": "0x001F", "area": "MapiEnvelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedByAddressType", "property_id": "0x0075", "description": "Contains the email message receiver's email address type."}, {"name": "Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedByEmailAddress", "property_id": "0x0076", "description": "Contains the email message receiver's email address."}, {"name": "ReceivedByEntryId", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedByEntryId", "property_id": "0x003F", "description": "Contains the address book EntryID of the mailbox receiving the Email object."}, {"name": "ReceivedByName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedByName", "property_id": "0x0040", "description": "Contains the email message receiver's display name."}, {"name": "ReceivedBySearchKey", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedBySearchKey", "property_id": "0x0051", "description": "Identifies an address book search key that contains a binary-comparable key that is"}, {"name": "ReceivedBySmtpAddress", "data_type": "0x001F", "area": "Mail", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedBySmtpAddress", "property_id": "0x5D07", "description": "Contains the email message receiver's SMTP email address."}, {"name": "ReceivedRepresentingAddressType", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedRepresentingAddressType", "property_id": "0x0077", "description": "Contains the email address type for the end user represented by the receiving mailbox owner."}, {"name": "ReceivedRepresentingEmailAddress", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedRepresentingEmailAddress", "property_id": "0x0078", "description": "Contains the email address for the end user represented by the receiving mailbox owner."}, {"name": "ReceivedRepresentingEntryId", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedRepresentingEntryId", "property_id": "0x0043", "description": "Contains an address book EntryID that identifies the end user represented by the"}, {"name": "ReceivedRepresentingName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedRepresentingName", "property_id": "0x0044", "description": "Contains the display name for the end user represented by the receiving mailbox owner."}, {"name": "ReceivedRepresentingSearchKey", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedRepresentingSearchKey", "property_id": "0x0052", "description": "Identifies an address book search key that contains a binary-comparable key of the end user represented by the receiving mailbox owner."}, {"name": "ReceivedRepresentingSmtpAddress", "data_type": "0x001F", "area": "Mail", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReceivedRepresentingSmtpAddress", "property_id": "0x5D08", "description": "Contains the SMTP email address of the user represented by the receiving mailbox owner."}, {"name": "RecipientDisplayName", "data_type": "0x001F", "area": "TransportRecipient", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientDisplayName", "property_id": "0x5FF6", "description": "Specifies the display name of the recipient."}, {"name": "RecipientEntryId", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientEntryId", "property_id": "0x5FF7", "description": "Identifies an Address Book object that specifies the recipient."}, {"name": "RecipientFlags", "data_type": "0x0003", "area": "TransportRecipient", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientFlags", "property_id": "0x5FFD", "description": "Specifies a bit field that describes the recipient status."}, {"name": "RecipientOrder", "data_type": "0x0003", "area": "TransportRecipient", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientOrder", "property_id": "0x5FDF", "description": "Specifies the location of the current recipient in the recipient table."}, {"name": "RecipientProposed", "data_type": "0x000B", "area": "TransportRecipient", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientProposed", "property_id": "0x5FE1", "description": "Indicates that the attendee proposed a new date and/or time."}, {"name": "RecipientProposedEndTime", "data_type": "0x0040", "area": "TransportRecipient", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientProposedEndTime", "property_id": "0x5FE4", "description": "Indicates the meeting end time requested by the attendee in a counter proposal."}, {"name": "RecipientProposedStartTime", "data_type": "0x0040", "area": "TransportRecipient", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientProposedStartTime", "property_id": "0x5FE3", "description": "Indicates the meeting start time requested by the attendee in a counter proposal."}, {"name": "RecipientReassignmentProhibited", "data_type": "0x000B", "area": "MapiEnvelope", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientReassignmentProhibited", "property_id": "0x002B", "description": "Specifies whether adding additional or different recipients is prohibited for the email"}, {"name": "RecipientTrackStatus", "data_type": "0x0003", "area": "TransportRecipient", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientTrackStatus", "property_id": "0x5FFF", "description": "Indicates the response status that is returned by the attendee."}, {"name": "RecipientTrackStatusTime", "data_type": "0x0040", "area": "TransportRecipient", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientTrackStatusTime", "property_id": "0x5FFB", "description": "Indicates the date and time at which the attendee responded."}, {"name": "RecipientType", "data_type": "0x0003", "area": "MapiRecipient", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRecipientType", "property_id": "0x0C15", "description": "Represents the recipient type of a recipient on the message."}, {"name": "Record<PERSON>ey", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRecordKey", "property_id": "0x0FF9", "description": "Contains a unique binary-comparable identifier for a specific object."}, {"name": "ReferredByName", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReferredByName", "property_id": "0x3A47", "description": "Contains the name of the mail user's referral."}, {"name": "RemindersOnlineEntryId", "data_type": "0x0102", "area": "MapiContainer", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRemindersOnlineEntryId", "property_id": "0x36D5", "description": "Contains an EntryID for the Reminders folder."}, {"name": "RemoteMessageTransferAgent", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRemoteMessageTransferAgent", "property_id": "0x0C21", "description": "Contains the value of the Remote-MTA field for a delivery status notification, as specified in."}, {"name": "RenderingPosition", "data_type": "0x0003", "area": "MapiAttachment", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRenderingPosition", "property_id": "0x370B", "description": "Represents an offset, in rendered characters, to use when rendering an attachment within the main message text."}, {"name": "ReplyRecipientEntries", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReplyRecipientEntries", "property_id": "0x004F", "description": "Identifies a FlatEntryList structure ( section 2.3.3) of address book EntryIDs for recipients that are to receive a reply."}, {"name": "ReplyRecipientNames", "data_type": "0x001F", "area": "MapiEnvelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReplyRecipientNames", "property_id": "0x0050", "description": "Contains a list of display names for recipients that are to receive a reply."}, {"name": "ReplyRequested", "data_type": "0x000B", "area": "MapiRecipient", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagReplyRequested", "property_id": "0x0C17", "description": "Indicates whether a reply is requested to a Message object."}, {"name": "ReplyTemplateId", "data_type": "0x0102", "area": "Rules", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReplyTemplateId", "property_id": "0x65C2", "description": "Contains the value of the GUID that points to a Reply template."}, {"name": "ReplyTime", "data_type": "0x0040", "area": "MapiEnvelope", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagReplyTime", "property_id": "0x0030", "description": "Specifies the time, in UTC, that the sender has designated for an associated work item to be due."}, {"name": "ReportDisposition", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReportDisposition", "property_id": "0x0080", "description": "Contains a string indicating whether the original message was displayed to the user or deleted (report messages only)."}, {"name": "ReportDispositionMode", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReportDispositionMode", "property_id": "0x0081", "description": "Contains a description of the action that a client has performed on behalf of a user(report messages only)."}, {"name": "ReportEntryId", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReportEntryId", "property_id": "0x0045", "description": "Specifies an entry ID that identifies the application that generated a report message."}, {"name": "ReportingMessageTransferAgent", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReportingMessageTransferAgent", "property_id": "0x6820", "description": "Contains the value of the Reporting-MTA field for a delivery status notification, as specified in ."}, {"name": "ReportName", "data_type": "0x001F", "area": "MapiEnvelope", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReportName", "property_id": "0x003A", "description": "Contains the display name for the entity (usually a server agent) that generated the report message."}, {"name": "ReportSearchKey", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReportSearchKey", "property_id": "0x0054", "description": "Contains an address book search key representing the entity (usually a server agent) that generated the report message."}, {"name": "ReportTag", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagReportTag", "property_id": "0x0031", "description": "Contains the data that is used to correlate the report and the original message."}, {"name": "ReportText", "data_type": "0x001F", "area": "MapiMessage", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagReportText", "property_id": "0x1001", "description": "Contains the optional text for a report message."}, {"name": "ReportTime", "data_type": "0x0040", "area": "MapiEnvelope Property set", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagReportTime", "property_id": "0x0032", "description": "Indicates the last time that the contact list that is controlled by thePidTagJunkIncludeContacts property (section 2.752) was updated."}, {"name": "ResolveMethod", "data_type": "0x0003", "area": "MapiStatus", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagResolveMethod", "property_id": "0x3FE7", "description": "Specifies how to resolve any conflicts with the message."}, {"name": "ResponseRequested", "data_type": "0x000B", "area": "MapiEnvelope Property set", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagResponseRequested", "property_id": "0x0063", "description": "Indicates whether a response is requested to a Message object."}, {"name": "Responsibility", "data_type": "0x000B", "area": "MapiNonTransmittable", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagResponsibility", "property_id": "0x0E0F", "description": "Specifies whether another mail agent has ensured that the message will be delivered."}, {"name": "RetentionDate", "data_type": "0x0040", "area": "Archive", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagRetentionDate", "property_id": "0x301C", "description": "Specifies the date, in UTC, after which a Message object is expired by the server."}, {"name": "RetentionFlags", "data_type": "0x0003", "area": "Archive", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRetentionFlags", "property_id": "0x301D", "description": "Contains flags that specify the status or nature of an item's retention tag or archive tag."}, {"name": "RetentionPeriod", "data_type": "0x0003", "area": "Archive", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRetentionPeriod", "property_id": "0x301A", "description": "Specifies the number of days that a Message object can remain unarchived."}, {"name": "Rights", "data_type": "0x0003", "area": "ExchangeFolder", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRights", "property_id": "0x6639", "description": "Specifies a user's folder permissions."}, {"name": "RoamingDatatypes", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRoamingDatatypes", "property_id": "0x7C06", "description": "Contains a bitmask that indicates which stream properties exist on the message."}, {"name": "RoamingDictionary", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRoamingDictionary", "property_id": "0x7C07", "description": "Contains a dictionary stream, as specified in  section *******."}, {"name": "RoamingXmlStream", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRoamingXmlStream", "property_id": "0x7C08", "description": "Contains an XML stream, as specified in  section *******."}, {"name": "<PERSON><PERSON>", "data_type": "0x0003", "area": "MapiCommon", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRowid", "property_id": "0x3000", "description": "Contains a unique identifier for a recipient in a message's recipient table."}, {"name": "RowType", "data_type": "0x0003", "area": "MapiNonTransmittable", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRowType", "property_id": "0x0FF5", "description": "Identifies the type of the row."}, {"name": "RtfCompressed", "data_type": "0x0102", "area": "Email", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRtfCompressed", "property_id": "0x1009", "description": "Contains message body text in compressed RTF format."}, {"name": "RtfInSync", "data_type": "0x000B", "area": "Email", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagRtfInSync", "property_id": "0x0E1F", "description": "Indicates whether the PidTagBody property (section 2.612) and the"}, {"name": "RuleActionNumber", "data_type": "0x0003", "area": "ExchangeMessageReadOnly", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleActionNumber", "property_id": "0x6650", "description": "Contains the index of a rule action that failed."}, {"name": "RuleActions", "data_type": "0x00FE", "area": "Server-Side Rules Properties", "data_type_name": "PtypRuleAction", "canonical_type": "PidTag", "canonical_name": "PidTagRuleActions", "property_id": "0x6680", "description": "Contains the set of actions associated with the rule."}, {"name": "RuleActionType", "data_type": "0x0003", "area": "ExchangeMessageReadOnly", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleActionType", "property_id": "0x6649", "description": "Contains the ActionType field ( section *******) of a rule that failed."}, {"name": "RuleCondition", "data_type": "0x00FD", "area": "Server-Side Rules Properties", "data_type_name": "PtypRestriction", "canonical_type": "PidTag", "canonical_name": "PidTagRuleCondition", "property_id": "0x6679", "description": "Defines the conditions under which a rule action is to be executed."}, {"name": "RuleError", "data_type": "0x0003", "area": "ExchangeMessageReadOnly", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleError", "property_id": "0x6648", "description": "Contains the error code that indicates the cause of an error encountered during the execution of the rule."}, {"name": "RuleFolderEntryId", "data_type": "0x0102", "area": "ExchangeMessageReadOnly", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRuleFolderEntryId", "property_id": "0x6651", "description": "Contains the EntryID of the folder where the rule that triggered the generation of a DAM is stored."}, {"name": "RuleId", "data_type": "0x0014", "area": "Server-Side Rules Properties", "data_type_name": "PtypInteger64", "canonical_type": "PidTag", "canonical_name": "PidTagRuleId", "property_id": "0x6674", "description": "Specifies a unique identifier that is generated by the messaging server for each rule"}, {"name": "RuleIds", "data_type": "0x0102", "area": "Server-Side Rules Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRuleIds", "property_id": "0x6675", "description": "Contains a buffer that is obtained by concatenating the PidTagRuleId property."}, {"name": "RuleLevel", "data_type": "0x0003", "area": "Server-Side Rules Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleLevel", "property_id": "0x6683", "description": "Contains 0x00000000. This property is not used."}, {"name": "RuleMessageLevel", "data_type": "0x0003", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageLevel", "property_id": "0x65ED", "description": "Contains 0x00000000. Set on the FAI message."}, {"name": "RuleMessageName", "data_type": "0x001F", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageName", "property_id": "0x65EC", "description": "Specifies the name of the rule. Set on the FAI message."}, {"name": "RuleMessageProvider", "data_type": "0x001F", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageProvider", "property_id": "0x65EB", "description": "Identifies the client application that owns the rule. Set on the FAI message."}, {"name": "RuleMessageProviderData", "data_type": "0x0102", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageProviderData", "property_id": "0x65EE", "description": "Contains opaque data set by the client for the exclusive use of the client. Set on the FAI message."}, {"name": "RuleMessageSequence", "data_type": "0x0003", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageSequence", "property_id": "0x65F3", "description": "Contains a value used to determine the order in which rules are evaluated and executed. Set on the FAI message."}, {"name": "RuleMessageState", "data_type": "0x0003", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageState", "property_id": "0x65E9", "description": "Contains flags that specify the state of the rule. Set on the FAI message."}, {"name": "RuleMessageUserFlags", "data_type": "0x0003", "area": "ExchangeNonTransmittableReserved", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleMessageUserFlags", "property_id": "0x65EA", "description": "Contains an opaque property that the client sets for the exclusive use of the client. Set on the FAI message."}, {"name": "RuleName", "data_type": "0x001F", "area": "Server-Side Rules Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRuleName", "property_id": "0x6682", "description": "Specifies the name of the rule."}, {"name": "Rule<PERSON>rovider", "data_type": "0x001F", "area": "Server-Side Rules Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagRuleProvider", "property_id": "0x6681", "description": "A string identifying the client application that owns a rule."}, {"name": "RuleProviderData", "data_type": "0x0102", "area": "Server-Side Rules Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRuleProviderData", "property_id": "0x6684", "description": "Contains opaque data set by the client for the exclusive use of the client."}, {"name": "RuleSequence", "data_type": "0x0003", "area": "Server-Side Rules Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleSequence", "property_id": "0x6676", "description": "Contains a value used to determine the order in which rules are evaluated and executed."}, {"name": "RuleState", "data_type": "0x0003", "area": "Server-Side Rules Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleState", "property_id": "0x6677", "description": "Contains flags that specify the state of the rule."}, {"name": "RuleUserFlags", "data_type": "0x0003", "area": "Server-Side Rules Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagRuleUserFlags", "property_id": "0x6678", "description": "Contains an opaque property that the client sets for the exclusive use of the client."}, {"name": "RwRulesStream", "data_type": "0x0102", "area": "Message Class Defined Transmittable", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagRwRulesStream", "property_id": "0x6802", "description": "Contains additional rule data about the Rule FAI message."}, {"name": "ScheduleInfoAppointmentTombstone", "data_type": "0x0102", "area": "Free/Busy Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoAppointmentTombstone", "property_id": "0x686A", "description": "Contains a list of tombstones, where each tombstone represents a Meeting object that has been declined."}, {"name": "ScheduleInfoAutoAcceptAppointments", "data_type": "0x000B", "area": "Free/Busy Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoAutoAcceptAppointments", "property_id": "0x686D", "description": "Indicates whether a client or server is to automatically respond to all meeting requests for the attendee or resource."}, {"name": "ScheduleInfoDelegateEntryIds", "data_type": "0x1102", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDelegateEntryIds", "property_id": "0x6845", "description": "Specifies the EntryIDs of the delegates."}, {"name": "ScheduleInfoDelegateNames", "data_type": "0x101F", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDelegateNames", "property_id": "0x6844", "description": "Specifies the names of the delegates."}, {"name": "ScheduleInfoDelegateNamesW", "data_type": "0x101F", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleString", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDelegateNamesW", "property_id": "0x684A", "description": "Specifies the names of the delegates in Unicode."}, {"name": "ScheduleInfoDelegatorWantsCopy", "data_type": "0x000B", "area": "Free/Busy Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDelegatorWantsCopy", "property_id": "0x6842", "description": "Indicates whether the delegator wants to receive copies of the meeting-related objects that are sent to the delegate."}, {"name": "ScheduleInfoDelegatorWantsInfo", "data_type": "0x000B", "area": "Free/Busy Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDelegatorWantsInfo", "property_id": "0x684B", "description": "Indicates whether the delegator wants to receive informational updates."}, {"name": "ScheduleInfoDisallowOverlappingAppts", "data_type": "0x000B", "area": "Free/Busy Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDisallowOverlappingAppts", "property_id": "0x686F", "description": "Indicates whether a client or server, when automatically responding to meeting requests, is to decline Meeting Request objects that overlap with previously scheduled events."}, {"name": "ScheduleInfoDisallowRecurringAppts", "data_type": "0x000B", "area": "Free/Busy Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDisallowRecurringAppts", "property_id": "0x686E", "description": "Indicates whether a client or server, when automatically responding to meeting requests, is to decline Meeting Request objects that represent a recurring series."}, {"name": "ScheduleInfoDontMailDelegates", "data_type": "0x000B", "area": "Free/Busy Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoDontMailDelegates", "property_id": "0x6843", "description": "Contains a value set to TRUE by the client, regardless of user input."}, {"name": "ScheduleInfoFreeBusy", "data_type": "0x0102", "area": "Free/Busy Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoFreeBusy", "property_id": "0x686C", "description": "This property is deprecated and is not to be used."}, {"name": "ScheduleInfoFreeBusyAway", "data_type": "0x1102", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoFreeBusyAway", "property_id": "0x6856", "description": "Specifies the times for which the free/busy status is set a value of OOF."}, {"name": "ScheduleInfoFreeBusyBusy", "data_type": "0x1102", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoFreeBusyBusy", "property_id": "0x6854", "description": "Specifies the blocks of time for which the free/busy status is set to a value of busy."}, {"name": "ScheduleInfoFreeBusyMerged", "data_type": "0x1102", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoFreeBusyMerged", "property_id": "0x6850", "description": "Specifies the blocks for which free/busy data of type busy or OOF is present in the free/busy message."}, {"name": "ScheduleInfoFreeBusyTentative", "data_type": "0x1102", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoFreeBusyTentative", "property_id": "0x6852", "description": "Specifies the blocks of times for which the free/busy status is set to a value of tentative."}, {"name": "ScheduleInfoMonthsAway", "data_type": "0x1003", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoMonthsAway", "property_id": "0x6855", "description": "Specifies the months for which free/busy data of type OOF is present in the free/busy message."}, {"name": "ScheduleInfoMonthsBusy", "data_type": "0x1003", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoMonthsBusy", "property_id": "0x6853", "description": "Specifies the months for which free/busy data of type busy is present in the free/busy message."}, {"name": "ScheduleInfoMonthsMerged", "data_type": "0x1003", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoMonthsMerged", "property_id": "0x684F", "description": "Specifies the months for which free/busy data of type busy or OOF is present in the free/busy message."}, {"name": "ScheduleInfoMonthsTentative", "data_type": "0x1003", "area": "Free/Busy Properties", "data_type_name": "PtypMultipleInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoMonthsTentative", "property_id": "0x6851", "description": "Specifies the months for which free/busy data of type tentative is present in the free/busy message."}, {"name": "ScheduleInfoResourceType", "data_type": "0x0003", "area": "Free/Busy Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagScheduleInfoResourceType", "property_id": "0x6841", "description": "Set to 0x00000000 when sending and is ignored on receipt."}, {"name": "SchedulePlusFreeBusyEntryId", "data_type": "0x0102", "area": "ExchangeMessageStore", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSchedulePlusFreeBusyEntryId", "property_id": "0x6622", "description": "Contains the EntryID of the folder named \"SCHEDULE+ FREE BUSY\" under the non-IPM subtree of the public folder message store."}, {"name": "ScriptData", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagScriptData", "property_id": "0x0004", "description": "Contains a series of instructions that can be executed to format an address and the data that is needed to execute those instructions."}, {"name": "SearchFolderDefinition", "data_type": "0x0102", "area": "Search", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderDefinition", "property_id": "0x6845", "description": "Specifies the search criteria and search options."}, {"name": "SearchFolderEfpFlags", "data_type": "0x0003", "area": "Search", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderEfpFlags", "property_id": "0x6848", "description": "Specifies flags that control how a folder is displayed."}, {"name": "SearchFolderExpiration", "data_type": "0x0003", "area": "Search", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderExpiration", "property_id": "0x683A", "description": "Contains the time, in UTC, at which the search folder container will be stale and has to be updated or recreated."}, {"name": "SearchFolderId", "data_type": "0x0102", "area": "Search", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderId", "property_id": "0x6842", "description": "Contains a GUID that identifies the search folder."}, {"name": "SearchFolderLastUsed", "data_type": "0x0003", "area": "Search", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderLastUsed", "property_id": "0x6834", "description": "Contains the last time, in UTC, that the folder was accessed."}, {"name": "SearchFolderRecreateInfo", "data_type": "0x0102", "area": "Search", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderRecreateInfo", "property_id": "0x6844", "description": "This property is not to be used."}, {"name": "SearchFolderStorageType", "data_type": "0x0003", "area": "Search", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderStorageType", "property_id": "0x6846", "description": "Contains flags that specify the binary large object (BLOB) data that appears in the PidTagSearchFolderDefinition property."}, {"name": "SearchFolderTag", "data_type": "0x0003", "area": "Search", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderTag", "property_id": "0x6847", "description": "Contains the value of the SearchFolderTag sub-property of the PidTagExtendedFolderFlags (section 2.685) property of the search folder container."}, {"name": "SearchFolderTemplateId", "data_type": "0x0003", "area": "Search", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSearchFolderTemplateId", "property_id": "0x6841", "description": "Contains the ID of the template that is being used for the search."}, {"name": "SearchKey", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSearchKey", "property_id": "0x300B", "description": "Contains a unique binary-comparable key that identifies an object for a search."}, {"name": "SecurityDescriptorAsXml", "data_type": "0x001F", "area": "Access Control Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSecurityDescriptorAsXml", "property_id": "0x0E6A", "description": "Contains security attributes in XML."}, {"name": "Selectable", "data_type": "0x000B", "area": "AB Container", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagSelectable", "property_id": "0x3609", "description": "This property is not set and, if set, is ignored."}, {"name": "SenderAddressType", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSenderAddressType", "property_id": "0x0C1E", "description": "Contains the email address type of the sending mailbox owner."}, {"name": "Sender<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSenderEmailAddress", "property_id": "0x0C1F", "description": "Contains the email address of the sending mailbox owner."}, {"name": "SenderEntryId", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSenderEntryId", "property_id": "0x0C19", "description": "Identifies an address book EntryID that contains the address book EntryID of the sending mailbox owner."}, {"name": "SenderIdStatus", "data_type": "0x0003", "area": "Secure Messaging Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSenderIdStatus", "property_id": "0x4079", "description": "Reports the results of a Sender-ID check."}, {"name": "SenderName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSenderName", "property_id": "0x0C1A", "description": "Contains the display name of the sending mailbox owner."}, {"name": "SenderS<PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSenderSearchKey", "property_id": "0x0C1D", "description": "Identifies an address book search key."}, {"name": "SenderSmtpAddress", "data_type": "0x001F", "area": "Mail", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSenderSmtpAddress", "property_id": "0x5D01", "description": "Contains the SMTP email address format of the e–mail address of the sending mailbox owner."}, {"name": "SenderTelephoneNumber", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSenderTelephoneNumber", "property_id": "0x6802", "description": "Contains the telephone number of the caller associated with a voice mail message."}, {"name": "SendInternetEncoding", "data_type": "0x0003", "area": "Address Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSendInternetEncoding", "property_id": "0x3A71", "description": "Contains a bitmask of message encoding preferences for email sent to an email-enabled entity that is represented by this Address Book object."}, {"name": "SendRichInfo", "data_type": "0x000B", "area": "Address Properties", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagSendRichInfo", "property_id": "0x3A40", "description": "Indicates whether the email-enabled entity represented by the Address Book object can receive all message content, including Rich Text Format (RTF) and other embedded objects."}, {"name": "Sensitivity", "data_type": "0x0003", "area": "General Message Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSensitivity", "property_id": "0x0036", "description": "Indicates the sender's assessment of the sensitivity of the Message object."}, {"name": "SentMailSvrEID", "data_type": "0x00FB", "area": "ProviderDefinedNonTransmittable", "data_type_name": "PtypServerId", "canonical_type": "PidTag", "canonical_name": "PidTagSentMailSvrEID", "property_id": "0x6740", "description": "Contains an EntryID that represents the Sent Items folder for the message."}, {"name": "SentRepresentingAddressType", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSentRepresentingAddressType", "property_id": "0x0064", "description": "Contains an email address type."}, {"name": "SentRepresentingEmailAddress", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSentRepresentingEmailAddress", "property_id": "0x0065", "description": "Contains an email address for the end user who is represented by the sending mailbox owner."}, {"name": "SentRepresentingEntryId", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSentRepresentingEntryId", "property_id": "0x0041", "description": "Contains the identifier of the end user who is represented by the sending mailbox owner."}, {"name": "SentRepresentingFlags", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "defining_reference": "", "canonical_name": "PidTagSentRepresentingFlags", "property_id": "0x401A", "description": ""}, {"name": "SentRepresentingName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSentRepresentingName", "property_id": "0x0042", "description": "Contains the display name for the end user who is represented by the sending mailbox owner."}, {"name": "SentRepresentingSearchKey", "data_type": "0x0102", "area": "Address Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSentRepresentingSearchKey", "property_id": "0x003B", "description": "Contains a binary-comparable key that represents the end user who is represented by"}, {"name": "SentRepresentingSmtpAddress", "data_type": "0x001F", "area": "Mail", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSentRepresentingSmtpAddress", "property_id": "0x5D02", "description": "Contains the SMTP email address of the end user who is represented by the sending mailbox owner."}, {"name": "SerializedReplidGuidMap", "data_type": "0x0102", "area": "Logon Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSerializedReplidGuidMap", "property_id": "0x6638", "description": "Contains a serialized list of REPLID and REPLGUID pairs which represent all or part of the REPLID / REPLGUID mapping of the associated Logon object."}, {"name": "SmtpAddress", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSmtpAddress", "property_id": "0x39FE", "description": "Contains the SMTP address of the Message object."}, {"name": "SortLocaleId", "data_type": "0x0003", "area": "ExchangeAdministrative", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagSortLocaleId", "property_id": "0x6705", "description": "Contains the locale identifier."}, {"name": "Source<PERSON>ey", "data_type": "0x0102", "area": "Sync", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSourceKey", "property_id": "0x65E0", "description": "Contains a value that contains an internal global identifier (GID) for this folder or"}, {"name": "SpokenName", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSpokenName", "property_id": "0x8CC2", "description": "Contains a recording of the mail user's name pronunciation."}, {"name": "SpouseName", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSpouseName", "property_id": "0x3A48", "description": "Contains the name of the mail user's spouse/partner."}, {"name": "StartDate", "data_type": "0x0040", "area": "MapiEnvelope", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagStartDate", "property_id": "0x0060", "description": "Contains the value of the PidLidAppointmentStartWhole property (section 2.29)."}, {"name": "StartDateEtc", "data_type": "0x0102", "area": "Archive", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagStartDateEtc", "property_id": "0x301B", "description": "Contains the default retention period, and the start date from which the age of a"}, {"name": "StateOrProvince", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagStateOrProvince", "property_id": "0x3A28", "description": "Contains the name of the mail user's state or province."}, {"name": "StoreEntryId", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagStoreEntryId", "property_id": "0x0FFB", "description": "Contains the unique EntryID of the message store where an object resides."}, {"name": "StoreState", "data_type": "0x0003", "area": "MapiMessageStore", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagStoreState", "property_id": "0x340E", "description": "Indicates whether a mailbox has any active Search folders."}, {"name": "StoreSupportMask", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagStoreSupportMask", "property_id": "0x340D", "description": "Indicates whether string properties within the .msg file are Unicode-encoded."}, {"name": "StreetAddress", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagStreetAddress", "property_id": "0x3A29", "description": "Contains the mail user's street address."}, {"name": "Subfolders", "data_type": "0x000B", "area": "MapiContainer", "data_type_name": "PtypBoolean", "canonical_type": "PidTag", "canonical_name": "PidTagSubfolders", "property_id": "0x360A", "description": "Specifies whether a folder has subfolders."}, {"name": "Subject", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSubject", "property_id": "0x0037", "description": "Contains the subject of the email message."}, {"name": "SubjectPrefix", "data_type": "0x001F", "area": "General Message Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSubjectPrefix", "property_id": "0x003D", "description": "Contains the prefix for the subject of the message."}, {"name": "SupplementaryInfo", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSupplementaryInfo", "property_id": "0x0C1B", "description": "Contains supplementary information about a delivery status notification, as specified in"}, {"name": "Surname", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagSurname", "property_id": "0x3A11", "description": "Contains the mail user's family name."}, {"name": "SwappedToDoData", "data_type": "0x0102", "area": "MapiNonTransmittable", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSwappedToDoData", "property_id": "0x0E2D", "description": "Contains a secondary storage location for flags when sender flags or sender reminders are supported."}, {"name": "SwappedToDoStore", "data_type": "0x0102", "area": "MapiNonTransmittable", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagSwappedToDoStore", "property_id": "0x0E2C", "description": "Contains the value of the PidTagStoreEntryId property."}, {"name": "TargetEntryId", "data_type": "0x0102", "area": "ID Properties", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagTargetEntryId", "property_id": "0x3010", "description": "Contains the message ID of a Message object being submitted for optimization ( section *******)."}, {"name": "TelecommunicationsDeviceForDeafTelephoneNumber", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagTelecommunicationsDeviceForDeafTelephoneNumber", "property_id": "0x3A4B", "description": "Contains the mail user's telecommunication device for the deaf (TTY/TDD) telephone number."}, {"name": "TelexNumber", "data_type": "0x001F; PtypMultipleBinary, 0x1102", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagTelexNumber", "property_id": "0x3A2C", "description": "Contains the mail user's telex number. This property is returned from an NSPI server as a PtypMultipleBinary. Otherwise, the data type is PtypString."}, {"name": "TemplateData", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagTemplateData", "property_id": "0x0001", "description": "Describes the controls used in the template that is used to retrieve address book information."}, {"name": "Templateid", "data_type": "0x0102", "area": "MapiAddressBook", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagTemplateid", "property_id": "0x3902", "description": "Contains the value of the PidTagEntryId property (section 2.677), expressed as a Permanent Entry ID format."}, {"name": "TextAttachmentCharset", "data_type": "0x001F", "area": "Message Attachment Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagTextAttachmentCharset", "property_id": "0x371B", "description": "Specifies the character set of an attachment received via MIME with the content-type of text."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "0x0102", "area": "Address Book", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagThumbnailPhoto", "property_id": "0x8C9E", "description": "Contains the mail user's photo in .jpg format."}, {"name": "Title", "data_type": "0x001F", "area": "MapiMailUser", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagTitle", "property_id": "0x3A17", "description": "Contains the mail user's job title."}, {"name": "TnefCorrelationKey", "data_type": "0x0102", "area": "MapiEnvelope", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagTnefCorrelationKey", "property_id": "0x007F", "description": "Contains a value that correlates a Transport Neutral Encapsulation Format (TNEF) attachment with a message."}, {"name": "ToDoItemFlags", "data_type": "0x0003", "area": "MapiNonTransmittable", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagToDoItemFlags", "property_id": "0x0E2B", "description": "Contains flags associated with objects."}, {"name": "TransmittableDisplayName", "data_type": "0x001F", "area": "Address Properties", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagTransmittableDisplayName", "property_id": "0x3A20", "description": "Contains an Address Book object's display name that is transmitted with the message."}, {"name": "TransportMessageHeaders", "data_type": "0x001F", "area": "Email", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagTransportMessageHeaders", "property_id": "0x007D", "description": "Contains transport-specific message envelope information for email."}, {"name": "TrustSender", "data_type": "0x0003", "area": "MapiNonTransmittable", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagTrustSender", "property_id": "0x0E79", "description": "Specifies whether the associated message was delivered through a trusted transport"}, {"name": "UserCertificate", "data_type": "0x0102", "area": "MapiMailUser", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagUserCertificate", "property_id": "0x3A22", "description": "Contains an ASN.1 authentication certificate for a messaging user."}, {"name": "UserEntryId", "data_type": "0x0102", "area": "ExchangeMessageStore", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagUserEntryId", "property_id": "0x6619", "description": "Address book EntryID of the user logged on to the public folders."}, {"name": "UserX509Certificate", "data_type": "0x1102", "area": "MapiMailUser", "data_type_name": "PtypMultipleBinary", "canonical_type": "PidTag", "canonical_name": "PidTagUserX509Certificate", "property_id": "0x3A70", "description": "Contains a list of certificates for the mail user."}, {"name": "ViewDescriptorBinary", "data_type": "0x0102", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagViewDescriptorBinary", "property_id": "0x7001", "description": "Contains view definitions."}, {"name": "ViewDescriptorName", "data_type": "0x001F", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypString", "canonical_type": "PidTag", "defining_reference": "", "canonical_name": "PidTagViewDescriptorName", "property_id": "0x7006", "description": ""}, {"name": "ViewDescriptorStrings", "data_type": "0x001F", "area": "MessageClassDefinedTransmittable", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagViewDescriptorStrings", "property_id": "0x7002", "description": "Contains view definitions in string format."}, {"name": "ViewDescriptorVersion", "data_type": "0x0003", "area": "Miscellaneous Properties", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "defining_reference": "", "canonical_name": "PidTagViewDescriptorVersion", "property_id": "0x7007", "description": "Contains the View Descriptor version."}, {"name": "VoiceMessageAttachmentOrder", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagVoiceMessageAttachmentOrder", "property_id": "0x6805", "description": "Contains a list of file names for the audio file attachments that are to be played as part of a message."}, {"name": "VoiceMessageDuration", "data_type": "0x0003", "area": "Unified Messaging", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagVoiceMessageDuration", "property_id": "0x6801", "description": "Specifies the length of the attached audio message, in seconds."}, {"name": "VoiceMessageSenderName", "data_type": "0x001F", "area": "Unified Messaging", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagVoiceMessageSenderName", "property_id": "0x6803", "description": "Specifies the name of the caller who left the attached voice message, as provided by the voice network's caller ID system."}, {"name": "WeddingAnniversary", "data_type": "0x0040", "area": "MapiMailUser", "data_type_name": "PtypTime", "canonical_type": "PidTag", "canonical_name": "PidTagWeddingAnniversary", "property_id": "0x3A41", "description": "Contains the date of the mail user's wedding anniversary."}, {"name": "WlinkAddressBookEID", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkAddressBookEID", "property_id": "0x6854", "description": "Specifies the value of the PidTagEntryId property (section 2.677) of the user to whom the folder belongs."}, {"name": "WlinkAddressBookStoreEID", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkAddressBookStoreEID", "property_id": "0x6891", "description": "Specifies the value of the PidTagStoreEntryId property (section 2.1022) of the current user (not the owner of the folder)."}, {"name": "WlinkCalendarColor", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkCalendarColor", "property_id": "0x6853", "description": "Specifies the background color of the calendar."}, {"name": "WlinkClientID", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkClientID", "property_id": "0x6890", "description": "Specifies the Client ID that allows the client to determine whether the shortcut was created on the current machine/user via an equality test."}, {"name": "WlinkEntryId", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkEntryId", "property_id": "0x684C", "description": "Specifies the EntryID of the folder pointed to by the shortcut."}, {"name": "WlinkFlags", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkFlags", "property_id": "0x684A", "description": "Specifies conditions associated with the shortcut."}, {"name": "WlinkFolderType", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkFolderType", "property_id": "0x684F", "description": "Specifies the type of folder pointed to by the shortcut."}, {"name": "WlinkGroupClsid", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkGroupClsid", "property_id": "0x6850", "description": "Specifies the value of the PidTagWlinkGroupHeaderID property of the group header associated with the shortcut."}, {"name": "WlinkGroupHeaderID", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkGroupHeaderID", "property_id": "0x6842", "description": "Specifies the ID of the navigation shortcut that groups other navigation shortcuts."}, {"name": "WlinkGroupName", "data_type": "0x001F", "area": "Configuration", "data_type_name": "PtypString", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkGroupName", "property_id": "0x6851", "description": "Specifies the value of the PidTagNormalizedSubject (section 2.806) of the group header associated with the shortcut."}, {"name": "WlinkOrdinal", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkOrdinal", "property_id": "0x684B", "description": "Specifies a variable-length binary property to be used to sort shortcuts lexicographically."}, {"name": "WlinkRecordKey", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkRecordKey", "property_id": "0x684D", "description": "Specifies the value of PidTagRecordKey property (section 2.904) of the folder pointed to by the shortcut."}, {"name": "WlinkROGroupType", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkROGroupType", "property_id": "0x6892", "description": "Specifies the type of group header."}, {"name": "WlinkSaveStamp", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkSaveStamp", "property_id": "0x6847", "description": "Specifies an integer that allows a client to identify with a high probability whether the navigation shortcut was saved by the current client session."}, {"name": "WlinkSection", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkSection", "property_id": "0x6852", "description": "Specifies the section where the shortcut will be grouped."}, {"name": "WlinkStoreEntryId", "data_type": "0x0102", "area": "Configuration", "data_type_name": "PtypBinary", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkStoreEntryId", "property_id": "0x684E", "description": "Specifies the value of the PidTagStoreEntryId property (section 2.1022) of the folder pointed to by the shortcut."}, {"name": "WlinkType", "data_type": "0x0003", "area": "Configuration", "data_type_name": "PtypInteger32", "canonical_type": "PidTag", "canonical_name": "PidTagWlinkType", "property_id": "0x6849", "description": "Specifies the type of navigation shortcut."}], "list_name": "Exchange Server Protocols Master Property List", "reference": "https://msdn.microsoft.com/en-us/library/cc433490(v=exchg.80).aspx"}