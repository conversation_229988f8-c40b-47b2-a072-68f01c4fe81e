# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

from typing import TYPE_CHECKING

from msrest import Deserializer, Serializer

from azure.mgmt.core import ARMPipelineClient
from azure.profiles import KnownProfiles, ProfileDefinition
from azure.profiles.multiapiclient import MultiApiClientMixin

from ._configuration import AzureMachineLearningWorkspacesConfiguration

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from typing import Any, Optional

    from azure.core.credentials import TokenCredential

class _SDKClient(object):
    def __init__(self, *args, **kwargs):
        """This is a fake class to support current implemetation of MultiApiClientMixin."
        Will be removed in final version of multiapi azure-core based client
        """
        pass

class AzureMachineLearningWorkspaces(MultiApiClientMixin, _SDKClient):
    """These APIs allow end users to operate on Azure Machine Learning Workspace resources.

    This ready contains multiple API versions, to help you deal with all of the Azure clouds
    (Azure Stack, Azure Government, Azure China, etc.).
    By default, it uses the latest API version available on public Azure.
    For production, you should stick to a particular api-version and/or profile.
    The profile sets a mapping between an operation group and its API version.
    The api-version parameter sets the default API version if the operation
    group is not described in the profile.

    :param credential: Credential needed for the client to connect to Azure.
    :type credential: ~azure.core.credentials.TokenCredential
    :param subscription_id: The ID of the target subscription.
    :type subscription_id: str
    :param api_version: API version to use if no profile is provided, or if missing in profile.
    :type api_version: str
    :param base_url: Service URL
    :type base_url: str
    :param profile: A profile definition, from KnownProfiles to dict.
    :type profile: azure.profiles.KnownProfiles
    :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
    """

    DEFAULT_API_VERSION = '2022-10-01'
    _PROFILE_TAG = "azure.mgmt.machinelearningservices.AzureMachineLearningWorkspaces"
    LATEST_PROFILE = ProfileDefinition({
        _PROFILE_TAG: {
            None: DEFAULT_API_VERSION,
            'assets': '1.0.0',
            'async_operations': 'v1.0',
            'batch_job_deployment': '2020-09-01-dataplanepreview',
            'batch_job_endpoint': '2020-09-01-dataplanepreview',
            'data_call': '1.5.0',
            'data_container': '1.5.0',
            'data_references': '2021-10-01-dataplanepreview',
            'data_version': '1.5.0',
            'dataset_containers': '2021-10-01',
            'dataset_controller_v2': '1.5.0',
            'dataset_v2': '1.5.0',
            'dataset_versions': '2021-10-01',
            'datasets_v1': '1.5.0',
            'delete': 'v1.0',
            'events': 'v1.0',
            'experiments': 'v1.0',
            'extensive_model': '1.0.0',
            'get_operation_status': '1.5.0',
            'metric': 'v1.0',
            'migration': '1.0.0',
            'models': '1.0.0',
            'registry_management_non_workspace': 'v1.0',
            'resource_management_asset_reference': '2021-10-01-dataplanepreview',
            'run': 'v1.0',
            'run_artifacts': 'v1.0',
            'runs': 'v1.0',
            'spans': 'v1.0',
            'temporary_data_references': '2021-10-01-dataplanepreview',
        }},
        _PROFILE_TAG + " latest"
    )

    def __init__(
        self,
        credential,  # type: "TokenCredential"
        subscription_id,  # type: str
        api_version=None, # type: Optional[str]
        base_url="https://management.azure.com",  # type: str
        profile=KnownProfiles.default, # type: KnownProfiles
        **kwargs  # type: Any
    ):
        self._config = AzureMachineLearningWorkspacesConfiguration(credential, subscription_id, **kwargs)
        self._client = ARMPipelineClient(base_url=base_url, config=self._config, **kwargs)
        super(AzureMachineLearningWorkspaces, self).__init__(
            api_version=api_version,
            profile=profile
        )

    @classmethod
    def _models_dict(cls, api_version):
        return {k: v for k, v in cls.models(api_version).__dict__.items() if isinstance(v, type)}

    @classmethod
    def models(cls, api_version=DEFAULT_API_VERSION):
        """Module depends on the API version:

           * 1.5.0: :mod:`dataset_dataplane.models<azure.mgmt.machinelearningservices.dataset_dataplane.models>`
           * 1.0.0: :mod:`model_dataplane.models<azure.mgmt.machinelearningservices.model_dataplane.models>`
           * v1.0: :mod:`registry_discovery.models<azure.mgmt.machinelearningservices.registry_discovery.models>`
           * v1.0: :mod:`runhistory.models<azure.mgmt.machinelearningservices.runhistory.models>`
           * 2020-09-01-dataplanepreview: :mod:`v2020_09_01_dataplanepreview.models<azure.mgmt.machinelearningservices.v2020_09_01_dataplanepreview.models>`
           * 2021-10-01: :mod:`v2021_10_01.models<azure.mgmt.machinelearningservices.v2021_10_01.models>`
           * 2021-10-01-dataplanepreview: :mod:`v2021_10_01_dataplanepreview.models<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.models>`
           * 2022-01-01-preview: :mod:`v2022_01_01_preview.models<azure.mgmt.machinelearningservices.v2022_01_01_preview.models>`
           * 2022-02-01-preview: :mod:`v2022_02_01_preview.models<azure.mgmt.machinelearningservices.v2022_02_01_preview.models>`
           * 2022-05-01: :mod:`v2022_05_01.models<azure.mgmt.machinelearningservices.v2022_05_01.models>`
           * 2022-06-01-preview: :mod:`v2022_06_01_preview.models<azure.mgmt.machinelearningservices.v2022_06_01_preview.models>`
           * 2022-10-01: :mod:`v2022_10_01.models<azure.mgmt.machinelearningservices.v2022_10_01.models>`
           * 2022-10-01-preview: :mod:`v2022_10_01_preview.models<azure.mgmt.machinelearningservices.v2022_10_01_preview.models>`
        """
        if api_version == '1.5.0':
            from .dataset_dataplane import models
            return models
        elif api_version == '1.0.0':
            from .model_dataplane import models
            return models
        elif api_version == 'v1.0':
            from .registry_discovery import models
            return models
        elif api_version == 'v1.0':
            from .runhistory import models
            return models
        elif api_version == '2020-09-01-dataplanepreview':
            from .v2020_09_01_dataplanepreview import models
            return models
        elif api_version == '2021-10-01':
            from .v2021_10_01 import models
            return models
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview import models
            return models
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview import models
            return models
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview import models
            return models
        elif api_version == '2022-05-01':
            from .v2022_05_01 import models
            return models
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview import models
            return models
        elif api_version == '2022-10-01':
            from .v2022_10_01 import models
            return models
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview import models
            return models
        raise ValueError("API version {} is not available".format(api_version))

    @property
    def assets(self):
        """Instance depends on the API version:

           * 1.0.0: :class:`AssetsOperations<azure.mgmt.machinelearningservices.model_dataplane.operations.AssetsOperations>`
        """
        api_version = self._get_api_version('assets')
        if api_version == '1.0.0':
            from .model_dataplane.operations import AssetsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'assets'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def async_operations(self):
        """Instance depends on the API version:

           * v1.0: :class:`AsyncOperationsOperations<azure.mgmt.machinelearningservices.registry_discovery.operations.AsyncOperationsOperations>`
        """
        api_version = self._get_api_version('async_operations')
        if api_version == 'v1.0':
            from .registry_discovery.operations import AsyncOperationsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'async_operations'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def batch_deployments(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`BatchDeploymentsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.BatchDeploymentsOperations>`
           * 2022-02-01-preview: :class:`BatchDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.BatchDeploymentsOperations>`
           * 2022-05-01: :class:`BatchDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.BatchDeploymentsOperations>`
           * 2022-06-01-preview: :class:`BatchDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.BatchDeploymentsOperations>`
           * 2022-10-01: :class:`BatchDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.BatchDeploymentsOperations>`
           * 2022-10-01-preview: :class:`BatchDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.BatchDeploymentsOperations>`
        """
        api_version = self._get_api_version('batch_deployments')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import BatchDeploymentsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import BatchDeploymentsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import BatchDeploymentsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import BatchDeploymentsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import BatchDeploymentsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import BatchDeploymentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'batch_deployments'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def batch_endpoints(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`BatchEndpointsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.BatchEndpointsOperations>`
           * 2022-02-01-preview: :class:`BatchEndpointsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.BatchEndpointsOperations>`
           * 2022-05-01: :class:`BatchEndpointsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.BatchEndpointsOperations>`
           * 2022-06-01-preview: :class:`BatchEndpointsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.BatchEndpointsOperations>`
           * 2022-10-01: :class:`BatchEndpointsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.BatchEndpointsOperations>`
           * 2022-10-01-preview: :class:`BatchEndpointsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.BatchEndpointsOperations>`
        """
        api_version = self._get_api_version('batch_endpoints')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import BatchEndpointsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import BatchEndpointsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import BatchEndpointsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import BatchEndpointsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import BatchEndpointsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import BatchEndpointsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'batch_endpoints'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def batch_job_deployment(self):
        """Instance depends on the API version:

           * 2020-09-01-dataplanepreview: :class:`BatchJobDeploymentOperations<azure.mgmt.machinelearningservices.v2020_09_01_dataplanepreview.operations.BatchJobDeploymentOperations>`
        """
        api_version = self._get_api_version('batch_job_deployment')
        if api_version == '2020-09-01-dataplanepreview':
            from .v2020_09_01_dataplanepreview.operations import BatchJobDeploymentOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'batch_job_deployment'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def batch_job_endpoint(self):
        """Instance depends on the API version:

           * 2020-09-01-dataplanepreview: :class:`BatchJobEndpointOperations<azure.mgmt.machinelearningservices.v2020_09_01_dataplanepreview.operations.BatchJobEndpointOperations>`
        """
        api_version = self._get_api_version('batch_job_endpoint')
        if api_version == '2020-09-01-dataplanepreview':
            from .v2020_09_01_dataplanepreview.operations import BatchJobEndpointOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'batch_job_endpoint'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def code_containers(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.CodeContainersOperations>`
           * 2021-10-01-dataplanepreview: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.CodeContainersOperations>`
           * 2022-02-01-preview: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.CodeContainersOperations>`
           * 2022-05-01: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.CodeContainersOperations>`
           * 2022-06-01-preview: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.CodeContainersOperations>`
           * 2022-10-01: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.CodeContainersOperations>`
           * 2022-10-01-preview: :class:`CodeContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.CodeContainersOperations>`
        """
        api_version = self._get_api_version('code_containers')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import CodeContainersOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import CodeContainersOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import CodeContainersOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import CodeContainersOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import CodeContainersOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import CodeContainersOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import CodeContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'code_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def code_versions(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.CodeVersionsOperations>`
           * 2021-10-01-dataplanepreview: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.CodeVersionsOperations>`
           * 2022-02-01-preview: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.CodeVersionsOperations>`
           * 2022-05-01: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.CodeVersionsOperations>`
           * 2022-06-01-preview: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.CodeVersionsOperations>`
           * 2022-10-01: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.CodeVersionsOperations>`
           * 2022-10-01-preview: :class:`CodeVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.CodeVersionsOperations>`
        """
        api_version = self._get_api_version('code_versions')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import CodeVersionsOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import CodeVersionsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import CodeVersionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import CodeVersionsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import CodeVersionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import CodeVersionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import CodeVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'code_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def component_containers(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.ComponentContainersOperations>`
           * 2021-10-01-dataplanepreview: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.ComponentContainersOperations>`
           * 2022-02-01-preview: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.ComponentContainersOperations>`
           * 2022-05-01: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.ComponentContainersOperations>`
           * 2022-06-01-preview: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.ComponentContainersOperations>`
           * 2022-10-01: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.ComponentContainersOperations>`
           * 2022-10-01-preview: :class:`ComponentContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.ComponentContainersOperations>`
        """
        api_version = self._get_api_version('component_containers')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import ComponentContainersOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import ComponentContainersOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import ComponentContainersOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import ComponentContainersOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import ComponentContainersOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import ComponentContainersOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import ComponentContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'component_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def component_versions(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.ComponentVersionsOperations>`
           * 2021-10-01-dataplanepreview: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.ComponentVersionsOperations>`
           * 2022-02-01-preview: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.ComponentVersionsOperations>`
           * 2022-05-01: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.ComponentVersionsOperations>`
           * 2022-06-01-preview: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.ComponentVersionsOperations>`
           * 2022-10-01: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.ComponentVersionsOperations>`
           * 2022-10-01-preview: :class:`ComponentVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.ComponentVersionsOperations>`
        """
        api_version = self._get_api_version('component_versions')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import ComponentVersionsOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import ComponentVersionsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import ComponentVersionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import ComponentVersionsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import ComponentVersionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import ComponentVersionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import ComponentVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'component_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def compute(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`ComputeOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.ComputeOperations>`
           * 2022-01-01-preview: :class:`ComputeOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.ComputeOperations>`
           * 2022-05-01: :class:`ComputeOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.ComputeOperations>`
           * 2022-10-01: :class:`ComputeOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.ComputeOperations>`
           * 2022-10-01-preview: :class:`ComputeOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.ComputeOperations>`
        """
        api_version = self._get_api_version('compute')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import ComputeOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import ComputeOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import ComputeOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import ComputeOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import ComputeOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'compute'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def data_call(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DataCallOperations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DataCallOperations>`
        """
        api_version = self._get_api_version('data_call')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DataCallOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'data_call'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def data_container(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DataContainerOperations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DataContainerOperations>`
        """
        api_version = self._get_api_version('data_container')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DataContainerOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'data_container'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def data_containers(self):
        """Instance depends on the API version:

           * 2021-10-01-dataplanepreview: :class:`DataContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.DataContainersOperations>`
           * 2022-02-01-preview: :class:`DataContainersOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.DataContainersOperations>`
           * 2022-05-01: :class:`DataContainersOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.DataContainersOperations>`
           * 2022-06-01-preview: :class:`DataContainersOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.DataContainersOperations>`
           * 2022-10-01: :class:`DataContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.DataContainersOperations>`
           * 2022-10-01-preview: :class:`DataContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.DataContainersOperations>`
        """
        api_version = self._get_api_version('data_containers')
        if api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import DataContainersOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import DataContainersOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import DataContainersOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import DataContainersOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import DataContainersOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import DataContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'data_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def data_references(self):
        """Instance depends on the API version:

           * 2021-10-01-dataplanepreview: :class:`DataReferencesOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.DataReferencesOperations>`
        """
        api_version = self._get_api_version('data_references')
        if api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import DataReferencesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'data_references'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def data_version(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DataVersionOperations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DataVersionOperations>`
        """
        api_version = self._get_api_version('data_version')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DataVersionOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'data_version'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def data_versions(self):
        """Instance depends on the API version:

           * 2021-10-01-dataplanepreview: :class:`DataVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.DataVersionsOperations>`
           * 2022-02-01-preview: :class:`DataVersionsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.DataVersionsOperations>`
           * 2022-05-01: :class:`DataVersionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.DataVersionsOperations>`
           * 2022-06-01-preview: :class:`DataVersionsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.DataVersionsOperations>`
           * 2022-10-01: :class:`DataVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.DataVersionsOperations>`
           * 2022-10-01-preview: :class:`DataVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.DataVersionsOperations>`
        """
        api_version = self._get_api_version('data_versions')
        if api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import DataVersionsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import DataVersionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import DataVersionsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import DataVersionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import DataVersionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import DataVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'data_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def dataset_containers(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`DatasetContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.DatasetContainersOperations>`
        """
        api_version = self._get_api_version('dataset_containers')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import DatasetContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'dataset_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def dataset_controller_v2(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DatasetControllerV2Operations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DatasetControllerV2Operations>`
        """
        api_version = self._get_api_version('dataset_controller_v2')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DatasetControllerV2Operations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'dataset_controller_v2'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def dataset_v2(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DatasetV2Operations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DatasetV2Operations>`
        """
        api_version = self._get_api_version('dataset_v2')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DatasetV2Operations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'dataset_v2'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def dataset_versions(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`DatasetVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.DatasetVersionsOperations>`
        """
        api_version = self._get_api_version('dataset_versions')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import DatasetVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'dataset_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def datasets_v1(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DatasetsV1Operations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DatasetsV1Operations>`
        """
        api_version = self._get_api_version('datasets_v1')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DatasetsV1Operations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'datasets_v1'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def datastores(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`DatastoresOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.DatastoresOperations>`
           * 2022-02-01-preview: :class:`DatastoresOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.DatastoresOperations>`
           * 2022-05-01: :class:`DatastoresOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.DatastoresOperations>`
           * 2022-06-01-preview: :class:`DatastoresOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.DatastoresOperations>`
           * 2022-10-01: :class:`DatastoresOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.DatastoresOperations>`
           * 2022-10-01-preview: :class:`DatastoresOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.DatastoresOperations>`
        """
        api_version = self._get_api_version('datastores')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import DatastoresOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import DatastoresOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import DatastoresOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import DatastoresOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import DatastoresOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import DatastoresOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'datastores'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def delete(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`DeleteOperations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.DeleteOperations>`
           * v1.0: :class:`DeleteOperations<azure.mgmt.machinelearningservices.runhistory.operations.DeleteOperations>`
        """
        api_version = self._get_api_version('delete')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import DeleteOperations as OperationClass
        elif api_version == 'v1.0':
            from .runhistory.operations import DeleteOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'delete'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def environment_containers(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.EnvironmentContainersOperations>`
           * 2021-10-01-dataplanepreview: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.EnvironmentContainersOperations>`
           * 2022-02-01-preview: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.EnvironmentContainersOperations>`
           * 2022-05-01: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.EnvironmentContainersOperations>`
           * 2022-06-01-preview: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.EnvironmentContainersOperations>`
           * 2022-10-01: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.EnvironmentContainersOperations>`
           * 2022-10-01-preview: :class:`EnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.EnvironmentContainersOperations>`
        """
        api_version = self._get_api_version('environment_containers')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import EnvironmentContainersOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import EnvironmentContainersOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import EnvironmentContainersOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import EnvironmentContainersOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import EnvironmentContainersOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import EnvironmentContainersOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import EnvironmentContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'environment_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def environment_versions(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.EnvironmentVersionsOperations>`
           * 2021-10-01-dataplanepreview: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.EnvironmentVersionsOperations>`
           * 2022-02-01-preview: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.EnvironmentVersionsOperations>`
           * 2022-05-01: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.EnvironmentVersionsOperations>`
           * 2022-06-01-preview: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.EnvironmentVersionsOperations>`
           * 2022-10-01: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.EnvironmentVersionsOperations>`
           * 2022-10-01-preview: :class:`EnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.EnvironmentVersionsOperations>`
        """
        api_version = self._get_api_version('environment_versions')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import EnvironmentVersionsOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import EnvironmentVersionsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import EnvironmentVersionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import EnvironmentVersionsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import EnvironmentVersionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import EnvironmentVersionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import EnvironmentVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'environment_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def events(self):
        """Instance depends on the API version:

           * v1.0: :class:`EventsOperations<azure.mgmt.machinelearningservices.runhistory.operations.EventsOperations>`
        """
        api_version = self._get_api_version('events')
        if api_version == 'v1.0':
            from .runhistory.operations import EventsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'events'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def experiments(self):
        """Instance depends on the API version:

           * v1.0: :class:`ExperimentsOperations<azure.mgmt.machinelearningservices.runhistory.operations.ExperimentsOperations>`
        """
        api_version = self._get_api_version('experiments')
        if api_version == 'v1.0':
            from .runhistory.operations import ExperimentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'experiments'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def extensive_model(self):
        """Instance depends on the API version:

           * 1.0.0: :class:`ExtensiveModelOperations<azure.mgmt.machinelearningservices.model_dataplane.operations.ExtensiveModelOperations>`
        """
        api_version = self._get_api_version('extensive_model')
        if api_version == '1.0.0':
            from .model_dataplane.operations import ExtensiveModelOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'extensive_model'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def get_operation_status(self):
        """Instance depends on the API version:

           * 1.5.0: :class:`GetOperationStatusOperations<azure.mgmt.machinelearningservices.dataset_dataplane.operations.GetOperationStatusOperations>`
        """
        api_version = self._get_api_version('get_operation_status')
        if api_version == '1.5.0':
            from .dataset_dataplane.operations import GetOperationStatusOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'get_operation_status'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def jobs(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`JobsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.JobsOperations>`
           * 2022-02-01-preview: :class:`JobsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.JobsOperations>`
           * 2022-05-01: :class:`JobsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.JobsOperations>`
           * 2022-06-01-preview: :class:`JobsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.JobsOperations>`
           * 2022-10-01: :class:`JobsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.JobsOperations>`
           * 2022-10-01-preview: :class:`JobsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.JobsOperations>`
        """
        api_version = self._get_api_version('jobs')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import JobsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import JobsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import JobsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import JobsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import JobsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import JobsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'jobs'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def labeling_jobs(self):
        """Instance depends on the API version:

           * 2022-06-01-preview: :class:`LabelingJobsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.LabelingJobsOperations>`
           * 2022-10-01-preview: :class:`LabelingJobsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.LabelingJobsOperations>`
        """
        api_version = self._get_api_version('labeling_jobs')
        if api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import LabelingJobsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import LabelingJobsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'labeling_jobs'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def metric(self):
        """Instance depends on the API version:

           * v1.0: :class:`MetricOperations<azure.mgmt.machinelearningservices.runhistory.operations.MetricOperations>`
        """
        api_version = self._get_api_version('metric')
        if api_version == 'v1.0':
            from .runhistory.operations import MetricOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'metric'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def migration(self):
        """Instance depends on the API version:

           * 1.0.0: :class:`MigrationOperations<azure.mgmt.machinelearningservices.model_dataplane.operations.MigrationOperations>`
        """
        api_version = self._get_api_version('migration')
        if api_version == '1.0.0':
            from .model_dataplane.operations import MigrationOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'migration'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def model_containers(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.ModelContainersOperations>`
           * 2021-10-01-dataplanepreview: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.ModelContainersOperations>`
           * 2022-02-01-preview: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.ModelContainersOperations>`
           * 2022-05-01: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.ModelContainersOperations>`
           * 2022-06-01-preview: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.ModelContainersOperations>`
           * 2022-10-01: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.ModelContainersOperations>`
           * 2022-10-01-preview: :class:`ModelContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.ModelContainersOperations>`
        """
        api_version = self._get_api_version('model_containers')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import ModelContainersOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import ModelContainersOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import ModelContainersOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import ModelContainersOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import ModelContainersOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import ModelContainersOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import ModelContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'model_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def model_versions(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.ModelVersionsOperations>`
           * 2021-10-01-dataplanepreview: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.ModelVersionsOperations>`
           * 2022-02-01-preview: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.ModelVersionsOperations>`
           * 2022-05-01: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.ModelVersionsOperations>`
           * 2022-06-01-preview: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.ModelVersionsOperations>`
           * 2022-10-01: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.ModelVersionsOperations>`
           * 2022-10-01-preview: :class:`ModelVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.ModelVersionsOperations>`
        """
        api_version = self._get_api_version('model_versions')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import ModelVersionsOperations as OperationClass
        elif api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import ModelVersionsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import ModelVersionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import ModelVersionsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import ModelVersionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import ModelVersionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import ModelVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'model_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def models(self):
        """Instance depends on the API version:

           * 1.0.0: :class:`ModelsOperations<azure.mgmt.machinelearningservices.model_dataplane.operations.ModelsOperations>`
        """
        api_version = self._get_api_version('models')
        if api_version == '1.0.0':
            from .model_dataplane.operations import ModelsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'models'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def online_deployments(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`OnlineDeploymentsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.OnlineDeploymentsOperations>`
           * 2022-02-01-preview: :class:`OnlineDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.OnlineDeploymentsOperations>`
           * 2022-05-01: :class:`OnlineDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.OnlineDeploymentsOperations>`
           * 2022-06-01-preview: :class:`OnlineDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.OnlineDeploymentsOperations>`
           * 2022-10-01: :class:`OnlineDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.OnlineDeploymentsOperations>`
           * 2022-10-01-preview: :class:`OnlineDeploymentsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.OnlineDeploymentsOperations>`
        """
        api_version = self._get_api_version('online_deployments')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import OnlineDeploymentsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import OnlineDeploymentsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import OnlineDeploymentsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import OnlineDeploymentsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import OnlineDeploymentsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import OnlineDeploymentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'online_deployments'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def online_endpoints(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`OnlineEndpointsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.OnlineEndpointsOperations>`
           * 2022-02-01-preview: :class:`OnlineEndpointsOperations<azure.mgmt.machinelearningservices.v2022_02_01_preview.operations.OnlineEndpointsOperations>`
           * 2022-05-01: :class:`OnlineEndpointsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.OnlineEndpointsOperations>`
           * 2022-06-01-preview: :class:`OnlineEndpointsOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.OnlineEndpointsOperations>`
           * 2022-10-01: :class:`OnlineEndpointsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.OnlineEndpointsOperations>`
           * 2022-10-01-preview: :class:`OnlineEndpointsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.OnlineEndpointsOperations>`
        """
        api_version = self._get_api_version('online_endpoints')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import OnlineEndpointsOperations as OperationClass
        elif api_version == '2022-02-01-preview':
            from .v2022_02_01_preview.operations import OnlineEndpointsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import OnlineEndpointsOperations as OperationClass
        elif api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import OnlineEndpointsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import OnlineEndpointsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import OnlineEndpointsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'online_endpoints'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def operations(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`Operations<azure.mgmt.machinelearningservices.v2021_10_01.operations.Operations>`
           * 2022-01-01-preview: :class:`Operations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.Operations>`
           * 2022-05-01: :class:`Operations<azure.mgmt.machinelearningservices.v2022_05_01.operations.Operations>`
           * 2022-10-01: :class:`Operations<azure.mgmt.machinelearningservices.v2022_10_01.operations.Operations>`
           * 2022-10-01-preview: :class:`Operations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.Operations>`
        """
        api_version = self._get_api_version('operations')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import Operations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import Operations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import Operations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import Operations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import Operations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'operations'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def private_endpoint_connections(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`PrivateEndpointConnectionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.PrivateEndpointConnectionsOperations>`
           * 2022-01-01-preview: :class:`PrivateEndpointConnectionsOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.PrivateEndpointConnectionsOperations>`
           * 2022-05-01: :class:`PrivateEndpointConnectionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.PrivateEndpointConnectionsOperations>`
           * 2022-10-01: :class:`PrivateEndpointConnectionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.PrivateEndpointConnectionsOperations>`
           * 2022-10-01-preview: :class:`PrivateEndpointConnectionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.PrivateEndpointConnectionsOperations>`
        """
        api_version = self._get_api_version('private_endpoint_connections')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import PrivateEndpointConnectionsOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import PrivateEndpointConnectionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import PrivateEndpointConnectionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import PrivateEndpointConnectionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import PrivateEndpointConnectionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'private_endpoint_connections'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def private_link_resources(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`PrivateLinkResourcesOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.PrivateLinkResourcesOperations>`
           * 2022-01-01-preview: :class:`PrivateLinkResourcesOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.PrivateLinkResourcesOperations>`
           * 2022-05-01: :class:`PrivateLinkResourcesOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.PrivateLinkResourcesOperations>`
           * 2022-10-01: :class:`PrivateLinkResourcesOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.PrivateLinkResourcesOperations>`
           * 2022-10-01-preview: :class:`PrivateLinkResourcesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.PrivateLinkResourcesOperations>`
        """
        api_version = self._get_api_version('private_link_resources')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import PrivateLinkResourcesOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import PrivateLinkResourcesOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import PrivateLinkResourcesOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import PrivateLinkResourcesOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import PrivateLinkResourcesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'private_link_resources'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def quotas(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`QuotasOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.QuotasOperations>`
           * 2022-01-01-preview: :class:`QuotasOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.QuotasOperations>`
           * 2022-05-01: :class:`QuotasOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.QuotasOperations>`
           * 2022-10-01: :class:`QuotasOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.QuotasOperations>`
           * 2022-10-01-preview: :class:`QuotasOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.QuotasOperations>`
        """
        api_version = self._get_api_version('quotas')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import QuotasOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import QuotasOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import QuotasOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import QuotasOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import QuotasOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'quotas'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registries(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistriesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistriesOperations>`
        """
        api_version = self._get_api_version('registries')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistriesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registries'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_code_containers(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryCodeContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryCodeContainersOperations>`
        """
        api_version = self._get_api_version('registry_code_containers')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryCodeContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_code_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_code_versions(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryCodeVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryCodeVersionsOperations>`
        """
        api_version = self._get_api_version('registry_code_versions')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryCodeVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_code_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_component_containers(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryComponentContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryComponentContainersOperations>`
        """
        api_version = self._get_api_version('registry_component_containers')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryComponentContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_component_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_component_versions(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryComponentVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryComponentVersionsOperations>`
        """
        api_version = self._get_api_version('registry_component_versions')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryComponentVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_component_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_environment_containers(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryEnvironmentContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryEnvironmentContainersOperations>`
        """
        api_version = self._get_api_version('registry_environment_containers')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryEnvironmentContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_environment_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_environment_versions(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryEnvironmentVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryEnvironmentVersionsOperations>`
        """
        api_version = self._get_api_version('registry_environment_versions')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryEnvironmentVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_environment_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_management_non_workspace(self):
        """Instance depends on the API version:

           * v1.0: :class:`RegistryManagementNonWorkspaceOperations<azure.mgmt.machinelearningservices.registry_discovery.operations.RegistryManagementNonWorkspaceOperations>`
        """
        api_version = self._get_api_version('registry_management_non_workspace')
        if api_version == 'v1.0':
            from .registry_discovery.operations import RegistryManagementNonWorkspaceOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_management_non_workspace'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_model_containers(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryModelContainersOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryModelContainersOperations>`
        """
        api_version = self._get_api_version('registry_model_containers')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryModelContainersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_model_containers'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def registry_model_versions(self):
        """Instance depends on the API version:

           * 2022-10-01-preview: :class:`RegistryModelVersionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.RegistryModelVersionsOperations>`
        """
        api_version = self._get_api_version('registry_model_versions')
        if api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import RegistryModelVersionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'registry_model_versions'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def resource_management_asset_reference(self):
        """Instance depends on the API version:

           * 2021-10-01-dataplanepreview: :class:`ResourceManagementAssetReferenceOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.ResourceManagementAssetReferenceOperations>`
        """
        api_version = self._get_api_version('resource_management_asset_reference')
        if api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import ResourceManagementAssetReferenceOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'resource_management_asset_reference'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def run(self):
        """Instance depends on the API version:

           * v1.0: :class:`RunOperations<azure.mgmt.machinelearningservices.runhistory.operations.RunOperations>`
        """
        api_version = self._get_api_version('run')
        if api_version == 'v1.0':
            from .runhistory.operations import RunOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'run'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def run_artifacts(self):
        """Instance depends on the API version:

           * v1.0: :class:`RunArtifactsOperations<azure.mgmt.machinelearningservices.runhistory.operations.RunArtifactsOperations>`
        """
        api_version = self._get_api_version('run_artifacts')
        if api_version == 'v1.0':
            from .runhistory.operations import RunArtifactsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'run_artifacts'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def runs(self):
        """Instance depends on the API version:

           * v1.0: :class:`RunsOperations<azure.mgmt.machinelearningservices.runhistory.operations.RunsOperations>`
        """
        api_version = self._get_api_version('runs')
        if api_version == 'v1.0':
            from .runhistory.operations import RunsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'runs'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def schedules(self):
        """Instance depends on the API version:

           * 2022-06-01-preview: :class:`SchedulesOperations<azure.mgmt.machinelearningservices.v2022_06_01_preview.operations.SchedulesOperations>`
           * 2022-10-01: :class:`SchedulesOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.SchedulesOperations>`
           * 2022-10-01-preview: :class:`SchedulesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.SchedulesOperations>`
        """
        api_version = self._get_api_version('schedules')
        if api_version == '2022-06-01-preview':
            from .v2022_06_01_preview.operations import SchedulesOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import SchedulesOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import SchedulesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'schedules'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def spans(self):
        """Instance depends on the API version:

           * v1.0: :class:`SpansOperations<azure.mgmt.machinelearningservices.runhistory.operations.SpansOperations>`
        """
        api_version = self._get_api_version('spans')
        if api_version == 'v1.0':
            from .runhistory.operations import SpansOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'spans'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def temporary_data_references(self):
        """Instance depends on the API version:

           * 2021-10-01-dataplanepreview: :class:`TemporaryDataReferencesOperations<azure.mgmt.machinelearningservices.v2021_10_01_dataplanepreview.operations.TemporaryDataReferencesOperations>`
        """
        api_version = self._get_api_version('temporary_data_references')
        if api_version == '2021-10-01-dataplanepreview':
            from .v2021_10_01_dataplanepreview.operations import TemporaryDataReferencesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'temporary_data_references'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def usages(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`UsagesOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.UsagesOperations>`
           * 2022-01-01-preview: :class:`UsagesOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.UsagesOperations>`
           * 2022-05-01: :class:`UsagesOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.UsagesOperations>`
           * 2022-10-01: :class:`UsagesOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.UsagesOperations>`
           * 2022-10-01-preview: :class:`UsagesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.UsagesOperations>`
        """
        api_version = self._get_api_version('usages')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import UsagesOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import UsagesOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import UsagesOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import UsagesOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import UsagesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'usages'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def virtual_machine_sizes(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`VirtualMachineSizesOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.VirtualMachineSizesOperations>`
           * 2022-01-01-preview: :class:`VirtualMachineSizesOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.VirtualMachineSizesOperations>`
           * 2022-05-01: :class:`VirtualMachineSizesOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.VirtualMachineSizesOperations>`
           * 2022-10-01: :class:`VirtualMachineSizesOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.VirtualMachineSizesOperations>`
           * 2022-10-01-preview: :class:`VirtualMachineSizesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.VirtualMachineSizesOperations>`
        """
        api_version = self._get_api_version('virtual_machine_sizes')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import VirtualMachineSizesOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import VirtualMachineSizesOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import VirtualMachineSizesOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import VirtualMachineSizesOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import VirtualMachineSizesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'virtual_machine_sizes'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def workspace_connections(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`WorkspaceConnectionsOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.WorkspaceConnectionsOperations>`
           * 2022-01-01-preview: :class:`WorkspaceConnectionsOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.WorkspaceConnectionsOperations>`
           * 2022-05-01: :class:`WorkspaceConnectionsOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.WorkspaceConnectionsOperations>`
           * 2022-10-01: :class:`WorkspaceConnectionsOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.WorkspaceConnectionsOperations>`
           * 2022-10-01-preview: :class:`WorkspaceConnectionsOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.WorkspaceConnectionsOperations>`
        """
        api_version = self._get_api_version('workspace_connections')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import WorkspaceConnectionsOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import WorkspaceConnectionsOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import WorkspaceConnectionsOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import WorkspaceConnectionsOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import WorkspaceConnectionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'workspace_connections'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def workspace_features(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`WorkspaceFeaturesOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.WorkspaceFeaturesOperations>`
           * 2022-01-01-preview: :class:`WorkspaceFeaturesOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.WorkspaceFeaturesOperations>`
           * 2022-05-01: :class:`WorkspaceFeaturesOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.WorkspaceFeaturesOperations>`
           * 2022-10-01: :class:`WorkspaceFeaturesOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.WorkspaceFeaturesOperations>`
           * 2022-10-01-preview: :class:`WorkspaceFeaturesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.WorkspaceFeaturesOperations>`
        """
        api_version = self._get_api_version('workspace_features')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import WorkspaceFeaturesOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import WorkspaceFeaturesOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import WorkspaceFeaturesOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import WorkspaceFeaturesOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import WorkspaceFeaturesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'workspace_features'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    @property
    def workspaces(self):
        """Instance depends on the API version:

           * 2021-10-01: :class:`WorkspacesOperations<azure.mgmt.machinelearningservices.v2021_10_01.operations.WorkspacesOperations>`
           * 2022-01-01-preview: :class:`WorkspacesOperations<azure.mgmt.machinelearningservices.v2022_01_01_preview.operations.WorkspacesOperations>`
           * 2022-05-01: :class:`WorkspacesOperations<azure.mgmt.machinelearningservices.v2022_05_01.operations.WorkspacesOperations>`
           * 2022-10-01: :class:`WorkspacesOperations<azure.mgmt.machinelearningservices.v2022_10_01.operations.WorkspacesOperations>`
           * 2022-10-01-preview: :class:`WorkspacesOperations<azure.mgmt.machinelearningservices.v2022_10_01_preview.operations.WorkspacesOperations>`
        """
        api_version = self._get_api_version('workspaces')
        if api_version == '2021-10-01':
            from .v2021_10_01.operations import WorkspacesOperations as OperationClass
        elif api_version == '2022-01-01-preview':
            from .v2022_01_01_preview.operations import WorkspacesOperations as OperationClass
        elif api_version == '2022-05-01':
            from .v2022_05_01.operations import WorkspacesOperations as OperationClass
        elif api_version == '2022-10-01':
            from .v2022_10_01.operations import WorkspacesOperations as OperationClass
        elif api_version == '2022-10-01-preview':
            from .v2022_10_01_preview.operations import WorkspacesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'workspaces'".format(api_version))
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)))

    def close(self):
        self._client.close()
    def __enter__(self):
        self._client.__enter__()
        return self
    def __exit__(self, *exc_details):
        self._client.__exit__(*exc_details)
