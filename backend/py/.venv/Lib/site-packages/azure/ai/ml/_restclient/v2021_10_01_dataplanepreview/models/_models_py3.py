# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import datetime
from typing import Any, Dict, List, Optional, Union

from azure.core.exceptions import HttpResponseError
import msrest.serialization

from ._azure_machine_learning_workspaces_enums import *


class DatastoreCredentials(msrest.serialization.Model):
    """Base definition for datastore credentials.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: AccountKeyDatastoreCredentials, CertificateDatastoreCredentials, NoneDatastoreCredentials, SasDatastoreCredentials, ServicePrincipalDatastoreCredentials.

    All required parameters must be populated in order to send to Azure.

    :ivar credentials_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "None", "Sas",
     "ServicePrincipal".
    :vartype credentials_type: str or ~azure.mgmt.machinelearningservices.models.CredentialsType
    """

    _validation = {
        "credentials_type": {"required": True},
    }

    _attribute_map = {
        "credentials_type": {"key": "credentialsType", "type": "str"},
    }

    _subtype_map = {
        "credentials_type": {
            "AccountKey": "AccountKeyDatastoreCredentials",
            "Certificate": "CertificateDatastoreCredentials",
            "None": "NoneDatastoreCredentials",
            "Sas": "SasDatastoreCredentials",
            "ServicePrincipal": "ServicePrincipalDatastoreCredentials",
        }
    }

    def __init__(self, **kwargs):
        """ """
        super(DatastoreCredentials, self).__init__(**kwargs)
        self.credentials_type = None  # type: Optional[str]


class AccountKeyDatastoreCredentials(DatastoreCredentials):
    """Account key datastore credentials configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar credentials_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "None", "Sas",
     "ServicePrincipal".
    :vartype credentials_type: str or ~azure.mgmt.machinelearningservices.models.CredentialsType
    :ivar secrets: Required. Storage account secrets.
    :vartype secrets: ~azure.mgmt.machinelearningservices.models.AccountKeyDatastoreSecrets
    """

    _validation = {
        "credentials_type": {"required": True},
        "secrets": {"required": True},
    }

    _attribute_map = {
        "credentials_type": {"key": "credentialsType", "type": "str"},
        "secrets": {"key": "secrets", "type": "AccountKeyDatastoreSecrets"},
    }

    def __init__(self, *, secrets: "AccountKeyDatastoreSecrets", **kwargs):
        """
        :keyword secrets: Required. Storage account secrets.
        :paramtype secrets: ~azure.mgmt.machinelearningservices.models.AccountKeyDatastoreSecrets
        """
        super(AccountKeyDatastoreCredentials, self).__init__(**kwargs)
        self.credentials_type = "AccountKey"  # type: str
        self.secrets = secrets


class DatastoreSecrets(msrest.serialization.Model):
    """Base definition for datastore secrets.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: AccountKeyDatastoreSecrets, CertificateDatastoreSecrets, SasDatastoreSecrets, ServicePrincipalDatastoreSecrets.

    All required parameters must be populated in order to send to Azure.

    :ivar secrets_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "Sas",
     "ServicePrincipal".
    :vartype secrets_type: str or ~azure.mgmt.machinelearningservices.models.SecretsType
    """

    _validation = {
        "secrets_type": {"required": True},
    }

    _attribute_map = {
        "secrets_type": {"key": "secretsType", "type": "str"},
    }

    _subtype_map = {
        "secrets_type": {
            "AccountKey": "AccountKeyDatastoreSecrets",
            "Certificate": "CertificateDatastoreSecrets",
            "Sas": "SasDatastoreSecrets",
            "ServicePrincipal": "ServicePrincipalDatastoreSecrets",
        }
    }

    def __init__(self, **kwargs):
        """ """
        super(DatastoreSecrets, self).__init__(**kwargs)
        self.secrets_type = None  # type: Optional[str]


class AccountKeyDatastoreSecrets(DatastoreSecrets):
    """Datastore account key secrets.

    All required parameters must be populated in order to send to Azure.

    :ivar secrets_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "Sas",
     "ServicePrincipal".
    :vartype secrets_type: str or ~azure.mgmt.machinelearningservices.models.SecretsType
    :ivar key: Storage account key.
    :vartype key: str
    """

    _validation = {
        "secrets_type": {"required": True},
    }

    _attribute_map = {
        "secrets_type": {"key": "secretsType", "type": "str"},
        "key": {"key": "key", "type": "str"},
    }

    def __init__(self, *, key: Optional[str] = None, **kwargs):
        """
        :keyword key: Storage account key.
        :paramtype key: str
        """
        super(AccountKeyDatastoreSecrets, self).__init__(**kwargs)
        self.secrets_type = "AccountKey"  # type: str
        self.key = key


class AcrDetail(msrest.serialization.Model):
    """AcrDetail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar acr_address:
    :vartype acr_address: str
    :ivar acr_name:
    :vartype acr_name: str
    :ivar acr_region:
    :vartype acr_region: str
    :ivar arm_scope:
    :vartype arm_scope: str
    :ivar resource_group_name:
    :vartype resource_group_name: str
    :ivar subscription_id:
    :vartype subscription_id: str
    """

    _validation = {
        "arm_scope": {"readonly": True},
    }

    _attribute_map = {
        "acr_address": {"key": "acrAddress", "type": "str"},
        "acr_name": {"key": "acrName", "type": "str"},
        "acr_region": {"key": "acrRegion", "type": "str"},
        "arm_scope": {"key": "armScope", "type": "str"},
        "resource_group_name": {"key": "resourceGroupName", "type": "str"},
        "subscription_id": {"key": "subscriptionId", "type": "str"},
    }

    def __init__(
        self,
        *,
        acr_address: Optional[str] = None,
        acr_name: Optional[str] = None,
        acr_region: Optional[str] = None,
        resource_group_name: Optional[str] = None,
        subscription_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword acr_address:
        :paramtype acr_address: str
        :keyword acr_name:
        :paramtype acr_name: str
        :keyword acr_region:
        :paramtype acr_region: str
        :keyword resource_group_name:
        :paramtype resource_group_name: str
        :keyword subscription_id:
        :paramtype subscription_id: str
        """
        super(AcrDetail, self).__init__(**kwargs)
        self.acr_address = acr_address
        self.acr_name = acr_name
        self.acr_region = acr_region
        self.arm_scope = None
        self.resource_group_name = resource_group_name
        self.subscription_id = subscription_id


class IdentityConfiguration(msrest.serialization.Model):
    """Base definition for identity configuration.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: AmlToken, ManagedIdentity.

    All required parameters must be populated in order to send to Azure.

    :ivar identity_type: Required. Specifies the type of identity framework.Constant filled by
     server. Possible values include: "Managed", "AMLToken".
    :vartype identity_type: str or
     ~azure.mgmt.machinelearningservices.models.IdentityConfigurationType
    """

    _validation = {
        "identity_type": {"required": True},
    }

    _attribute_map = {
        "identity_type": {"key": "identityType", "type": "str"},
    }

    _subtype_map = {"identity_type": {"AMLToken": "AmlToken", "Managed": "ManagedIdentity"}}

    def __init__(self, **kwargs):
        """ """
        super(IdentityConfiguration, self).__init__(**kwargs)
        self.identity_type = None  # type: Optional[str]


class AmlToken(IdentityConfiguration):
    """AML Token identity configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar identity_type: Required. Specifies the type of identity framework.Constant filled by
     server. Possible values include: "Managed", "AMLToken".
    :vartype identity_type: str or
     ~azure.mgmt.machinelearningservices.models.IdentityConfigurationType
    """

    _validation = {
        "identity_type": {"required": True},
    }

    _attribute_map = {
        "identity_type": {"key": "identityType", "type": "str"},
    }

    def __init__(self, **kwargs):
        """ """
        super(AmlToken, self).__init__(**kwargs)
        self.identity_type = "AMLToken"  # type: str


class ResourceBase(msrest.serialization.Model):
    """ResourceBase.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    """

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        """
        super(ResourceBase, self).__init__(**kwargs)
        self.description = description
        self.properties = properties
        self.tags = tags


class AssetBase(ResourceBase):
    """AssetBase.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    """

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        """
        super(AssetBase, self).__init__(description=description, properties=properties, tags=tags, **kwargs)
        self.is_anonymous = is_anonymous
        self.is_archived = is_archived


class AssetContainer(ResourceBase):
    """AssetContainer.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar latest_version: The latest version inside this container.
    :vartype latest_version: str
    :ivar next_version: The next auto incremental version.
    :vartype next_version: str
    """

    _validation = {
        "latest_version": {"readonly": True},
        "next_version": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "latest_version": {"key": "latestVersion", "type": "str"},
        "next_version": {"key": "nextVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        """
        super(AssetContainer, self).__init__(description=description, properties=properties, tags=tags, **kwargs)
        self.is_archived = is_archived
        self.latest_version = None
        self.next_version = None


class AssetReferenceBase(msrest.serialization.Model):
    """Base definition for asset references.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: DataPathAssetReference, IdAssetReference, OutputPathAssetReference.

    All required parameters must be populated in order to send to Azure.

    :ivar reference_type: Required. Specifies the type of asset reference.Constant filled by
     server. Possible values include: "Id", "DataPath", "OutputPath".
    :vartype reference_type: str or ~azure.mgmt.machinelearningservices.models.ReferenceType
    """

    _validation = {
        "reference_type": {"required": True},
    }

    _attribute_map = {
        "reference_type": {"key": "referenceType", "type": "str"},
    }

    _subtype_map = {
        "reference_type": {
            "DataPath": "DataPathAssetReference",
            "Id": "IdAssetReference",
            "OutputPath": "OutputPathAssetReference",
        }
    }

    def __init__(self, **kwargs):
        """ """
        super(AssetReferenceBase, self).__init__(**kwargs)
        self.reference_type = None  # type: Optional[str]


class Datastore(ResourceBase):
    """Base definition for datastore contents configuration.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: AzureBlobDatastore, AzureDataLakeGen1Datastore, AzureDataLakeGen2Datastore, AzureFileDatastore.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar credentials: Required. Account credentials.
    :vartype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
    :ivar datastore_type: Required. Storage type backing the datastore.Constant filled by server.
     Possible values include: "AzureBlob", "AzureDataLakeGen1", "AzureDataLakeGen2", "AzureFile".
    :vartype datastore_type: str or ~azure.mgmt.machinelearningservices.models.DatastoreType
    :ivar is_default: Readonly property to indicate if datastore is the workspace default
     datastore.
    :vartype is_default: bool
    """

    _validation = {
        "credentials": {"required": True},
        "datastore_type": {"required": True},
        "is_default": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "credentials": {"key": "credentials", "type": "DatastoreCredentials"},
        "datastore_type": {"key": "datastoreType", "type": "str"},
        "is_default": {"key": "isDefault", "type": "bool"},
    }

    _subtype_map = {
        "datastore_type": {
            "AzureBlob": "AzureBlobDatastore",
            "AzureDataLakeGen1": "AzureDataLakeGen1Datastore",
            "AzureDataLakeGen2": "AzureDataLakeGen2Datastore",
            "AzureFile": "AzureFileDatastore",
        }
    }

    def __init__(
        self,
        *,
        credentials: "DatastoreCredentials",
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword credentials: Required. Account credentials.
        :paramtype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
        """
        super(Datastore, self).__init__(description=description, properties=properties, tags=tags, **kwargs)
        self.credentials = credentials
        self.datastore_type = "Datastore"  # type: str
        self.is_default = None


class AzureBlobDatastore(Datastore):
    """Azure Blob datastore configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar credentials: Required. Account credentials.
    :vartype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
    :ivar datastore_type: Required. Storage type backing the datastore.Constant filled by server.
     Possible values include: "AzureBlob", "AzureDataLakeGen1", "AzureDataLakeGen2", "AzureFile".
    :vartype datastore_type: str or ~azure.mgmt.machinelearningservices.models.DatastoreType
    :ivar is_default: Readonly property to indicate if datastore is the workspace default
     datastore.
    :vartype is_default: bool
    :ivar account_name: Storage account name.
    :vartype account_name: str
    :ivar container_name: Storage account container name.
    :vartype container_name: str
    :ivar endpoint: Azure cloud endpoint for the storage account.
    :vartype endpoint: str
    :ivar protocol: Protocol used to communicate with the storage account.
    :vartype protocol: str
    :ivar service_data_access_auth_identity: Indicates which identity to use to authenticate
     service data access to customer's storage. Possible values include: "None",
     "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
    :vartype service_data_access_auth_identity: str or
     ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
    """

    _validation = {
        "credentials": {"required": True},
        "datastore_type": {"required": True},
        "is_default": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "credentials": {"key": "credentials", "type": "DatastoreCredentials"},
        "datastore_type": {"key": "datastoreType", "type": "str"},
        "is_default": {"key": "isDefault", "type": "bool"},
        "account_name": {"key": "accountName", "type": "str"},
        "container_name": {"key": "containerName", "type": "str"},
        "endpoint": {"key": "endpoint", "type": "str"},
        "protocol": {"key": "protocol", "type": "str"},
        "service_data_access_auth_identity": {"key": "serviceDataAccessAuthIdentity", "type": "str"},
    }

    def __init__(
        self,
        *,
        credentials: "DatastoreCredentials",
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        account_name: Optional[str] = None,
        container_name: Optional[str] = None,
        endpoint: Optional[str] = None,
        protocol: Optional[str] = None,
        service_data_access_auth_identity: Optional[Union[str, "ServiceDataAccessAuthIdentity"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword credentials: Required. Account credentials.
        :paramtype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
        :keyword account_name: Storage account name.
        :paramtype account_name: str
        :keyword container_name: Storage account container name.
        :paramtype container_name: str
        :keyword endpoint: Azure cloud endpoint for the storage account.
        :paramtype endpoint: str
        :keyword protocol: Protocol used to communicate with the storage account.
        :paramtype protocol: str
        :keyword service_data_access_auth_identity: Indicates which identity to use to authenticate
         service data access to customer's storage. Possible values include: "None",
         "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
        :paramtype service_data_access_auth_identity: str or
         ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
        """
        super(AzureBlobDatastore, self).__init__(
            description=description, properties=properties, tags=tags, credentials=credentials, **kwargs
        )
        self.datastore_type = "AzureBlob"  # type: str
        self.account_name = account_name
        self.container_name = container_name
        self.endpoint = endpoint
        self.protocol = protocol
        self.service_data_access_auth_identity = service_data_access_auth_identity


class AzureDataLakeGen1Datastore(Datastore):
    """Azure Data Lake Gen1 datastore configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar credentials: Required. Account credentials.
    :vartype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
    :ivar datastore_type: Required. Storage type backing the datastore.Constant filled by server.
     Possible values include: "AzureBlob", "AzureDataLakeGen1", "AzureDataLakeGen2", "AzureFile".
    :vartype datastore_type: str or ~azure.mgmt.machinelearningservices.models.DatastoreType
    :ivar is_default: Readonly property to indicate if datastore is the workspace default
     datastore.
    :vartype is_default: bool
    :ivar service_data_access_auth_identity: Indicates which identity to use to authenticate
     service data access to customer's storage. Possible values include: "None",
     "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
    :vartype service_data_access_auth_identity: str or
     ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
    :ivar store_name: Required. Azure Data Lake store name.
    :vartype store_name: str
    """

    _validation = {
        "credentials": {"required": True},
        "datastore_type": {"required": True},
        "is_default": {"readonly": True},
        "store_name": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "credentials": {"key": "credentials", "type": "DatastoreCredentials"},
        "datastore_type": {"key": "datastoreType", "type": "str"},
        "is_default": {"key": "isDefault", "type": "bool"},
        "service_data_access_auth_identity": {"key": "serviceDataAccessAuthIdentity", "type": "str"},
        "store_name": {"key": "storeName", "type": "str"},
    }

    def __init__(
        self,
        *,
        credentials: "DatastoreCredentials",
        store_name: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        service_data_access_auth_identity: Optional[Union[str, "ServiceDataAccessAuthIdentity"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword credentials: Required. Account credentials.
        :paramtype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
        :keyword service_data_access_auth_identity: Indicates which identity to use to authenticate
         service data access to customer's storage. Possible values include: "None",
         "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
        :paramtype service_data_access_auth_identity: str or
         ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
        :keyword store_name: Required. Azure Data Lake store name.
        :paramtype store_name: str
        """
        super(AzureDataLakeGen1Datastore, self).__init__(
            description=description, properties=properties, tags=tags, credentials=credentials, **kwargs
        )
        self.datastore_type = "AzureDataLakeGen1"  # type: str
        self.service_data_access_auth_identity = service_data_access_auth_identity
        self.store_name = store_name


class AzureDataLakeGen2Datastore(Datastore):
    """Azure Data Lake Gen2 datastore configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar credentials: Required. Account credentials.
    :vartype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
    :ivar datastore_type: Required. Storage type backing the datastore.Constant filled by server.
     Possible values include: "AzureBlob", "AzureDataLakeGen1", "AzureDataLakeGen2", "AzureFile".
    :vartype datastore_type: str or ~azure.mgmt.machinelearningservices.models.DatastoreType
    :ivar is_default: Readonly property to indicate if datastore is the workspace default
     datastore.
    :vartype is_default: bool
    :ivar account_name: Required. Storage account name.
    :vartype account_name: str
    :ivar endpoint: Azure cloud endpoint for the storage account.
    :vartype endpoint: str
    :ivar filesystem: Required. The name of the Data Lake Gen2 filesystem.
    :vartype filesystem: str
    :ivar protocol: Protocol used to communicate with the storage account.
    :vartype protocol: str
    :ivar service_data_access_auth_identity: Indicates which identity to use to authenticate
     service data access to customer's storage. Possible values include: "None",
     "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
    :vartype service_data_access_auth_identity: str or
     ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
    """

    _validation = {
        "credentials": {"required": True},
        "datastore_type": {"required": True},
        "is_default": {"readonly": True},
        "account_name": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
        "filesystem": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "credentials": {"key": "credentials", "type": "DatastoreCredentials"},
        "datastore_type": {"key": "datastoreType", "type": "str"},
        "is_default": {"key": "isDefault", "type": "bool"},
        "account_name": {"key": "accountName", "type": "str"},
        "endpoint": {"key": "endpoint", "type": "str"},
        "filesystem": {"key": "filesystem", "type": "str"},
        "protocol": {"key": "protocol", "type": "str"},
        "service_data_access_auth_identity": {"key": "serviceDataAccessAuthIdentity", "type": "str"},
    }

    def __init__(
        self,
        *,
        credentials: "DatastoreCredentials",
        account_name: str,
        filesystem: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        endpoint: Optional[str] = None,
        protocol: Optional[str] = None,
        service_data_access_auth_identity: Optional[Union[str, "ServiceDataAccessAuthIdentity"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword credentials: Required. Account credentials.
        :paramtype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
        :keyword account_name: Required. Storage account name.
        :paramtype account_name: str
        :keyword endpoint: Azure cloud endpoint for the storage account.
        :paramtype endpoint: str
        :keyword filesystem: Required. The name of the Data Lake Gen2 filesystem.
        :paramtype filesystem: str
        :keyword protocol: Protocol used to communicate with the storage account.
        :paramtype protocol: str
        :keyword service_data_access_auth_identity: Indicates which identity to use to authenticate
         service data access to customer's storage. Possible values include: "None",
         "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
        :paramtype service_data_access_auth_identity: str or
         ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
        """
        super(AzureDataLakeGen2Datastore, self).__init__(
            description=description, properties=properties, tags=tags, credentials=credentials, **kwargs
        )
        self.datastore_type = "AzureDataLakeGen2"  # type: str
        self.account_name = account_name
        self.endpoint = endpoint
        self.filesystem = filesystem
        self.protocol = protocol
        self.service_data_access_auth_identity = service_data_access_auth_identity


class AzureFileDatastore(Datastore):
    """Azure File datastore configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar credentials: Required. Account credentials.
    :vartype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
    :ivar datastore_type: Required. Storage type backing the datastore.Constant filled by server.
     Possible values include: "AzureBlob", "AzureDataLakeGen1", "AzureDataLakeGen2", "AzureFile".
    :vartype datastore_type: str or ~azure.mgmt.machinelearningservices.models.DatastoreType
    :ivar is_default: Readonly property to indicate if datastore is the workspace default
     datastore.
    :vartype is_default: bool
    :ivar account_name: Required. Storage account name.
    :vartype account_name: str
    :ivar endpoint: Azure cloud endpoint for the storage account.
    :vartype endpoint: str
    :ivar file_share_name: Required. TODO - File share name.
    :vartype file_share_name: str
    :ivar protocol: Protocol used to communicate with the storage account.
    :vartype protocol: str
    :ivar service_data_access_auth_identity: Indicates which identity to use to authenticate
     service data access to customer's storage. Possible values include: "None",
     "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
    :vartype service_data_access_auth_identity: str or
     ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
    """

    _validation = {
        "credentials": {"required": True},
        "datastore_type": {"required": True},
        "is_default": {"readonly": True},
        "account_name": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
        "file_share_name": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "credentials": {"key": "credentials", "type": "DatastoreCredentials"},
        "datastore_type": {"key": "datastoreType", "type": "str"},
        "is_default": {"key": "isDefault", "type": "bool"},
        "account_name": {"key": "accountName", "type": "str"},
        "endpoint": {"key": "endpoint", "type": "str"},
        "file_share_name": {"key": "fileShareName", "type": "str"},
        "protocol": {"key": "protocol", "type": "str"},
        "service_data_access_auth_identity": {"key": "serviceDataAccessAuthIdentity", "type": "str"},
    }

    def __init__(
        self,
        *,
        credentials: "DatastoreCredentials",
        account_name: str,
        file_share_name: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        endpoint: Optional[str] = None,
        protocol: Optional[str] = None,
        service_data_access_auth_identity: Optional[Union[str, "ServiceDataAccessAuthIdentity"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword credentials: Required. Account credentials.
        :paramtype credentials: ~azure.mgmt.machinelearningservices.models.DatastoreCredentials
        :keyword account_name: Required. Storage account name.
        :paramtype account_name: str
        :keyword endpoint: Azure cloud endpoint for the storage account.
        :paramtype endpoint: str
        :keyword file_share_name: Required. TODO - File share name.
        :paramtype file_share_name: str
        :keyword protocol: Protocol used to communicate with the storage account.
        :paramtype protocol: str
        :keyword service_data_access_auth_identity: Indicates which identity to use to authenticate
         service data access to customer's storage. Possible values include: "None",
         "WorkspaceSystemAssignedIdentity", "WorkspaceUserAssignedIdentity".
        :paramtype service_data_access_auth_identity: str or
         ~azure.mgmt.machinelearningservices.models.ServiceDataAccessAuthIdentity
        """
        super(AzureFileDatastore, self).__init__(
            description=description, properties=properties, tags=tags, credentials=credentials, **kwargs
        )
        self.datastore_type = "AzureFile"  # type: str
        self.account_name = account_name
        self.endpoint = endpoint
        self.file_share_name = file_share_name
        self.protocol = protocol
        self.service_data_access_auth_identity = service_data_access_auth_identity


class InferencingServer(msrest.serialization.Model):
    """InferencingServer.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: AzureMLBatchInferencingServer, AzureMLOnlineInferencingServer, CustomInferencingServer, TritonInferencingServer.

    All required parameters must be populated in order to send to Azure.

    :ivar server_type: Required. Inferencing server type for various targets.Constant filled by
     server. Possible values include: "AzureMLOnline", "AzureMLBatch", "Triton", "Custom".
    :vartype server_type: str or ~azure.mgmt.machinelearningservices.models.InferencingServerType
    """

    _validation = {
        "server_type": {"required": True},
    }

    _attribute_map = {
        "server_type": {"key": "serverType", "type": "str"},
    }

    _subtype_map = {
        "server_type": {
            "AzureMLBatch": "AzureMLBatchInferencingServer",
            "AzureMLOnline": "AzureMLOnlineInferencingServer",
            "Custom": "CustomInferencingServer",
            "Triton": "TritonInferencingServer",
        }
    }

    def __init__(self, **kwargs):
        """ """
        super(InferencingServer, self).__init__(**kwargs)
        self.server_type = None  # type: Optional[str]


class AzureMLBatchInferencingServer(InferencingServer):
    """Azure ML batch inferencing server configurations.

    All required parameters must be populated in order to send to Azure.

    :ivar server_type: Required. Inferencing server type for various targets.Constant filled by
     server. Possible values include: "AzureMLOnline", "AzureMLBatch", "Triton", "Custom".
    :vartype server_type: str or ~azure.mgmt.machinelearningservices.models.InferencingServerType
    :ivar code_configuration: Code configuration for AML batch inferencing server.
    :vartype code_configuration: ~azure.mgmt.machinelearningservices.models.CodeConfiguration
    """

    _validation = {
        "server_type": {"required": True},
    }

    _attribute_map = {
        "server_type": {"key": "serverType", "type": "str"},
        "code_configuration": {"key": "codeConfiguration", "type": "CodeConfiguration"},
    }

    def __init__(self, *, code_configuration: Optional["CodeConfiguration"] = None, **kwargs):
        """
        :keyword code_configuration: Code configuration for AML batch inferencing server.
        :paramtype code_configuration: ~azure.mgmt.machinelearningservices.models.CodeConfiguration
        """
        super(AzureMLBatchInferencingServer, self).__init__(**kwargs)
        self.server_type = "AzureMLBatch"  # type: str
        self.code_configuration = code_configuration


class AzureMLOnlineInferencingServer(InferencingServer):
    """Azure ML online inferencing configurations.

    All required parameters must be populated in order to send to Azure.

    :ivar server_type: Required. Inferencing server type for various targets.Constant filled by
     server. Possible values include: "AzureMLOnline", "AzureMLBatch", "Triton", "Custom".
    :vartype server_type: str or ~azure.mgmt.machinelearningservices.models.InferencingServerType
    :ivar code_configuration: Code configuration for AML inferencing server.
    :vartype code_configuration: ~azure.mgmt.machinelearningservices.models.CodeConfiguration
    """

    _validation = {
        "server_type": {"required": True},
    }

    _attribute_map = {
        "server_type": {"key": "serverType", "type": "str"},
        "code_configuration": {"key": "codeConfiguration", "type": "CodeConfiguration"},
    }

    def __init__(self, *, code_configuration: Optional["CodeConfiguration"] = None, **kwargs):
        """
        :keyword code_configuration: Code configuration for AML inferencing server.
        :paramtype code_configuration: ~azure.mgmt.machinelearningservices.models.CodeConfiguration
        """
        super(AzureMLOnlineInferencingServer, self).__init__(**kwargs)
        self.server_type = "AzureMLOnline"  # type: str
        self.code_configuration = code_configuration


class EarlyTerminationPolicy(msrest.serialization.Model):
    """Early termination policies enable canceling poor-performing runs before they complete.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: BanditPolicy, MedianStoppingPolicy, TruncationSelectionPolicy.

    All required parameters must be populated in order to send to Azure.

    :ivar delay_evaluation: Number of intervals by which to delay the first evaluation.
    :vartype delay_evaluation: int
    :ivar evaluation_interval: Interval (number of runs) between policy evaluations.
    :vartype evaluation_interval: int
    :ivar policy_type: Required. Name of policy configuration.Constant filled by server. Possible
     values include: "Bandit", "MedianStopping", "TruncationSelection".
    :vartype policy_type: str or
     ~azure.mgmt.machinelearningservices.models.EarlyTerminationPolicyType
    """

    _validation = {
        "policy_type": {"required": True},
    }

    _attribute_map = {
        "delay_evaluation": {"key": "delayEvaluation", "type": "int"},
        "evaluation_interval": {"key": "evaluationInterval", "type": "int"},
        "policy_type": {"key": "policyType", "type": "str"},
    }

    _subtype_map = {
        "policy_type": {
            "Bandit": "BanditPolicy",
            "MedianStopping": "MedianStoppingPolicy",
            "TruncationSelection": "TruncationSelectionPolicy",
        }
    }

    def __init__(self, *, delay_evaluation: Optional[int] = 0, evaluation_interval: Optional[int] = 0, **kwargs):
        """
        :keyword delay_evaluation: Number of intervals by which to delay the first evaluation.
        :paramtype delay_evaluation: int
        :keyword evaluation_interval: Interval (number of runs) between policy evaluations.
        :paramtype evaluation_interval: int
        """
        super(EarlyTerminationPolicy, self).__init__(**kwargs)
        self.delay_evaluation = delay_evaluation
        self.evaluation_interval = evaluation_interval
        self.policy_type = None  # type: Optional[str]


class BanditPolicy(EarlyTerminationPolicy):
    """Defines an early termination policy based on slack criteria, and a frequency and delay interval for evaluation.

    All required parameters must be populated in order to send to Azure.

    :ivar delay_evaluation: Number of intervals by which to delay the first evaluation.
    :vartype delay_evaluation: int
    :ivar evaluation_interval: Interval (number of runs) between policy evaluations.
    :vartype evaluation_interval: int
    :ivar policy_type: Required. Name of policy configuration.Constant filled by server. Possible
     values include: "Bandit", "MedianStopping", "TruncationSelection".
    :vartype policy_type: str or
     ~azure.mgmt.machinelearningservices.models.EarlyTerminationPolicyType
    :ivar slack_amount: Absolute distance allowed from the best performing run.
    :vartype slack_amount: float
    :ivar slack_factor: Ratio of the allowed distance from the best performing run.
    :vartype slack_factor: float
    """

    _validation = {
        "policy_type": {"required": True},
    }

    _attribute_map = {
        "delay_evaluation": {"key": "delayEvaluation", "type": "int"},
        "evaluation_interval": {"key": "evaluationInterval", "type": "int"},
        "policy_type": {"key": "policyType", "type": "str"},
        "slack_amount": {"key": "slackAmount", "type": "float"},
        "slack_factor": {"key": "slackFactor", "type": "float"},
    }

    def __init__(
        self,
        *,
        delay_evaluation: Optional[int] = 0,
        evaluation_interval: Optional[int] = 0,
        slack_amount: Optional[float] = 0,
        slack_factor: Optional[float] = 0,
        **kwargs
    ):
        """
        :keyword delay_evaluation: Number of intervals by which to delay the first evaluation.
        :paramtype delay_evaluation: int
        :keyword evaluation_interval: Interval (number of runs) between policy evaluations.
        :paramtype evaluation_interval: int
        :keyword slack_amount: Absolute distance allowed from the best performing run.
        :paramtype slack_amount: float
        :keyword slack_factor: Ratio of the allowed distance from the best performing run.
        :paramtype slack_factor: float
        """
        super(BanditPolicy, self).__init__(
            delay_evaluation=delay_evaluation, evaluation_interval=evaluation_interval, **kwargs
        )
        self.policy_type = "Bandit"  # type: str
        self.slack_amount = slack_amount
        self.slack_factor = slack_factor


class BaseEnvironmentSource(msrest.serialization.Model):
    """BaseEnvironmentSource.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: BaseEnvironmentId.

    All required parameters must be populated in order to send to Azure.

    :ivar base_environment_source_type: Required. Base environment type.Constant filled by server.
     Possible values include: "EnvironmentAsset".
    :vartype base_environment_source_type: str or
     ~azure.mgmt.machinelearningservices.models.BaseEnvironmentSourceType
    """

    _validation = {
        "base_environment_source_type": {"required": True},
    }

    _attribute_map = {
        "base_environment_source_type": {"key": "baseEnvironmentSourceType", "type": "str"},
    }

    _subtype_map = {"base_environment_source_type": {"EnvironmentAsset": "BaseEnvironmentId"}}

    def __init__(self, **kwargs):
        """ """
        super(BaseEnvironmentSource, self).__init__(**kwargs)
        self.base_environment_source_type = None  # type: Optional[str]


class BaseEnvironmentId(BaseEnvironmentSource):
    """Base environment type.

    All required parameters must be populated in order to send to Azure.

    :ivar base_environment_source_type: Required. Base environment type.Constant filled by server.
     Possible values include: "EnvironmentAsset".
    :vartype base_environment_source_type: str or
     ~azure.mgmt.machinelearningservices.models.BaseEnvironmentSourceType
    :ivar resource_id: Required. Resource id accepting ArmId or AzureMlId.
    :vartype resource_id: str
    """

    _validation = {
        "base_environment_source_type": {"required": True},
        "resource_id": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "base_environment_source_type": {"key": "baseEnvironmentSourceType", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
    }

    def __init__(self, *, resource_id: str, **kwargs):
        """
        :keyword resource_id: Required. Resource id accepting ArmId or AzureMlId.
        :paramtype resource_id: str
        """
        super(BaseEnvironmentId, self).__init__(**kwargs)
        self.base_environment_source_type = "EnvironmentAsset"  # type: str
        self.resource_id = resource_id


class Binding(msrest.serialization.Model):
    """Binding Inputs/Outputs to ComponentJob Inputs/Outputs etc.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: BasicBinding.

    All required parameters must be populated in order to send to Azure.

    :ivar binding_type: Required. Type of Binding.Constant filled by server. Possible values
     include: "Basic".
    :vartype binding_type: str or ~azure.mgmt.machinelearningservices.models.BindingType
    """

    _validation = {
        "binding_type": {"required": True},
    }

    _attribute_map = {
        "binding_type": {"key": "bindingType", "type": "str"},
    }

    _subtype_map = {"binding_type": {"Basic": "BasicBinding"}}

    def __init__(self, **kwargs):
        """ """
        super(Binding, self).__init__(**kwargs)
        self.binding_type = None  # type: Optional[str]


class BasicBinding(Binding):
    """Basic binding with simple source and destination.

    All required parameters must be populated in order to send to Azure.

    :ivar binding_type: Required. Type of Binding.Constant filled by server. Possible values
     include: "Basic".
    :vartype binding_type: str or ~azure.mgmt.machinelearningservices.models.BindingType
    :ivar destination: Destination reference.
    :vartype destination: str
    :ivar source: Source reference.
    :vartype source: str
    """

    _validation = {
        "binding_type": {"required": True},
    }

    _attribute_map = {
        "binding_type": {"key": "bindingType", "type": "str"},
        "destination": {"key": "destination", "type": "str"},
        "source": {"key": "source", "type": "str"},
    }

    def __init__(self, *, destination: Optional[str] = None, source: Optional[str] = None, **kwargs):
        """
        :keyword destination: Destination reference.
        :paramtype destination: str
        :keyword source: Source reference.
        :paramtype source: str
        """
        super(BasicBinding, self).__init__(**kwargs)
        self.binding_type = "Basic"  # type: str
        self.destination = destination
        self.source = source


class BlobReferenceForConsumptionDto(msrest.serialization.Model):
    """BlobReferenceForConsumptionDto.

    :ivar blob_uri: https://blob.windows.core.net/Container/Path.
    :vartype blob_uri: str
    :ivar credential:
    :vartype credential: ~azure.mgmt.machinelearningservices.models.DataReferenceCredentialDto
    :ivar storage_account_arm_id:
    :vartype storage_account_arm_id: str
    """

    _attribute_map = {
        "blob_uri": {"key": "blobUri", "type": "str"},
        "credential": {"key": "credential", "type": "DataReferenceCredentialDto"},
        "storage_account_arm_id": {"key": "storageAccountArmId", "type": "str"},
    }

    def __init__(
        self,
        *,
        blob_uri: Optional[str] = None,
        credential: Optional["DataReferenceCredentialDto"] = None,
        storage_account_arm_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword blob_uri: https://blob.windows.core.net/Container/Path.
        :paramtype blob_uri: str
        :keyword credential:
        :paramtype credential: ~azure.mgmt.machinelearningservices.models.DataReferenceCredentialDto
        :keyword storage_account_arm_id:
        :paramtype storage_account_arm_id: str
        """
        super(BlobReferenceForConsumptionDto, self).__init__(**kwargs)
        self.blob_uri = blob_uri
        self.credential = credential
        self.storage_account_arm_id = storage_account_arm_id


class BlobReferenceSASRequestDto(msrest.serialization.Model):
    """BlobReferenceSASRequestDto.

    :ivar asset_id:
    :vartype asset_id: str
    :ivar blob_uri:
    :vartype blob_uri: str
    """

    _attribute_map = {
        "asset_id": {"key": "assetId", "type": "str"},
        "blob_uri": {"key": "blobUri", "type": "str"},
    }

    def __init__(self, *, asset_id: Optional[str] = None, blob_uri: Optional[str] = None, **kwargs):
        """
        :keyword asset_id:
        :paramtype asset_id: str
        :keyword blob_uri:
        :paramtype blob_uri: str
        """
        super(BlobReferenceSASRequestDto, self).__init__(**kwargs)
        self.asset_id = asset_id
        self.blob_uri = blob_uri


class BlobReferenceSASResponseDto(msrest.serialization.Model):
    """BlobReferenceSASResponseDto.

    :ivar blob_reference_for_consumption:
    :vartype blob_reference_for_consumption:
     ~azure.mgmt.machinelearningservices.models.BlobReferenceForConsumptionDto
    """

    _attribute_map = {
        "blob_reference_for_consumption": {
            "key": "blobReferenceForConsumption",
            "type": "BlobReferenceForConsumptionDto",
        },
    }

    def __init__(self, *, blob_reference_for_consumption: Optional["BlobReferenceForConsumptionDto"] = None, **kwargs):
        """
        :keyword blob_reference_for_consumption:
        :paramtype blob_reference_for_consumption:
         ~azure.mgmt.machinelearningservices.models.BlobReferenceForConsumptionDto
        """
        super(BlobReferenceSASResponseDto, self).__init__(**kwargs)
        self.blob_reference_for_consumption = blob_reference_for_consumption


class BuildContext(msrest.serialization.Model):
    """Configuration settings for Docker build context.

    All required parameters must be populated in order to send to Azure.

    :ivar context_uri: Required. URI of the Docker build context used to build the image. Supports
     blob URIs on environment creation and may return blob or Git URIs.


     .. raw:: html

        <seealso
     href="https://docs.docker.com/engine/reference/commandline/build/#extended-description" />.
    :vartype context_uri: str
    :ivar dockerfile_path: Path to the Dockerfile in the build context.


     .. raw:: html

        <seealso href="https://docs.docker.com/engine/reference/builder/" />.
    :vartype dockerfile_path: str
    """

    _validation = {
        "context_uri": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "context_uri": {"key": "contextUri", "type": "str"},
        "dockerfile_path": {"key": "dockerfilePath", "type": "str"},
    }

    def __init__(self, *, context_uri: str, dockerfile_path: Optional[str] = "Dockerfile", **kwargs):
        """
        :keyword context_uri: Required. URI of the Docker build context used to build the image.
         Supports blob URIs on environment creation and may return blob or Git URIs.


         .. raw:: html

            <seealso
         href="https://docs.docker.com/engine/reference/commandline/build/#extended-description" />.
        :paramtype context_uri: str
        :keyword dockerfile_path: Path to the Dockerfile in the build context.


         .. raw:: html

            <seealso href="https://docs.docker.com/engine/reference/builder/" />.
        :paramtype dockerfile_path: str
        """
        super(BuildContext, self).__init__(**kwargs)
        self.context_uri = context_uri
        self.dockerfile_path = dockerfile_path


class CertificateDatastoreCredentials(DatastoreCredentials):
    """Certificate datastore credentials configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar credentials_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "None", "Sas",
     "ServicePrincipal".
    :vartype credentials_type: str or ~azure.mgmt.machinelearningservices.models.CredentialsType
    :ivar authority_url: Authority URL used for authentication.
    :vartype authority_url: str
    :ivar client_id: Required. Service principal client ID.
    :vartype client_id: str
    :ivar resource_url: Resource the service principal has access to.
    :vartype resource_url: str
    :ivar secrets: Required. Service principal secrets.
    :vartype secrets: ~azure.mgmt.machinelearningservices.models.CertificateDatastoreSecrets
    :ivar tenant_id: Required. ID of the tenant to which the service principal belongs.
    :vartype tenant_id: str
    :ivar thumbprint: Required. Thumbprint of the certificate used for authentication.
    :vartype thumbprint: str
    """

    _validation = {
        "credentials_type": {"required": True},
        "client_id": {"required": True},
        "secrets": {"required": True},
        "tenant_id": {"required": True},
        "thumbprint": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "credentials_type": {"key": "credentialsType", "type": "str"},
        "authority_url": {"key": "authorityUrl", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
        "resource_url": {"key": "resourceUrl", "type": "str"},
        "secrets": {"key": "secrets", "type": "CertificateDatastoreSecrets"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "thumbprint": {"key": "thumbprint", "type": "str"},
    }

    def __init__(
        self,
        *,
        client_id: str,
        secrets: "CertificateDatastoreSecrets",
        tenant_id: str,
        thumbprint: str,
        authority_url: Optional[str] = None,
        resource_url: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword authority_url: Authority URL used for authentication.
        :paramtype authority_url: str
        :keyword client_id: Required. Service principal client ID.
        :paramtype client_id: str
        :keyword resource_url: Resource the service principal has access to.
        :paramtype resource_url: str
        :keyword secrets: Required. Service principal secrets.
        :paramtype secrets: ~azure.mgmt.machinelearningservices.models.CertificateDatastoreSecrets
        :keyword tenant_id: Required. ID of the tenant to which the service principal belongs.
        :paramtype tenant_id: str
        :keyword thumbprint: Required. Thumbprint of the certificate used for authentication.
        :paramtype thumbprint: str
        """
        super(CertificateDatastoreCredentials, self).__init__(**kwargs)
        self.credentials_type = "Certificate"  # type: str
        self.authority_url = authority_url
        self.client_id = client_id
        self.resource_url = resource_url
        self.secrets = secrets
        self.tenant_id = tenant_id
        self.thumbprint = thumbprint


class CertificateDatastoreSecrets(DatastoreSecrets):
    """Datastore certificate secrets.

    All required parameters must be populated in order to send to Azure.

    :ivar secrets_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "Sas",
     "ServicePrincipal".
    :vartype secrets_type: str or ~azure.mgmt.machinelearningservices.models.SecretsType
    :ivar certificate: Service principal certificate.
    :vartype certificate: str
    """

    _validation = {
        "secrets_type": {"required": True},
    }

    _attribute_map = {
        "secrets_type": {"key": "secretsType", "type": "str"},
        "certificate": {"key": "certificate", "type": "str"},
    }

    def __init__(self, *, certificate: Optional[str] = None, **kwargs):
        """
        :keyword certificate: Service principal certificate.
        :paramtype certificate: str
        """
        super(CertificateDatastoreSecrets, self).__init__(**kwargs)
        self.secrets_type = "Certificate"  # type: str
        self.certificate = certificate


class CodeConfiguration(msrest.serialization.Model):
    """Configuration for a scoring code asset.

    All required parameters must be populated in order to send to Azure.

    :ivar code_id: ARM resource ID of the code asset.
    :vartype code_id: str
    :ivar scoring_script: Required. The script to execute on startup. eg. "score.py".
    :vartype scoring_script: str
    """

    _validation = {
        "scoring_script": {"required": True, "min_length": 1, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "code_id": {"key": "codeId", "type": "str"},
        "scoring_script": {"key": "scoringScript", "type": "str"},
    }

    def __init__(self, *, scoring_script: str, code_id: Optional[str] = None, **kwargs):
        """
        :keyword code_id: ARM resource ID of the code asset.
        :paramtype code_id: str
        :keyword scoring_script: Required. The script to execute on startup. eg. "score.py".
        :paramtype scoring_script: str
        """
        super(CodeConfiguration, self).__init__(**kwargs)
        self.code_id = code_id
        self.scoring_script = scoring_script


class Resource(msrest.serialization.Model):
    """Common fields that are returned in the response for all Azure Resource Manager resources.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
    }

    def __init__(self, **kwargs):
        """ """
        super(Resource, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.system_data = None


class CodeContainerData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.CodeContainerDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "CodeContainerDetails"},
    }

    def __init__(self, *, properties: "CodeContainerDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.CodeContainerDetails
        """
        super(CodeContainerData, self).__init__(**kwargs)
        self.properties = properties


class CodeContainerDetails(AssetContainer):
    """Container for code asset versions.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar latest_version: The latest version inside this container.
    :vartype latest_version: str
    :ivar next_version: The next auto incremental version.
    :vartype next_version: str
    """

    _validation = {
        "latest_version": {"readonly": True},
        "next_version": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "latest_version": {"key": "latestVersion", "type": "str"},
        "next_version": {"key": "nextVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        """
        super(CodeContainerDetails, self).__init__(
            description=description, properties=properties, tags=tags, is_archived=is_archived, **kwargs
        )


class CodeContainerResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of CodeContainer entities.

    :ivar next_link: The link to the next page of CodeContainer objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type CodeContainer.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.CodeContainerData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[CodeContainerData]"},
    }

    def __init__(self, *, next_link: Optional[str] = None, value: Optional[List["CodeContainerData"]] = None, **kwargs):
        """
        :keyword next_link: The link to the next page of CodeContainer objects. If null, there are no
         additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type CodeContainer.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.CodeContainerData]
        """
        super(CodeContainerResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class CodeVersionData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.CodeVersionDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "CodeVersionDetails"},
    }

    def __init__(self, *, properties: "CodeVersionDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.CodeVersionDetails
        """
        super(CodeVersionData, self).__init__(**kwargs)
        self.properties = properties


class CodeVersionDetails(AssetBase):
    """Code asset version details.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar code_uri: Uri where code is located.
    :vartype code_uri: str
    """

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "code_uri": {"key": "codeUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        code_uri: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword code_uri: Uri where code is located.
        :paramtype code_uri: str
        """
        super(CodeVersionDetails, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            **kwargs
        )
        self.code_uri = code_uri


class CodeVersionResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of CodeVersion entities.

    :ivar next_link: The link to the next page of CodeVersion objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type CodeVersion.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.CodeVersionData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[CodeVersionData]"},
    }

    def __init__(self, *, next_link: Optional[str] = None, value: Optional[List["CodeVersionData"]] = None, **kwargs):
        """
        :keyword next_link: The link to the next page of CodeVersion objects. If null, there are no
         additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type CodeVersion.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.CodeVersionData]
        """
        super(CodeVersionResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class JobBase(ResourceBase):
    """Base definition for a job.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: Job, CommandJob, PipelineJob, SweepJob.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar compute_id: ARM resource ID of the compute resource.
    :vartype compute_id: str
    :ivar display_name: Display name of job.
    :vartype display_name: str
    :ivar experiment_name: The name of the experiment the job belongs to. If not set, the job is
     placed in the "Default" experiment.
    :vartype experiment_name: str
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar job_type: Required. Specifies the type of job.Constant filled by server. Possible values
     include: "Command", "Sweep", "Pipeline", "Base".
    :vartype job_type: str or ~azure.mgmt.machinelearningservices.models.JobType
    :ivar parent_job_name: TODO - Parent job name.
    :vartype parent_job_name: str
    :ivar services: List of JobEndpoints.
     For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
    :ivar status: Status of the job. Possible values include: "NotStarted", "Starting",
     "Provisioning", "Preparing", "Queued", "Running", "Finalizing", "CancelRequested", "Completed",
     "Failed", "Canceled", "NotResponding", "Paused", "Unknown".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.JobStatus
    """

    _validation = {
        "job_type": {"required": True},
        "parent_job_name": {"readonly": True},
        "status": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "compute_id": {"key": "computeId", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "job_type": {"key": "jobType", "type": "str"},
        "parent_job_name": {"key": "parentJobName", "type": "str"},
        "services": {"key": "services", "type": "{JobService}"},
        "status": {"key": "status", "type": "str"},
    }

    _subtype_map = {
        "job_type": {"Base": "Job", "Command": "CommandJob", "Pipeline": "PipelineJob", "Sweep": "SweepJob"}
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        compute_id: Optional[str] = None,
        display_name: Optional[str] = None,
        experiment_name: Optional[str] = "Default",
        is_archived: Optional[bool] = False,
        services: Optional[Dict[str, "JobService"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword compute_id: ARM resource ID of the compute resource.
        :paramtype compute_id: str
        :keyword display_name: Display name of job.
        :paramtype display_name: str
        :keyword experiment_name: The name of the experiment the job belongs to. If not set, the job is
         placed in the "Default" experiment.
        :paramtype experiment_name: str
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword services: List of JobEndpoints.
         For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
        """
        super(JobBase, self).__init__(description=description, properties=properties, tags=tags, **kwargs)
        self.compute_id = compute_id
        self.display_name = display_name
        self.experiment_name = experiment_name
        self.is_archived = is_archived
        self.job_type = "JobBase"  # type: str
        self.parent_job_name = None
        self.services = services
        self.status = None


class CommandJob(JobBase):
    """Command job definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar compute_id: ARM resource ID of the compute resource.
    :vartype compute_id: str
    :ivar display_name: Display name of job.
    :vartype display_name: str
    :ivar experiment_name: The name of the experiment the job belongs to. If not set, the job is
     placed in the "Default" experiment.
    :vartype experiment_name: str
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar job_type: Required. Specifies the type of job.Constant filled by server. Possible values
     include: "Command", "Sweep", "Pipeline", "Base".
    :vartype job_type: str or ~azure.mgmt.machinelearningservices.models.JobType
    :ivar parent_job_name: TODO - Parent job name.
    :vartype parent_job_name: str
    :ivar services: List of JobEndpoints.
     For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
    :ivar status: Status of the job. Possible values include: "NotStarted", "Starting",
     "Provisioning", "Preparing", "Queued", "Running", "Finalizing", "CancelRequested", "Completed",
     "Failed", "Canceled", "NotResponding", "Paused", "Unknown".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.JobStatus
    :ivar code_id: ARM resource ID of the code asset.
    :vartype code_id: str
    :ivar command: Required. The command to execute on startup of the job. eg. "python train.py".
    :vartype command: str
    :ivar distribution: Distribution configuration of the job. If set, this should be one of Mpi,
     Tensorflow, PyTorch, or null.
    :vartype distribution: ~azure.mgmt.machinelearningservices.models.DistributionConfiguration
    :ivar environment_id: Required. The ARM resource ID of the Environment specification for the
     job.
    :vartype environment_id: str
    :ivar environment_variables: Environment variables included in the job.
    :vartype environment_variables: dict[str, str]
    :ivar identity: Identity configuration. If set, this should be one of AmlToken,
     ManagedIdentity, or null.
     Defaults to AmlToken if null.
    :vartype identity: ~azure.mgmt.machinelearningservices.models.IdentityConfiguration
    :ivar inputs: Mapping of input data bindings used in the job.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
    :ivar limits: Command Job limit.
    :vartype limits: ~azure.mgmt.machinelearningservices.models.CommandJobLimits
    :ivar outputs: Mapping of output data bindings used in the job.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
    :ivar parameters: Input parameters.
    :vartype parameters: any
    :ivar resources: Compute Resource configuration for the job.
    :vartype resources: ~azure.mgmt.machinelearningservices.models.ResourceConfiguration
    """

    _validation = {
        "job_type": {"required": True},
        "parent_job_name": {"readonly": True},
        "status": {"readonly": True},
        "command": {"required": True, "min_length": 1, "pattern": r"[a-zA-Z0-9_]"},
        "environment_id": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
        "parameters": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "compute_id": {"key": "computeId", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "job_type": {"key": "jobType", "type": "str"},
        "parent_job_name": {"key": "parentJobName", "type": "str"},
        "services": {"key": "services", "type": "{JobService}"},
        "status": {"key": "status", "type": "str"},
        "code_id": {"key": "codeId", "type": "str"},
        "command": {"key": "command", "type": "str"},
        "distribution": {"key": "distribution", "type": "DistributionConfiguration"},
        "environment_id": {"key": "environmentId", "type": "str"},
        "environment_variables": {"key": "environmentVariables", "type": "{str}"},
        "identity": {"key": "identity", "type": "IdentityConfiguration"},
        "inputs": {"key": "inputs", "type": "{JobInput}"},
        "limits": {"key": "limits", "type": "CommandJobLimits"},
        "outputs": {"key": "outputs", "type": "{JobOutput}"},
        "parameters": {"key": "parameters", "type": "object"},
        "resources": {"key": "resources", "type": "ResourceConfiguration"},
    }

    def __init__(
        self,
        *,
        command: str,
        environment_id: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        compute_id: Optional[str] = None,
        display_name: Optional[str] = None,
        experiment_name: Optional[str] = "Default",
        is_archived: Optional[bool] = False,
        services: Optional[Dict[str, "JobService"]] = None,
        code_id: Optional[str] = None,
        distribution: Optional["DistributionConfiguration"] = None,
        environment_variables: Optional[Dict[str, str]] = None,
        identity: Optional["IdentityConfiguration"] = None,
        inputs: Optional[Dict[str, "JobInput"]] = None,
        limits: Optional["CommandJobLimits"] = None,
        outputs: Optional[Dict[str, "JobOutput"]] = None,
        resources: Optional["ResourceConfiguration"] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword compute_id: ARM resource ID of the compute resource.
        :paramtype compute_id: str
        :keyword display_name: Display name of job.
        :paramtype display_name: str
        :keyword experiment_name: The name of the experiment the job belongs to. If not set, the job is
         placed in the "Default" experiment.
        :paramtype experiment_name: str
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword services: List of JobEndpoints.
         For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
        :keyword code_id: ARM resource ID of the code asset.
        :paramtype code_id: str
        :keyword command: Required. The command to execute on startup of the job. eg. "python
         train.py".
        :paramtype command: str
        :keyword distribution: Distribution configuration of the job. If set, this should be one of
         Mpi, Tensorflow, PyTorch, or null.
        :paramtype distribution: ~azure.mgmt.machinelearningservices.models.DistributionConfiguration
        :keyword environment_id: Required. The ARM resource ID of the Environment specification for the
         job.
        :paramtype environment_id: str
        :keyword environment_variables: Environment variables included in the job.
        :paramtype environment_variables: dict[str, str]
        :keyword identity: Identity configuration. If set, this should be one of AmlToken,
         ManagedIdentity, or null.
         Defaults to AmlToken if null.
        :paramtype identity: ~azure.mgmt.machinelearningservices.models.IdentityConfiguration
        :keyword inputs: Mapping of input data bindings used in the job.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
        :keyword limits: Command Job limit.
        :paramtype limits: ~azure.mgmt.machinelearningservices.models.CommandJobLimits
        :keyword outputs: Mapping of output data bindings used in the job.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
        :keyword resources: Compute Resource configuration for the job.
        :paramtype resources: ~azure.mgmt.machinelearningservices.models.ResourceConfiguration
        """
        super(CommandJob, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            compute_id=compute_id,
            display_name=display_name,
            experiment_name=experiment_name,
            is_archived=is_archived,
            services=services,
            **kwargs
        )
        self.job_type = "Command"  # type: str
        self.code_id = code_id
        self.command = command
        self.distribution = distribution
        self.environment_id = environment_id
        self.environment_variables = environment_variables
        self.identity = identity
        self.inputs = inputs
        self.limits = limits
        self.outputs = outputs
        self.parameters = None
        self.resources = resources


class JobLimits(msrest.serialization.Model):
    """JobLimits.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: CommandJobLimits, SweepJobLimits.

    All required parameters must be populated in order to send to Azure.

    :ivar job_limits_type: Required. JobLimit type.Constant filled by server. Possible values
     include: "Command", "Sweep".
    :vartype job_limits_type: str or ~azure.mgmt.machinelearningservices.models.JobLimitsType
    :ivar timeout: The max run duration in ISO 8601 format, after which the job will be cancelled.
     Only supports duration with precision as low as Seconds.
    :vartype timeout: ~datetime.timedelta
    """

    _validation = {
        "job_limits_type": {"required": True},
    }

    _attribute_map = {
        "job_limits_type": {"key": "jobLimitsType", "type": "str"},
        "timeout": {"key": "timeout", "type": "duration"},
    }

    _subtype_map = {"job_limits_type": {"Command": "CommandJobLimits", "Sweep": "SweepJobLimits"}}

    def __init__(self, *, timeout: Optional[datetime.timedelta] = None, **kwargs):
        """
        :keyword timeout: The max run duration in ISO 8601 format, after which the job will be
         cancelled. Only supports duration with precision as low as Seconds.
        :paramtype timeout: ~datetime.timedelta
        """
        super(JobLimits, self).__init__(**kwargs)
        self.job_limits_type = None  # type: Optional[str]
        self.timeout = timeout


class CommandJobLimits(JobLimits):
    """Command Job limit class.

    All required parameters must be populated in order to send to Azure.

    :ivar job_limits_type: Required. JobLimit type.Constant filled by server. Possible values
     include: "Command", "Sweep".
    :vartype job_limits_type: str or ~azure.mgmt.machinelearningservices.models.JobLimitsType
    :ivar timeout: The max run duration in ISO 8601 format, after which the job will be cancelled.
     Only supports duration with precision as low as Seconds.
    :vartype timeout: ~datetime.timedelta
    """

    _validation = {
        "job_limits_type": {"required": True},
    }

    _attribute_map = {
        "job_limits_type": {"key": "jobLimitsType", "type": "str"},
        "timeout": {"key": "timeout", "type": "duration"},
    }

    def __init__(self, *, timeout: Optional[datetime.timedelta] = None, **kwargs):
        """
        :keyword timeout: The max run duration in ISO 8601 format, after which the job will be
         cancelled. Only supports duration with precision as low as Seconds.
        :paramtype timeout: ~datetime.timedelta
        """
        super(CommandJobLimits, self).__init__(timeout=timeout, **kwargs)
        self.job_limits_type = "Command"  # type: str


class ComponentContainerData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.ComponentContainerDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ComponentContainerDetails"},
    }

    def __init__(self, *, properties: "ComponentContainerDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.ComponentContainerDetails
        """
        super(ComponentContainerData, self).__init__(**kwargs)
        self.properties = properties


class ComponentContainerDetails(AssetContainer):
    """Component container definition.


    .. raw:: html

       <see href="https://learn.microsoft.com/azure/machine-learning/reference-yaml-component-command" />.

        Variables are only populated by the server, and will be ignored when sending a request.

        :ivar description: The asset description text.
        :vartype description: str
        :ivar properties: The asset property dictionary.
        :vartype properties: dict[str, str]
        :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :vartype tags: dict[str, str]
        :ivar is_archived: Is the asset archived?.
        :vartype is_archived: bool
        :ivar latest_version: The latest version inside this container.
        :vartype latest_version: str
        :ivar next_version: The next auto incremental version.
        :vartype next_version: str
    """

    _validation = {
        "latest_version": {"readonly": True},
        "next_version": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "latest_version": {"key": "latestVersion", "type": "str"},
        "next_version": {"key": "nextVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        """
        super(ComponentContainerDetails, self).__init__(
            description=description, properties=properties, tags=tags, is_archived=is_archived, **kwargs
        )


class ComponentContainerResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of ComponentContainer entities.

    :ivar next_link: The link to the next page of ComponentContainer objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type ComponentContainer.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.ComponentContainerData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[ComponentContainerData]"},
    }

    def __init__(
        self, *, next_link: Optional[str] = None, value: Optional[List["ComponentContainerData"]] = None, **kwargs
    ):
        """
        :keyword next_link: The link to the next page of ComponentContainer objects. If null, there are
         no additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type ComponentContainer.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.ComponentContainerData]
        """
        super(ComponentContainerResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class ComponentJob(msrest.serialization.Model):
    """Definition of a ComponentJob.

    :ivar component_id: Reference to component artifact.
    :vartype component_id: str
    :ivar compute_id: ARM resource ID of the compute resource.
    :vartype compute_id: str
    :ivar inputs: Data input set for job.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
    :ivar outputs: Data output set for job.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
    :ivar overrides: Override component default settings.
    :vartype overrides: any
    """

    _attribute_map = {
        "component_id": {"key": "componentId", "type": "str"},
        "compute_id": {"key": "computeId", "type": "str"},
        "inputs": {"key": "inputs", "type": "{JobInput}"},
        "outputs": {"key": "outputs", "type": "{JobOutput}"},
        "overrides": {"key": "overrides", "type": "object"},
    }

    def __init__(
        self,
        *,
        component_id: Optional[str] = None,
        compute_id: Optional[str] = None,
        inputs: Optional[Dict[str, "JobInput"]] = None,
        outputs: Optional[Dict[str, "JobOutput"]] = None,
        overrides: Optional[Any] = None,
        **kwargs
    ):
        """
        :keyword component_id: Reference to component artifact.
        :paramtype component_id: str
        :keyword compute_id: ARM resource ID of the compute resource.
        :paramtype compute_id: str
        :keyword inputs: Data input set for job.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
        :keyword outputs: Data output set for job.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
        :keyword overrides: Override component default settings.
        :paramtype overrides: any
        """
        super(ComponentJob, self).__init__(**kwargs)
        self.component_id = component_id
        self.compute_id = compute_id
        self.inputs = inputs
        self.outputs = outputs
        self.overrides = overrides


class ComponentVersionData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.ComponentVersionDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ComponentVersionDetails"},
    }

    def __init__(self, *, properties: "ComponentVersionDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.ComponentVersionDetails
        """
        super(ComponentVersionData, self).__init__(**kwargs)
        self.properties = properties


class ComponentVersionDetails(AssetBase):
    """Definition of a component version: defines resources that span component types.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar component_spec: Defines Component definition details.


     .. raw:: html

        <see
     href="https://learn.microsoft.com/azure/machine-learning/reference-yaml-component-command"
     />.
    :vartype component_spec: any
    """

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "component_spec": {"key": "componentSpec", "type": "object"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        component_spec: Optional[Any] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword component_spec: Defines Component definition details.


         .. raw:: html

            <see
         href="https://learn.microsoft.com/azure/machine-learning/reference-yaml-component-command"
         />.
        :paramtype component_spec: any
        """
        super(ComponentVersionDetails, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            **kwargs
        )
        self.component_spec = component_spec


class ComponentVersionResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of ComponentVersion entities.

    :ivar next_link: The link to the next page of ComponentVersion objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type ComponentVersion.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.ComponentVersionData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[ComponentVersionData]"},
    }

    def __init__(
        self, *, next_link: Optional[str] = None, value: Optional[List["ComponentVersionData"]] = None, **kwargs
    ):
        """
        :keyword next_link: The link to the next page of ComponentVersion objects. If null, there are
         no additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type ComponentVersion.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.ComponentVersionData]
        """
        super(ComponentVersionResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class CustomInferencingServer(InferencingServer):
    """Custom inference server configurations.

    All required parameters must be populated in order to send to Azure.

    :ivar server_type: Required. Inferencing server type for various targets.Constant filled by
     server. Possible values include: "AzureMLOnline", "AzureMLBatch", "Triton", "Custom".
    :vartype server_type: str or ~azure.mgmt.machinelearningservices.models.InferencingServerType
    :ivar inference_configuration: Inference configuration for custom inferencing.
    :vartype inference_configuration:
     ~azure.mgmt.machinelearningservices.models.OnlineInferenceConfiguration
    """

    _validation = {
        "server_type": {"required": True},
    }

    _attribute_map = {
        "server_type": {"key": "serverType", "type": "str"},
        "inference_configuration": {"key": "inferenceConfiguration", "type": "OnlineInferenceConfiguration"},
    }

    def __init__(self, *, inference_configuration: Optional["OnlineInferenceConfiguration"] = None, **kwargs):
        """
        :keyword inference_configuration: Inference configuration for custom inferencing.
        :paramtype inference_configuration:
         ~azure.mgmt.machinelearningservices.models.OnlineInferenceConfiguration
        """
        super(CustomInferencingServer, self).__init__(**kwargs)
        self.server_type = "Custom"  # type: str
        self.inference_configuration = inference_configuration


class DataContainerData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.DataContainerDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "DataContainerDetails"},
    }

    def __init__(self, *, properties: "DataContainerDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.DataContainerDetails
        """
        super(DataContainerData, self).__init__(**kwargs)
        self.properties = properties


class DataContainerDetails(AssetContainer):
    """Container for data asset versions.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar latest_version: The latest version inside this container.
    :vartype latest_version: str
    :ivar next_version: The next auto incremental version.
    :vartype next_version: str
    :ivar data_type: Required. Specifies the type of data. Possible values include: "uri_file",
     "uri_folder", "mltable".
    :vartype data_type: str or ~azure.mgmt.machinelearningservices.models.DataType
    """

    _validation = {
        "latest_version": {"readonly": True},
        "next_version": {"readonly": True},
        "data_type": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "latest_version": {"key": "latestVersion", "type": "str"},
        "next_version": {"key": "nextVersion", "type": "str"},
        "data_type": {"key": "dataType", "type": "str"},
    }

    def __init__(
        self,
        *,
        data_type: Union[str, "DataType"],
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword data_type: Required. Specifies the type of data. Possible values include: "uri_file",
         "uri_folder", "mltable".
        :paramtype data_type: str or ~azure.mgmt.machinelearningservices.models.DataType
        """
        super(DataContainerDetails, self).__init__(
            description=description, properties=properties, tags=tags, is_archived=is_archived, **kwargs
        )
        self.data_type = data_type


class DataContainerResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of DataContainer entities.

    :ivar next_link: The link to the next page of DataContainer objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type DataContainer.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.DataContainerData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[DataContainerData]"},
    }

    def __init__(self, *, next_link: Optional[str] = None, value: Optional[List["DataContainerData"]] = None, **kwargs):
        """
        :keyword next_link: The link to the next page of DataContainer objects. If null, there are no
         additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type DataContainer.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.DataContainerData]
        """
        super(DataContainerResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class DataPathAssetReference(AssetReferenceBase):
    """Reference to an asset via its path in a datastore.

    All required parameters must be populated in order to send to Azure.

    :ivar reference_type: Required. Specifies the type of asset reference.Constant filled by
     server. Possible values include: "Id", "DataPath", "OutputPath".
    :vartype reference_type: str or ~azure.mgmt.machinelearningservices.models.ReferenceType
    :ivar datastore_id: ARM resource ID of the datastore where the asset is located.
    :vartype datastore_id: str
    :ivar path: The path of the file/directory in the datastore.
    :vartype path: str
    """

    _validation = {
        "reference_type": {"required": True},
    }

    _attribute_map = {
        "reference_type": {"key": "referenceType", "type": "str"},
        "datastore_id": {"key": "datastoreId", "type": "str"},
        "path": {"key": "path", "type": "str"},
    }

    def __init__(self, *, datastore_id: Optional[str] = None, path: Optional[str] = None, **kwargs):
        """
        :keyword datastore_id: ARM resource ID of the datastore where the asset is located.
        :paramtype datastore_id: str
        :keyword path: The path of the file/directory in the datastore.
        :paramtype path: str
        """
        super(DataPathAssetReference, self).__init__(**kwargs)
        self.reference_type = "DataPath"  # type: str
        self.datastore_id = datastore_id
        self.path = path


class DataReferenceCredentialDto(msrest.serialization.Model):
    """DataReferenceCredentialDto.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: .

    All required parameters must be populated in order to send to Azure.

    :ivar credential_type: Required. Constant filled by server. Possible values include: "SAS",
     "DockerCredentials", "ManagedIdentity", "NoCredentials".
    :vartype credential_type: str or
     ~azure.mgmt.machinelearningservices.models.DataReferenceCredentialType
    """

    _validation = {
        "credential_type": {"required": True},
    }

    _attribute_map = {
        "credential_type": {"key": "credentialType", "type": "str"},
    }

    _subtype_map = {"credential_type": {}}

    def __init__(self, **kwargs):
        """ """
        super(DataReferenceCredentialDto, self).__init__(**kwargs)
        self.credential_type = "SAS"  # type: str


class DataVersionBaseData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.DataVersionBaseDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "DataVersionBaseDetails"},
    }

    def __init__(self, *, properties: "DataVersionBaseDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.DataVersionBaseDetails
        """
        super(DataVersionBaseData, self).__init__(**kwargs)
        self.properties = properties


class DataVersionBaseDetails(AssetBase):
    """Data version base definition.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: MLTableData, UriFileDataVersion, UriFolderDataVersion.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar data_type: Required. Specifies the type of data.Constant filled by server. Possible
     values include: "uri_file", "uri_folder", "mltable".
    :vartype data_type: str or ~azure.mgmt.machinelearningservices.models.DataType
    :ivar data_uri: Required. Uri of the data. Usage/meaning depends on
     Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
    :vartype data_uri: str
    :ivar intellectual_property: Intellectual Property details. Used if data is an Intellectual
     Property.
    :vartype intellectual_property: ~azure.mgmt.machinelearningservices.models.IntellectualProperty
    """

    _validation = {
        "data_type": {"required": True},
        "data_uri": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "data_type": {"key": "dataType", "type": "str"},
        "data_uri": {"key": "dataUri", "type": "str"},
        "intellectual_property": {"key": "intellectualProperty", "type": "IntellectualProperty"},
    }

    _subtype_map = {
        "data_type": {"mltable": "MLTableData", "uri_file": "UriFileDataVersion", "uri_folder": "UriFolderDataVersion"}
    }

    def __init__(
        self,
        *,
        data_uri: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        intellectual_property: Optional["IntellectualProperty"] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword data_uri: Required. Uri of the data. Usage/meaning depends on
         Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
        :paramtype data_uri: str
        :keyword intellectual_property: Intellectual Property details. Used if data is an Intellectual
         Property.
        :paramtype intellectual_property:
         ~azure.mgmt.machinelearningservices.models.IntellectualProperty
        """
        super(DataVersionBaseDetails, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            **kwargs
        )
        self.data_type = "DataVersionBaseDetails"  # type: str
        self.data_uri = data_uri
        self.intellectual_property = intellectual_property


class DataVersionBaseResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of DataVersionBase entities.

    :ivar next_link: The link to the next page of DataVersionBase objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type DataVersionBase.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.DataVersionBaseData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[DataVersionBaseData]"},
    }

    def __init__(
        self, *, next_link: Optional[str] = None, value: Optional[List["DataVersionBaseData"]] = None, **kwargs
    ):
        """
        :keyword next_link: The link to the next page of DataVersionBase objects. If null, there are no
         additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type DataVersionBase.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.DataVersionBaseData]
        """
        super(DataVersionBaseResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class DistributionConfiguration(msrest.serialization.Model):
    """Base definition for job distribution configuration.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: Mpi, PyTorch, TensorFlow.

    All required parameters must be populated in order to send to Azure.

    :ivar distribution_type: Required. Specifies the type of distribution framework.Constant filled
     by server. Possible values include: "PyTorch", "TensorFlow", "Mpi".
    :vartype distribution_type: str or ~azure.mgmt.machinelearningservices.models.DistributionType
    """

    _validation = {
        "distribution_type": {"required": True},
    }

    _attribute_map = {
        "distribution_type": {"key": "distributionType", "type": "str"},
    }

    _subtype_map = {"distribution_type": {"Mpi": "Mpi", "PyTorch": "PyTorch", "TensorFlow": "TensorFlow"}}

    def __init__(self, **kwargs):
        """ """
        super(DistributionConfiguration, self).__init__(**kwargs)
        self.distribution_type = None  # type: Optional[str]


class DockerCredentialDto(msrest.serialization.Model):
    """DockerCredentialDto.

    :ivar password:
    :vartype password: str
    :ivar user_name:
    :vartype user_name: str
    """

    _attribute_map = {
        "password": {"key": "password", "type": "str"},
        "user_name": {"key": "userName", "type": "str"},
    }

    def __init__(self, *, password: Optional[str] = None, user_name: Optional[str] = None, **kwargs):
        """
        :keyword password:
        :paramtype password: str
        :keyword user_name:
        :paramtype user_name: str
        """
        super(DockerCredentialDto, self).__init__(**kwargs)
        self.password = password
        self.user_name = user_name


class EnvironmentContainerData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.EnvironmentContainerDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "EnvironmentContainerDetails"},
    }

    def __init__(self, *, properties: "EnvironmentContainerDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.EnvironmentContainerDetails
        """
        super(EnvironmentContainerData, self).__init__(**kwargs)
        self.properties = properties


class EnvironmentContainerDetails(AssetContainer):
    """Container for environment specification versions.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar latest_version: The latest version inside this container.
    :vartype latest_version: str
    :ivar next_version: The next auto incremental version.
    :vartype next_version: str
    """

    _validation = {
        "latest_version": {"readonly": True},
        "next_version": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "latest_version": {"key": "latestVersion", "type": "str"},
        "next_version": {"key": "nextVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        """
        super(EnvironmentContainerDetails, self).__init__(
            description=description, properties=properties, tags=tags, is_archived=is_archived, **kwargs
        )


class EnvironmentContainerResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of EnvironmentContainer entities.

    :ivar next_link: The link to the next page of EnvironmentContainer objects. If null, there are
     no additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type EnvironmentContainer.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.EnvironmentContainerData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[EnvironmentContainerData]"},
    }

    def __init__(
        self, *, next_link: Optional[str] = None, value: Optional[List["EnvironmentContainerData"]] = None, **kwargs
    ):
        """
        :keyword next_link: The link to the next page of EnvironmentContainer objects. If null, there
         are no additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type EnvironmentContainer.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.EnvironmentContainerData]
        """
        super(EnvironmentContainerResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class EnvironmentVersionData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.EnvironmentVersionDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "EnvironmentVersionDetails"},
    }

    def __init__(self, *, properties: "EnvironmentVersionDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.EnvironmentVersionDetails
        """
        super(EnvironmentVersionData, self).__init__(**kwargs)
        self.properties = properties


class EnvironmentVersionDetails(AssetBase):
    """Environment version details.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar build: Configuration settings for Docker build context.
    :vartype build: ~azure.mgmt.machinelearningservices.models.BuildContext
    :ivar conda_file: Standard configuration file used by Conda that lets you install any kind of
     package, including Python, R, and C/C++ packages.


     .. raw:: html

        <see
     href="https://repo2docker.readthedocs.io/en/latest/config_files.html#environment-yml-install-a-conda-environment"
     />.
    :vartype conda_file: str
    :ivar environment_type: Environment type is either user managed or curated by the Azure ML
     service


     .. raw:: html

        <see
     href="https://learn.microsoft.com/azure/machine-learning/resource-curated-environments"
     />. Possible values include: "Curated", "UserCreated".
    :vartype environment_type: str or ~azure.mgmt.machinelearningservices.models.EnvironmentType
    :ivar image: Name of the image that will be used for the environment.


     .. raw:: html

        <seealso
     href="https://learn.microsoft.com/azure/machine-learning/how-to-deploy-custom-docker-image#use-a-custom-base-image"
     />.
    :vartype image: str
    :ivar inference_config: Defines configuration specific to inference.
    :vartype inference_config:
     ~azure.mgmt.machinelearningservices.models.InferenceContainerProperties
    :ivar intellectual_property: Intellectual Property details. Used if environment is an
     Intellectual Property.
    :vartype intellectual_property: ~azure.mgmt.machinelearningservices.models.IntellectualProperty
    :ivar os_type: The OS type of the environment. Possible values include: "Linux", "Windows".
    :vartype os_type: str or ~azure.mgmt.machinelearningservices.models.OperatingSystemType
    :ivar stage: Stage in the environment lifecycle assigned to this environment.
    :vartype stage: str
    """

    _validation = {
        "environment_type": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "build": {"key": "build", "type": "BuildContext"},
        "conda_file": {"key": "condaFile", "type": "str"},
        "environment_type": {"key": "environmentType", "type": "str"},
        "image": {"key": "image", "type": "str"},
        "inference_config": {"key": "inferenceConfig", "type": "InferenceContainerProperties"},
        "intellectual_property": {"key": "intellectualProperty", "type": "IntellectualProperty"},
        "os_type": {"key": "osType", "type": "str"},
        "stage": {"key": "stage", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        build: Optional["BuildContext"] = None,
        conda_file: Optional[str] = None,
        image: Optional[str] = None,
        inference_config: Optional["InferenceContainerProperties"] = None,
        intellectual_property: Optional["IntellectualProperty"] = None,
        os_type: Optional[Union[str, "OperatingSystemType"]] = None,
        stage: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword build: Configuration settings for Docker build context.
        :paramtype build: ~azure.mgmt.machinelearningservices.models.BuildContext
        :keyword conda_file: Standard configuration file used by Conda that lets you install any kind
         of package, including Python, R, and C/C++ packages.


         .. raw:: html

            <see
         href="https://repo2docker.readthedocs.io/en/latest/config_files.html#environment-yml-install-a-conda-environment"
         />.
        :paramtype conda_file: str
        :keyword image: Name of the image that will be used for the environment.


         .. raw:: html

            <seealso
         href="https://learn.microsoft.com/azure/machine-learning/how-to-deploy-custom-docker-image#use-a-custom-base-image"
         />.
        :paramtype image: str
        :keyword inference_config: Defines configuration specific to inference.
        :paramtype inference_config:
         ~azure.mgmt.machinelearningservices.models.InferenceContainerProperties
        :keyword intellectual_property: Intellectual Property details. Used if environment is an
         Intellectual Property.
        :paramtype intellectual_property:
         ~azure.mgmt.machinelearningservices.models.IntellectualProperty
        :keyword os_type: The OS type of the environment. Possible values include: "Linux", "Windows".
        :paramtype os_type: str or ~azure.mgmt.machinelearningservices.models.OperatingSystemType
        :keyword stage: Stage in the environment lifecycle assigned to this environment.
        :paramtype stage: str
        """
        super(EnvironmentVersionDetails, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            **kwargs
        )
        self.build = build
        self.conda_file = conda_file
        self.environment_type = None
        self.image = image
        self.inference_config = inference_config
        self.intellectual_property = intellectual_property
        self.os_type = os_type
        self.stage = stage


class EnvironmentVersionResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of EnvironmentVersion entities.

    :ivar next_link: The link to the next page of EnvironmentVersion objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type EnvironmentVersion.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.EnvironmentVersionData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[EnvironmentVersionData]"},
    }

    def __init__(
        self, *, next_link: Optional[str] = None, value: Optional[List["EnvironmentVersionData"]] = None, **kwargs
    ):
        """
        :keyword next_link: The link to the next page of EnvironmentVersion objects. If null, there are
         no additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type EnvironmentVersion.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.EnvironmentVersionData]
        """
        super(EnvironmentVersionResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class ErrorAdditionalInfo(msrest.serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: any
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs):
        """ """
        super(ErrorAdditionalInfo, self).__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(msrest.serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.machinelearningservices.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info: list[~azure.mgmt.machinelearningservices.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs):
        """ """
        super(ErrorDetail, self).__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(msrest.serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.machinelearningservices.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["ErrorDetail"] = None, **kwargs):
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.machinelearningservices.models.ErrorDetail
        """
        super(ErrorResponse, self).__init__(**kwargs)
        self.error = error


class FlavorData(msrest.serialization.Model):
    """FlavorData.

    :ivar data: Model flavor-specific data.
    :vartype data: dict[str, str]
    """

    _attribute_map = {
        "data": {"key": "data", "type": "{str}"},
    }

    def __init__(self, *, data: Optional[Dict[str, str]] = None, **kwargs):
        """
        :keyword data: Model flavor-specific data.
        :paramtype data: dict[str, str]
        """
        super(FlavorData, self).__init__(**kwargs)
        self.data = data


class IdAssetReference(AssetReferenceBase):
    """Reference to an asset via its ARM resource ID.

    All required parameters must be populated in order to send to Azure.

    :ivar reference_type: Required. Specifies the type of asset reference.Constant filled by
     server. Possible values include: "Id", "DataPath", "OutputPath".
    :vartype reference_type: str or ~azure.mgmt.machinelearningservices.models.ReferenceType
    :ivar asset_id: Required. ARM resource ID of the asset.
    :vartype asset_id: str
    """

    _validation = {
        "reference_type": {"required": True},
        "asset_id": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "reference_type": {"key": "referenceType", "type": "str"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(self, *, asset_id: str, **kwargs):
        """
        :keyword asset_id: Required. ARM resource ID of the asset.
        :paramtype asset_id: str
        """
        super(IdAssetReference, self).__init__(**kwargs)
        self.reference_type = "Id"  # type: str
        self.asset_id = asset_id


class ImageReferenceForConsumptionDto(msrest.serialization.Model):
    """ImageReferenceForConsumptionDto.

    :ivar acr_details:
    :vartype acr_details: ~azure.mgmt.machinelearningservices.models.AcrDetail
    :ivar credential:
    :vartype credential: ~azure.mgmt.machinelearningservices.models.DataReferenceCredentialDto
    :ivar image_name:
    :vartype image_name: str
    :ivar image_registry_reference:
    :vartype image_registry_reference: str
    """

    _attribute_map = {
        "acr_details": {"key": "acrDetails", "type": "AcrDetail"},
        "credential": {"key": "credential", "type": "DataReferenceCredentialDto"},
        "image_name": {"key": "imageName", "type": "str"},
        "image_registry_reference": {"key": "imageRegistryReference", "type": "str"},
    }

    def __init__(
        self,
        *,
        acr_details: Optional["AcrDetail"] = None,
        credential: Optional["DataReferenceCredentialDto"] = None,
        image_name: Optional[str] = None,
        image_registry_reference: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword acr_details:
        :paramtype acr_details: ~azure.mgmt.machinelearningservices.models.AcrDetail
        :keyword credential:
        :paramtype credential: ~azure.mgmt.machinelearningservices.models.DataReferenceCredentialDto
        :keyword image_name:
        :paramtype image_name: str
        :keyword image_registry_reference:
        :paramtype image_registry_reference: str
        """
        super(ImageReferenceForConsumptionDto, self).__init__(**kwargs)
        self.acr_details = acr_details
        self.credential = credential
        self.image_name = image_name
        self.image_registry_reference = image_registry_reference


class InferenceContainerProperties(msrest.serialization.Model):
    """InferenceContainerProperties.

    :ivar liveness_route: The route to check the liveness of the inference server container.
    :vartype liveness_route: ~azure.mgmt.machinelearningservices.models.Route
    :ivar readiness_route: The route to check the readiness of the inference server container.
    :vartype readiness_route: ~azure.mgmt.machinelearningservices.models.Route
    :ivar scoring_route: The port to send the scoring requests to, within the inference server
     container.
    :vartype scoring_route: ~azure.mgmt.machinelearningservices.models.Route
    """

    _attribute_map = {
        "liveness_route": {"key": "livenessRoute", "type": "Route"},
        "readiness_route": {"key": "readinessRoute", "type": "Route"},
        "scoring_route": {"key": "scoringRoute", "type": "Route"},
    }

    def __init__(
        self,
        *,
        liveness_route: Optional["Route"] = None,
        readiness_route: Optional["Route"] = None,
        scoring_route: Optional["Route"] = None,
        **kwargs
    ):
        """
        :keyword liveness_route: The route to check the liveness of the inference server container.
        :paramtype liveness_route: ~azure.mgmt.machinelearningservices.models.Route
        :keyword readiness_route: The route to check the readiness of the inference server container.
        :paramtype readiness_route: ~azure.mgmt.machinelearningservices.models.Route
        :keyword scoring_route: The port to send the scoring requests to, within the inference server
         container.
        :paramtype scoring_route: ~azure.mgmt.machinelearningservices.models.Route
        """
        super(InferenceContainerProperties, self).__init__(**kwargs)
        self.liveness_route = liveness_route
        self.readiness_route = readiness_route
        self.scoring_route = scoring_route


class IntellectualProperty(msrest.serialization.Model):
    """Intellectual Property details for a resource.

    All required parameters must be populated in order to send to Azure.

    :ivar protection_level: Protection level of the Intellectual Property. Possible values include:
     "All", "None".
    :vartype protection_level: str or ~azure.mgmt.machinelearningservices.models.ProtectionLevel
    :ivar publisher: Required. Publisher of the Intellectual Property. Must be the same as Registry
     publisher name.
    :vartype publisher: str
    """

    _validation = {
        "publisher": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "protection_level": {"key": "protectionLevel", "type": "str"},
        "publisher": {"key": "publisher", "type": "str"},
    }

    def __init__(self, *, publisher: str, protection_level: Optional[Union[str, "ProtectionLevel"]] = None, **kwargs):
        """
        :keyword protection_level: Protection level of the Intellectual Property. Possible values
         include: "All", "None".
        :paramtype protection_level: str or ~azure.mgmt.machinelearningservices.models.ProtectionLevel
        :keyword publisher: Required. Publisher of the Intellectual Property. Must be the same as
         Registry publisher name.
        :paramtype publisher: str
        """
        super(IntellectualProperty, self).__init__(**kwargs)
        self.protection_level = protection_level
        self.publisher = publisher


class Job(JobBase):
    """Basic Job class with all job base properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar compute_id: ARM resource ID of the compute resource.
    :vartype compute_id: str
    :ivar display_name: Display name of job.
    :vartype display_name: str
    :ivar experiment_name: The name of the experiment the job belongs to. If not set, the job is
     placed in the "Default" experiment.
    :vartype experiment_name: str
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar job_type: Required. Specifies the type of job.Constant filled by server. Possible values
     include: "Command", "Sweep", "Pipeline", "Base".
    :vartype job_type: str or ~azure.mgmt.machinelearningservices.models.JobType
    :ivar parent_job_name: TODO - Parent job name.
    :vartype parent_job_name: str
    :ivar services: List of JobEndpoints.
     For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
    :ivar status: Status of the job. Possible values include: "NotStarted", "Starting",
     "Provisioning", "Preparing", "Queued", "Running", "Finalizing", "CancelRequested", "Completed",
     "Failed", "Canceled", "NotResponding", "Paused", "Unknown".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.JobStatus
    """

    _validation = {
        "job_type": {"required": True},
        "parent_job_name": {"readonly": True},
        "status": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "compute_id": {"key": "computeId", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "job_type": {"key": "jobType", "type": "str"},
        "parent_job_name": {"key": "parentJobName", "type": "str"},
        "services": {"key": "services", "type": "{JobService}"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        compute_id: Optional[str] = None,
        display_name: Optional[str] = None,
        experiment_name: Optional[str] = "Default",
        is_archived: Optional[bool] = False,
        services: Optional[Dict[str, "JobService"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword compute_id: ARM resource ID of the compute resource.
        :paramtype compute_id: str
        :keyword display_name: Display name of job.
        :paramtype display_name: str
        :keyword experiment_name: The name of the experiment the job belongs to. If not set, the job is
         placed in the "Default" experiment.
        :paramtype experiment_name: str
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword services: List of JobEndpoints.
         For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
        """
        super(Job, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            compute_id=compute_id,
            display_name=display_name,
            experiment_name=experiment_name,
            is_archived=is_archived,
            services=services,
            **kwargs
        )
        self.job_type = "Base"  # type: str


class JobInput(msrest.serialization.Model):
    """Command job definition.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: JobInputDataset, JobInputLiteral, JobInputUri.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the input.
    :vartype description: str
    :ivar job_input_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Dataset", "Uri", "Literal".
    :vartype job_input_type: str or ~azure.mgmt.machinelearningservices.models.JobInputType
    """

    _validation = {
        "job_input_type": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_input_type": {"key": "jobInputType", "type": "str"},
    }

    _subtype_map = {
        "job_input_type": {"Dataset": "JobInputDataset", "Literal": "JobInputLiteral", "Uri": "JobInputUri"}
    }

    def __init__(self, *, description: Optional[str] = None, **kwargs):
        """
        :keyword description: Description for the input.
        :paramtype description: str
        """
        super(JobInput, self).__init__(**kwargs)
        self.description = description
        self.job_input_type = None  # type: Optional[str]


class JobInputDataset(JobInput):
    """InputDataset type.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the input.
    :vartype description: str
    :ivar job_input_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Dataset", "Uri", "Literal".
    :vartype job_input_type: str or ~azure.mgmt.machinelearningservices.models.JobInputType
    :ivar dataset_id: Required. Dataset ARM Id for the input.
    :vartype dataset_id: str
    :ivar mode: Dataset Delivery Mode. Possible values include: "ReadOnlyMount", "ReadWriteMount",
     "Download".
    :vartype mode: str or ~azure.mgmt.machinelearningservices.models.InputDataDeliveryMode
    """

    _validation = {
        "job_input_type": {"required": True},
        "dataset_id": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_input_type": {"key": "jobInputType", "type": "str"},
        "dataset_id": {"key": "datasetId", "type": "str"},
        "mode": {"key": "mode", "type": "str"},
    }

    def __init__(
        self,
        *,
        dataset_id: str,
        description: Optional[str] = None,
        mode: Optional[Union[str, "InputDataDeliveryMode"]] = None,
        **kwargs
    ):
        """
        :keyword description: Description for the input.
        :paramtype description: str
        :keyword dataset_id: Required. Dataset ARM Id for the input.
        :paramtype dataset_id: str
        :keyword mode: Dataset Delivery Mode. Possible values include: "ReadOnlyMount",
         "ReadWriteMount", "Download".
        :paramtype mode: str or ~azure.mgmt.machinelearningservices.models.InputDataDeliveryMode
        """
        super(JobInputDataset, self).__init__(description=description, **kwargs)
        self.job_input_type = "Dataset"  # type: str
        self.dataset_id = dataset_id
        self.mode = mode


class JobInputLiteral(JobInput):
    """Literal input type.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the input.
    :vartype description: str
    :ivar job_input_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Dataset", "Uri", "Literal".
    :vartype job_input_type: str or ~azure.mgmt.machinelearningservices.models.JobInputType
    :ivar value: Literal value for the input.
    :vartype value: str
    """

    _validation = {
        "job_input_type": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_input_type": {"key": "jobInputType", "type": "str"},
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, *, description: Optional[str] = None, value: Optional[str] = None, **kwargs):
        """
        :keyword description: Description for the input.
        :paramtype description: str
        :keyword value: Literal value for the input.
        :paramtype value: str
        """
        super(JobInputLiteral, self).__init__(description=description, **kwargs)
        self.job_input_type = "Literal"  # type: str
        self.value = value


class JobInputUri(JobInput):
    """Input uri type.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the input.
    :vartype description: str
    :ivar job_input_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Dataset", "Uri", "Literal".
    :vartype job_input_type: str or ~azure.mgmt.machinelearningservices.models.JobInputType
    :ivar mode: Input Uri Delivery Mode. Possible values include: "ReadOnlyMount",
     "ReadWriteMount", "Download".
    :vartype mode: str or ~azure.mgmt.machinelearningservices.models.InputDataDeliveryMode
    :ivar uri: Required. Uri path.
    :vartype uri: ~azure.mgmt.machinelearningservices.models.UriReference
    """

    _validation = {
        "job_input_type": {"required": True},
        "uri": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_input_type": {"key": "jobInputType", "type": "str"},
        "mode": {"key": "mode", "type": "str"},
        "uri": {"key": "uri", "type": "UriReference"},
    }

    def __init__(
        self,
        *,
        uri: "UriReference",
        description: Optional[str] = None,
        mode: Optional[Union[str, "InputDataDeliveryMode"]] = None,
        **kwargs
    ):
        """
        :keyword description: Description for the input.
        :paramtype description: str
        :keyword mode: Input Uri Delivery Mode. Possible values include: "ReadOnlyMount",
         "ReadWriteMount", "Download".
        :paramtype mode: str or ~azure.mgmt.machinelearningservices.models.InputDataDeliveryMode
        :keyword uri: Required. Uri path.
        :paramtype uri: ~azure.mgmt.machinelearningservices.models.UriReference
        """
        super(JobInputUri, self).__init__(description=description, **kwargs)
        self.job_input_type = "Uri"  # type: str
        self.mode = mode
        self.uri = uri


class JobOutput(msrest.serialization.Model):
    """Job output definition container information on where to find job output/logs.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: JobOutputDataset, JobOutputUri.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the output.
    :vartype description: str
    :ivar job_output_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Uri", "Dataset".
    :vartype job_output_type: str or ~azure.mgmt.machinelearningservices.models.JobOutputType
    """

    _validation = {
        "job_output_type": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_output_type": {"key": "jobOutputType", "type": "str"},
    }

    _subtype_map = {"job_output_type": {"Dataset": "JobOutputDataset", "Uri": "JobOutputUri"}}

    def __init__(self, *, description: Optional[str] = None, **kwargs):
        """
        :keyword description: Description for the output.
        :paramtype description: str
        """
        super(JobOutput, self).__init__(**kwargs)
        self.description = description
        self.job_output_type = None  # type: Optional[str]


class JobOutputDataset(JobOutput):
    """Dataset output.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the output.
    :vartype description: str
    :ivar job_output_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Uri", "Dataset".
    :vartype job_output_type: str or ~azure.mgmt.machinelearningservices.models.JobOutputType
    :ivar mode: Output Delivery Mode. Possible values include: "ReadWriteMount", "Upload".
    :vartype mode: str or ~azure.mgmt.machinelearningservices.models.OutputDataDeliveryMode
    """

    _validation = {
        "job_output_type": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_output_type": {"key": "jobOutputType", "type": "str"},
        "mode": {"key": "mode", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        mode: Optional[Union[str, "OutputDataDeliveryMode"]] = None,
        **kwargs
    ):
        """
        :keyword description: Description for the output.
        :paramtype description: str
        :keyword mode: Output Delivery Mode. Possible values include: "ReadWriteMount", "Upload".
        :paramtype mode: str or ~azure.mgmt.machinelearningservices.models.OutputDataDeliveryMode
        """
        super(JobOutputDataset, self).__init__(description=description, **kwargs)
        self.job_output_type = "Dataset"  # type: str
        self.mode = mode


class JobOutputUri(JobOutput):
    """Uri output.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Description for the output.
    :vartype description: str
    :ivar job_output_type: Required. Specifies the type of job.Constant filled by server. Possible
     values include: "Uri", "Dataset".
    :vartype job_output_type: str or ~azure.mgmt.machinelearningservices.models.JobOutputType
    :ivar mode: Output Delivery Mode. Possible values include: "ReadWriteMount", "Upload".
    :vartype mode: str or ~azure.mgmt.machinelearningservices.models.OutputDataDeliveryMode
    :ivar uri: Uri path.
    :vartype uri: ~azure.mgmt.machinelearningservices.models.UriReference
    """

    _validation = {
        "job_output_type": {"required": True},
        "mode": {"readonly": True},
        "uri": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "job_output_type": {"key": "jobOutputType", "type": "str"},
        "mode": {"key": "mode", "type": "str"},
        "uri": {"key": "uri", "type": "UriReference"},
    }

    def __init__(self, *, description: Optional[str] = None, **kwargs):
        """
        :keyword description: Description for the output.
        :paramtype description: str
        """
        super(JobOutputUri, self).__init__(description=description, **kwargs)
        self.job_output_type = "Uri"  # type: str
        self.mode = None
        self.uri = None


class JobService(msrest.serialization.Model):
    """Job endpoint definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar endpoint: Url for endpoint.
    :vartype endpoint: str
    :ivar error_message: Any error in the service.
    :vartype error_message: str
    :ivar job_service_type: Endpoint type.
    :vartype job_service_type: str
    :ivar port: Port for endpoint.
    :vartype port: int
    :ivar properties: Additional properties to set on the endpoint.
    :vartype properties: dict[str, str]
    :ivar status: Status of endpoint.
    :vartype status: str
    """

    _validation = {
        "error_message": {"readonly": True},
        "status": {"readonly": True},
    }

    _attribute_map = {
        "endpoint": {"key": "endpoint", "type": "str"},
        "error_message": {"key": "errorMessage", "type": "str"},
        "job_service_type": {"key": "jobServiceType", "type": "str"},
        "port": {"key": "port", "type": "int"},
        "properties": {"key": "properties", "type": "{str}"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        endpoint: Optional[str] = None,
        job_service_type: Optional[str] = None,
        port: Optional[int] = None,
        properties: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """
        :keyword endpoint: Url for endpoint.
        :paramtype endpoint: str
        :keyword job_service_type: Endpoint type.
        :paramtype job_service_type: str
        :keyword port: Port for endpoint.
        :paramtype port: int
        :keyword properties: Additional properties to set on the endpoint.
        :paramtype properties: dict[str, str]
        """
        super(JobService, self).__init__(**kwargs)
        self.endpoint = endpoint
        self.error_message = None
        self.job_service_type = job_service_type
        self.port = port
        self.properties = properties
        self.status = None


class ManagedIdentity(IdentityConfiguration):
    """Managed identity configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar identity_type: Required. Specifies the type of identity framework.Constant filled by
     server. Possible values include: "Managed", "AMLToken".
    :vartype identity_type: str or
     ~azure.mgmt.machinelearningservices.models.IdentityConfigurationType
    :ivar client_id: Specifies a user-assigned identity by client ID. For system-assigned, do not
     set this field.
    :vartype client_id: str
    :ivar object_id: Specifies a user-assigned identity by object ID. For system-assigned, do not
     set this field.
    :vartype object_id: str
    :ivar resource_id: Specifies a user-assigned identity by ARM resource ID. For system-assigned,
     do not set this field.
    :vartype resource_id: str
    """

    _validation = {
        "identity_type": {"required": True},
    }

    _attribute_map = {
        "identity_type": {"key": "identityType", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
        "object_id": {"key": "objectId", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
    }

    def __init__(
        self,
        *,
        client_id: Optional[str] = None,
        object_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword client_id: Specifies a user-assigned identity by client ID. For system-assigned, do
         not set this field.
        :paramtype client_id: str
        :keyword object_id: Specifies a user-assigned identity by object ID. For system-assigned, do
         not set this field.
        :paramtype object_id: str
        :keyword resource_id: Specifies a user-assigned identity by ARM resource ID. For
         system-assigned, do not set this field.
        :paramtype resource_id: str
        """
        super(ManagedIdentity, self).__init__(**kwargs)
        self.identity_type = "Managed"  # type: str
        self.client_id = client_id
        self.object_id = object_id
        self.resource_id = resource_id


class ManagedIdentityCredentialDto(msrest.serialization.Model):
    """ManagedIdentityCredentialDto.

    :ivar managed_identity_type:
    :vartype managed_identity_type: str
    :ivar user_managed_identity_client_id: ClientId for the UAMI. For ManagedIdentityType =
     SystemManaged, this field is null.
    :vartype user_managed_identity_client_id: str
    :ivar user_managed_identity_principal_id: PrincipalId for the UAMI. For ManagedIdentityType =
     SystemManaged, this field is null.
    :vartype user_managed_identity_principal_id: str
    :ivar user_managed_identity_resource_id: Full arm scope for the Id. For ManagedIdentityType =
     SystemManaged, this field is null.
    :vartype user_managed_identity_resource_id: str
    :ivar user_managed_identity_tenant_id: TenantId for the UAMI. For ManagedIdentityType =
     SystemManaged, this field is null.
    :vartype user_managed_identity_tenant_id: str
    """

    _attribute_map = {
        "managed_identity_type": {"key": "managedIdentityType", "type": "str"},
        "user_managed_identity_client_id": {"key": "userManagedIdentityClientId", "type": "str"},
        "user_managed_identity_principal_id": {"key": "userManagedIdentityPrincipalId", "type": "str"},
        "user_managed_identity_resource_id": {"key": "userManagedIdentityResourceId", "type": "str"},
        "user_managed_identity_tenant_id": {"key": "userManagedIdentityTenantId", "type": "str"},
    }

    def __init__(
        self,
        *,
        managed_identity_type: Optional[str] = None,
        user_managed_identity_client_id: Optional[str] = None,
        user_managed_identity_principal_id: Optional[str] = None,
        user_managed_identity_resource_id: Optional[str] = None,
        user_managed_identity_tenant_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword managed_identity_type:
        :paramtype managed_identity_type: str
        :keyword user_managed_identity_client_id: ClientId for the UAMI. For ManagedIdentityType =
         SystemManaged, this field is null.
        :paramtype user_managed_identity_client_id: str
        :keyword user_managed_identity_principal_id: PrincipalId for the UAMI. For ManagedIdentityType
         = SystemManaged, this field is null.
        :paramtype user_managed_identity_principal_id: str
        :keyword user_managed_identity_resource_id: Full arm scope for the Id. For ManagedIdentityType
         = SystemManaged, this field is null.
        :paramtype user_managed_identity_resource_id: str
        :keyword user_managed_identity_tenant_id: TenantId for the UAMI. For ManagedIdentityType =
         SystemManaged, this field is null.
        :paramtype user_managed_identity_tenant_id: str
        """
        super(ManagedIdentityCredentialDto, self).__init__(**kwargs)
        self.managed_identity_type = managed_identity_type
        self.user_managed_identity_client_id = user_managed_identity_client_id
        self.user_managed_identity_principal_id = user_managed_identity_principal_id
        self.user_managed_identity_resource_id = user_managed_identity_resource_id
        self.user_managed_identity_tenant_id = user_managed_identity_tenant_id


class MedianStoppingPolicy(EarlyTerminationPolicy):
    """Defines an early termination policy based on running averages of the primary metric of all runs.

    All required parameters must be populated in order to send to Azure.

    :ivar delay_evaluation: Number of intervals by which to delay the first evaluation.
    :vartype delay_evaluation: int
    :ivar evaluation_interval: Interval (number of runs) between policy evaluations.
    :vartype evaluation_interval: int
    :ivar policy_type: Required. Name of policy configuration.Constant filled by server. Possible
     values include: "Bandit", "MedianStopping", "TruncationSelection".
    :vartype policy_type: str or
     ~azure.mgmt.machinelearningservices.models.EarlyTerminationPolicyType
    """

    _validation = {
        "policy_type": {"required": True},
    }

    _attribute_map = {
        "delay_evaluation": {"key": "delayEvaluation", "type": "int"},
        "evaluation_interval": {"key": "evaluationInterval", "type": "int"},
        "policy_type": {"key": "policyType", "type": "str"},
    }

    def __init__(self, *, delay_evaluation: Optional[int] = 0, evaluation_interval: Optional[int] = 0, **kwargs):
        """
        :keyword delay_evaluation: Number of intervals by which to delay the first evaluation.
        :paramtype delay_evaluation: int
        :keyword evaluation_interval: Interval (number of runs) between policy evaluations.
        :paramtype evaluation_interval: int
        """
        super(MedianStoppingPolicy, self).__init__(
            delay_evaluation=delay_evaluation, evaluation_interval=evaluation_interval, **kwargs
        )
        self.policy_type = "MedianStopping"  # type: str


class MLTableData(DataVersionBaseDetails):
    """MLTable data definition.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar data_type: Required. Specifies the type of data.Constant filled by server. Possible
     values include: "uri_file", "uri_folder", "mltable".
    :vartype data_type: str or ~azure.mgmt.machinelearningservices.models.DataType
    :ivar data_uri: Required. Uri of the data. Usage/meaning depends on
     Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
    :vartype data_uri: str
    :ivar intellectual_property: Intellectual Property details. Used if data is an Intellectual
     Property.
    :vartype intellectual_property: ~azure.mgmt.machinelearningservices.models.IntellectualProperty
    :ivar referenced_uris: Uris referenced in the MLTable definition (required for lineage).
    :vartype referenced_uris: list[str]
    """

    _validation = {
        "data_type": {"required": True},
        "data_uri": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "data_type": {"key": "dataType", "type": "str"},
        "data_uri": {"key": "dataUri", "type": "str"},
        "intellectual_property": {"key": "intellectualProperty", "type": "IntellectualProperty"},
        "referenced_uris": {"key": "referencedUris", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        data_uri: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        intellectual_property: Optional["IntellectualProperty"] = None,
        referenced_uris: Optional[List[str]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword data_uri: Required. Uri of the data. Usage/meaning depends on
         Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
        :paramtype data_uri: str
        :keyword intellectual_property: Intellectual Property details. Used if data is an Intellectual
         Property.
        :paramtype intellectual_property:
         ~azure.mgmt.machinelearningservices.models.IntellectualProperty
        :keyword referenced_uris: Uris referenced in the MLTable definition (required for lineage).
        :paramtype referenced_uris: list[str]
        """
        super(MLTableData, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            data_uri=data_uri,
            intellectual_property=intellectual_property,
            **kwargs
        )
        self.data_type = "mltable"  # type: str
        self.referenced_uris = referenced_uris


class ModelConfiguration(msrest.serialization.Model):
    """Model configuration options.

    :ivar mode: Input delivery mode for the model. Possible values include: "Copy", "Download".
    :vartype mode: str or ~azure.mgmt.machinelearningservices.models.PackageInputDeliveryMode
    :ivar mount_path: Relative mounting path of the model in the target image.
    :vartype mount_path: str
    """

    _attribute_map = {
        "mode": {"key": "mode", "type": "str"},
        "mount_path": {"key": "mountPath", "type": "str"},
    }

    def __init__(
        self,
        *,
        mode: Optional[Union[str, "PackageInputDeliveryMode"]] = None,
        mount_path: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword mode: Input delivery mode for the model. Possible values include: "Copy", "Download".
        :paramtype mode: str or ~azure.mgmt.machinelearningservices.models.PackageInputDeliveryMode
        :keyword mount_path: Relative mounting path of the model in the target image.
        :paramtype mount_path: str
        """
        super(ModelConfiguration, self).__init__(**kwargs)
        self.mode = mode
        self.mount_path = mount_path


class ModelContainerData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.ModelContainerDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ModelContainerDetails"},
    }

    def __init__(self, *, properties: "ModelContainerDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.ModelContainerDetails
        """
        super(ModelContainerData, self).__init__(**kwargs)
        self.properties = properties


class ModelContainerDetails(AssetContainer):
    """ModelContainerDetails.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar latest_version: The latest version inside this container.
    :vartype latest_version: str
    :ivar next_version: The next auto incremental version.
    :vartype next_version: str
    """

    _validation = {
        "latest_version": {"readonly": True},
        "next_version": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "latest_version": {"key": "latestVersion", "type": "str"},
        "next_version": {"key": "nextVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_archived: Optional[bool] = False,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        """
        super(ModelContainerDetails, self).__init__(
            description=description, properties=properties, tags=tags, is_archived=is_archived, **kwargs
        )


class ModelContainerResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of ModelContainer entities.

    :ivar next_link: The link to the next page of ModelContainer objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type ModelContainer.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.ModelContainerData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[ModelContainerData]"},
    }

    def __init__(
        self, *, next_link: Optional[str] = None, value: Optional[List["ModelContainerData"]] = None, **kwargs
    ):
        """
        :keyword next_link: The link to the next page of ModelContainer objects. If null, there are no
         additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type ModelContainer.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.ModelContainerData]
        """
        super(ModelContainerResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class ModelPackageInput(msrest.serialization.Model):
    """Model package input options.

    All required parameters must be populated in order to send to Azure.

    :ivar input_type: Required. Type of the input included in the target image. Possible values
     include: "UriFile", "UriFolder".
    :vartype input_type: str or ~azure.mgmt.machinelearningservices.models.PackageInputType
    :ivar mode: Input delivery mode of the input. Possible values include: "Copy", "Download".
    :vartype mode: str or ~azure.mgmt.machinelearningservices.models.PackageInputDeliveryMode
    :ivar mount_path: Relative mount path of the input in the target image.
    :vartype mount_path: str
    :ivar path: Required. Location of the input.
    :vartype path: ~azure.mgmt.machinelearningservices.models.PackageInputPathBase
    """

    _validation = {
        "input_type": {"required": True},
        "path": {"required": True},
    }

    _attribute_map = {
        "input_type": {"key": "inputType", "type": "str"},
        "mode": {"key": "mode", "type": "str"},
        "mount_path": {"key": "mountPath", "type": "str"},
        "path": {"key": "path", "type": "PackageInputPathBase"},
    }

    def __init__(
        self,
        *,
        input_type: Union[str, "PackageInputType"],
        path: "PackageInputPathBase",
        mode: Optional[Union[str, "PackageInputDeliveryMode"]] = None,
        mount_path: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword input_type: Required. Type of the input included in the target image. Possible values
         include: "UriFile", "UriFolder".
        :paramtype input_type: str or ~azure.mgmt.machinelearningservices.models.PackageInputType
        :keyword mode: Input delivery mode of the input. Possible values include: "Copy", "Download".
        :paramtype mode: str or ~azure.mgmt.machinelearningservices.models.PackageInputDeliveryMode
        :keyword mount_path: Relative mount path of the input in the target image.
        :paramtype mount_path: str
        :keyword path: Required. Location of the input.
        :paramtype path: ~azure.mgmt.machinelearningservices.models.PackageInputPathBase
        """
        super(ModelPackageInput, self).__init__(**kwargs)
        self.input_type = input_type
        self.mode = mode
        self.mount_path = mount_path
        self.path = path


class ModelVersionData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties: ~azure.mgmt.machinelearningservices.models.ModelVersionDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ModelVersionDetails"},
    }

    def __init__(self, *, properties: "ModelVersionDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.ModelVersionDetails
        """
        super(ModelVersionData, self).__init__(**kwargs)
        self.properties = properties


class ModelVersionDetails(AssetBase):
    """Model asset version details.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar flavors: Mapping of model flavors to their properties.
    :vartype flavors: dict[str, ~azure.mgmt.machinelearningservices.models.FlavorData]
    :ivar intellectual_property: Intellectual Property details. Used if model is an Intellectual
     Property.
    :vartype intellectual_property: ~azure.mgmt.machinelearningservices.models.IntellectualProperty
    :ivar job_name: Name of the training job which produced this model.
    :vartype job_name: str
    :ivar model_type: The storage format for this entity. Used for NCD.
    :vartype model_type: str
    :ivar model_uri: The URI path to the model contents.
    :vartype model_uri: str
    :ivar origin_asset_id: AssetId of origin model.
    :vartype origin_asset_id: str
    """

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "flavors": {"key": "flavors", "type": "{FlavorData}"},
        "intellectual_property": {"key": "intellectualProperty", "type": "IntellectualProperty"},
        "job_name": {"key": "jobName", "type": "str"},
        "model_type": {"key": "modelType", "type": "str"},
        "model_uri": {"key": "modelUri", "type": "str"},
        "origin_asset_id": {"key": "originAssetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        flavors: Optional[Dict[str, "FlavorData"]] = None,
        intellectual_property: Optional["IntellectualProperty"] = None,
        job_name: Optional[str] = None,
        model_type: Optional[str] = None,
        model_uri: Optional[str] = None,
        origin_asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword flavors: Mapping of model flavors to their properties.
        :paramtype flavors: dict[str, ~azure.mgmt.machinelearningservices.models.FlavorData]
        :keyword intellectual_property: Intellectual Property details. Used if model is an Intellectual
         Property.
        :paramtype intellectual_property:
         ~azure.mgmt.machinelearningservices.models.IntellectualProperty
        :keyword job_name: Name of the training job which produced this model.
        :paramtype job_name: str
        :keyword model_type: The storage format for this entity. Used for NCD.
        :paramtype model_type: str
        :keyword model_uri: The URI path to the model contents.
        :paramtype model_uri: str
        :keyword origin_asset_id: AssetId of origin model.
        :paramtype origin_asset_id: str
        """
        super(ModelVersionDetails, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            **kwargs
        )
        self.flavors = flavors
        self.intellectual_property = intellectual_property
        self.job_name = job_name
        self.model_type = model_type
        self.model_uri = model_uri
        self.origin_asset_id = origin_asset_id


class ModelVersionResourceArmPaginatedResult(msrest.serialization.Model):
    """A paginated list of ModelVersion entities.

    :ivar next_link: The link to the next page of ModelVersion objects. If null, there are no
     additional pages.
    :vartype next_link: str
    :ivar value: An array of objects of type ModelVersion.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.ModelVersionData]
    """

    _attribute_map = {
        "next_link": {"key": "nextLink", "type": "str"},
        "value": {"key": "value", "type": "[ModelVersionData]"},
    }

    def __init__(self, *, next_link: Optional[str] = None, value: Optional[List["ModelVersionData"]] = None, **kwargs):
        """
        :keyword next_link: The link to the next page of ModelVersion objects. If null, there are no
         additional pages.
        :paramtype next_link: str
        :keyword value: An array of objects of type ModelVersion.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.ModelVersionData]
        """
        super(ModelVersionResourceArmPaginatedResult, self).__init__(**kwargs)
        self.next_link = next_link
        self.value = value


class Mpi(DistributionConfiguration):
    """MPI distribution configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar distribution_type: Required. Specifies the type of distribution framework.Constant filled
     by server. Possible values include: "PyTorch", "TensorFlow", "Mpi".
    :vartype distribution_type: str or ~azure.mgmt.machinelearningservices.models.DistributionType
    :ivar process_count_per_instance: Number of processes per MPI node.
    :vartype process_count_per_instance: int
    """

    _validation = {
        "distribution_type": {"required": True},
    }

    _attribute_map = {
        "distribution_type": {"key": "distributionType", "type": "str"},
        "process_count_per_instance": {"key": "processCountPerInstance", "type": "int"},
    }

    def __init__(self, *, process_count_per_instance: Optional[int] = None, **kwargs):
        """
        :keyword process_count_per_instance: Number of processes per MPI node.
        :paramtype process_count_per_instance: int
        """
        super(Mpi, self).__init__(**kwargs)
        self.distribution_type = "Mpi"  # type: str
        self.process_count_per_instance = process_count_per_instance


class NoneDatastoreCredentials(DatastoreCredentials):
    """Empty/none datastore credentials.

    All required parameters must be populated in order to send to Azure.

    :ivar credentials_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "None", "Sas",
     "ServicePrincipal".
    :vartype credentials_type: str or ~azure.mgmt.machinelearningservices.models.CredentialsType
    """

    _validation = {
        "credentials_type": {"required": True},
    }

    _attribute_map = {
        "credentials_type": {"key": "credentialsType", "type": "str"},
    }

    def __init__(self, **kwargs):
        """ """
        super(NoneDatastoreCredentials, self).__init__(**kwargs)
        self.credentials_type = "None"  # type: str


class Objective(msrest.serialization.Model):
    """Optimization objective.

    All required parameters must be populated in order to send to Azure.

    :ivar goal: Required. Defines supported metric goals for hyperparameter tuning. Possible values
     include: "Minimize", "Maximize".
    :vartype goal: str or ~azure.mgmt.machinelearningservices.models.Goal
    :ivar primary_metric: Required. Name of the metric to optimize.
    :vartype primary_metric: str
    """

    _validation = {
        "goal": {"required": True},
        "primary_metric": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "goal": {"key": "goal", "type": "str"},
        "primary_metric": {"key": "primaryMetric", "type": "str"},
    }

    def __init__(self, *, goal: Union[str, "Goal"], primary_metric: str, **kwargs):
        """
        :keyword goal: Required. Defines supported metric goals for hyperparameter tuning. Possible
         values include: "Minimize", "Maximize".
        :paramtype goal: str or ~azure.mgmt.machinelearningservices.models.Goal
        :keyword primary_metric: Required. Name of the metric to optimize.
        :paramtype primary_metric: str
        """
        super(Objective, self).__init__(**kwargs)
        self.goal = goal
        self.primary_metric = primary_metric


class OnlineInferenceConfiguration(msrest.serialization.Model):
    """Online inference configuration options.

    :ivar configurations: Additional configurations.
    :vartype configurations: dict[str, str]
    :ivar entry_script: Entry script or command to invoke.
    :vartype entry_script: str
    :ivar liveness_route: The route to check the liveness of the inference server container.
    :vartype liveness_route: ~azure.mgmt.machinelearningservices.models.Route
    :ivar readiness_route: The route to check the readiness of the inference server container.
    :vartype readiness_route: ~azure.mgmt.machinelearningservices.models.Route
    :ivar scoring_route: The port to send the scoring requests to, within the inference server
     container.
    :vartype scoring_route: ~azure.mgmt.machinelearningservices.models.Route
    """

    _attribute_map = {
        "configurations": {"key": "configurations", "type": "{str}"},
        "entry_script": {"key": "entryScript", "type": "str"},
        "liveness_route": {"key": "livenessRoute", "type": "Route"},
        "readiness_route": {"key": "readinessRoute", "type": "Route"},
        "scoring_route": {"key": "scoringRoute", "type": "Route"},
    }

    def __init__(
        self,
        *,
        configurations: Optional[Dict[str, str]] = None,
        entry_script: Optional[str] = None,
        liveness_route: Optional["Route"] = None,
        readiness_route: Optional["Route"] = None,
        scoring_route: Optional["Route"] = None,
        **kwargs
    ):
        """
        :keyword configurations: Additional configurations.
        :paramtype configurations: dict[str, str]
        :keyword entry_script: Entry script or command to invoke.
        :paramtype entry_script: str
        :keyword liveness_route: The route to check the liveness of the inference server container.
        :paramtype liveness_route: ~azure.mgmt.machinelearningservices.models.Route
        :keyword readiness_route: The route to check the readiness of the inference server container.
        :paramtype readiness_route: ~azure.mgmt.machinelearningservices.models.Route
        :keyword scoring_route: The port to send the scoring requests to, within the inference server
         container.
        :paramtype scoring_route: ~azure.mgmt.machinelearningservices.models.Route
        """
        super(OnlineInferenceConfiguration, self).__init__(**kwargs)
        self.configurations = configurations
        self.entry_script = entry_script
        self.liveness_route = liveness_route
        self.readiness_route = readiness_route
        self.scoring_route = scoring_route


class OutputPathAssetReference(AssetReferenceBase):
    """Reference to an asset via its path in a job output.

    All required parameters must be populated in order to send to Azure.

    :ivar reference_type: Required. Specifies the type of asset reference.Constant filled by
     server. Possible values include: "Id", "DataPath", "OutputPath".
    :vartype reference_type: str or ~azure.mgmt.machinelearningservices.models.ReferenceType
    :ivar job_id: ARM resource ID of the job.
    :vartype job_id: str
    :ivar path: The path of the file/directory in the job output.
    :vartype path: str
    """

    _validation = {
        "reference_type": {"required": True},
    }

    _attribute_map = {
        "reference_type": {"key": "referenceType", "type": "str"},
        "job_id": {"key": "jobId", "type": "str"},
        "path": {"key": "path", "type": "str"},
    }

    def __init__(self, *, job_id: Optional[str] = None, path: Optional[str] = None, **kwargs):
        """
        :keyword job_id: ARM resource ID of the job.
        :paramtype job_id: str
        :keyword path: The path of the file/directory in the job output.
        :paramtype path: str
        """
        super(OutputPathAssetReference, self).__init__(**kwargs)
        self.reference_type = "OutputPath"  # type: str
        self.job_id = job_id
        self.path = path


class PackageInputPathBase(msrest.serialization.Model):
    """PackageInputPathBase.

    You probably want to use the sub-classes and not this class directly. Known
    sub-classes are: PackageInputPathId, PackageInputPathVersion, PackageInputPathUrl.

    All required parameters must be populated in order to send to Azure.

    :ivar input_path_type: Required. Input path type for package inputs.Constant filled by server.
     Possible values include: "Url", "PathId", "PathVersion".
    :vartype input_path_type: str or ~azure.mgmt.machinelearningservices.models.InputPathType
    """

    _validation = {
        "input_path_type": {"required": True},
    }

    _attribute_map = {
        "input_path_type": {"key": "inputPathType", "type": "str"},
    }

    _subtype_map = {
        "input_path_type": {
            "PathId": "PackageInputPathId",
            "PathVersion": "PackageInputPathVersion",
            "Url": "PackageInputPathUrl",
        }
    }

    def __init__(self, **kwargs):
        """ """
        super(PackageInputPathBase, self).__init__(**kwargs)
        self.input_path_type = None  # type: Optional[str]


class PackageInputPathId(PackageInputPathBase):
    """Package input path specified with a resource id.

    All required parameters must be populated in order to send to Azure.

    :ivar input_path_type: Required. Input path type for package inputs.Constant filled by server.
     Possible values include: "Url", "PathId", "PathVersion".
    :vartype input_path_type: str or ~azure.mgmt.machinelearningservices.models.InputPathType
    :ivar resource_id: Input resource id.
    :vartype resource_id: str
    """

    _validation = {
        "input_path_type": {"required": True},
    }

    _attribute_map = {
        "input_path_type": {"key": "inputPathType", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
    }

    def __init__(self, *, resource_id: Optional[str] = None, **kwargs):
        """
        :keyword resource_id: Input resource id.
        :paramtype resource_id: str
        """
        super(PackageInputPathId, self).__init__(**kwargs)
        self.input_path_type = "PathId"  # type: str
        self.resource_id = resource_id


class PackageInputPathUrl(PackageInputPathBase):
    """Package input path specified as an url.

    All required parameters must be populated in order to send to Azure.

    :ivar input_path_type: Required. Input path type for package inputs.Constant filled by server.
     Possible values include: "Url", "PathId", "PathVersion".
    :vartype input_path_type: str or ~azure.mgmt.machinelearningservices.models.InputPathType
    :ivar url: Input path url.
    :vartype url: str
    """

    _validation = {
        "input_path_type": {"required": True},
    }

    _attribute_map = {
        "input_path_type": {"key": "inputPathType", "type": "str"},
        "url": {"key": "url", "type": "str"},
    }

    def __init__(self, *, url: Optional[str] = None, **kwargs):
        """
        :keyword url: Input path url.
        :paramtype url: str
        """
        super(PackageInputPathUrl, self).__init__(**kwargs)
        self.input_path_type = "Url"  # type: str
        self.url = url


class PackageInputPathVersion(PackageInputPathBase):
    """Package input path specified with name and version.

    All required parameters must be populated in order to send to Azure.

    :ivar input_path_type: Required. Input path type for package inputs.Constant filled by server.
     Possible values include: "Url", "PathId", "PathVersion".
    :vartype input_path_type: str or ~azure.mgmt.machinelearningservices.models.InputPathType
    :ivar resource_name: Input resource name.
    :vartype resource_name: str
    :ivar resource_version: Input resource version.
    :vartype resource_version: str
    """

    _validation = {
        "input_path_type": {"required": True},
    }

    _attribute_map = {
        "input_path_type": {"key": "inputPathType", "type": "str"},
        "resource_name": {"key": "resourceName", "type": "str"},
        "resource_version": {"key": "resourceVersion", "type": "str"},
    }

    def __init__(self, *, resource_name: Optional[str] = None, resource_version: Optional[str] = None, **kwargs):
        """
        :keyword resource_name: Input resource name.
        :paramtype resource_name: str
        :keyword resource_version: Input resource version.
        :paramtype resource_version: str
        """
        super(PackageInputPathVersion, self).__init__(**kwargs)
        self.input_path_type = "PathVersion"  # type: str
        self.resource_name = resource_name
        self.resource_version = resource_version


class PackageRequest(msrest.serialization.Model):
    """Model package operation request properties.

    All required parameters must be populated in order to send to Azure.

    :ivar base_environment_source: Base environment to start with.
    :vartype base_environment_source:
     ~azure.mgmt.machinelearningservices.models.BaseEnvironmentSource
    :ivar environment_variables: Collection of environment variables.
    :vartype environment_variables: dict[str, str]
    :ivar inferencing_server: Required. Inferencing server configurations.
    :vartype inferencing_server: ~azure.mgmt.machinelearningservices.models.InferencingServer
    :ivar inputs: Collection of inputs.
    :vartype inputs: list[~azure.mgmt.machinelearningservices.models.ModelPackageInput]
    :ivar model_configuration: Model configuration including the mount mode.
    :vartype model_configuration: ~azure.mgmt.machinelearningservices.models.ModelConfiguration
    :ivar properties: Properties dictionary.
    :vartype properties: dict[str, str]
    :ivar sku_architecture_type: The sku architecture type.
    :vartype sku_architecture_type: str
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar target_environment_id: Required. Arm ID of the target environment to be created by
     package operation.
    :vartype target_environment_id: str
    """

    _validation = {
        "inferencing_server": {"required": True},
        "target_environment_id": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "base_environment_source": {"key": "baseEnvironmentSource", "type": "BaseEnvironmentSource"},
        "environment_variables": {"key": "environmentVariables", "type": "{str}"},
        "inferencing_server": {"key": "inferencingServer", "type": "InferencingServer"},
        "inputs": {"key": "inputs", "type": "[ModelPackageInput]"},
        "model_configuration": {"key": "modelConfiguration", "type": "ModelConfiguration"},
        "properties": {"key": "properties", "type": "{str}"},
        "sku_architecture_type": {"key": "skuArchitectureType", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "target_environment_id": {"key": "targetEnvironmentId", "type": "str"},
    }

    def __init__(
        self,
        *,
        inferencing_server: "InferencingServer",
        target_environment_id: str,
        base_environment_source: Optional["BaseEnvironmentSource"] = None,
        environment_variables: Optional[Dict[str, str]] = None,
        inputs: Optional[List["ModelPackageInput"]] = None,
        model_configuration: Optional["ModelConfiguration"] = None,
        properties: Optional[Dict[str, str]] = None,
        sku_architecture_type: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """
        :keyword base_environment_source: Base environment to start with.
        :paramtype base_environment_source:
         ~azure.mgmt.machinelearningservices.models.BaseEnvironmentSource
        :keyword environment_variables: Collection of environment variables.
        :paramtype environment_variables: dict[str, str]
        :keyword inferencing_server: Required. Inferencing server configurations.
        :paramtype inferencing_server: ~azure.mgmt.machinelearningservices.models.InferencingServer
        :keyword inputs: Collection of inputs.
        :paramtype inputs: list[~azure.mgmt.machinelearningservices.models.ModelPackageInput]
        :keyword model_configuration: Model configuration including the mount mode.
        :paramtype model_configuration: ~azure.mgmt.machinelearningservices.models.ModelConfiguration
        :keyword properties: Properties dictionary.
        :paramtype properties: dict[str, str]
        :keyword sku_architecture_type: The sku architecture type.
        :paramtype sku_architecture_type: str
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword target_environment_id: Required. Arm ID of the target environment to be created by
         package operation.
        :paramtype target_environment_id: str
        """
        super(PackageRequest, self).__init__(**kwargs)
        self.base_environment_source = base_environment_source
        self.environment_variables = environment_variables
        self.inferencing_server = inferencing_server
        self.inputs = inputs
        self.model_configuration = model_configuration
        self.properties = properties
        self.sku_architecture_type = sku_architecture_type
        self.tags = tags
        self.target_environment_id = target_environment_id


class PackageResponse(msrest.serialization.Model):
    """Package response returned after async package operation completes successfully.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar base_environment_source: Base environment to start with.
    :vartype base_environment_source:
     ~azure.mgmt.machinelearningservices.models.BaseEnvironmentSource
    :ivar build_id: Build id of the image build operation.
    :vartype build_id: str
    :ivar build_state: Build state of the image build operation. Possible values include:
     "NotStarted", "Running", "Succeeded", "Failed".
    :vartype build_state: str or ~azure.mgmt.machinelearningservices.models.PackageBuildState
    :ivar environment_variables: Collection of environment variables.
    :vartype environment_variables: dict[str, str]
    :ivar inferencing_server: Inferencing server configurations.
    :vartype inferencing_server: ~azure.mgmt.machinelearningservices.models.InferencingServer
    :ivar inputs: Collection of inputs.
    :vartype inputs: list[~azure.mgmt.machinelearningservices.models.ModelPackageInput]
    :ivar log_url: Log url of the image build operation.
    :vartype log_url: str
    :ivar model_configuration: Model configuration including the mount mode.
    :vartype model_configuration: ~azure.mgmt.machinelearningservices.models.ModelConfiguration
    :ivar properties: Properties dictionary.
    :vartype properties: dict[str, str]
    :ivar sku_architecture_type: The sku architecture type.
    :vartype sku_architecture_type: str
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar target_environment_id: Asset ID of the target environment created by package operation.
    :vartype target_environment_id: str
    """

    _validation = {
        "base_environment_source": {"readonly": True},
        "build_id": {"readonly": True},
        "build_state": {"readonly": True},
        "environment_variables": {"readonly": True},
        "inferencing_server": {"readonly": True},
        "inputs": {"readonly": True},
        "log_url": {"readonly": True},
        "model_configuration": {"readonly": True},
        "tags": {"readonly": True},
        "target_environment_id": {"readonly": True},
    }

    _attribute_map = {
        "base_environment_source": {"key": "baseEnvironmentSource", "type": "BaseEnvironmentSource"},
        "build_id": {"key": "buildId", "type": "str"},
        "build_state": {"key": "buildState", "type": "str"},
        "environment_variables": {"key": "environmentVariables", "type": "{str}"},
        "inferencing_server": {"key": "inferencingServer", "type": "InferencingServer"},
        "inputs": {"key": "inputs", "type": "[ModelPackageInput]"},
        "log_url": {"key": "logUrl", "type": "str"},
        "model_configuration": {"key": "modelConfiguration", "type": "ModelConfiguration"},
        "properties": {"key": "properties", "type": "{str}"},
        "sku_architecture_type": {"key": "skuArchitectureType", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "target_environment_id": {"key": "targetEnvironmentId", "type": "str"},
    }

    def __init__(
        self, *, properties: Optional[Dict[str, str]] = None, sku_architecture_type: Optional[str] = None, **kwargs
    ):
        """
        :keyword properties: Properties dictionary.
        :paramtype properties: dict[str, str]
        :keyword sku_architecture_type: The sku architecture type.
        :paramtype sku_architecture_type: str
        """
        super(PackageResponse, self).__init__(**kwargs)
        self.base_environment_source = None
        self.build_id = None
        self.build_state = None
        self.environment_variables = None
        self.inferencing_server = None
        self.inputs = None
        self.log_url = None
        self.model_configuration = None
        self.properties = properties
        self.sku_architecture_type = sku_architecture_type
        self.tags = None
        self.target_environment_id = None


class PipelineJob(JobBase):
    """Pipeline Job definition: defines generic to MFE attributes.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar compute_id: ARM resource ID of the compute resource.
    :vartype compute_id: str
    :ivar display_name: Display name of job.
    :vartype display_name: str
    :ivar experiment_name: The name of the experiment the job belongs to. If not set, the job is
     placed in the "Default" experiment.
    :vartype experiment_name: str
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar job_type: Required. Specifies the type of job.Constant filled by server. Possible values
     include: "Command", "Sweep", "Pipeline", "Base".
    :vartype job_type: str or ~azure.mgmt.machinelearningservices.models.JobType
    :ivar parent_job_name: TODO - Parent job name.
    :vartype parent_job_name: str
    :ivar services: List of JobEndpoints.
     For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
    :ivar status: Status of the job. Possible values include: "NotStarted", "Starting",
     "Provisioning", "Preparing", "Queued", "Running", "Finalizing", "CancelRequested", "Completed",
     "Failed", "Canceled", "NotResponding", "Paused", "Unknown".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.JobStatus
    :ivar bindings: Binding to represent relation between inputs, outputs and parameters.
    :vartype bindings: list[~azure.mgmt.machinelearningservices.models.Binding]
    :ivar component_jobs: JobDefinition set for PipelineStepJobs.
    :vartype component_jobs: dict[str, ~azure.mgmt.machinelearningservices.models.ComponentJob]
    :ivar inputs: Data input set for jobs.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
    :ivar outputs: Data output set for jobs.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
    :ivar settings: Pipeline settings, for things like ContinueRunOnStepFailure etc.
    :vartype settings: any
    """

    _validation = {
        "job_type": {"required": True},
        "parent_job_name": {"readonly": True},
        "status": {"readonly": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "compute_id": {"key": "computeId", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "job_type": {"key": "jobType", "type": "str"},
        "parent_job_name": {"key": "parentJobName", "type": "str"},
        "services": {"key": "services", "type": "{JobService}"},
        "status": {"key": "status", "type": "str"},
        "bindings": {"key": "bindings", "type": "[Binding]"},
        "component_jobs": {"key": "componentJobs", "type": "{ComponentJob}"},
        "inputs": {"key": "inputs", "type": "{JobInput}"},
        "outputs": {"key": "outputs", "type": "{JobOutput}"},
        "settings": {"key": "settings", "type": "object"},
    }

    def __init__(
        self,
        *,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        compute_id: Optional[str] = None,
        display_name: Optional[str] = None,
        experiment_name: Optional[str] = "Default",
        is_archived: Optional[bool] = False,
        services: Optional[Dict[str, "JobService"]] = None,
        bindings: Optional[List["Binding"]] = None,
        component_jobs: Optional[Dict[str, "ComponentJob"]] = None,
        inputs: Optional[Dict[str, "JobInput"]] = None,
        outputs: Optional[Dict[str, "JobOutput"]] = None,
        settings: Optional[Any] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword compute_id: ARM resource ID of the compute resource.
        :paramtype compute_id: str
        :keyword display_name: Display name of job.
        :paramtype display_name: str
        :keyword experiment_name: The name of the experiment the job belongs to. If not set, the job is
         placed in the "Default" experiment.
        :paramtype experiment_name: str
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword services: List of JobEndpoints.
         For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
        :keyword bindings: Binding to represent relation between inputs, outputs and parameters.
        :paramtype bindings: list[~azure.mgmt.machinelearningservices.models.Binding]
        :keyword component_jobs: JobDefinition set for PipelineStepJobs.
        :paramtype component_jobs: dict[str, ~azure.mgmt.machinelearningservices.models.ComponentJob]
        :keyword inputs: Data input set for jobs.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
        :keyword outputs: Data output set for jobs.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
        :keyword settings: Pipeline settings, for things like ContinueRunOnStepFailure etc.
        :paramtype settings: any
        """
        super(PipelineJob, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            compute_id=compute_id,
            display_name=display_name,
            experiment_name=experiment_name,
            is_archived=is_archived,
            services=services,
            **kwargs
        )
        self.job_type = "Pipeline"  # type: str
        self.bindings = bindings
        self.component_jobs = component_jobs
        self.inputs = inputs
        self.outputs = outputs
        self.settings = settings


class PyTorch(DistributionConfiguration):
    """PyTorch distribution configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar distribution_type: Required. Specifies the type of distribution framework.Constant filled
     by server. Possible values include: "PyTorch", "TensorFlow", "Mpi".
    :vartype distribution_type: str or ~azure.mgmt.machinelearningservices.models.DistributionType
    :ivar process_count_per_instance: Number of processes per node.
    :vartype process_count_per_instance: int
    """

    _validation = {
        "distribution_type": {"required": True},
    }

    _attribute_map = {
        "distribution_type": {"key": "distributionType", "type": "str"},
        "process_count_per_instance": {"key": "processCountPerInstance", "type": "int"},
    }

    def __init__(self, *, process_count_per_instance: Optional[int] = None, **kwargs):
        """
        :keyword process_count_per_instance: Number of processes per node.
        :paramtype process_count_per_instance: int
        """
        super(PyTorch, self).__init__(**kwargs)
        self.distribution_type = "PyTorch"  # type: str
        self.process_count_per_instance = process_count_per_instance


class ResourceConfiguration(msrest.serialization.Model):
    """ResourceConfiguration.

    :ivar instance_count: Optional number of instances or nodes used by the compute target.
    :vartype instance_count: int
    :ivar instance_type: Optional type of VM used as supported by the compute target.
    :vartype instance_type: str
    :ivar properties: Additional properties bag.
    :vartype properties: dict[str, any]
    """

    _attribute_map = {
        "instance_count": {"key": "instanceCount", "type": "int"},
        "instance_type": {"key": "instanceType", "type": "str"},
        "properties": {"key": "properties", "type": "{object}"},
    }

    def __init__(
        self,
        *,
        instance_count: Optional[int] = 1,
        instance_type: Optional[str] = None,
        properties: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        :keyword instance_count: Optional number of instances or nodes used by the compute target.
        :paramtype instance_count: int
        :keyword instance_type: Optional type of VM used as supported by the compute target.
        :paramtype instance_type: str
        :keyword properties: Additional properties bag.
        :paramtype properties: dict[str, any]
        """
        super(ResourceConfiguration, self).__init__(**kwargs)
        self.instance_count = instance_count
        self.instance_type = instance_type
        self.properties = properties


class ResourceManagementAssetReferenceData(Resource):
    """Azure Resource Manager resource envelope.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar properties: Required. Additional attributes of the entity.
    :vartype properties:
     ~azure.mgmt.machinelearningservices.models.ResourceManagementAssetReferenceDetails
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "system_data": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "system_data": {"key": "systemData", "type": "SystemData"},
        "properties": {"key": "properties", "type": "ResourceManagementAssetReferenceDetails"},
    }

    def __init__(self, *, properties: "ResourceManagementAssetReferenceDetails", **kwargs):
        """
        :keyword properties: Required. Additional attributes of the entity.
        :paramtype properties:
         ~azure.mgmt.machinelearningservices.models.ResourceManagementAssetReferenceDetails
        """
        super(ResourceManagementAssetReferenceData, self).__init__(**kwargs)
        self.properties = properties


class ResourceManagementAssetReferenceDetails(AssetReferenceBase):
    """Resource Management asset reference.

    All required parameters must be populated in order to send to Azure.

    :ivar reference_type: Required. Specifies the type of asset reference.Constant filled by
     server. Possible values include: "Id", "DataPath", "OutputPath".
    :vartype reference_type: str or ~azure.mgmt.machinelearningservices.models.ReferenceType
    :ivar destination_name: Destination asset name for import.
    :vartype destination_name: str
    :ivar destination_version: Destination asset version for import.
    :vartype destination_version: str
    :ivar source_asset_id: Required. ARM resource ID of the source asset.
    :vartype source_asset_id: str
    """

    _validation = {
        "reference_type": {"required": True},
        "source_asset_id": {"required": True},
    }

    _attribute_map = {
        "reference_type": {"key": "referenceType", "type": "str"},
        "destination_name": {"key": "destinationName", "type": "str"},
        "destination_version": {"key": "destinationVersion", "type": "str"},
        "source_asset_id": {"key": "sourceAssetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        source_asset_id: str,
        destination_name: Optional[str] = None,
        destination_version: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword destination_name: Destination asset name for import.
        :paramtype destination_name: str
        :keyword destination_version: Destination asset version for import.
        :paramtype destination_version: str
        :keyword source_asset_id: Required. ARM resource ID of the source asset.
        :paramtype source_asset_id: str
        """
        super(ResourceManagementAssetReferenceDetails, self).__init__(**kwargs)
        self.reference_type = "Id"  # type: str
        self.destination_name = destination_name
        self.destination_version = destination_version
        self.source_asset_id = source_asset_id


class Route(msrest.serialization.Model):
    """Route.

    All required parameters must be populated in order to send to Azure.

    :ivar path: Required. The path for the route.
    :vartype path: str
    :ivar port: Required. The port for the route.
    :vartype port: int
    """

    _validation = {
        "path": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
        "port": {"required": True},
    }

    _attribute_map = {
        "path": {"key": "path", "type": "str"},
        "port": {"key": "port", "type": "int"},
    }

    def __init__(self, *, path: str, port: int, **kwargs):
        """
        :keyword path: Required. The path for the route.
        :paramtype path: str
        :keyword port: Required. The port for the route.
        :paramtype port: int
        """
        super(Route, self).__init__(**kwargs)
        self.path = path
        self.port = port


class SASCredentialDto(msrest.serialization.Model):
    """SASCredentialDto.

    :ivar sas_uri: Full SAS Uri, including the storage, container/blob path and SAS token.
    :vartype sas_uri: str
    """

    _attribute_map = {
        "sas_uri": {"key": "sasUri", "type": "str"},
    }

    def __init__(self, *, sas_uri: Optional[str] = None, **kwargs):
        """
        :keyword sas_uri: Full SAS Uri, including the storage, container/blob path and SAS token.
        :paramtype sas_uri: str
        """
        super(SASCredentialDto, self).__init__(**kwargs)
        self.sas_uri = sas_uri


class SasDatastoreCredentials(DatastoreCredentials):
    """SAS datastore credentials configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar credentials_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "None", "Sas",
     "ServicePrincipal".
    :vartype credentials_type: str or ~azure.mgmt.machinelearningservices.models.CredentialsType
    :ivar secrets: Required. Storage container secrets.
    :vartype secrets: ~azure.mgmt.machinelearningservices.models.SasDatastoreSecrets
    """

    _validation = {
        "credentials_type": {"required": True},
        "secrets": {"required": True},
    }

    _attribute_map = {
        "credentials_type": {"key": "credentialsType", "type": "str"},
        "secrets": {"key": "secrets", "type": "SasDatastoreSecrets"},
    }

    def __init__(self, *, secrets: "SasDatastoreSecrets", **kwargs):
        """
        :keyword secrets: Required. Storage container secrets.
        :paramtype secrets: ~azure.mgmt.machinelearningservices.models.SasDatastoreSecrets
        """
        super(SasDatastoreCredentials, self).__init__(**kwargs)
        self.credentials_type = "Sas"  # type: str
        self.secrets = secrets


class SasDatastoreSecrets(DatastoreSecrets):
    """Datastore SAS secrets.

    All required parameters must be populated in order to send to Azure.

    :ivar secrets_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "Sas",
     "ServicePrincipal".
    :vartype secrets_type: str or ~azure.mgmt.machinelearningservices.models.SecretsType
    :ivar sas_token: Storage container SAS token.
    :vartype sas_token: str
    """

    _validation = {
        "secrets_type": {"required": True},
    }

    _attribute_map = {
        "secrets_type": {"key": "secretsType", "type": "str"},
        "sas_token": {"key": "sasToken", "type": "str"},
    }

    def __init__(self, *, sas_token: Optional[str] = None, **kwargs):
        """
        :keyword sas_token: Storage container SAS token.
        :paramtype sas_token: str
        """
        super(SasDatastoreSecrets, self).__init__(**kwargs)
        self.secrets_type = "Sas"  # type: str
        self.sas_token = sas_token


class ServicePrincipalDatastoreCredentials(DatastoreCredentials):
    """Service Principal datastore credentials configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar credentials_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "None", "Sas",
     "ServicePrincipal".
    :vartype credentials_type: str or ~azure.mgmt.machinelearningservices.models.CredentialsType
    :ivar authority_url: Authority URL used for authentication.
    :vartype authority_url: str
    :ivar client_id: Required. Service principal client ID.
    :vartype client_id: str
    :ivar resource_url: Resource the service principal has access to.
    :vartype resource_url: str
    :ivar secrets: Required. Service principal secrets.
    :vartype secrets: ~azure.mgmt.machinelearningservices.models.ServicePrincipalDatastoreSecrets
    :ivar tenant_id: Required. ID of the tenant to which the service principal belongs.
    :vartype tenant_id: str
    """

    _validation = {
        "credentials_type": {"required": True},
        "client_id": {"required": True},
        "secrets": {"required": True},
        "tenant_id": {"required": True},
    }

    _attribute_map = {
        "credentials_type": {"key": "credentialsType", "type": "str"},
        "authority_url": {"key": "authorityUrl", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
        "resource_url": {"key": "resourceUrl", "type": "str"},
        "secrets": {"key": "secrets", "type": "ServicePrincipalDatastoreSecrets"},
        "tenant_id": {"key": "tenantId", "type": "str"},
    }

    def __init__(
        self,
        *,
        client_id: str,
        secrets: "ServicePrincipalDatastoreSecrets",
        tenant_id: str,
        authority_url: Optional[str] = None,
        resource_url: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword authority_url: Authority URL used for authentication.
        :paramtype authority_url: str
        :keyword client_id: Required. Service principal client ID.
        :paramtype client_id: str
        :keyword resource_url: Resource the service principal has access to.
        :paramtype resource_url: str
        :keyword secrets: Required. Service principal secrets.
        :paramtype secrets: ~azure.mgmt.machinelearningservices.models.ServicePrincipalDatastoreSecrets
        :keyword tenant_id: Required. ID of the tenant to which the service principal belongs.
        :paramtype tenant_id: str
        """
        super(ServicePrincipalDatastoreCredentials, self).__init__(**kwargs)
        self.credentials_type = "ServicePrincipal"  # type: str
        self.authority_url = authority_url
        self.client_id = client_id
        self.resource_url = resource_url
        self.secrets = secrets
        self.tenant_id = tenant_id


class ServicePrincipalDatastoreSecrets(DatastoreSecrets):
    """Datastore Service Principal secrets.

    All required parameters must be populated in order to send to Azure.

    :ivar secrets_type: Required. Credential type used to authentication with storage.Constant
     filled by server. Possible values include: "AccountKey", "Certificate", "Sas",
     "ServicePrincipal".
    :vartype secrets_type: str or ~azure.mgmt.machinelearningservices.models.SecretsType
    :ivar client_secret: Service principal secret.
    :vartype client_secret: str
    """

    _validation = {
        "secrets_type": {"required": True},
    }

    _attribute_map = {
        "secrets_type": {"key": "secretsType", "type": "str"},
        "client_secret": {"key": "clientSecret", "type": "str"},
    }

    def __init__(self, *, client_secret: Optional[str] = None, **kwargs):
        """
        :keyword client_secret: Service principal secret.
        :paramtype client_secret: str
        """
        super(ServicePrincipalDatastoreSecrets, self).__init__(**kwargs)
        self.secrets_type = "ServicePrincipal"  # type: str
        self.client_secret = client_secret


class SweepJob(JobBase):
    """Sweep job definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar compute_id: ARM resource ID of the compute resource.
    :vartype compute_id: str
    :ivar display_name: Display name of job.
    :vartype display_name: str
    :ivar experiment_name: The name of the experiment the job belongs to. If not set, the job is
     placed in the "Default" experiment.
    :vartype experiment_name: str
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar job_type: Required. Specifies the type of job.Constant filled by server. Possible values
     include: "Command", "Sweep", "Pipeline", "Base".
    :vartype job_type: str or ~azure.mgmt.machinelearningservices.models.JobType
    :ivar parent_job_name: TODO - Parent job name.
    :vartype parent_job_name: str
    :ivar services: List of JobEndpoints.
     For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
    :ivar status: Status of the job. Possible values include: "NotStarted", "Starting",
     "Provisioning", "Preparing", "Queued", "Running", "Finalizing", "CancelRequested", "Completed",
     "Failed", "Canceled", "NotResponding", "Paused", "Unknown".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.JobStatus
    :ivar early_termination: Early termination policies enable canceling poor-performing runs
     before they complete.
    :vartype early_termination: ~azure.mgmt.machinelearningservices.models.EarlyTerminationPolicy
    :ivar identity: Identity configuration. If set, this should be one of AmlToken, ManagedIdentity
     or null.
     Defaults to AmlToken if null.
    :vartype identity: ~azure.mgmt.machinelearningservices.models.IdentityConfiguration
    :ivar inputs: Mapping of input data bindings used in the job.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
    :ivar limits: Sweep Job limit.
    :vartype limits: ~azure.mgmt.machinelearningservices.models.SweepJobLimits
    :ivar objective: Required. Optimization objective.
    :vartype objective: ~azure.mgmt.machinelearningservices.models.Objective
    :ivar outputs: Mapping of output data bindings used in the job.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
    :ivar sampling_algorithm: Required. Type of the hyperparameter sampling algorithms. Possible
     values include: "Grid", "Random", "Bayesian".
    :vartype sampling_algorithm: str or
     ~azure.mgmt.machinelearningservices.models.SamplingAlgorithm
    :ivar search_space: Required. A dictionary containing each parameter and its distribution. The
     dictionary key is the name of the parameter.
    :vartype search_space: any
    :ivar trial: Required. Trial component definition.
    :vartype trial: ~azure.mgmt.machinelearningservices.models.TrialComponent
    """

    _validation = {
        "job_type": {"required": True},
        "parent_job_name": {"readonly": True},
        "status": {"readonly": True},
        "objective": {"required": True},
        "sampling_algorithm": {"required": True},
        "search_space": {"required": True},
        "trial": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "compute_id": {"key": "computeId", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "job_type": {"key": "jobType", "type": "str"},
        "parent_job_name": {"key": "parentJobName", "type": "str"},
        "services": {"key": "services", "type": "{JobService}"},
        "status": {"key": "status", "type": "str"},
        "early_termination": {"key": "earlyTermination", "type": "EarlyTerminationPolicy"},
        "identity": {"key": "identity", "type": "IdentityConfiguration"},
        "inputs": {"key": "inputs", "type": "{JobInput}"},
        "limits": {"key": "limits", "type": "SweepJobLimits"},
        "objective": {"key": "objective", "type": "Objective"},
        "outputs": {"key": "outputs", "type": "{JobOutput}"},
        "sampling_algorithm": {"key": "samplingAlgorithm", "type": "str"},
        "search_space": {"key": "searchSpace", "type": "object"},
        "trial": {"key": "trial", "type": "TrialComponent"},
    }

    def __init__(
        self,
        *,
        objective: "Objective",
        sampling_algorithm: Union[str, "SamplingAlgorithm"],
        search_space: Any,
        trial: "TrialComponent",
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        compute_id: Optional[str] = None,
        display_name: Optional[str] = None,
        experiment_name: Optional[str] = "Default",
        is_archived: Optional[bool] = False,
        services: Optional[Dict[str, "JobService"]] = None,
        early_termination: Optional["EarlyTerminationPolicy"] = None,
        identity: Optional["IdentityConfiguration"] = None,
        inputs: Optional[Dict[str, "JobInput"]] = None,
        limits: Optional["SweepJobLimits"] = None,
        outputs: Optional[Dict[str, "JobOutput"]] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword compute_id: ARM resource ID of the compute resource.
        :paramtype compute_id: str
        :keyword display_name: Display name of job.
        :paramtype display_name: str
        :keyword experiment_name: The name of the experiment the job belongs to. If not set, the job is
         placed in the "Default" experiment.
        :paramtype experiment_name: str
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword services: List of JobEndpoints.
         For local jobs, a job endpoint will have an endpoint value of FileStreamObject.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.JobService]
        :keyword early_termination: Early termination policies enable canceling poor-performing runs
         before they complete.
        :paramtype early_termination: ~azure.mgmt.machinelearningservices.models.EarlyTerminationPolicy
        :keyword identity: Identity configuration. If set, this should be one of AmlToken,
         ManagedIdentity or null.
         Defaults to AmlToken if null.
        :paramtype identity: ~azure.mgmt.machinelearningservices.models.IdentityConfiguration
        :keyword inputs: Mapping of input data bindings used in the job.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobInput]
        :keyword limits: Sweep Job limit.
        :paramtype limits: ~azure.mgmt.machinelearningservices.models.SweepJobLimits
        :keyword objective: Required. Optimization objective.
        :paramtype objective: ~azure.mgmt.machinelearningservices.models.Objective
        :keyword outputs: Mapping of output data bindings used in the job.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.JobOutput]
        :keyword sampling_algorithm: Required. Type of the hyperparameter sampling algorithms. Possible
         values include: "Grid", "Random", "Bayesian".
        :paramtype sampling_algorithm: str or
         ~azure.mgmt.machinelearningservices.models.SamplingAlgorithm
        :keyword search_space: Required. A dictionary containing each parameter and its distribution.
         The dictionary key is the name of the parameter.
        :paramtype search_space: any
        :keyword trial: Required. Trial component definition.
        :paramtype trial: ~azure.mgmt.machinelearningservices.models.TrialComponent
        """
        super(SweepJob, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            compute_id=compute_id,
            display_name=display_name,
            experiment_name=experiment_name,
            is_archived=is_archived,
            services=services,
            **kwargs
        )
        self.job_type = "Sweep"  # type: str
        self.early_termination = early_termination
        self.identity = identity
        self.inputs = inputs
        self.limits = limits
        self.objective = objective
        self.outputs = outputs
        self.sampling_algorithm = sampling_algorithm
        self.search_space = search_space
        self.trial = trial


class SweepJobLimits(JobLimits):
    """Sweep Job limit class.

    All required parameters must be populated in order to send to Azure.

    :ivar job_limits_type: Required. JobLimit type.Constant filled by server. Possible values
     include: "Command", "Sweep".
    :vartype job_limits_type: str or ~azure.mgmt.machinelearningservices.models.JobLimitsType
    :ivar timeout: The max run duration in ISO 8601 format, after which the job will be cancelled.
     Only supports duration with precision as low as Seconds.
    :vartype timeout: ~datetime.timedelta
    :ivar max_concurrent_trials: Sweep Job max concurrent trials.
    :vartype max_concurrent_trials: int
    :ivar max_total_trials: Sweep Job max total trials.
    :vartype max_total_trials: int
    :ivar trial_timeout: Sweep Job Trial timeout value.
    :vartype trial_timeout: ~datetime.timedelta
    """

    _validation = {
        "job_limits_type": {"required": True},
    }

    _attribute_map = {
        "job_limits_type": {"key": "jobLimitsType", "type": "str"},
        "timeout": {"key": "timeout", "type": "duration"},
        "max_concurrent_trials": {"key": "maxConcurrentTrials", "type": "int"},
        "max_total_trials": {"key": "maxTotalTrials", "type": "int"},
        "trial_timeout": {"key": "trialTimeout", "type": "duration"},
    }

    def __init__(
        self,
        *,
        timeout: Optional[datetime.timedelta] = None,
        max_concurrent_trials: Optional[int] = None,
        max_total_trials: Optional[int] = None,
        trial_timeout: Optional[datetime.timedelta] = None,
        **kwargs
    ):
        """
        :keyword timeout: The max run duration in ISO 8601 format, after which the job will be
         cancelled. Only supports duration with precision as low as Seconds.
        :paramtype timeout: ~datetime.timedelta
        :keyword max_concurrent_trials: Sweep Job max concurrent trials.
        :paramtype max_concurrent_trials: int
        :keyword max_total_trials: Sweep Job max total trials.
        :paramtype max_total_trials: int
        :keyword trial_timeout: Sweep Job Trial timeout value.
        :paramtype trial_timeout: ~datetime.timedelta
        """
        super(SweepJobLimits, self).__init__(timeout=timeout, **kwargs)
        self.job_limits_type = "Sweep"  # type: str
        self.max_concurrent_trials = max_concurrent_trials
        self.max_total_trials = max_total_trials
        self.trial_timeout = trial_timeout


class SystemData(msrest.serialization.Model):
    """Metadata pertaining to creation and last modification of the resource.

    :ivar created_by: The identity that created the resource.
    :vartype created_by: str
    :ivar created_by_type: The type of identity that created the resource. Possible values include:
     "User", "Application", "ManagedIdentity", "Key".
    :vartype created_by_type: str or ~azure.mgmt.machinelearningservices.models.CreatedByType
    :ivar created_at: The timestamp of resource creation (UTC).
    :vartype created_at: ~datetime.datetime
    :ivar last_modified_by: The identity that last modified the resource.
    :vartype last_modified_by: str
    :ivar last_modified_by_type: The type of identity that last modified the resource. Possible
     values include: "User", "Application", "ManagedIdentity", "Key".
    :vartype last_modified_by_type: str or ~azure.mgmt.machinelearningservices.models.CreatedByType
    :ivar last_modified_at: The timestamp of resource last modification (UTC).
    :vartype last_modified_at: ~datetime.datetime
    """

    _attribute_map = {
        "created_by": {"key": "createdBy", "type": "str"},
        "created_by_type": {"key": "createdByType", "type": "str"},
        "created_at": {"key": "createdAt", "type": "iso-8601"},
        "last_modified_by": {"key": "lastModifiedBy", "type": "str"},
        "last_modified_by_type": {"key": "lastModifiedByType", "type": "str"},
        "last_modified_at": {"key": "lastModifiedAt", "type": "iso-8601"},
    }

    def __init__(
        self,
        *,
        created_by: Optional[str] = None,
        created_by_type: Optional[Union[str, "CreatedByType"]] = None,
        created_at: Optional[datetime.datetime] = None,
        last_modified_by: Optional[str] = None,
        last_modified_by_type: Optional[Union[str, "CreatedByType"]] = None,
        last_modified_at: Optional[datetime.datetime] = None,
        **kwargs
    ):
        """
        :keyword created_by: The identity that created the resource.
        :paramtype created_by: str
        :keyword created_by_type: The type of identity that created the resource. Possible values
         include: "User", "Application", "ManagedIdentity", "Key".
        :paramtype created_by_type: str or ~azure.mgmt.machinelearningservices.models.CreatedByType
        :keyword created_at: The timestamp of resource creation (UTC).
        :paramtype created_at: ~datetime.datetime
        :keyword last_modified_by: The identity that last modified the resource.
        :paramtype last_modified_by: str
        :keyword last_modified_by_type: The type of identity that last modified the resource. Possible
         values include: "User", "Application", "ManagedIdentity", "Key".
        :paramtype last_modified_by_type: str or
         ~azure.mgmt.machinelearningservices.models.CreatedByType
        :keyword last_modified_at: The timestamp of resource last modification (UTC).
        :paramtype last_modified_at: ~datetime.datetime
        """
        super(SystemData, self).__init__(**kwargs)
        self.created_by = created_by
        self.created_by_type = created_by_type
        self.created_at = created_at
        self.last_modified_by = last_modified_by
        self.last_modified_by_type = last_modified_by_type
        self.last_modified_at = last_modified_at


class TemporaryDataReferenceRequestDto(msrest.serialization.Model):
    """TemporaryDataReferenceRequestDto.

    :ivar asset_id:
    :vartype asset_id: str
    :ivar temporary_data_reference_id: If TemporaryDataReferenceId = null then random guid will be
     used.
    :vartype temporary_data_reference_id: str
    :ivar temporary_data_reference_type: Either TemporaryBlobReference or TemporaryImageReference.
    :vartype temporary_data_reference_type: str
    """

    _attribute_map = {
        "asset_id": {"key": "assetId", "type": "str"},
        "temporary_data_reference_id": {"key": "temporaryDataReferenceId", "type": "str"},
        "temporary_data_reference_type": {"key": "temporaryDataReferenceType", "type": "str"},
    }

    def __init__(
        self,
        *,
        asset_id: Optional[str] = None,
        temporary_data_reference_id: Optional[str] = None,
        temporary_data_reference_type: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword asset_id:
        :paramtype asset_id: str
        :keyword temporary_data_reference_id: If TemporaryDataReferenceId = null then random guid will
         be used.
        :paramtype temporary_data_reference_id: str
        :keyword temporary_data_reference_type: Either TemporaryBlobReference or
         TemporaryImageReference.
        :paramtype temporary_data_reference_type: str
        """
        super(TemporaryDataReferenceRequestDto, self).__init__(**kwargs)
        self.asset_id = asset_id
        self.temporary_data_reference_id = temporary_data_reference_id
        self.temporary_data_reference_type = temporary_data_reference_type


class TemporaryDataReferenceResponseDto(msrest.serialization.Model):
    """TemporaryDataReferenceResponseDto.

    :ivar blob_reference_for_consumption: Container level read, write, list SAS.
    :vartype blob_reference_for_consumption:
     ~azure.mgmt.machinelearningservices.models.BlobReferenceForConsumptionDto
    :ivar image_reference_for_consumption:
    :vartype image_reference_for_consumption:
     ~azure.mgmt.machinelearningservices.models.ImageReferenceForConsumptionDto
    :ivar temporary_data_reference_id:
    :vartype temporary_data_reference_id: str
    :ivar temporary_data_reference_type:
    :vartype temporary_data_reference_type: str
    """

    _attribute_map = {
        "blob_reference_for_consumption": {
            "key": "blobReferenceForConsumption",
            "type": "BlobReferenceForConsumptionDto",
        },
        "image_reference_for_consumption": {
            "key": "imageReferenceForConsumption",
            "type": "ImageReferenceForConsumptionDto",
        },
        "temporary_data_reference_id": {"key": "temporaryDataReferenceId", "type": "str"},
        "temporary_data_reference_type": {"key": "temporaryDataReferenceType", "type": "str"},
    }

    def __init__(
        self,
        *,
        blob_reference_for_consumption: Optional["BlobReferenceForConsumptionDto"] = None,
        image_reference_for_consumption: Optional["ImageReferenceForConsumptionDto"] = None,
        temporary_data_reference_id: Optional[str] = None,
        temporary_data_reference_type: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword blob_reference_for_consumption: Container level read, write, list SAS.
        :paramtype blob_reference_for_consumption:
         ~azure.mgmt.machinelearningservices.models.BlobReferenceForConsumptionDto
        :keyword image_reference_for_consumption:
        :paramtype image_reference_for_consumption:
         ~azure.mgmt.machinelearningservices.models.ImageReferenceForConsumptionDto
        :keyword temporary_data_reference_id:
        :paramtype temporary_data_reference_id: str
        :keyword temporary_data_reference_type:
        :paramtype temporary_data_reference_type: str
        """
        super(TemporaryDataReferenceResponseDto, self).__init__(**kwargs)
        self.blob_reference_for_consumption = blob_reference_for_consumption
        self.image_reference_for_consumption = image_reference_for_consumption
        self.temporary_data_reference_id = temporary_data_reference_id
        self.temporary_data_reference_type = temporary_data_reference_type


class TensorFlow(DistributionConfiguration):
    """TensorFlow distribution configuration.

    All required parameters must be populated in order to send to Azure.

    :ivar distribution_type: Required. Specifies the type of distribution framework.Constant filled
     by server. Possible values include: "PyTorch", "TensorFlow", "Mpi".
    :vartype distribution_type: str or ~azure.mgmt.machinelearningservices.models.DistributionType
    :ivar parameter_server_count: Number of parameter server tasks.
    :vartype parameter_server_count: int
    :ivar worker_count: Number of workers. If not specified, will default to the instance count.
    :vartype worker_count: int
    """

    _validation = {
        "distribution_type": {"required": True},
    }

    _attribute_map = {
        "distribution_type": {"key": "distributionType", "type": "str"},
        "parameter_server_count": {"key": "parameterServerCount", "type": "int"},
        "worker_count": {"key": "workerCount", "type": "int"},
    }

    def __init__(self, *, parameter_server_count: Optional[int] = 0, worker_count: Optional[int] = None, **kwargs):
        """
        :keyword parameter_server_count: Number of parameter server tasks.
        :paramtype parameter_server_count: int
        :keyword worker_count: Number of workers. If not specified, will default to the instance count.
        :paramtype worker_count: int
        """
        super(TensorFlow, self).__init__(**kwargs)
        self.distribution_type = "TensorFlow"  # type: str
        self.parameter_server_count = parameter_server_count
        self.worker_count = worker_count


class TrialComponent(msrest.serialization.Model):
    """Trial component definition.

    All required parameters must be populated in order to send to Azure.

    :ivar code_id: ARM resource ID of the code asset.
    :vartype code_id: str
    :ivar command: Required. The command to execute on startup of the job. eg. "python train.py".
    :vartype command: str
    :ivar distribution: Distribution configuration of the job. If set, this should be one of Mpi,
     Tensorflow, PyTorch, or null.
    :vartype distribution: ~azure.mgmt.machinelearningservices.models.DistributionConfiguration
    :ivar environment_id: Required. The ARM resource ID of the Environment specification for the
     job.
    :vartype environment_id: str
    :ivar environment_variables: Environment variables included in the job.
    :vartype environment_variables: dict[str, str]
    :ivar resources: Compute Resource configuration for the job.
    :vartype resources: ~azure.mgmt.machinelearningservices.models.ResourceConfiguration
    """

    _validation = {
        "command": {"required": True, "min_length": 1, "pattern": r"[a-zA-Z0-9_]"},
        "environment_id": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "code_id": {"key": "codeId", "type": "str"},
        "command": {"key": "command", "type": "str"},
        "distribution": {"key": "distribution", "type": "DistributionConfiguration"},
        "environment_id": {"key": "environmentId", "type": "str"},
        "environment_variables": {"key": "environmentVariables", "type": "{str}"},
        "resources": {"key": "resources", "type": "ResourceConfiguration"},
    }

    def __init__(
        self,
        *,
        command: str,
        environment_id: str,
        code_id: Optional[str] = None,
        distribution: Optional["DistributionConfiguration"] = None,
        environment_variables: Optional[Dict[str, str]] = None,
        resources: Optional["ResourceConfiguration"] = None,
        **kwargs
    ):
        """
        :keyword code_id: ARM resource ID of the code asset.
        :paramtype code_id: str
        :keyword command: Required. The command to execute on startup of the job. eg. "python
         train.py".
        :paramtype command: str
        :keyword distribution: Distribution configuration of the job. If set, this should be one of
         Mpi, Tensorflow, PyTorch, or null.
        :paramtype distribution: ~azure.mgmt.machinelearningservices.models.DistributionConfiguration
        :keyword environment_id: Required. The ARM resource ID of the Environment specification for the
         job.
        :paramtype environment_id: str
        :keyword environment_variables: Environment variables included in the job.
        :paramtype environment_variables: dict[str, str]
        :keyword resources: Compute Resource configuration for the job.
        :paramtype resources: ~azure.mgmt.machinelearningservices.models.ResourceConfiguration
        """
        super(TrialComponent, self).__init__(**kwargs)
        self.code_id = code_id
        self.command = command
        self.distribution = distribution
        self.environment_id = environment_id
        self.environment_variables = environment_variables
        self.resources = resources


class TritonInferencingServer(InferencingServer):
    """Triton inferencing server configurations.

    All required parameters must be populated in order to send to Azure.

    :ivar server_type: Required. Inferencing server type for various targets.Constant filled by
     server. Possible values include: "AzureMLOnline", "AzureMLBatch", "Triton", "Custom".
    :vartype server_type: str or ~azure.mgmt.machinelearningservices.models.InferencingServerType
    :ivar inference_configuration: Inference configuration for Triton.
    :vartype inference_configuration:
     ~azure.mgmt.machinelearningservices.models.OnlineInferenceConfiguration
    """

    _validation = {
        "server_type": {"required": True},
    }

    _attribute_map = {
        "server_type": {"key": "serverType", "type": "str"},
        "inference_configuration": {"key": "inferenceConfiguration", "type": "OnlineInferenceConfiguration"},
    }

    def __init__(self, *, inference_configuration: Optional["OnlineInferenceConfiguration"] = None, **kwargs):
        """
        :keyword inference_configuration: Inference configuration for Triton.
        :paramtype inference_configuration:
         ~azure.mgmt.machinelearningservices.models.OnlineInferenceConfiguration
        """
        super(TritonInferencingServer, self).__init__(**kwargs)
        self.server_type = "Triton"  # type: str
        self.inference_configuration = inference_configuration


class TruncationSelectionPolicy(EarlyTerminationPolicy):
    """Defines an early termination policy that cancels a given percentage of runs at each evaluation interval.

    All required parameters must be populated in order to send to Azure.

    :ivar delay_evaluation: Number of intervals by which to delay the first evaluation.
    :vartype delay_evaluation: int
    :ivar evaluation_interval: Interval (number of runs) between policy evaluations.
    :vartype evaluation_interval: int
    :ivar policy_type: Required. Name of policy configuration.Constant filled by server. Possible
     values include: "Bandit", "MedianStopping", "TruncationSelection".
    :vartype policy_type: str or
     ~azure.mgmt.machinelearningservices.models.EarlyTerminationPolicyType
    :ivar truncation_percentage: The percentage of runs to cancel at each evaluation interval.
    :vartype truncation_percentage: int
    """

    _validation = {
        "policy_type": {"required": True},
    }

    _attribute_map = {
        "delay_evaluation": {"key": "delayEvaluation", "type": "int"},
        "evaluation_interval": {"key": "evaluationInterval", "type": "int"},
        "policy_type": {"key": "policyType", "type": "str"},
        "truncation_percentage": {"key": "truncationPercentage", "type": "int"},
    }

    def __init__(
        self,
        *,
        delay_evaluation: Optional[int] = 0,
        evaluation_interval: Optional[int] = 0,
        truncation_percentage: Optional[int] = 0,
        **kwargs
    ):
        """
        :keyword delay_evaluation: Number of intervals by which to delay the first evaluation.
        :paramtype delay_evaluation: int
        :keyword evaluation_interval: Interval (number of runs) between policy evaluations.
        :paramtype evaluation_interval: int
        :keyword truncation_percentage: The percentage of runs to cancel at each evaluation interval.
        :paramtype truncation_percentage: int
        """
        super(TruncationSelectionPolicy, self).__init__(
            delay_evaluation=delay_evaluation, evaluation_interval=evaluation_interval, **kwargs
        )
        self.policy_type = "TruncationSelection"  # type: str
        self.truncation_percentage = truncation_percentage


class UriFileDataVersion(DataVersionBaseDetails):
    """uri-file data version entity.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar data_type: Required. Specifies the type of data.Constant filled by server. Possible
     values include: "uri_file", "uri_folder", "mltable".
    :vartype data_type: str or ~azure.mgmt.machinelearningservices.models.DataType
    :ivar data_uri: Required. Uri of the data. Usage/meaning depends on
     Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
    :vartype data_uri: str
    :ivar intellectual_property: Intellectual Property details. Used if data is an Intellectual
     Property.
    :vartype intellectual_property: ~azure.mgmt.machinelearningservices.models.IntellectualProperty
    """

    _validation = {
        "data_type": {"required": True},
        "data_uri": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "data_type": {"key": "dataType", "type": "str"},
        "data_uri": {"key": "dataUri", "type": "str"},
        "intellectual_property": {"key": "intellectualProperty", "type": "IntellectualProperty"},
    }

    def __init__(
        self,
        *,
        data_uri: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        intellectual_property: Optional["IntellectualProperty"] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword data_uri: Required. Uri of the data. Usage/meaning depends on
         Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
        :paramtype data_uri: str
        :keyword intellectual_property: Intellectual Property details. Used if data is an Intellectual
         Property.
        :paramtype intellectual_property:
         ~azure.mgmt.machinelearningservices.models.IntellectualProperty
        """
        super(UriFileDataVersion, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            data_uri=data_uri,
            intellectual_property=intellectual_property,
            **kwargs
        )
        self.data_type = "uri_file"  # type: str


class UriFolderDataVersion(DataVersionBaseDetails):
    """uri-folder data version entity.

    All required parameters must be populated in order to send to Azure.

    :ivar description: The asset description text.
    :vartype description: str
    :ivar properties: The asset property dictionary.
    :vartype properties: dict[str, str]
    :ivar tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar is_anonymous: If the name version are system generated (anonymous registration).
    :vartype is_anonymous: bool
    :ivar is_archived: Is the asset archived?.
    :vartype is_archived: bool
    :ivar data_type: Required. Specifies the type of data.Constant filled by server. Possible
     values include: "uri_file", "uri_folder", "mltable".
    :vartype data_type: str or ~azure.mgmt.machinelearningservices.models.DataType
    :ivar data_uri: Required. Uri of the data. Usage/meaning depends on
     Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
    :vartype data_uri: str
    :ivar intellectual_property: Intellectual Property details. Used if data is an Intellectual
     Property.
    :vartype intellectual_property: ~azure.mgmt.machinelearningservices.models.IntellectualProperty
    """

    _validation = {
        "data_type": {"required": True},
        "data_uri": {"required": True, "pattern": r"[a-zA-Z0-9_]"},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "is_archived": {"key": "isArchived", "type": "bool"},
        "data_type": {"key": "dataType", "type": "str"},
        "data_uri": {"key": "dataUri", "type": "str"},
        "intellectual_property": {"key": "intellectualProperty", "type": "IntellectualProperty"},
    }

    def __init__(
        self,
        *,
        data_uri: str,
        description: Optional[str] = None,
        properties: Optional[Dict[str, str]] = None,
        tags: Optional[Dict[str, str]] = None,
        is_anonymous: Optional[bool] = False,
        is_archived: Optional[bool] = False,
        intellectual_property: Optional["IntellectualProperty"] = None,
        **kwargs
    ):
        """
        :keyword description: The asset description text.
        :paramtype description: str
        :keyword properties: The asset property dictionary.
        :paramtype properties: dict[str, str]
        :keyword tags: A set of tags. Tag dictionary. Tags can be added, removed, and updated.
        :paramtype tags: dict[str, str]
        :keyword is_anonymous: If the name version are system generated (anonymous registration).
        :paramtype is_anonymous: bool
        :keyword is_archived: Is the asset archived?.
        :paramtype is_archived: bool
        :keyword data_uri: Required. Uri of the data. Usage/meaning depends on
         Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20211001Dataplane.Assets.DataVersionBase.DataType.
        :paramtype data_uri: str
        :keyword intellectual_property: Intellectual Property details. Used if data is an Intellectual
         Property.
        :paramtype intellectual_property:
         ~azure.mgmt.machinelearningservices.models.IntellectualProperty
        """
        super(UriFolderDataVersion, self).__init__(
            description=description,
            properties=properties,
            tags=tags,
            is_anonymous=is_anonymous,
            is_archived=is_archived,
            data_uri=data_uri,
            intellectual_property=intellectual_property,
            **kwargs
        )
        self.data_type = "uri_folder"  # type: str


class UriReference(msrest.serialization.Model):
    """TODO - UriReference.

    :ivar file: Single file uri path.
    :vartype file: str
    :ivar folder: Folder uri path.
    :vartype folder: str
    """

    _attribute_map = {
        "file": {"key": "file", "type": "str"},
        "folder": {"key": "folder", "type": "str"},
    }

    def __init__(self, *, file: Optional[str] = None, folder: Optional[str] = None, **kwargs):
        """
        :keyword file: Single file uri path.
        :paramtype file: str
        :keyword folder: Folder uri path.
        :paramtype folder: str
        """
        super(UriReference, self).__init__(**kwargs)
        self.file = file
        self.folder = folder
