# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import msrest.serialization


class Alias(msrest.serialization.Model):
    """The alias type.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param name: The alias name.
    :type name: str
    :param paths: The paths for an alias.
    :type paths: list[~azure.mgmt.resource.resources.v2020_06_01.models.AliasPath]
    :param type: The type of the alias. Possible values include: "NotSpecified", "PlainText",
     "Mask".
    :type type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.AliasType
    :param default_path: The default path for an alias.
    :type default_path: str
    :param default_pattern: The default pattern for an alias.
    :type default_pattern: ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPattern
    :ivar default_metadata: The default alias path metadata. Applies to the default path and to any
     alias path that doesn't have metadata.
    :vartype default_metadata: ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPathMetadata
    """

    _validation = {
        "default_metadata": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "paths": {"key": "paths", "type": "[AliasPath]"},
        "type": {"key": "type", "type": "str"},
        "default_path": {"key": "defaultPath", "type": "str"},
        "default_pattern": {"key": "defaultPattern", "type": "AliasPattern"},
        "default_metadata": {"key": "defaultMetadata", "type": "AliasPathMetadata"},
    }

    def __init__(self, **kwargs):
        super(Alias, self).__init__(**kwargs)
        self.name = kwargs.get("name", None)
        self.paths = kwargs.get("paths", None)
        self.type = kwargs.get("type", None)
        self.default_path = kwargs.get("default_path", None)
        self.default_pattern = kwargs.get("default_pattern", None)
        self.default_metadata = None


class AliasPath(msrest.serialization.Model):
    """The type of the paths for alias.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param path: The path of an alias.
    :type path: str
    :param api_versions: The API versions.
    :type api_versions: list[str]
    :param pattern: The pattern for an alias path.
    :type pattern: ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPattern
    :ivar metadata: The metadata of the alias path. If missing, fall back to the default metadata
     of the alias.
    :vartype metadata: ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPathMetadata
    """

    _validation = {
        "metadata": {"readonly": True},
    }

    _attribute_map = {
        "path": {"key": "path", "type": "str"},
        "api_versions": {"key": "apiVersions", "type": "[str]"},
        "pattern": {"key": "pattern", "type": "AliasPattern"},
        "metadata": {"key": "metadata", "type": "AliasPathMetadata"},
    }

    def __init__(self, **kwargs):
        super(AliasPath, self).__init__(**kwargs)
        self.path = kwargs.get("path", None)
        self.api_versions = kwargs.get("api_versions", None)
        self.pattern = kwargs.get("pattern", None)
        self.metadata = None


class AliasPathMetadata(msrest.serialization.Model):
    """AliasPathMetadata.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The type of the token that the alias path is referring to. Possible values include:
     "NotSpecified", "Any", "String", "Object", "Array", "Integer", "Number", "Boolean".
    :vartype type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPathTokenType
    :ivar attributes: The attributes of the token that the alias path is referring to. Possible
     values include: "None", "Modifiable".
    :vartype attributes: str or
     ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPathAttributes
    """

    _validation = {
        "type": {"readonly": True},
        "attributes": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "attributes": {"key": "attributes", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(AliasPathMetadata, self).__init__(**kwargs)
        self.type = None
        self.attributes = None


class AliasPattern(msrest.serialization.Model):
    """The type of the pattern for an alias path.

    :param phrase: The alias pattern phrase.
    :type phrase: str
    :param variable: The alias pattern variable.
    :type variable: str
    :param type: The type of alias pattern. Possible values include: "NotSpecified", "Extract".
    :type type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.AliasPatternType
    """

    _attribute_map = {
        "phrase": {"key": "phrase", "type": "str"},
        "variable": {"key": "variable", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(AliasPattern, self).__init__(**kwargs)
        self.phrase = kwargs.get("phrase", None)
        self.variable = kwargs.get("variable", None)
        self.type = kwargs.get("type", None)


class ApiProfile(msrest.serialization.Model):
    """ApiProfile.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar profile_version: The profile version.
    :vartype profile_version: str
    :ivar api_version: The API version.
    :vartype api_version: str
    """

    _validation = {
        "profile_version": {"readonly": True},
        "api_version": {"readonly": True},
    }

    _attribute_map = {
        "profile_version": {"key": "profileVersion", "type": "str"},
        "api_version": {"key": "apiVersion", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ApiProfile, self).__init__(**kwargs)
        self.profile_version = None
        self.api_version = None


class BasicDependency(msrest.serialization.Model):
    """Deployment dependency information.

    :param id: The ID of the dependency.
    :type id: str
    :param resource_type: The dependency resource type.
    :type resource_type: str
    :param resource_name: The dependency resource name.
    :type resource_name: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "resource_name": {"key": "resourceName", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(BasicDependency, self).__init__(**kwargs)
        self.id = kwargs.get("id", None)
        self.resource_type = kwargs.get("resource_type", None)
        self.resource_name = kwargs.get("resource_name", None)


class DebugSetting(msrest.serialization.Model):
    """The debug setting.

    :param detail_level: Specifies the type of information to log for debugging. The permitted
     values are none, requestContent, responseContent, or both requestContent and responseContent
     separated by a comma. The default is none. When setting this value, carefully consider the type
     of information you are passing in during deployment. By logging information about the request
     or response, you could potentially expose sensitive data that is retrieved through the
     deployment operations.
    :type detail_level: str
    """

    _attribute_map = {
        "detail_level": {"key": "detailLevel", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(DebugSetting, self).__init__(**kwargs)
        self.detail_level = kwargs.get("detail_level", None)


class Dependency(msrest.serialization.Model):
    """Deployment dependency information.

    :param depends_on: The list of dependencies.
    :type depends_on: list[~azure.mgmt.resource.resources.v2020_06_01.models.BasicDependency]
    :param id: The ID of the dependency.
    :type id: str
    :param resource_type: The dependency resource type.
    :type resource_type: str
    :param resource_name: The dependency resource name.
    :type resource_name: str
    """

    _attribute_map = {
        "depends_on": {"key": "dependsOn", "type": "[BasicDependency]"},
        "id": {"key": "id", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "resource_name": {"key": "resourceName", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(Dependency, self).__init__(**kwargs)
        self.depends_on = kwargs.get("depends_on", None)
        self.id = kwargs.get("id", None)
        self.resource_type = kwargs.get("resource_type", None)
        self.resource_name = kwargs.get("resource_name", None)


class Deployment(msrest.serialization.Model):
    """Deployment operation parameters.

    All required parameters must be populated in order to send to Azure.

    :param location: The location to store the deployment data.
    :type location: str
    :param properties: Required. The deployment properties.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentProperties
    :param tags: A set of tags. Deployment tags.
    :type tags: dict[str, str]
    """

    _validation = {
        "properties": {"required": True},
    }

    _attribute_map = {
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "DeploymentProperties"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(Deployment, self).__init__(**kwargs)
        self.location = kwargs.get("location", None)
        self.properties = kwargs["properties"]
        self.tags = kwargs.get("tags", None)


class DeploymentExportResult(msrest.serialization.Model):
    """The deployment export result.

    :param template: The template content.
    :type template: object
    """

    _attribute_map = {
        "template": {"key": "template", "type": "object"},
    }

    def __init__(self, **kwargs):
        super(DeploymentExportResult, self).__init__(**kwargs)
        self.template = kwargs.get("template", None)


class DeploymentExtended(msrest.serialization.Model):
    """Deployment information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The ID of the deployment.
    :vartype id: str
    :ivar name: The name of the deployment.
    :vartype name: str
    :ivar type: The type of the deployment.
    :vartype type: str
    :param location: the location of the deployment.
    :type location: str
    :param properties: Deployment properties.
    :type properties:
     ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentPropertiesExtended
    :param tags: A set of tags. Deployment tags.
    :type tags: dict[str, str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "DeploymentPropertiesExtended"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(DeploymentExtended, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.location = kwargs.get("location", None)
        self.properties = kwargs.get("properties", None)
        self.tags = kwargs.get("tags", None)


class DeploymentExtendedFilter(msrest.serialization.Model):
    """Deployment filter.

    :param provisioning_state: The provisioning state.
    :type provisioning_state: str
    """

    _attribute_map = {
        "provisioning_state": {"key": "provisioningState", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(DeploymentExtendedFilter, self).__init__(**kwargs)
        self.provisioning_state = kwargs.get("provisioning_state", None)


class DeploymentListResult(msrest.serialization.Model):
    """List of deployments.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param value: An array of deployments.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DeploymentExtended]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(DeploymentListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = None


class DeploymentOperation(msrest.serialization.Model):
    """Deployment operation information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Full deployment operation ID.
    :vartype id: str
    :ivar operation_id: Deployment operation ID.
    :vartype operation_id: str
    :param properties: Deployment properties.
    :type properties:
     ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentOperationProperties
    """

    _validation = {
        "id": {"readonly": True},
        "operation_id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "operation_id": {"key": "operationId", "type": "str"},
        "properties": {"key": "properties", "type": "DeploymentOperationProperties"},
    }

    def __init__(self, **kwargs):
        super(DeploymentOperation, self).__init__(**kwargs)
        self.id = None
        self.operation_id = None
        self.properties = kwargs.get("properties", None)


class DeploymentOperationProperties(msrest.serialization.Model):
    """Deployment operation properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provisioning_operation: The name of the current provisioning operation. Possible values
     include: "NotSpecified", "Create", "Delete", "Waiting", "AzureAsyncOperationWaiting",
     "ResourceCacheWaiting", "Action", "Read", "EvaluateDeploymentOutput", "DeploymentCleanup".
    :vartype provisioning_operation: str or
     ~azure.mgmt.resource.resources.v2020_06_01.models.ProvisioningOperation
    :ivar provisioning_state: The state of the provisioning.
    :vartype provisioning_state: str
    :ivar timestamp: The date and time of the operation.
    :vartype timestamp: ~datetime.datetime
    :ivar duration: The duration of the operation.
    :vartype duration: str
    :ivar service_request_id: Deployment operation service request id.
    :vartype service_request_id: str
    :ivar status_code: Operation status code from the resource provider. This property may not be
     set if a response has not yet been received.
    :vartype status_code: str
    :ivar status_message: Operation status message from the resource provider. This property is
     optional.  It will only be provided if an error was received from the resource provider.
    :vartype status_message: ~azure.mgmt.resource.resources.v2020_06_01.models.StatusMessage
    :ivar target_resource: The target resource.
    :vartype target_resource: ~azure.mgmt.resource.resources.v2020_06_01.models.TargetResource
    :ivar request: The HTTP request message.
    :vartype request: ~azure.mgmt.resource.resources.v2020_06_01.models.HttpMessage
    :ivar response: The HTTP response message.
    :vartype response: ~azure.mgmt.resource.resources.v2020_06_01.models.HttpMessage
    """

    _validation = {
        "provisioning_operation": {"readonly": True},
        "provisioning_state": {"readonly": True},
        "timestamp": {"readonly": True},
        "duration": {"readonly": True},
        "service_request_id": {"readonly": True},
        "status_code": {"readonly": True},
        "status_message": {"readonly": True},
        "target_resource": {"readonly": True},
        "request": {"readonly": True},
        "response": {"readonly": True},
    }

    _attribute_map = {
        "provisioning_operation": {"key": "provisioningOperation", "type": "str"},
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "timestamp": {"key": "timestamp", "type": "iso-8601"},
        "duration": {"key": "duration", "type": "str"},
        "service_request_id": {"key": "serviceRequestId", "type": "str"},
        "status_code": {"key": "statusCode", "type": "str"},
        "status_message": {"key": "statusMessage", "type": "StatusMessage"},
        "target_resource": {"key": "targetResource", "type": "TargetResource"},
        "request": {"key": "request", "type": "HttpMessage"},
        "response": {"key": "response", "type": "HttpMessage"},
    }

    def __init__(self, **kwargs):
        super(DeploymentOperationProperties, self).__init__(**kwargs)
        self.provisioning_operation = None
        self.provisioning_state = None
        self.timestamp = None
        self.duration = None
        self.service_request_id = None
        self.status_code = None
        self.status_message = None
        self.target_resource = None
        self.request = None
        self.response = None


class DeploymentOperationsListResult(msrest.serialization.Model):
    """List of deployment operations.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param value: An array of deployment operations.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentOperation]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DeploymentOperation]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(DeploymentOperationsListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = None


class DeploymentProperties(msrest.serialization.Model):
    """Deployment properties.

    All required parameters must be populated in order to send to Azure.

    :param template: The template content. You use this element when you want to pass the template
     syntax directly in the request rather than link to an existing template. It can be a JObject or
     well-formed JSON string. Use either the templateLink property or the template property, but not
     both.
    :type template: object
    :param template_link: The URI of the template. Use either the templateLink property or the
     template property, but not both.
    :type template_link: ~azure.mgmt.resource.resources.v2020_06_01.models.TemplateLink
    :param parameters: Name and value pairs that define the deployment parameters for the template.
     You use this element when you want to provide the parameter values directly in the request
     rather than link to an existing parameter file. Use either the parametersLink property or the
     parameters property, but not both. It can be a JObject or a well formed JSON string.
    :type parameters: object
    :param parameters_link: The URI of parameters file. You use this element to link to an existing
     parameters file. Use either the parametersLink property or the parameters property, but not
     both.
    :type parameters_link: ~azure.mgmt.resource.resources.v2020_06_01.models.ParametersLink
    :param mode: Required. The mode that is used to deploy resources. This value can be either
     Incremental or Complete. In Incremental mode, resources are deployed without deleting existing
     resources that are not included in the template. In Complete mode, resources are deployed and
     existing resources in the resource group that are not included in the template are deleted. Be
     careful when using Complete mode as you may unintentionally delete resources. Possible values
     include: "Incremental", "Complete".
    :type mode: str or ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentMode
    :param debug_setting: The debug setting of the deployment.
    :type debug_setting: ~azure.mgmt.resource.resources.v2020_06_01.models.DebugSetting
    :param on_error_deployment: The deployment on error behavior.
    :type on_error_deployment: ~azure.mgmt.resource.resources.v2020_06_01.models.OnErrorDeployment
    """

    _validation = {
        "mode": {"required": True},
    }

    _attribute_map = {
        "template": {"key": "template", "type": "object"},
        "template_link": {"key": "templateLink", "type": "TemplateLink"},
        "parameters": {"key": "parameters", "type": "object"},
        "parameters_link": {"key": "parametersLink", "type": "ParametersLink"},
        "mode": {"key": "mode", "type": "str"},
        "debug_setting": {"key": "debugSetting", "type": "DebugSetting"},
        "on_error_deployment": {"key": "onErrorDeployment", "type": "OnErrorDeployment"},
    }

    def __init__(self, **kwargs):
        super(DeploymentProperties, self).__init__(**kwargs)
        self.template = kwargs.get("template", None)
        self.template_link = kwargs.get("template_link", None)
        self.parameters = kwargs.get("parameters", None)
        self.parameters_link = kwargs.get("parameters_link", None)
        self.mode = kwargs["mode"]
        self.debug_setting = kwargs.get("debug_setting", None)
        self.on_error_deployment = kwargs.get("on_error_deployment", None)


class DeploymentPropertiesExtended(msrest.serialization.Model):
    """Deployment properties with additional details.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provisioning_state: Denotes the state of provisioning. Possible values include:
     "NotSpecified", "Accepted", "Running", "Ready", "Creating", "Created", "Deleting", "Deleted",
     "Canceled", "Failed", "Succeeded", "Updating".
    :vartype provisioning_state: str or
     ~azure.mgmt.resource.resources.v2020_06_01.models.ProvisioningState
    :ivar correlation_id: The correlation ID of the deployment.
    :vartype correlation_id: str
    :ivar timestamp: The timestamp of the template deployment.
    :vartype timestamp: ~datetime.datetime
    :ivar duration: The duration of the template deployment.
    :vartype duration: str
    :ivar outputs: Key/value pairs that represent deployment output.
    :vartype outputs: object
    :ivar providers: The list of resource providers needed for the deployment.
    :vartype providers: list[~azure.mgmt.resource.resources.v2020_06_01.models.Provider]
    :ivar dependencies: The list of deployment dependencies.
    :vartype dependencies: list[~azure.mgmt.resource.resources.v2020_06_01.models.Dependency]
    :ivar template_link: The URI referencing the template.
    :vartype template_link: ~azure.mgmt.resource.resources.v2020_06_01.models.TemplateLink
    :ivar parameters: Deployment parameters.
    :vartype parameters: object
    :ivar parameters_link: The URI referencing the parameters.
    :vartype parameters_link: ~azure.mgmt.resource.resources.v2020_06_01.models.ParametersLink
    :ivar mode: The deployment mode. Possible values are Incremental and Complete. Possible values
     include: "Incremental", "Complete".
    :vartype mode: str or ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentMode
    :ivar debug_setting: The debug setting of the deployment.
    :vartype debug_setting: ~azure.mgmt.resource.resources.v2020_06_01.models.DebugSetting
    :ivar on_error_deployment: The deployment on error behavior.
    :vartype on_error_deployment:
     ~azure.mgmt.resource.resources.v2020_06_01.models.OnErrorDeploymentExtended
    :ivar template_hash: The hash produced for the template.
    :vartype template_hash: str
    :ivar output_resources: Array of provisioned resources.
    :vartype output_resources:
     list[~azure.mgmt.resource.resources.v2020_06_01.models.ResourceReference]
    :ivar validated_resources: Array of validated resources.
    :vartype validated_resources:
     list[~azure.mgmt.resource.resources.v2020_06_01.models.ResourceReference]
    :ivar error: The deployment error.
    :vartype error: ~azure.mgmt.resource.resources.v2020_06_01.models.ErrorResponse
    """

    _validation = {
        "provisioning_state": {"readonly": True},
        "correlation_id": {"readonly": True},
        "timestamp": {"readonly": True},
        "duration": {"readonly": True},
        "outputs": {"readonly": True},
        "providers": {"readonly": True},
        "dependencies": {"readonly": True},
        "template_link": {"readonly": True},
        "parameters": {"readonly": True},
        "parameters_link": {"readonly": True},
        "mode": {"readonly": True},
        "debug_setting": {"readonly": True},
        "on_error_deployment": {"readonly": True},
        "template_hash": {"readonly": True},
        "output_resources": {"readonly": True},
        "validated_resources": {"readonly": True},
        "error": {"readonly": True},
    }

    _attribute_map = {
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "timestamp": {"key": "timestamp", "type": "iso-8601"},
        "duration": {"key": "duration", "type": "str"},
        "outputs": {"key": "outputs", "type": "object"},
        "providers": {"key": "providers", "type": "[Provider]"},
        "dependencies": {"key": "dependencies", "type": "[Dependency]"},
        "template_link": {"key": "templateLink", "type": "TemplateLink"},
        "parameters": {"key": "parameters", "type": "object"},
        "parameters_link": {"key": "parametersLink", "type": "ParametersLink"},
        "mode": {"key": "mode", "type": "str"},
        "debug_setting": {"key": "debugSetting", "type": "DebugSetting"},
        "on_error_deployment": {"key": "onErrorDeployment", "type": "OnErrorDeploymentExtended"},
        "template_hash": {"key": "templateHash", "type": "str"},
        "output_resources": {"key": "outputResources", "type": "[ResourceReference]"},
        "validated_resources": {"key": "validatedResources", "type": "[ResourceReference]"},
        "error": {"key": "error", "type": "ErrorResponse"},
    }

    def __init__(self, **kwargs):
        super(DeploymentPropertiesExtended, self).__init__(**kwargs)
        self.provisioning_state = None
        self.correlation_id = None
        self.timestamp = None
        self.duration = None
        self.outputs = None
        self.providers = None
        self.dependencies = None
        self.template_link = None
        self.parameters = None
        self.parameters_link = None
        self.mode = None
        self.debug_setting = None
        self.on_error_deployment = None
        self.template_hash = None
        self.output_resources = None
        self.validated_resources = None
        self.error = None


class DeploymentValidateResult(msrest.serialization.Model):
    """Information from validate template deployment response.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar error: The deployment validation error.
    :vartype error: ~azure.mgmt.resource.resources.v2020_06_01.models.ErrorResponse
    :param properties: The template deployment properties.
    :type properties:
     ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentPropertiesExtended
    """

    _validation = {
        "error": {"readonly": True},
    }

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorResponse"},
        "properties": {"key": "properties", "type": "DeploymentPropertiesExtended"},
    }

    def __init__(self, **kwargs):
        super(DeploymentValidateResult, self).__init__(**kwargs)
        self.error = None
        self.properties = kwargs.get("properties", None)


class DeploymentWhatIf(msrest.serialization.Model):
    """Deployment What-if operation parameters.

    All required parameters must be populated in order to send to Azure.

    :param location: The location to store the deployment data.
    :type location: str
    :param properties: Required. The deployment properties.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentWhatIfProperties
    """

    _validation = {
        "properties": {"required": True},
    }

    _attribute_map = {
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "DeploymentWhatIfProperties"},
    }

    def __init__(self, **kwargs):
        super(DeploymentWhatIf, self).__init__(**kwargs)
        self.location = kwargs.get("location", None)
        self.properties = kwargs["properties"]


class DeploymentWhatIfProperties(DeploymentProperties):
    """Deployment What-if properties.

    All required parameters must be populated in order to send to Azure.

    :param template: The template content. You use this element when you want to pass the template
     syntax directly in the request rather than link to an existing template. It can be a JObject or
     well-formed JSON string. Use either the templateLink property or the template property, but not
     both.
    :type template: object
    :param template_link: The URI of the template. Use either the templateLink property or the
     template property, but not both.
    :type template_link: ~azure.mgmt.resource.resources.v2020_06_01.models.TemplateLink
    :param parameters: Name and value pairs that define the deployment parameters for the template.
     You use this element when you want to provide the parameter values directly in the request
     rather than link to an existing parameter file. Use either the parametersLink property or the
     parameters property, but not both. It can be a JObject or a well formed JSON string.
    :type parameters: object
    :param parameters_link: The URI of parameters file. You use this element to link to an existing
     parameters file. Use either the parametersLink property or the parameters property, but not
     both.
    :type parameters_link: ~azure.mgmt.resource.resources.v2020_06_01.models.ParametersLink
    :param mode: Required. The mode that is used to deploy resources. This value can be either
     Incremental or Complete. In Incremental mode, resources are deployed without deleting existing
     resources that are not included in the template. In Complete mode, resources are deployed and
     existing resources in the resource group that are not included in the template are deleted. Be
     careful when using Complete mode as you may unintentionally delete resources. Possible values
     include: "Incremental", "Complete".
    :type mode: str or ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentMode
    :param debug_setting: The debug setting of the deployment.
    :type debug_setting: ~azure.mgmt.resource.resources.v2020_06_01.models.DebugSetting
    :param on_error_deployment: The deployment on error behavior.
    :type on_error_deployment: ~azure.mgmt.resource.resources.v2020_06_01.models.OnErrorDeployment
    :param what_if_settings: Optional What-If operation settings.
    :type what_if_settings:
     ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentWhatIfSettings
    """

    _validation = {
        "mode": {"required": True},
    }

    _attribute_map = {
        "template": {"key": "template", "type": "object"},
        "template_link": {"key": "templateLink", "type": "TemplateLink"},
        "parameters": {"key": "parameters", "type": "object"},
        "parameters_link": {"key": "parametersLink", "type": "ParametersLink"},
        "mode": {"key": "mode", "type": "str"},
        "debug_setting": {"key": "debugSetting", "type": "DebugSetting"},
        "on_error_deployment": {"key": "onErrorDeployment", "type": "OnErrorDeployment"},
        "what_if_settings": {"key": "whatIfSettings", "type": "DeploymentWhatIfSettings"},
    }

    def __init__(self, **kwargs):
        super(DeploymentWhatIfProperties, self).__init__(**kwargs)
        self.what_if_settings = kwargs.get("what_if_settings", None)


class DeploymentWhatIfSettings(msrest.serialization.Model):
    """Deployment What-If operation settings.

    :param result_format: The format of the What-If results. Possible values include:
     "ResourceIdOnly", "FullResourcePayloads".
    :type result_format: str or
     ~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfResultFormat
    """

    _attribute_map = {
        "result_format": {"key": "resultFormat", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(DeploymentWhatIfSettings, self).__init__(**kwargs)
        self.result_format = kwargs.get("result_format", None)


class ErrorAdditionalInfo(msrest.serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: object
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs):
        super(ErrorAdditionalInfo, self).__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorResponse(msrest.serialization.Model):
    """The resource management error response.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.resource.resources.v2020_06_01.models.ErrorResponse]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.resource.resources.v2020_06_01.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorResponse]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs):
        super(ErrorResponse, self).__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ExportTemplateRequest(msrest.serialization.Model):
    """Export resource group template request parameters.

    :param resources: The IDs of the resources to filter the export by. To export all resources,
     supply an array with single entry '*'.
    :type resources: list[str]
    :param options: The export template options. A CSV-formatted list containing zero or more of
     the following: 'IncludeParameterDefaultValue', 'IncludeComments',
     'SkipResourceNameParameterization', 'SkipAllParameterization'.
    :type options: str
    """

    _attribute_map = {
        "resources": {"key": "resources", "type": "[str]"},
        "options": {"key": "options", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ExportTemplateRequest, self).__init__(**kwargs)
        self.resources = kwargs.get("resources", None)
        self.options = kwargs.get("options", None)


class Resource(msrest.serialization.Model):
    """Specified resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Resource ID.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :param location: Resource location.
    :type location: str
    :param tags: A set of tags. Resource tags.
    :type tags: dict[str, str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(Resource, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.location = kwargs.get("location", None)
        self.tags = kwargs.get("tags", None)


class GenericResource(Resource):
    """Resource information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Resource ID.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :param location: Resource location.
    :type location: str
    :param tags: A set of tags. Resource tags.
    :type tags: dict[str, str]
    :param plan: The plan of the resource.
    :type plan: ~azure.mgmt.resource.resources.v2020_06_01.models.Plan
    :param properties: The resource properties.
    :type properties: object
    :param kind: The kind of the resource.
    :type kind: str
    :param managed_by: ID of the resource that manages this resource.
    :type managed_by: str
    :param sku: The SKU of the resource.
    :type sku: ~azure.mgmt.resource.resources.v2020_06_01.models.Sku
    :param identity: The identity of the resource.
    :type identity: ~azure.mgmt.resource.resources.v2020_06_01.models.Identity
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "kind": {"pattern": r"^[-\w\._,\(\)]+$"},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "plan": {"key": "plan", "type": "Plan"},
        "properties": {"key": "properties", "type": "object"},
        "kind": {"key": "kind", "type": "str"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "sku": {"key": "sku", "type": "Sku"},
        "identity": {"key": "identity", "type": "Identity"},
    }

    def __init__(self, **kwargs):
        super(GenericResource, self).__init__(**kwargs)
        self.plan = kwargs.get("plan", None)
        self.properties = kwargs.get("properties", None)
        self.kind = kwargs.get("kind", None)
        self.managed_by = kwargs.get("managed_by", None)
        self.sku = kwargs.get("sku", None)
        self.identity = kwargs.get("identity", None)


class GenericResourceExpanded(GenericResource):
    """Resource information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Resource ID.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :param location: Resource location.
    :type location: str
    :param tags: A set of tags. Resource tags.
    :type tags: dict[str, str]
    :param plan: The plan of the resource.
    :type plan: ~azure.mgmt.resource.resources.v2020_06_01.models.Plan
    :param properties: The resource properties.
    :type properties: object
    :param kind: The kind of the resource.
    :type kind: str
    :param managed_by: ID of the resource that manages this resource.
    :type managed_by: str
    :param sku: The SKU of the resource.
    :type sku: ~azure.mgmt.resource.resources.v2020_06_01.models.Sku
    :param identity: The identity of the resource.
    :type identity: ~azure.mgmt.resource.resources.v2020_06_01.models.Identity
    :ivar created_time: The created time of the resource. This is only present if requested via the
     $expand query parameter.
    :vartype created_time: ~datetime.datetime
    :ivar changed_time: The changed time of the resource. This is only present if requested via the
     $expand query parameter.
    :vartype changed_time: ~datetime.datetime
    :ivar provisioning_state: The provisioning state of the resource. This is only present if
     requested via the $expand query parameter.
    :vartype provisioning_state: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "kind": {"pattern": r"^[-\w\._,\(\)]+$"},
        "created_time": {"readonly": True},
        "changed_time": {"readonly": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "plan": {"key": "plan", "type": "Plan"},
        "properties": {"key": "properties", "type": "object"},
        "kind": {"key": "kind", "type": "str"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "sku": {"key": "sku", "type": "Sku"},
        "identity": {"key": "identity", "type": "Identity"},
        "created_time": {"key": "createdTime", "type": "iso-8601"},
        "changed_time": {"key": "changedTime", "type": "iso-8601"},
        "provisioning_state": {"key": "provisioningState", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(GenericResourceExpanded, self).__init__(**kwargs)
        self.created_time = None
        self.changed_time = None
        self.provisioning_state = None


class GenericResourceFilter(msrest.serialization.Model):
    """Resource filter.

    :param resource_type: The resource type.
    :type resource_type: str
    :param tagname: The tag name.
    :type tagname: str
    :param tagvalue: The tag value.
    :type tagvalue: str
    """

    _attribute_map = {
        "resource_type": {"key": "resourceType", "type": "str"},
        "tagname": {"key": "tagname", "type": "str"},
        "tagvalue": {"key": "tagvalue", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(GenericResourceFilter, self).__init__(**kwargs)
        self.resource_type = kwargs.get("resource_type", None)
        self.tagname = kwargs.get("tagname", None)
        self.tagvalue = kwargs.get("tagvalue", None)


class HttpMessage(msrest.serialization.Model):
    """HTTP message.

    :param content: HTTP message content.
    :type content: object
    """

    _attribute_map = {
        "content": {"key": "content", "type": "object"},
    }

    def __init__(self, **kwargs):
        super(HttpMessage, self).__init__(**kwargs)
        self.content = kwargs.get("content", None)


class Identity(msrest.serialization.Model):
    """Identity for the resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar principal_id: The principal ID of resource identity.
    :vartype principal_id: str
    :ivar tenant_id: The tenant ID of resource.
    :vartype tenant_id: str
    :param type: The identity type. Possible values include: "SystemAssigned", "UserAssigned",
     "SystemAssigned, UserAssigned", "None".
    :type type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.ResourceIdentityType
    :param user_assigned_identities: The list of user identities associated with the resource. The
     user identity dictionary key references will be ARM resource ids in the form:
     '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.
    :type user_assigned_identities: dict[str,
     ~azure.mgmt.resource.resources.v2020_06_01.models.IdentityUserAssignedIdentitiesValue]
    """

    _validation = {
        "principal_id": {"readonly": True},
        "tenant_id": {"readonly": True},
    }

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "user_assigned_identities": {"key": "userAssignedIdentities", "type": "{IdentityUserAssignedIdentitiesValue}"},
    }

    def __init__(self, **kwargs):
        super(Identity, self).__init__(**kwargs)
        self.principal_id = None
        self.tenant_id = None
        self.type = kwargs.get("type", None)
        self.user_assigned_identities = kwargs.get("user_assigned_identities", None)


class IdentityUserAssignedIdentitiesValue(msrest.serialization.Model):
    """IdentityUserAssignedIdentitiesValue.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar principal_id: The principal id of user assigned identity.
    :vartype principal_id: str
    :ivar client_id: The client id of user assigned identity.
    :vartype client_id: str
    """

    _validation = {
        "principal_id": {"readonly": True},
        "client_id": {"readonly": True},
    }

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(IdentityUserAssignedIdentitiesValue, self).__init__(**kwargs)
        self.principal_id = None
        self.client_id = None


class OnErrorDeployment(msrest.serialization.Model):
    """Deployment on error behavior.

    :param type: The deployment on error behavior type. Possible values are LastSuccessful and
     SpecificDeployment. Possible values include: "LastSuccessful", "SpecificDeployment".
    :type type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.OnErrorDeploymentType
    :param deployment_name: The deployment to be used on error case.
    :type deployment_name: str
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "deployment_name": {"key": "deploymentName", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(OnErrorDeployment, self).__init__(**kwargs)
        self.type = kwargs.get("type", None)
        self.deployment_name = kwargs.get("deployment_name", None)


class OnErrorDeploymentExtended(msrest.serialization.Model):
    """Deployment on error behavior with additional details.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provisioning_state: The state of the provisioning for the on error deployment.
    :vartype provisioning_state: str
    :param type: The deployment on error behavior type. Possible values are LastSuccessful and
     SpecificDeployment. Possible values include: "LastSuccessful", "SpecificDeployment".
    :type type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.OnErrorDeploymentType
    :param deployment_name: The deployment to be used on error case.
    :type deployment_name: str
    """

    _validation = {
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "provisioning_state": {"key": "provisioningState", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "deployment_name": {"key": "deploymentName", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(OnErrorDeploymentExtended, self).__init__(**kwargs)
        self.provisioning_state = None
        self.type = kwargs.get("type", None)
        self.deployment_name = kwargs.get("deployment_name", None)


class Operation(msrest.serialization.Model):
    """Microsoft.Resources operation.

    :param name: Operation name: {provider}/{resource}/{operation}.
    :type name: str
    :param display: The object that represents the operation.
    :type display: ~azure.mgmt.resource.resources.v2020_06_01.models.OperationDisplay
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "display": {"key": "display", "type": "OperationDisplay"},
    }

    def __init__(self, **kwargs):
        super(Operation, self).__init__(**kwargs)
        self.name = kwargs.get("name", None)
        self.display = kwargs.get("display", None)


class OperationDisplay(msrest.serialization.Model):
    """The object that represents the operation.

    :param provider: Service provider: Microsoft.Resources.
    :type provider: str
    :param resource: Resource on which the operation is performed: Profile, endpoint, etc.
    :type resource: str
    :param operation: Operation type: Read, write, delete, etc.
    :type operation: str
    :param description: Description of the operation.
    :type description: str
    """

    _attribute_map = {
        "provider": {"key": "provider", "type": "str"},
        "resource": {"key": "resource", "type": "str"},
        "operation": {"key": "operation", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(OperationDisplay, self).__init__(**kwargs)
        self.provider = kwargs.get("provider", None)
        self.resource = kwargs.get("resource", None)
        self.operation = kwargs.get("operation", None)
        self.description = kwargs.get("description", None)


class OperationListResult(msrest.serialization.Model):
    """Result of the request to list Microsoft.Resources operations. It contains a list of operations and a URL link to get the next set of results.

    :param value: List of Microsoft.Resources operations.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.Operation]
    :param next_link: URL to get the next set of operation list results if there are any.
    :type next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[Operation]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(OperationListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = kwargs.get("next_link", None)


class ParametersLink(msrest.serialization.Model):
    """Entity representing the reference to the deployment parameters.

    All required parameters must be populated in order to send to Azure.

    :param uri: Required. The URI of the parameters file.
    :type uri: str
    :param content_version: If included, must match the ContentVersion in the template.
    :type content_version: str
    """

    _validation = {
        "uri": {"required": True},
    }

    _attribute_map = {
        "uri": {"key": "uri", "type": "str"},
        "content_version": {"key": "contentVersion", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ParametersLink, self).__init__(**kwargs)
        self.uri = kwargs["uri"]
        self.content_version = kwargs.get("content_version", None)


class Plan(msrest.serialization.Model):
    """Plan for the resource.

    :param name: The plan ID.
    :type name: str
    :param publisher: The publisher ID.
    :type publisher: str
    :param product: The offer ID.
    :type product: str
    :param promotion_code: The promotion code.
    :type promotion_code: str
    :param version: The plan's version.
    :type version: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "publisher": {"key": "publisher", "type": "str"},
        "product": {"key": "product", "type": "str"},
        "promotion_code": {"key": "promotionCode", "type": "str"},
        "version": {"key": "version", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(Plan, self).__init__(**kwargs)
        self.name = kwargs.get("name", None)
        self.publisher = kwargs.get("publisher", None)
        self.product = kwargs.get("product", None)
        self.promotion_code = kwargs.get("promotion_code", None)
        self.version = kwargs.get("version", None)


class Provider(msrest.serialization.Model):
    """Resource provider information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The provider ID.
    :vartype id: str
    :param namespace: The namespace of the resource provider.
    :type namespace: str
    :ivar registration_state: The registration state of the resource provider.
    :vartype registration_state: str
    :ivar registration_policy: The registration policy of the resource provider.
    :vartype registration_policy: str
    :ivar resource_types: The collection of provider resource types.
    :vartype resource_types:
     list[~azure.mgmt.resource.resources.v2020_06_01.models.ProviderResourceType]
    """

    _validation = {
        "id": {"readonly": True},
        "registration_state": {"readonly": True},
        "registration_policy": {"readonly": True},
        "resource_types": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "namespace": {"key": "namespace", "type": "str"},
        "registration_state": {"key": "registrationState", "type": "str"},
        "registration_policy": {"key": "registrationPolicy", "type": "str"},
        "resource_types": {"key": "resourceTypes", "type": "[ProviderResourceType]"},
    }

    def __init__(self, **kwargs):
        super(Provider, self).__init__(**kwargs)
        self.id = None
        self.namespace = kwargs.get("namespace", None)
        self.registration_state = None
        self.registration_policy = None
        self.resource_types = None


class ProviderListResult(msrest.serialization.Model):
    """List of resource providers.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param value: An array of resource providers.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.Provider]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Provider]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ProviderListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = None


class ProviderResourceType(msrest.serialization.Model):
    """Resource type managed by the resource provider.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param resource_type: The resource type.
    :type resource_type: str
    :param locations: The collection of locations where this resource type can be created.
    :type locations: list[str]
    :param aliases: The aliases that are supported by this resource type.
    :type aliases: list[~azure.mgmt.resource.resources.v2020_06_01.models.Alias]
    :param api_versions: The API version.
    :type api_versions: list[str]
    :ivar default_api_version: The default API version.
    :vartype default_api_version: str
    :ivar api_profiles: The API profiles for the resource provider.
    :vartype api_profiles: list[~azure.mgmt.resource.resources.v2020_06_01.models.ApiProfile]
    :param capabilities: The additional capabilities offered by this resource type.
    :type capabilities: str
    :param properties: The properties.
    :type properties: dict[str, str]
    """

    _validation = {
        "default_api_version": {"readonly": True},
        "api_profiles": {"readonly": True},
    }

    _attribute_map = {
        "resource_type": {"key": "resourceType", "type": "str"},
        "locations": {"key": "locations", "type": "[str]"},
        "aliases": {"key": "aliases", "type": "[Alias]"},
        "api_versions": {"key": "apiVersions", "type": "[str]"},
        "default_api_version": {"key": "defaultApiVersion", "type": "str"},
        "api_profiles": {"key": "apiProfiles", "type": "[ApiProfile]"},
        "capabilities": {"key": "capabilities", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(ProviderResourceType, self).__init__(**kwargs)
        self.resource_type = kwargs.get("resource_type", None)
        self.locations = kwargs.get("locations", None)
        self.aliases = kwargs.get("aliases", None)
        self.api_versions = kwargs.get("api_versions", None)
        self.default_api_version = None
        self.api_profiles = None
        self.capabilities = kwargs.get("capabilities", None)
        self.properties = kwargs.get("properties", None)


class ResourceGroup(msrest.serialization.Model):
    """Resource group information.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The ID of the resource group.
    :vartype id: str
    :ivar name: The name of the resource group.
    :vartype name: str
    :ivar type: The type of the resource group.
    :vartype type: str
    :param properties: The resource group properties.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.ResourceGroupProperties
    :param location: Required. The location of the resource group. It cannot be changed after the
     resource group has been created. It must be one of the supported Azure locations.
    :type location: str
    :param managed_by: The ID of the resource that manages this resource group.
    :type managed_by: str
    :param tags: A set of tags. The tags attached to the resource group.
    :type tags: dict[str, str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "location": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "properties": {"key": "properties", "type": "ResourceGroupProperties"},
        "location": {"key": "location", "type": "str"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(ResourceGroup, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.properties = kwargs.get("properties", None)
        self.location = kwargs["location"]
        self.managed_by = kwargs.get("managed_by", None)
        self.tags = kwargs.get("tags", None)


class ResourceGroupExportResult(msrest.serialization.Model):
    """Resource group export result.

    :param template: The template content.
    :type template: object
    :param error: The template export error.
    :type error: ~azure.mgmt.resource.resources.v2020_06_01.models.ErrorResponse
    """

    _attribute_map = {
        "template": {"key": "template", "type": "object"},
        "error": {"key": "error", "type": "ErrorResponse"},
    }

    def __init__(self, **kwargs):
        super(ResourceGroupExportResult, self).__init__(**kwargs)
        self.template = kwargs.get("template", None)
        self.error = kwargs.get("error", None)


class ResourceGroupFilter(msrest.serialization.Model):
    """Resource group filter.

    :param tag_name: The tag name.
    :type tag_name: str
    :param tag_value: The tag value.
    :type tag_value: str
    """

    _attribute_map = {
        "tag_name": {"key": "tagName", "type": "str"},
        "tag_value": {"key": "tagValue", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourceGroupFilter, self).__init__(**kwargs)
        self.tag_name = kwargs.get("tag_name", None)
        self.tag_value = kwargs.get("tag_value", None)


class ResourceGroupListResult(msrest.serialization.Model):
    """List of resource groups.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param value: An array of resource groups.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.ResourceGroup]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[ResourceGroup]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourceGroupListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = None


class ResourceGroupPatchable(msrest.serialization.Model):
    """Resource group information.

    :param name: The name of the resource group.
    :type name: str
    :param properties: The resource group properties.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.ResourceGroupProperties
    :param managed_by: The ID of the resource that manages this resource group.
    :type managed_by: str
    :param tags: A set of tags. The tags attached to the resource group.
    :type tags: dict[str, str]
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "properties": {"key": "properties", "type": "ResourceGroupProperties"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(ResourceGroupPatchable, self).__init__(**kwargs)
        self.name = kwargs.get("name", None)
        self.properties = kwargs.get("properties", None)
        self.managed_by = kwargs.get("managed_by", None)
        self.tags = kwargs.get("tags", None)


class ResourceGroupProperties(msrest.serialization.Model):
    """The resource group properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provisioning_state: The provisioning state.
    :vartype provisioning_state: str
    """

    _validation = {
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "provisioning_state": {"key": "provisioningState", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourceGroupProperties, self).__init__(**kwargs)
        self.provisioning_state = None


class ResourceListResult(msrest.serialization.Model):
    """List of resource groups.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param value: An array of resources.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.GenericResourceExpanded]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[GenericResourceExpanded]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourceListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = None


class ResourceProviderOperationDisplayProperties(msrest.serialization.Model):
    """Resource provider operation's display properties.

    :param publisher: Operation description.
    :type publisher: str
    :param provider: Operation provider.
    :type provider: str
    :param resource: Operation resource.
    :type resource: str
    :param operation: Resource provider operation.
    :type operation: str
    :param description: Operation description.
    :type description: str
    """

    _attribute_map = {
        "publisher": {"key": "publisher", "type": "str"},
        "provider": {"key": "provider", "type": "str"},
        "resource": {"key": "resource", "type": "str"},
        "operation": {"key": "operation", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourceProviderOperationDisplayProperties, self).__init__(**kwargs)
        self.publisher = kwargs.get("publisher", None)
        self.provider = kwargs.get("provider", None)
        self.resource = kwargs.get("resource", None)
        self.operation = kwargs.get("operation", None)
        self.description = kwargs.get("description", None)


class ResourceReference(msrest.serialization.Model):
    """The resource Id model.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The fully qualified resource Id.
    :vartype id: str
    """

    _validation = {
        "id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourceReference, self).__init__(**kwargs)
        self.id = None


class ResourcesMoveInfo(msrest.serialization.Model):
    """Parameters of move resources.

    :param resources: The IDs of the resources.
    :type resources: list[str]
    :param target_resource_group: The target resource group.
    :type target_resource_group: str
    """

    _attribute_map = {
        "resources": {"key": "resources", "type": "[str]"},
        "target_resource_group": {"key": "targetResourceGroup", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(ResourcesMoveInfo, self).__init__(**kwargs)
        self.resources = kwargs.get("resources", None)
        self.target_resource_group = kwargs.get("target_resource_group", None)


class ScopedDeployment(msrest.serialization.Model):
    """Deployment operation parameters.

    All required parameters must be populated in order to send to Azure.

    :param location: Required. The location to store the deployment data.
    :type location: str
    :param properties: Required. The deployment properties.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentProperties
    :param tags: A set of tags. Deployment tags.
    :type tags: dict[str, str]
    """

    _validation = {
        "location": {"required": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "DeploymentProperties"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(ScopedDeployment, self).__init__(**kwargs)
        self.location = kwargs["location"]
        self.properties = kwargs["properties"]
        self.tags = kwargs.get("tags", None)


class ScopedDeploymentWhatIf(msrest.serialization.Model):
    """Deployment What-if operation parameters.

    All required parameters must be populated in order to send to Azure.

    :param location: Required. The location to store the deployment data.
    :type location: str
    :param properties: Required. The deployment properties.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentWhatIfProperties
    """

    _validation = {
        "location": {"required": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "location": {"key": "location", "type": "str"},
        "properties": {"key": "properties", "type": "DeploymentWhatIfProperties"},
    }

    def __init__(self, **kwargs):
        super(ScopedDeploymentWhatIf, self).__init__(**kwargs)
        self.location = kwargs["location"]
        self.properties = kwargs["properties"]


class Sku(msrest.serialization.Model):
    """SKU for the resource.

    :param name: The SKU name.
    :type name: str
    :param tier: The SKU tier.
    :type tier: str
    :param size: The SKU size.
    :type size: str
    :param family: The SKU family.
    :type family: str
    :param model: The SKU model.
    :type model: str
    :param capacity: The SKU capacity.
    :type capacity: int
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "tier": {"key": "tier", "type": "str"},
        "size": {"key": "size", "type": "str"},
        "family": {"key": "family", "type": "str"},
        "model": {"key": "model", "type": "str"},
        "capacity": {"key": "capacity", "type": "int"},
    }

    def __init__(self, **kwargs):
        super(Sku, self).__init__(**kwargs)
        self.name = kwargs.get("name", None)
        self.tier = kwargs.get("tier", None)
        self.size = kwargs.get("size", None)
        self.family = kwargs.get("family", None)
        self.model = kwargs.get("model", None)
        self.capacity = kwargs.get("capacity", None)


class StatusMessage(msrest.serialization.Model):
    """Operation status message object.

    :param status: Status of the deployment operation.
    :type status: str
    :param error: The error reported by the operation.
    :type error: ~azure.mgmt.resource.resources.v2020_06_01.models.ErrorResponse
    """

    _attribute_map = {
        "status": {"key": "status", "type": "str"},
        "error": {"key": "error", "type": "ErrorResponse"},
    }

    def __init__(self, **kwargs):
        super(StatusMessage, self).__init__(**kwargs)
        self.status = kwargs.get("status", None)
        self.error = kwargs.get("error", None)


class SubResource(msrest.serialization.Model):
    """Sub-resource.

    :param id: Resource ID.
    :type id: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(SubResource, self).__init__(**kwargs)
        self.id = kwargs.get("id", None)


class TagCount(msrest.serialization.Model):
    """Tag count.

    :param type: Type of count.
    :type type: str
    :param value: Value of count.
    :type value: int
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "value": {"key": "value", "type": "int"},
    }

    def __init__(self, **kwargs):
        super(TagCount, self).__init__(**kwargs)
        self.type = kwargs.get("type", None)
        self.value = kwargs.get("value", None)


class TagDetails(msrest.serialization.Model):
    """Tag details.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The tag name ID.
    :vartype id: str
    :param tag_name: The tag name.
    :type tag_name: str
    :param count: The total number of resources that use the resource tag. When a tag is initially
     created and has no associated resources, the value is 0.
    :type count: ~azure.mgmt.resource.resources.v2020_06_01.models.TagCount
    :param values: The list of tag values.
    :type values: list[~azure.mgmt.resource.resources.v2020_06_01.models.TagValue]
    """

    _validation = {
        "id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "tag_name": {"key": "tagName", "type": "str"},
        "count": {"key": "count", "type": "TagCount"},
        "values": {"key": "values", "type": "[TagValue]"},
    }

    def __init__(self, **kwargs):
        super(TagDetails, self).__init__(**kwargs)
        self.id = None
        self.tag_name = kwargs.get("tag_name", None)
        self.count = kwargs.get("count", None)
        self.values = kwargs.get("values", None)


class Tags(msrest.serialization.Model):
    """A dictionary of name and value pairs.

    :param tags: A set of tags. Dictionary of :code:`<string>`.
    :type tags: dict[str, str]
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, **kwargs):
        super(Tags, self).__init__(**kwargs)
        self.tags = kwargs.get("tags", None)


class TagsListResult(msrest.serialization.Model):
    """List of subscription tags.

    Variables are only populated by the server, and will be ignored when sending a request.

    :param value: An array of tags.
    :type value: list[~azure.mgmt.resource.resources.v2020_06_01.models.TagDetails]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[TagDetails]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(TagsListResult, self).__init__(**kwargs)
        self.value = kwargs.get("value", None)
        self.next_link = None


class TagsPatchResource(msrest.serialization.Model):
    """Wrapper resource for tags patch API request only.

    :param operation: The operation type for the patch API. Possible values include: "Replace",
     "Merge", "Delete".
    :type operation: str or ~azure.mgmt.resource.resources.v2020_06_01.models.TagsPatchOperation
    :param properties: The set of tags.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.Tags
    """

    _attribute_map = {
        "operation": {"key": "operation", "type": "str"},
        "properties": {"key": "properties", "type": "Tags"},
    }

    def __init__(self, **kwargs):
        super(TagsPatchResource, self).__init__(**kwargs)
        self.operation = kwargs.get("operation", None)
        self.properties = kwargs.get("properties", None)


class TagsResource(msrest.serialization.Model):
    """Wrapper resource for tags API requests and responses.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The ID of the tags wrapper resource.
    :vartype id: str
    :ivar name: The name of the tags wrapper resource.
    :vartype name: str
    :ivar type: The type of the tags wrapper resource.
    :vartype type: str
    :param properties: Required. The set of tags.
    :type properties: ~azure.mgmt.resource.resources.v2020_06_01.models.Tags
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "properties": {"key": "properties", "type": "Tags"},
    }

    def __init__(self, **kwargs):
        super(TagsResource, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.properties = kwargs["properties"]


class TagValue(msrest.serialization.Model):
    """Tag information.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The tag value ID.
    :vartype id: str
    :param tag_value: The tag value.
    :type tag_value: str
    :param count: The tag value count.
    :type count: ~azure.mgmt.resource.resources.v2020_06_01.models.TagCount
    """

    _validation = {
        "id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "tag_value": {"key": "tagValue", "type": "str"},
        "count": {"key": "count", "type": "TagCount"},
    }

    def __init__(self, **kwargs):
        super(TagValue, self).__init__(**kwargs)
        self.id = None
        self.tag_value = kwargs.get("tag_value", None)
        self.count = kwargs.get("count", None)


class TargetResource(msrest.serialization.Model):
    """Target resource.

    :param id: The ID of the resource.
    :type id: str
    :param resource_name: The name of the resource.
    :type resource_name: str
    :param resource_type: The type of the resource.
    :type resource_type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "resource_name": {"key": "resourceName", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(TargetResource, self).__init__(**kwargs)
        self.id = kwargs.get("id", None)
        self.resource_name = kwargs.get("resource_name", None)
        self.resource_type = kwargs.get("resource_type", None)


class TemplateHashResult(msrest.serialization.Model):
    """Result of the request to calculate template hash. It contains a string of minified template and its hash.

    :param minified_template: The minified template string.
    :type minified_template: str
    :param template_hash: The template hash.
    :type template_hash: str
    """

    _attribute_map = {
        "minified_template": {"key": "minifiedTemplate", "type": "str"},
        "template_hash": {"key": "templateHash", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(TemplateHashResult, self).__init__(**kwargs)
        self.minified_template = kwargs.get("minified_template", None)
        self.template_hash = kwargs.get("template_hash", None)


class TemplateLink(msrest.serialization.Model):
    """Entity representing the reference to the template.

    :param uri: The URI of the template to deploy. Use either the uri or id property, but not both.
    :type uri: str
    :param id: The resource id of a Template Spec. Use either the id or uri property, but not both.
    :type id: str
    :param relative_path: Applicable only if this template link references a Template Spec. This
     relativePath property can optionally be used to reference a Template Spec artifact by path.
    :type relative_path: str
    :param content_version: If included, must match the ContentVersion in the template.
    :type content_version: str
    """

    _attribute_map = {
        "uri": {"key": "uri", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "relative_path": {"key": "relativePath", "type": "str"},
        "content_version": {"key": "contentVersion", "type": "str"},
    }

    def __init__(self, **kwargs):
        super(TemplateLink, self).__init__(**kwargs)
        self.uri = kwargs.get("uri", None)
        self.id = kwargs.get("id", None)
        self.relative_path = kwargs.get("relative_path", None)
        self.content_version = kwargs.get("content_version", None)


class WhatIfChange(msrest.serialization.Model):
    """Information about a single resource change predicted by What-If operation.

    All required parameters must be populated in order to send to Azure.

    :param resource_id: Required. Resource ID.
    :type resource_id: str
    :param change_type: Required. Type of change that will be made to the resource when the
     deployment is executed. Possible values include: "Create", "Delete", "Ignore", "Deploy",
     "NoChange", "Modify".
    :type change_type: str or ~azure.mgmt.resource.resources.v2020_06_01.models.ChangeType
    :param before: The snapshot of the resource before the deployment is executed.
    :type before: object
    :param after: The predicted snapshot of the resource after the deployment is executed.
    :type after: object
    :param delta: The predicted changes to resource properties.
    :type delta: list[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfPropertyChange]
    """

    _validation = {
        "resource_id": {"required": True},
        "change_type": {"required": True},
    }

    _attribute_map = {
        "resource_id": {"key": "resourceId", "type": "str"},
        "change_type": {"key": "changeType", "type": "str"},
        "before": {"key": "before", "type": "object"},
        "after": {"key": "after", "type": "object"},
        "delta": {"key": "delta", "type": "[WhatIfPropertyChange]"},
    }

    def __init__(self, **kwargs):
        super(WhatIfChange, self).__init__(**kwargs)
        self.resource_id = kwargs["resource_id"]
        self.change_type = kwargs["change_type"]
        self.before = kwargs.get("before", None)
        self.after = kwargs.get("after", None)
        self.delta = kwargs.get("delta", None)


class WhatIfOperationResult(msrest.serialization.Model):
    """Result of the What-If operation. Contains a list of predicted changes and a URL link to get to the next set of results.

    :param status: Status of the What-If operation.
    :type status: str
    :param error: Error when What-If operation fails.
    :type error: ~azure.mgmt.resource.resources.v2020_06_01.models.ErrorResponse
    :param changes: List of resource changes predicted by What-If operation.
    :type changes: list[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfChange]
    """

    _attribute_map = {
        "status": {"key": "status", "type": "str"},
        "error": {"key": "error", "type": "ErrorResponse"},
        "changes": {"key": "properties.changes", "type": "[WhatIfChange]"},
    }

    def __init__(self, **kwargs):
        super(WhatIfOperationResult, self).__init__(**kwargs)
        self.status = kwargs.get("status", None)
        self.error = kwargs.get("error", None)
        self.changes = kwargs.get("changes", None)


class WhatIfPropertyChange(msrest.serialization.Model):
    """The predicted change to the resource property.

    All required parameters must be populated in order to send to Azure.

    :param path: Required. The path of the property.
    :type path: str
    :param property_change_type: Required. The type of property change. Possible values include:
     "Create", "Delete", "Modify", "Array".
    :type property_change_type: str or
     ~azure.mgmt.resource.resources.v2020_06_01.models.PropertyChangeType
    :param before: The value of the property before the deployment is executed.
    :type before: object
    :param after: The value of the property after the deployment is executed.
    :type after: object
    :param children: Nested property changes.
    :type children: list[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfPropertyChange]
    """

    _validation = {
        "path": {"required": True},
        "property_change_type": {"required": True},
    }

    _attribute_map = {
        "path": {"key": "path", "type": "str"},
        "property_change_type": {"key": "propertyChangeType", "type": "str"},
        "before": {"key": "before", "type": "object"},
        "after": {"key": "after", "type": "object"},
        "children": {"key": "children", "type": "[WhatIfPropertyChange]"},
    }

    def __init__(self, **kwargs):
        super(WhatIfPropertyChange, self).__init__(**kwargs)
        self.path = kwargs["path"]
        self.property_change_type = kwargs["property_change_type"]
        self.before = kwargs.get("before", None)
        self.after = kwargs.get("after", None)
        self.children = kwargs.get("children", None)
