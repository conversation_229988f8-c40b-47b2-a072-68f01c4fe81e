# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from enum import Enum
from six import with_metaclass
from azure.core import CaseInsensitiveEnumMeta


class ActionType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum. Indicates the action type. "Internal" refers to actions that are for internal only APIs.
    """

    INTERNAL = "Internal"

class AllocationState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Allocation state of the compute. Possible values are: steady - Indicates that the compute is
    not resizing. There are no changes to the number of compute nodes in the compute in progress. A
    compute enters this state when it is created and when no operations are being performed on the
    compute to change the number of compute nodes. resizing - Indicates that the compute is
    resizing; that is, compute nodes are being added to or removed from the compute.
    """

    STEADY = "Steady"
    RESIZING = "Resizing"

class AllowedContentLevel(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Level at which content is filtered.
    """

    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"

class ApplicationSharingPolicy(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Policy for sharing applications on this compute instance among users of parent workspace. If
    Personal, only the creator can access applications on this compute instance. When Shared, any
    workspace user can access applications on this instance depending on his/her assigned role.
    """

    PERSONAL = "Personal"
    SHARED = "Shared"

class AssetProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Provisioning state of registry asset.
    """

    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"
    CREATING = "Creating"
    UPDATING = "Updating"
    DELETING = "Deleting"

class AutoRebuildSetting(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """AutoRebuild setting for the derived image
    """

    DISABLED = "Disabled"
    ON_BASE_IMAGE_UPDATE = "OnBaseImageUpdate"

class Autosave(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Auto save settings.
    """

    NONE = "None"
    LOCAL = "Local"
    REMOTE = "Remote"

class BatchDeploymentConfigurationType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The enumerated property types for batch deployments.
    """

    MODEL = "Model"
    PIPELINE_COMPONENT = "PipelineComponent"

class BatchLoggingLevel(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Log verbosity for batch inferencing.
    Increasing verbosity order for logging is : Warning, Info and Debug.
    The default value is Info.
    """

    INFO = "Info"
    WARNING = "Warning"
    DEBUG = "Debug"

class BatchOutputAction(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine how batch inferencing will handle output
    """

    SUMMARY_ONLY = "SummaryOnly"
    APPEND_ROW = "AppendRow"

class BillingCurrency(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Three lettered code specifying the currency of the VM price. Example: USD
    """

    USD = "USD"

class BlockedTransformers(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum for all classification models supported by AutoML.
    """

    #: Target encoding for text data.
    TEXT_TARGET_ENCODER = "TextTargetEncoder"
    #: Ohe hot encoding creates a binary feature transformation.
    ONE_HOT_ENCODER = "OneHotEncoder"
    #: Target encoding for categorical data.
    CAT_TARGET_ENCODER = "CatTargetEncoder"
    #: Tf-Idf stands for, term-frequency times inverse document-frequency. This is a common term
    #: weighting scheme for identifying information from documents.
    TF_IDF = "TfIdf"
    #: Weight of Evidence encoding is a technique used to encode categorical variables. It uses the
    #: natural log of the P(1)/P(0) to create weights.
    WO_E_TARGET_ENCODER = "WoETargetEncoder"
    #: Label encoder converts labels/categorical variables in a numerical form.
    LABEL_ENCODER = "LabelEncoder"
    #: Word embedding helps represents words or phrases as a vector, or a series of numbers.
    WORD_EMBEDDING = "WordEmbedding"
    #: Naive Bayes is a classified that is used for classification of discrete features that are
    #: categorically distributed.
    NAIVE_BAYES = "NaiveBayes"
    #: Count Vectorizer converts a collection of text documents to a matrix of token counts.
    COUNT_VECTORIZER = "CountVectorizer"
    #: Hashing One Hot Encoder can turn categorical variables into a limited number of new features.
    #: This is often used for high-cardinality categorical features.
    HASH_ONE_HOT_ENCODER = "HashOneHotEncoder"

class Caching(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Caching type of Data Disk.
    """

    NONE = "None"
    READ_ONLY = "ReadOnly"
    READ_WRITE = "ReadWrite"

class CategoricalDataDriftMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: The Jensen Shannon Distance (JSD) metric.
    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    #: The Population Stability Index (PSI) metric.
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    #: The Pearsons Chi Squared Test metric.
    PEARSONS_CHI_SQUARED_TEST = "PearsonsChiSquaredTest"

class CategoricalDataQualityMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Calculates the rate of null values.
    NULL_VALUE_RATE = "NullValueRate"
    #: Calculates the rate of data type errors.
    DATA_TYPE_ERROR_RATE = "DataTypeErrorRate"
    #: Calculates the rate values are out of bounds.
    OUT_OF_BOUNDS_RATE = "OutOfBoundsRate"

class CategoricalPredictionDriftMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: The Jensen Shannon Distance (JSD) metric.
    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    #: The Population Stability Index (PSI) metric.
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    #: The Pearsons Chi Squared Test metric.
    PEARSONS_CHI_SQUARED_TEST = "PearsonsChiSquaredTest"

class ClassificationModels(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum for all classification models supported by AutoML.
    """

    #: Logistic regression is a fundamental classification technique.
    #: It belongs to the group of linear classifiers and is somewhat similar to polynomial and linear
    #: regression.
    #: Logistic regression is fast and relatively uncomplicated, and it's convenient for you to
    #: interpret the results.
    #: Although it's essentially a method for binary classification, it can also be applied to
    #: multiclass problems.
    LOGISTIC_REGRESSION = "LogisticRegression"
    #: SGD: Stochastic gradient descent is an optimization algorithm often used in machine learning
    #: applications
    #: to find the model parameters that correspond to the best fit between predicted and actual
    #: outputs.
    SGD = "SGD"
    #: The multinomial Naive Bayes classifier is suitable for classification with discrete features
    #: (e.g., word counts for text classification).
    #: The multinomial distribution normally requires integer feature counts. However, in practice,
    #: fractional counts such as tf-idf may also work.
    MULTINOMIAL_NAIVE_BAYES = "MultinomialNaiveBayes"
    #: Naive Bayes classifier for multivariate Bernoulli models.
    BERNOULLI_NAIVE_BAYES = "BernoulliNaiveBayes"
    #: A support vector machine (SVM) is a supervised machine learning model that uses classification
    #: algorithms for two-group classification problems.
    #: After giving an SVM model sets of labeled training data for each category, they're able to
    #: categorize new text.
    SVM = "SVM"
    #: A support vector machine (SVM) is a supervised machine learning model that uses classification
    #: algorithms for two-group classification problems.
    #: After giving an SVM model sets of labeled training data for each category, they're able to
    #: categorize new text.
    #: Linear SVM performs best when input data is linear, i.e., data can be easily classified by
    #: drawing the straight line between classified values on a plotted graph.
    LINEAR_SVM = "LinearSVM"
    #: K-nearest neighbors (KNN) algorithm uses 'feature similarity' to predict the values of new
    #: datapoints
    #: which further means that the new data point will be assigned a value based on how closely it
    #: matches the points in the training set.
    KNN = "KNN"
    #: Decision Trees are a non-parametric supervised learning method used for both classification and
    #: regression tasks.
    #: The goal is to create a model that predicts the value of a target variable by learning simple
    #: decision rules inferred from the data features.
    DECISION_TREE = "DecisionTree"
    #: Random forest is a supervised learning algorithm.
    #: The "forest"\\   it builds, is an ensemble of decision trees, usually trained with the
    #: “bagging”\\   method.
    #: The general idea of the bagging method is that a combination of learning models increases the
    #: overall result.
    RANDOM_FOREST = "RandomForest"
    #: Extreme Trees is an ensemble machine learning algorithm that combines the predictions from many
    #: decision trees. It is related to the widely used random forest algorithm.
    EXTREME_RANDOM_TREES = "ExtremeRandomTrees"
    #: LightGBM is a gradient boosting framework that uses tree based learning algorithms.
    LIGHT_GBM = "LightGBM"
    #: The technique of transiting week learners into a strong learner is called Boosting. The
    #: gradient boosting algorithm process works on this theory of execution.
    GRADIENT_BOOSTING = "GradientBoosting"
    #: XGBoost: Extreme Gradient Boosting Algorithm. This algorithm is used for structured data where
    #: target column values can be divided into distinct class values.
    XG_BOOST_CLASSIFIER = "XGBoostClassifier"

class ClassificationMultilabelPrimaryMetrics(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Primary metrics for classification multilabel tasks.
    """

    #: AUC is the Area under the curve.
    #: This metric represents arithmetic mean of the score for each class,
    #: weighted by the number of true instances in each class.
    AUC_WEIGHTED = "AUCWeighted"
    #: Accuracy is the ratio of predictions that exactly match the true class labels.
    ACCURACY = "Accuracy"
    #: Normalized macro recall is recall macro-averaged and normalized, so that random
    #: performance has a score of 0, and perfect performance has a score of 1.
    NORM_MACRO_RECALL = "NormMacroRecall"
    #: The arithmetic mean of the average precision score for each class, weighted by
    #: the number of true instances in each class.
    AVERAGE_PRECISION_SCORE_WEIGHTED = "AveragePrecisionScoreWeighted"
    #: The arithmetic mean of precision for each class, weighted by number of true instances in each
    #: class.
    PRECISION_SCORE_WEIGHTED = "PrecisionScoreWeighted"
    #: Intersection Over Union. Intersection of predictions divided by union of predictions.
    IOU = "IOU"

class ClassificationPrimaryMetrics(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Primary metrics for classification tasks.
    """

    #: AUC is the Area under the curve.
    #: This metric represents arithmetic mean of the score for each class,
    #: weighted by the number of true instances in each class.
    AUC_WEIGHTED = "AUCWeighted"
    #: Accuracy is the ratio of predictions that exactly match the true class labels.
    ACCURACY = "Accuracy"
    #: Normalized macro recall is recall macro-averaged and normalized, so that random
    #: performance has a score of 0, and perfect performance has a score of 1.
    NORM_MACRO_RECALL = "NormMacroRecall"
    #: The arithmetic mean of the average precision score for each class, weighted by
    #: the number of true instances in each class.
    AVERAGE_PRECISION_SCORE_WEIGHTED = "AveragePrecisionScoreWeighted"
    #: The arithmetic mean of precision for each class, weighted by number of true instances in each
    #: class.
    PRECISION_SCORE_WEIGHTED = "PrecisionScoreWeighted"

class ClusterPurpose(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Intended usage of the cluster
    """

    FAST_PROD = "FastProd"
    DENSE_PROD = "DenseProd"
    DEV_TEST = "DevTest"

class ComputeInstanceAuthorizationType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The Compute Instance Authorization type. Available values are personal (default).
    """

    PERSONAL = "personal"

class ComputeInstanceState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Current state of an ComputeInstance.
    """

    CREATING = "Creating"
    CREATE_FAILED = "CreateFailed"
    DELETING = "Deleting"
    RUNNING = "Running"
    RESTARTING = "Restarting"
    RESIZING = "Resizing"
    JOB_RUNNING = "JobRunning"
    SETTING_UP = "SettingUp"
    SETUP_FAILED = "SetupFailed"
    STARTING = "Starting"
    STOPPED = "Stopped"
    STOPPING = "Stopping"
    USER_SETTING_UP = "UserSettingUp"
    USER_SETUP_FAILED = "UserSetupFailed"
    UNKNOWN = "Unknown"
    UNUSABLE = "Unusable"

class ComputePowerAction(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """[Required] The compute power action.
    """

    START = "Start"
    STOP = "Stop"

class ComputeRecurrenceFrequency(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to describe the frequency of a compute recurrence schedule
    """

    #: Minute frequency.
    MINUTE = "Minute"
    #: Hour frequency.
    HOUR = "Hour"
    #: Day frequency.
    DAY = "Day"
    #: Week frequency.
    WEEK = "Week"
    #: Month frequency.
    MONTH = "Month"

class ComputeTriggerType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    RECURRENCE = "Recurrence"
    CRON = "Cron"

class ComputeType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The type of compute
    """

    AKS = "AKS"
    KUBERNETES = "Kubernetes"
    AML_COMPUTE = "AmlCompute"
    COMPUTE_INSTANCE = "ComputeInstance"
    DATA_FACTORY = "DataFactory"
    VIRTUAL_MACHINE = "VirtualMachine"
    HD_INSIGHT = "HDInsight"
    DATABRICKS = "Databricks"
    DATA_LAKE_ANALYTICS = "DataLakeAnalytics"
    SYNAPSE_SPARK = "SynapseSpark"

class ComputeWeekDay(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum of weekday
    """

    #: Monday weekday.
    MONDAY = "Monday"
    #: Tuesday weekday.
    TUESDAY = "Tuesday"
    #: Wednesday weekday.
    WEDNESDAY = "Wednesday"
    #: Thursday weekday.
    THURSDAY = "Thursday"
    #: Friday weekday.
    FRIDAY = "Friday"
    #: Saturday weekday.
    SATURDAY = "Saturday"
    #: Sunday weekday.
    SUNDAY = "Sunday"

class ConnectionAuthType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Authentication type of the connection target
    """

    PAT = "PAT"
    MANAGED_IDENTITY = "ManagedIdentity"
    USERNAME_PASSWORD = "UsernamePassword"
    NONE = "None"
    SAS = "SAS"
    ACCOUNT_KEY = "AccountKey"
    SERVICE_PRINCIPAL = "ServicePrincipal"
    ACCESS_KEY = "AccessKey"
    API_KEY = "ApiKey"
    CUSTOM_KEYS = "CustomKeys"
    O_AUTH2 = "OAuth2"
    AAD = "AAD"

class ConnectionCategory(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Category of the connection
    """

    PYTHON_FEED = "PythonFeed"
    CONTAINER_REGISTRY = "ContainerRegistry"
    GIT = "Git"
    S3 = "S3"
    SNOWFLAKE = "Snowflake"
    AZURE_SQL_DB = "AzureSqlDb"
    AZURE_SYNAPSE_ANALYTICS = "AzureSynapseAnalytics"
    AZURE_MY_SQL_DB = "AzureMySqlDb"
    AZURE_POSTGRES_DB = "AzurePostgresDb"
    ADLS_GEN2 = "ADLSGen2"
    REDIS = "Redis"
    API_KEY = "ApiKey"
    AZURE_OPEN_AI = "AzureOpenAI"
    AI_SERVICES = "AIServices"
    COGNITIVE_SEARCH = "CognitiveSearch"
    COGNITIVE_SERVICE = "CognitiveService"
    CUSTOM_KEYS = "CustomKeys"
    AZURE_BLOB = "AzureBlob"
    AZURE_ONE_LAKE = "AzureOneLake"
    COSMOS_DB = "CosmosDb"
    COSMOS_DB_MONGO_DB_API = "CosmosDbMongoDbApi"
    AZURE_DATA_EXPLORER = "AzureDataExplorer"
    AZURE_MARIA_DB = "AzureMariaDb"
    AZURE_DATABRICKS_DELTA_LAKE = "AzureDatabricksDeltaLake"
    AZURE_SQL_MI = "AzureSqlMi"
    AZURE_TABLE_STORAGE = "AzureTableStorage"
    AMAZON_RDS_FOR_ORACLE = "AmazonRdsForOracle"
    AMAZON_RDS_FOR_SQL_SERVER = "AmazonRdsForSqlServer"
    AMAZON_REDSHIFT = "AmazonRedshift"
    DB2 = "Db2"
    DRILL = "Drill"
    GOOGLE_BIG_QUERY = "GoogleBigQuery"
    GREENPLUM = "Greenplum"
    HBASE = "Hbase"
    HIVE = "Hive"
    IMPALA = "Impala"
    INFORMIX = "Informix"
    MARIA_DB = "MariaDb"
    MICROSOFT_ACCESS = "MicrosoftAccess"
    MY_SQL = "MySql"
    NETEZZA = "Netezza"
    ORACLE = "Oracle"
    PHOENIX = "Phoenix"
    POSTGRE_SQL = "PostgreSql"
    PRESTO = "Presto"
    SAP_OPEN_HUB = "SapOpenHub"
    SAP_BW = "SapBw"
    SAP_HANA = "SapHana"
    SAP_TABLE = "SapTable"
    SPARK = "Spark"
    SQL_SERVER = "SqlServer"
    SYBASE = "Sybase"
    TERADATA = "Teradata"
    VERTICA = "Vertica"
    CASSANDRA = "Cassandra"
    COUCHBASE = "Couchbase"
    MONGO_DB_V2 = "MongoDbV2"
    MONGO_DB_ATLAS = "MongoDbAtlas"
    AMAZON_S3_COMPATIBLE = "AmazonS3Compatible"
    FILE_SERVER = "FileServer"
    FTP_SERVER = "FtpServer"
    GOOGLE_CLOUD_STORAGE = "GoogleCloudStorage"
    HDFS = "Hdfs"
    ORACLE_CLOUD_STORAGE = "OracleCloudStorage"
    SFTP = "Sftp"
    GENERIC_HTTP = "GenericHttp"
    O_DATA_REST = "ODataRest"
    ODBC = "Odbc"
    GENERIC_REST = "GenericRest"
    AMAZON_MWS = "AmazonMws"
    CONCUR = "Concur"
    DYNAMICS = "Dynamics"
    DYNAMICS_AX = "DynamicsAx"
    DYNAMICS_CRM = "DynamicsCrm"
    GOOGLE_AD_WORDS = "GoogleAdWords"
    HUBSPOT = "Hubspot"
    JIRA = "Jira"
    MAGENTO = "Magento"
    MARKETO = "Marketo"
    OFFICE365 = "Office365"
    ELOQUA = "Eloqua"
    RESPONSYS = "Responsys"
    ORACLE_SERVICE_CLOUD = "OracleServiceCloud"
    PAY_PAL = "PayPal"
    QUICK_BOOKS = "QuickBooks"
    SALESFORCE = "Salesforce"
    SALESFORCE_SERVICE_CLOUD = "SalesforceServiceCloud"
    SALESFORCE_MARKETING_CLOUD = "SalesforceMarketingCloud"
    SAP_CLOUD_FOR_CUSTOMER = "SapCloudForCustomer"
    SAP_ECC = "SapEcc"
    SERVICE_NOW = "ServiceNow"
    SHARE_POINT_ONLINE_LIST = "SharePointOnlineList"
    SHOPIFY = "Shopify"
    SQUARE = "Square"
    WEB_TABLE = "WebTable"
    XERO = "Xero"
    ZOHO = "Zoho"
    GENERIC_CONTAINER_REGISTRY = "GenericContainerRegistry"
    OPEN_AI = "OpenAI"
    SERP = "Serp"
    BING_LLM_SEARCH = "BingLLMSearch"
    SERVERLESS = "Serverless"

class ConnectionGroup(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Group based on connection category
    """

    AZURE = "Azure"
    AZURE_AI = "AzureAI"
    DATABASE = "Database"
    NO_SQL = "NoSQL"
    FILE = "File"
    GENERIC_PROTOCOL = "GenericProtocol"
    SERVICES_AND_APPS = "ServicesAndApps"

class ContainerType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    STORAGE_INITIALIZER = "StorageInitializer"
    INFERENCE_SERVER = "InferenceServer"

class ContentSafetyStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Specifies the status of content safety.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class CreatedByType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The type of identity that created the resource.
    """

    USER = "User"
    APPLICATION = "Application"
    MANAGED_IDENTITY = "ManagedIdentity"
    KEY = "Key"

class CredentialsType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the datastore credentials type.
    """

    ACCOUNT_KEY = "AccountKey"
    CERTIFICATE = "Certificate"
    NONE = "None"
    SAS = "Sas"
    SERVICE_PRINCIPAL = "ServicePrincipal"

class DataAvailabilityStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    NONE = "None"
    PENDING = "Pending"
    INCOMPLETE = "Incomplete"
    COMPLETE = "Complete"

class DataCollectionMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class DataReferenceCredentialType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the DataReference credentials type.
    """

    SAS = "SAS"
    DOCKER_CREDENTIALS = "DockerCredentials"
    MANAGED_IDENTITY = "ManagedIdentity"
    NO_CREDENTIALS = "NoCredentials"

class DatastoreType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the datastore contents type.
    """

    AZURE_BLOB = "AzureBlob"
    AZURE_DATA_LAKE_GEN1 = "AzureDataLakeGen1"
    AZURE_DATA_LAKE_GEN2 = "AzureDataLakeGen2"
    AZURE_FILE = "AzureFile"
    ONE_LAKE = "OneLake"

class DataType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the type of data.
    """

    URI_FILE = "uri_file"
    URI_FOLDER = "uri_folder"
    MLTABLE = "mltable"

class DefaultResourceProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    NOT_STARTED = "NotStarted"
    FAILED = "Failed"
    CREATING = "Creating"
    UPDATING = "Updating"
    SUCCEEDED = "Succeeded"
    DELETING = "Deleting"
    ACCEPTED = "Accepted"
    CANCELED = "Canceled"
    SCALING = "Scaling"
    DISABLED = "Disabled"

class DeploymentModelVersionUpgradeOption(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Deployment model version upgrade option.
    """

    ONCE_NEW_DEFAULT_VERSION_AVAILABLE = "OnceNewDefaultVersionAvailable"
    ONCE_CURRENT_VERSION_EXPIRED = "OnceCurrentVersionExpired"
    NO_AUTO_UPGRADE = "NoAutoUpgrade"

class DeploymentProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Possible values for DeploymentProvisioningState.
    """

    CREATING = "Creating"
    DELETING = "Deleting"
    SCALING = "Scaling"
    UPDATING = "Updating"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"

class DiagnoseResultLevel(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Level of workspace setup error
    """

    WARNING = "Warning"
    ERROR = "Error"
    INFORMATION = "Information"

class DistributionType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the job distribution type.
    """

    PY_TORCH = "PyTorch"
    TENSOR_FLOW = "TensorFlow"
    MPI = "Mpi"

class EarlyTerminationPolicyType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    BANDIT = "Bandit"
    MEDIAN_STOPPING = "MedianStopping"
    TRUNCATION_SELECTION = "TruncationSelection"

class EgressPublicNetworkAccessType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine whether PublicNetworkAccess is Enabled or Disabled for egress of a
    deployment.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class EmailNotificationEnableType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the email notification type.
    """

    JOB_COMPLETED = "JobCompleted"
    JOB_FAILED = "JobFailed"
    JOB_CANCELLED = "JobCancelled"

class EncryptionStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Indicates whether or not the encryption is enabled for the workspace.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class EndpointAuthMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine endpoint authentication mode.
    """

    AML_TOKEN = "AMLToken"
    KEY = "Key"
    AAD_TOKEN = "AADToken"

class EndpointComputeType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine endpoint compute type.
    """

    MANAGED = "Managed"
    KUBERNETES = "Kubernetes"
    AZURE_ML_COMPUTE = "AzureMLCompute"

class EndpointProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """State of endpoint provisioning.
    """

    CREATING = "Creating"
    DELETING = "Deleting"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    UPDATING = "Updating"
    CANCELED = "Canceled"

class EndpointServiceConnectionStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Connection status of the service consumer with the service provider
    """

    APPROVED = "Approved"
    PENDING = "Pending"
    REJECTED = "Rejected"
    DISCONNECTED = "Disconnected"
    TIMEOUT = "Timeout"

class EndpointType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of the endpoint.
    """

    AZURE_OPEN_AI = "Azure.OpenAI"
    AZURE_SPEECH = "Azure.Speech"
    AZURE_CONTENT_SAFETY = "Azure.ContentSafety"
    AZURE_LLAMA = "Azure.Llama"
    MANAGED_ONLINE_ENDPOINT = "managedOnlineEndpoint"
    SERVERLESS_ENDPOINT = "serverlessEndpoint"

class EnvironmentType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Environment type is either user created or curated by Azure ML service
    """

    CURATED = "Curated"
    USER_CREATED = "UserCreated"

class EnvironmentVariableType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of the Environment Variable. Possible values are: local - For local variable
    """

    LOCAL = "local"

class FeatureAttributionMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: The Normalized Discounted Cumulative Gain metric.
    NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN = "NormalizedDiscountedCumulativeGain"

class FeatureDataType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    STRING = "String"
    INTEGER = "Integer"
    LONG = "Long"
    FLOAT = "Float"
    DOUBLE = "Double"
    BINARY = "Binary"
    DATETIME = "Datetime"
    BOOLEAN = "Boolean"

class FeatureImportanceMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The mode of operation for computing feature importance.
    """

    #: Disables computing feature importance within a signal.
    DISABLED = "Disabled"
    #: Enables computing feature importance within a signal.
    ENABLED = "Enabled"

class FeatureLags(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Flag for generating lags for the numeric features.
    """

    #: No feature lags generated.
    NONE = "None"
    #: System auto-generates feature lags.
    AUTO = "Auto"

class FeaturizationMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Featurization mode - determines data featurization mode.
    """

    #: Auto mode, system performs featurization without any custom featurization inputs.
    AUTO = "Auto"
    #: Custom featurization.
    CUSTOM = "Custom"
    #: Featurization off. 'Forecasting' task cannot use this value.
    OFF = "Off"

class ForecastHorizonMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine forecast horizon selection mode.
    """

    #: Forecast horizon to be determined automatically.
    AUTO = "Auto"
    #: Use the custom forecast horizon.
    CUSTOM = "Custom"

class ForecastingModels(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum for all forecasting models supported by AutoML.
    """

    #: Auto-Autoregressive Integrated Moving Average (ARIMA) model uses time-series data and
    #: statistical analysis to interpret the data and make future predictions.
    #: This model aims to explain data by using time series data on its past values and uses linear
    #: regression to make predictions.
    AUTO_ARIMA = "AutoArima"
    #: Prophet is a procedure for forecasting time series data based on an additive model where
    #: non-linear trends are fit with yearly, weekly, and daily seasonality, plus holiday effects.
    #: It works best with time series that have strong seasonal effects and several seasons of
    #: historical data. Prophet is robust to missing data and shifts in the trend, and typically
    #: handles outliers well.
    PROPHET = "Prophet"
    #: The Naive forecasting model makes predictions by carrying forward the latest target value for
    #: each time-series in the training data.
    NAIVE = "Naive"
    #: The Seasonal Naive forecasting model makes predictions by carrying forward the latest season of
    #: target values for each time-series in the training data.
    SEASONAL_NAIVE = "SeasonalNaive"
    #: The Average forecasting model makes predictions by carrying forward the average of the target
    #: values for each time-series in the training data.
    AVERAGE = "Average"
    #: The Seasonal Average forecasting model makes predictions by carrying forward the average value
    #: of the latest season of data for each time-series in the training data.
    SEASONAL_AVERAGE = "SeasonalAverage"
    #: Exponential smoothing is a time series forecasting method for univariate data that can be
    #: extended to support data with a systematic trend or seasonal component.
    EXPONENTIAL_SMOOTHING = "ExponentialSmoothing"
    #: An Autoregressive Integrated Moving Average with Explanatory Variable (ARIMAX) model can be
    #: viewed as a multiple regression model with one or more autoregressive (AR) terms and/or one or
    #: more moving average (MA) terms.
    #: This method is suitable for forecasting when data is stationary/non stationary, and
    #: multivariate with any type of data pattern, i.e., level/trend /seasonality/cyclicity.
    ARIMAX = "Arimax"
    #: TCNForecaster: Temporal Convolutional Networks Forecaster. //TODO: Ask forecasting team for
    #: brief intro.
    TCN_FORECASTER = "TCNForecaster"
    #: Elastic net is a popular type of regularized linear regression that combines two popular
    #: penalties, specifically the L1 and L2 penalty functions.
    ELASTIC_NET = "ElasticNet"
    #: The technique of transiting week learners into a strong learner is called Boosting. The
    #: gradient boosting algorithm process works on this theory of execution.
    GRADIENT_BOOSTING = "GradientBoosting"
    #: Decision Trees are a non-parametric supervised learning method used for both classification and
    #: regression tasks.
    #: The goal is to create a model that predicts the value of a target variable by learning simple
    #: decision rules inferred from the data features.
    DECISION_TREE = "DecisionTree"
    #: K-nearest neighbors (KNN) algorithm uses 'feature similarity' to predict the values of new
    #: datapoints
    #: which further means that the new data point will be assigned a value based on how closely it
    #: matches the points in the training set.
    KNN = "KNN"
    #: Lasso model fit with Least Angle Regression a.k.a. Lars. It is a Linear Model trained with an
    #: L1 prior as regularizer.
    LASSO_LARS = "LassoLars"
    #: SGD: Stochastic gradient descent is an optimization algorithm often used in machine learning
    #: applications
    #: to find the model parameters that correspond to the best fit between predicted and actual
    #: outputs.
    #: It's an inexact but powerful technique.
    SGD = "SGD"
    #: Random forest is a supervised learning algorithm.
    #: The "forest"\\   it builds, is an ensemble of decision trees, usually trained with the
    #: “bagging”\\   method.
    #: The general idea of the bagging method is that a combination of learning models increases the
    #: overall result.
    RANDOM_FOREST = "RandomForest"
    #: Extreme Trees is an ensemble machine learning algorithm that combines the predictions from many
    #: decision trees. It is related to the widely used random forest algorithm.
    EXTREME_RANDOM_TREES = "ExtremeRandomTrees"
    #: LightGBM is a gradient boosting framework that uses tree based learning algorithms.
    LIGHT_GBM = "LightGBM"
    #: XGBoostRegressor: Extreme Gradient Boosting Regressor is a supervised machine learning model
    #: using ensemble of base learners.
    XG_BOOST_REGRESSOR = "XGBoostRegressor"

class ForecastingPrimaryMetrics(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Primary metrics for Forecasting task.
    """

    #: The Spearman's rank coefficient of correlation is a non-parametric measure of rank correlation.
    SPEARMAN_CORRELATION = "SpearmanCorrelation"
    #: The Normalized Root Mean Squared Error (NRMSE) the RMSE facilitates the comparison between
    #: models with different scales.
    NORMALIZED_ROOT_MEAN_SQUARED_ERROR = "NormalizedRootMeanSquaredError"
    #: The R2 score is one of the performance evaluation measures for forecasting-based machine
    #: learning models.
    R2_SCORE = "R2Score"
    #: The Normalized Mean Absolute Error (NMAE) is a validation metric to compare the Mean Absolute
    #: Error (MAE) of (time) series with different scales.
    NORMALIZED_MEAN_ABSOLUTE_ERROR = "NormalizedMeanAbsoluteError"

class Goal(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Defines supported metric goals for hyperparameter tuning
    """

    MINIMIZE = "Minimize"
    MAXIMIZE = "Maximize"

class IdentityConfigurationType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine identity framework.
    """

    MANAGED = "Managed"
    AML_TOKEN = "AMLToken"
    USER_IDENTITY = "UserIdentity"

class ImageType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of the image. Possible values are: docker - For docker images. azureml - For AzureML
    Environment images (custom and curated)
    """

    DOCKER = "docker"
    AZUREML = "azureml"

class InputDeliveryMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the input data delivery mode.
    """

    READ_ONLY_MOUNT = "ReadOnlyMount"
    READ_WRITE_MOUNT = "ReadWriteMount"
    DOWNLOAD = "Download"
    DIRECT = "Direct"
    EVAL_MOUNT = "EvalMount"
    EVAL_DOWNLOAD = "EvalDownload"

class InstanceSegmentationPrimaryMetrics(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Primary metrics for InstanceSegmentation tasks.
    """

    #: Mean Average Precision (MAP) is the average of AP (Average Precision).
    #: AP is calculated for each class and averaged to get the MAP.
    MEAN_AVERAGE_PRECISION = "MeanAveragePrecision"

class IsolationMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Isolation mode for the managed network of a machine learning workspace.
    """

    DISABLED = "Disabled"
    ALLOW_INTERNET_OUTBOUND = "AllowInternetOutbound"
    ALLOW_ONLY_APPROVED_OUTBOUND = "AllowOnlyApprovedOutbound"

class JobInputType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the Job Input Type.
    """

    LITERAL = "literal"
    URI_FILE = "uri_file"
    URI_FOLDER = "uri_folder"
    MLTABLE = "mltable"
    CUSTOM_MODEL = "custom_model"
    MLFLOW_MODEL = "mlflow_model"
    TRITON_MODEL = "triton_model"

class JobLimitsType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    COMMAND = "Command"
    SWEEP = "Sweep"

class JobOutputType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the Job Output Type.
    """

    URI_FILE = "uri_file"
    URI_FOLDER = "uri_folder"
    MLTABLE = "mltable"
    CUSTOM_MODEL = "custom_model"
    MLFLOW_MODEL = "mlflow_model"
    TRITON_MODEL = "triton_model"

class JobStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The status of a job.
    """

    #: Run hasn't started yet.
    NOT_STARTED = "NotStarted"
    #: Run has started. The user has a run ID.
    STARTING = "Starting"
    #: (Not used currently) It will be used if ES is creating the compute target.
    PROVISIONING = "Provisioning"
    #: The run environment is being prepared.
    PREPARING = "Preparing"
    #: The job is queued in the compute target. For example, in BatchAI the job is in queued state,
    #: while waiting for all required nodes to be ready.
    QUEUED = "Queued"
    #: The job started to run in the compute target.
    RUNNING = "Running"
    #: Job is completed in the target. It is in output collection state now.
    FINALIZING = "Finalizing"
    #: Cancellation has been requested for the job.
    CANCEL_REQUESTED = "CancelRequested"
    #: Job completed successfully. This reflects that both the job itself and output collection states
    #: completed successfully.
    COMPLETED = "Completed"
    #: Job failed.
    FAILED = "Failed"
    #: Following cancellation request, the job is now successfully canceled.
    CANCELED = "Canceled"
    #: When heartbeat is enabled, if the run isn't updating any information to RunHistory then the run
    #: goes to NotResponding state.
    #: NotResponding is the only state that is exempt from strict transition orders. A run can go from
    #: NotResponding to any of the previous states.
    NOT_RESPONDING = "NotResponding"
    #: The job is paused by users. Some adjustment to labeling jobs can be made only in paused state.
    PAUSED = "Paused"
    #: Default job status if not mapped to all other statuses.
    UNKNOWN = "Unknown"

class JobTier(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the job tier.
    """

    NULL = "Null"
    SPOT = "Spot"
    BASIC = "Basic"
    STANDARD = "Standard"
    PREMIUM = "Premium"

class JobType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the type of job.
    """

    AUTO_ML = "AutoML"
    COMMAND = "Command"
    SWEEP = "Sweep"
    PIPELINE = "Pipeline"
    SPARK = "Spark"

class KeyType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    PRIMARY = "Primary"
    SECONDARY = "Secondary"

class LearningRateScheduler(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Learning rate scheduler enum.
    """

    #: No learning rate scheduler selected.
    NONE = "None"
    #: Cosine Annealing With Warmup.
    WARMUP_COSINE = "WarmupCosine"
    #: Step learning rate scheduler.
    STEP = "Step"

class ListViewType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    ACTIVE_ONLY = "ActiveOnly"
    ARCHIVED_ONLY = "ArchivedOnly"
    ALL = "All"

class LoadBalancerType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Load Balancer Type
    """

    PUBLIC_IP = "PublicIp"
    INTERNAL_LOAD_BALANCER = "InternalLoadBalancer"

class LogVerbosity(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum for setting log verbosity.
    """

    #: No logs emitted.
    NOT_SET = "NotSet"
    #: Debug and above log statements logged.
    DEBUG = "Debug"
    #: Info and above log statements logged.
    INFO = "Info"
    #: Warning and above log statements logged.
    WARNING = "Warning"
    #: Error and above log statements logged.
    ERROR = "Error"
    #: Only critical statements logged.
    CRITICAL = "Critical"

class ManagedNetworkStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Status for the managed network of a machine learning workspace.
    """

    INACTIVE = "Inactive"
    ACTIVE = "Active"

class ManagedPERequirement(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    REQUIRED = "Required"
    NOT_REQUIRED = "NotRequired"
    NOT_APPLICABLE = "NotApplicable"

class ManagedPEStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    INACTIVE = "Inactive"
    ACTIVE = "Active"
    NOT_APPLICABLE = "NotApplicable"

class ManagedServiceIdentityType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of managed service identity (where both SystemAssigned and UserAssigned types are
    allowed).
    """

    NONE = "None"
    SYSTEM_ASSIGNED = "SystemAssigned"
    USER_ASSIGNED = "UserAssigned"
    SYSTEM_ASSIGNED_USER_ASSIGNED = "SystemAssigned,UserAssigned"

class MarketplaceSubscriptionProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: MarketplaceSubscription is being created.
    CREATING = "Creating"
    #: MarketplaceSubscription is being deleted.
    DELETING = "Deleting"
    #: MarketplaceSubscription is successfully provisioned.
    SUCCEEDED = "Succeeded"
    #: MarketplaceSubscription provisioning failed.
    FAILED = "Failed"
    #: MarketplaceSubscription is being updated.
    UPDATING = "Updating"
    CANCELED = "Canceled"

class MarketplaceSubscriptionStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: The customer can now use the Marketplace Subscription's
    #: model and will be billed.
    SUBSCRIBED = "Subscribed"
    #: The customer could not be billed for the Marketplace Subscription.
    #: The customer will not be able to access the model.
    SUSPENDED = "Suspended"
    #: Marketplace Subscriptions reach this state in response to an explicit customer or CSP action.
    #: A Marketplace Subscription can also be canceled implicitly, as a result of nonpayment of dues,
    #: after being in the Suspended state for some time.
    UNSUBSCRIBED = "Unsubscribed"

class MaterializationStoreType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    NONE = "None"
    ONLINE = "Online"
    OFFLINE = "Offline"
    ONLINE_AND_OFFLINE = "OnlineAndOffline"

class MlflowAutologger(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Indicates whether mlflow autologger is enabled for notebooks.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class ModelLifecycleStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Model lifecycle status.
    """

    GENERALLY_AVAILABLE = "GenerallyAvailable"
    PREVIEW = "Preview"

class ModelSize(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Image model size.
    """

    #: No value selected.
    NONE = "None"
    #: Small size.
    SMALL = "Small"
    #: Medium size.
    MEDIUM = "Medium"
    #: Large size.
    LARGE = "Large"
    #: Extra large size.
    EXTRA_LARGE = "ExtraLarge"

class ModelTaskType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Model task type enum.
    """

    CLASSIFICATION = "Classification"
    REGRESSION = "Regression"

class MonitorComputeIdentityType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Monitor compute identity type enum.
    """

    #: Authenticates through user's AML token.
    AML_TOKEN = "AmlToken"
    #: Authenticates through a user-provided managed identity.
    MANAGED_IDENTITY = "ManagedIdentity"

class MonitorComputeType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Monitor compute type enum.
    """

    #: Serverless Spark compute.
    SERVERLESS_SPARK = "ServerlessSpark"

class MonitoringFeatureDataType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Used for features of numerical data type.
    NUMERICAL = "Numerical"
    #: Used for features of categorical data type.
    CATEGORICAL = "Categorical"

class MonitoringFeatureFilterType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Includes all features.
    ALL_FEATURES = "AllFeatures"
    #: Only includes the top contributing features, measured by feature attribution.
    TOP_N_BY_ATTRIBUTION = "TopNByAttribution"
    #: Includes a user-defined subset of features.
    FEATURE_SUBSET = "FeatureSubset"

class MonitoringInputDataType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Monitoring input data type enum.
    """

    #: An input data with a fixed window size.
    STATIC = "Static"
    #: An input data which rolls relatively to the monitor's current run time.
    ROLLING = "Rolling"
    #: An input data with tabular format which doesn't require preprocessing.
    FIXED = "Fixed"

class MonitoringNotificationType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Enables email notifications through AML notifications.
    AML_NOTIFICATION = "AmlNotification"

class MonitoringSignalType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Tracks model input data distribution change, comparing against training data or past production
    #: data.
    DATA_DRIFT = "DataDrift"
    #: Tracks prediction result data distribution change, comparing against validation/test label data
    #: or past production data.
    PREDICTION_DRIFT = "PredictionDrift"
    #: Tracks model input data integrity.
    DATA_QUALITY = "DataQuality"
    #: Tracks feature importance change in production, comparing against feature importance at
    #: training time.
    FEATURE_ATTRIBUTION_DRIFT = "FeatureAttributionDrift"
    #: Tracks a custom signal provided by users.
    CUSTOM = "Custom"

class MountAction(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Mount Action.
    """

    MOUNT = "Mount"
    UNMOUNT = "Unmount"

class MountMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Mount Mode.
    """

    READ_ONLY = "ReadOnly"
    READ_WRITE = "ReadWrite"

class MountState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Mount state.
    """

    MOUNT_REQUESTED = "MountRequested"
    MOUNTED = "Mounted"
    MOUNT_FAILED = "MountFailed"
    UNMOUNT_REQUESTED = "UnmountRequested"
    UNMOUNT_FAILED = "UnmountFailed"
    UNMOUNTED = "Unmounted"

class NCrossValidationsMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Determines how N-Cross validations value is determined.
    """

    #: Determine N-Cross validations value automatically. Supported only for 'Forecasting' AutoML
    #: task.
    AUTO = "Auto"
    #: Use custom N-Cross validations value.
    CUSTOM = "Custom"

class Network(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """network of this container.
    """

    BRIDGE = "Bridge"
    HOST = "Host"

class NodeState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """State of the compute node. Values are idle, running, preparing, unusable, leaving and
    preempted.
    """

    IDLE = "idle"
    RUNNING = "running"
    PREPARING = "preparing"
    UNUSABLE = "unusable"
    LEAVING = "leaving"
    PREEMPTED = "preempted"

class NodesValueType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The enumerated types for the nodes value
    """

    ALL = "All"

class NumericalDataDriftMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: The Jensen Shannon Distance (JSD) metric.
    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    #: The Population Stability Index (PSI) metric.
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    #: The Normalized Wasserstein Distance metric.
    NORMALIZED_WASSERSTEIN_DISTANCE = "NormalizedWassersteinDistance"
    #: The Two Sample Kolmogorov-Smirnov Test (two-sample K–S) metric.
    TWO_SAMPLE_KOLMOGOROV_SMIRNOV_TEST = "TwoSampleKolmogorovSmirnovTest"

class NumericalDataQualityMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Calculates the rate of null values.
    NULL_VALUE_RATE = "NullValueRate"
    #: Calculates the rate of data type errors.
    DATA_TYPE_ERROR_RATE = "DataTypeErrorRate"
    #: Calculates the rate values are out of bounds.
    OUT_OF_BOUNDS_RATE = "OutOfBoundsRate"

class NumericalPredictionDriftMetric(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: The Jensen Shannon Distance (JSD) metric.
    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    #: The Population Stability Index (PSI) metric.
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    #: The Normalized Wasserstein Distance metric.
    NORMALIZED_WASSERSTEIN_DISTANCE = "NormalizedWassersteinDistance"
    #: The Two Sample Kolmogorov-Smirnov Test (two-sample K–S) metric.
    TWO_SAMPLE_KOLMOGOROV_SMIRNOV_TEST = "TwoSampleKolmogorovSmirnovTest"

class ObjectDetectionPrimaryMetrics(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Primary metrics for Image ObjectDetection task.
    """

    #: Mean Average Precision (MAP) is the average of AP (Average Precision).
    #: AP is calculated for each class and averaged to get the MAP.
    MEAN_AVERAGE_PRECISION = "MeanAveragePrecision"

class OneLakeArtifactType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine OneLake artifact type.
    """

    LAKE_HOUSE = "LakeHouse"

class OperatingSystemType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The type of operating system.
    """

    LINUX = "Linux"
    WINDOWS = "Windows"

class OperationName(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Name of the last operation.
    """

    CREATE = "Create"
    START = "Start"
    STOP = "Stop"
    RESTART = "Restart"
    RESIZE = "Resize"
    REIMAGE = "Reimage"
    DELETE = "Delete"

class OperationStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Operation status.
    """

    IN_PROGRESS = "InProgress"
    SUCCEEDED = "Succeeded"
    CREATE_FAILED = "CreateFailed"
    START_FAILED = "StartFailed"
    STOP_FAILED = "StopFailed"
    RESTART_FAILED = "RestartFailed"
    RESIZE_FAILED = "ResizeFailed"
    REIMAGE_FAILED = "ReimageFailed"
    DELETE_FAILED = "DeleteFailed"

class OperationTrigger(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Trigger of operation.
    """

    USER = "User"
    SCHEDULE = "Schedule"
    IDLE_SHUTDOWN = "IdleShutdown"

class OrderString(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    CREATED_AT_DESC = "CreatedAtDesc"
    CREATED_AT_ASC = "CreatedAtAsc"
    UPDATED_AT_DESC = "UpdatedAtDesc"
    UPDATED_AT_ASC = "UpdatedAtAsc"

class Origin(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The intended executor of the operation; as in Resource Based Access Control (RBAC) and audit
    logs UX. Default value is "user,system"
    """

    USER = "user"
    SYSTEM = "system"
    USER_SYSTEM = "user,system"

class OsType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Compute OS Type
    """

    LINUX = "Linux"
    WINDOWS = "Windows"

class OutputDeliveryMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Output data delivery mode enums.
    """

    READ_WRITE_MOUNT = "ReadWriteMount"
    UPLOAD = "Upload"
    DIRECT = "Direct"

class PatchStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The os patching status.
    """

    COMPLETED_WITH_WARNINGS = "CompletedWithWarnings"
    FAILED = "Failed"
    IN_PROGRESS = "InProgress"
    SUCCEEDED = "Succeeded"
    UNKNOWN = "Unknown"

class PendingUploadCredentialType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the PendingUpload credentials type.
    """

    SAS = "SAS"

class PendingUploadType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of storage to use for the pending upload location
    """

    NONE = "None"
    TEMPORARY_BLOB_REFERENCE = "TemporaryBlobReference"

class PrivateEndpointConnectionProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The current provisioning state.
    """

    SUCCEEDED = "Succeeded"
    CREATING = "Creating"
    DELETING = "Deleting"
    FAILED = "Failed"

class Protocol(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Protocol over which communication will happen over this endpoint
    """

    TCP = "tcp"
    UDP = "udp"
    HTTP = "http"

class ProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The provision state of the cluster. Valid values are Unknown, Updating, Provisioning,
    Succeeded, and Failed.
    """

    UNKNOWN = "Unknown"
    UPDATING = "Updating"
    CREATING = "Creating"
    DELETING = "Deleting"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"

class ProvisioningStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The current deployment state of schedule.
    """

    COMPLETED = "Completed"
    PROVISIONING = "Provisioning"
    FAILED = "Failed"

class PublicNetworkAccessType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine whether PublicNetworkAccess is Enabled or Disabled.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class QuotaUnit(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """An enum describing the unit of quota measurement.
    """

    COUNT = "Count"

class RaiPolicyContentSource(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Content source to apply the Content Filters.
    """

    PROMPT = "Prompt"
    COMPLETION = "Completion"

class RaiPolicyMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Content Filters mode.
    """

    DEFAULT = "Default"
    DEFERRED = "Deferred"
    BLOCKING = "Blocking"

class RaiPolicyType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Content Filters policy type.
    """

    USER_MANAGED = "UserManaged"
    SYSTEM_MANAGED = "SystemManaged"

class RandomSamplingAlgorithmRule(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The specific type of random algorithm
    """

    RANDOM = "Random"
    SOBOL = "Sobol"

class RecurrenceFrequency(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to describe the frequency of a recurrence schedule
    """

    #: Minute frequency.
    MINUTE = "Minute"
    #: Hour frequency.
    HOUR = "Hour"
    #: Day frequency.
    DAY = "Day"
    #: Week frequency.
    WEEK = "Week"
    #: Month frequency.
    MONTH = "Month"

class ReferenceType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine which reference method to use for an asset.
    """

    ID = "Id"
    DATA_PATH = "DataPath"
    OUTPUT_PATH = "OutputPath"

class RegressionModels(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum for all Regression models supported by AutoML.
    """

    #: Elastic net is a popular type of regularized linear regression that combines two popular
    #: penalties, specifically the L1 and L2 penalty functions.
    ELASTIC_NET = "ElasticNet"
    #: The technique of transiting week learners into a strong learner is called Boosting. The
    #: gradient boosting algorithm process works on this theory of execution.
    GRADIENT_BOOSTING = "GradientBoosting"
    #: Decision Trees are a non-parametric supervised learning method used for both classification and
    #: regression tasks.
    #: The goal is to create a model that predicts the value of a target variable by learning simple
    #: decision rules inferred from the data features.
    DECISION_TREE = "DecisionTree"
    #: K-nearest neighbors (KNN) algorithm uses 'feature similarity' to predict the values of new
    #: datapoints
    #: which further means that the new data point will be assigned a value based on how closely it
    #: matches the points in the training set.
    KNN = "KNN"
    #: Lasso model fit with Least Angle Regression a.k.a. Lars. It is a Linear Model trained with an
    #: L1 prior as regularizer.
    LASSO_LARS = "LassoLars"
    #: SGD: Stochastic gradient descent is an optimization algorithm often used in machine learning
    #: applications
    #: to find the model parameters that correspond to the best fit between predicted and actual
    #: outputs.
    #: It's an inexact but powerful technique.
    SGD = "SGD"
    #: Random forest is a supervised learning algorithm.
    #: The "forest"\\   it builds, is an ensemble of decision trees, usually trained with the
    #: “bagging”\\   method.
    #: The general idea of the bagging method is that a combination of learning models increases the
    #: overall result.
    RANDOM_FOREST = "RandomForest"
    #: Extreme Trees is an ensemble machine learning algorithm that combines the predictions from many
    #: decision trees. It is related to the widely used random forest algorithm.
    EXTREME_RANDOM_TREES = "ExtremeRandomTrees"
    #: LightGBM is a gradient boosting framework that uses tree based learning algorithms.
    LIGHT_GBM = "LightGBM"
    #: XGBoostRegressor: Extreme Gradient Boosting Regressor is a supervised machine learning model
    #: using ensemble of base learners.
    XG_BOOST_REGRESSOR = "XGBoostRegressor"

class RegressionPrimaryMetrics(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Primary metrics for Regression task.
    """

    #: The Spearman's rank coefficient of correlation is a nonparametric measure of rank correlation.
    SPEARMAN_CORRELATION = "SpearmanCorrelation"
    #: The Normalized Root Mean Squared Error (NRMSE) the RMSE facilitates the comparison between
    #: models with different scales.
    NORMALIZED_ROOT_MEAN_SQUARED_ERROR = "NormalizedRootMeanSquaredError"
    #: The R2 score is one of the performance evaluation measures for forecasting-based machine
    #: learning models.
    R2_SCORE = "R2Score"
    #: The Normalized Mean Absolute Error (NMAE) is a validation metric to compare the Mean Absolute
    #: Error (MAE) of (time) series with different scales.
    NORMALIZED_MEAN_ABSOLUTE_ERROR = "NormalizedMeanAbsoluteError"

class RemoteLoginPortPublicAccess(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """State of the public SSH port. Possible values are: Disabled - Indicates that the public ssh
    port is closed on all nodes of the cluster. Enabled - Indicates that the public ssh port is
    open on all nodes of the cluster. NotSpecified - Indicates that the public ssh port is closed
    on all nodes of the cluster if VNet is defined, else is open all public nodes. It can be
    default only during cluster creation time, after creation it will be either enabled or
    disabled.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"
    NOT_SPECIFIED = "NotSpecified"

class RollingRateType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    YEAR = "Year"
    MONTH = "Month"
    DAY = "Day"
    HOUR = "Hour"
    MINUTE = "Minute"

class RuleAction(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The action enum for networking rule.
    """

    ALLOW = "Allow"
    DENY = "Deny"

class RuleCategory(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Category of a managed network Outbound Rule of a machine learning workspace.
    """

    REQUIRED = "Required"
    RECOMMENDED = "Recommended"
    USER_DEFINED = "UserDefined"
    DEPENDENCY = "Dependency"

class RuleStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of a managed network Outbound Rule of a machine learning workspace.
    """

    INACTIVE = "Inactive"
    ACTIVE = "Active"

class RuleType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of a managed network Outbound Rule of a machine learning workspace.
    """

    FQDN = "FQDN"
    PRIVATE_ENDPOINT = "PrivateEndpoint"
    SERVICE_TAG = "ServiceTag"

class SamplingAlgorithmType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    GRID = "Grid"
    RANDOM = "Random"
    BAYESIAN = "Bayesian"

class ScaleType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    DEFAULT = "Default"
    TARGET_UTILIZATION = "TargetUtilization"

class ScheduleActionType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    CREATE_JOB = "CreateJob"
    INVOKE_BATCH_ENDPOINT = "InvokeBatchEndpoint"
    CREATE_MONITOR = "CreateMonitor"

class ScheduleListViewType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    ENABLED_ONLY = "EnabledOnly"
    DISABLED_ONLY = "DisabledOnly"
    ALL = "All"

class ScheduleProvisioningState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The current deployment state of schedule.
    """

    COMPLETED = "Completed"
    PROVISIONING = "Provisioning"
    FAILED = "Failed"

class ScheduleProvisioningStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    CREATING = "Creating"
    UPDATING = "Updating"
    DELETING = "Deleting"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"

class ScheduleStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Is the schedule enabled or disabled?
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class SeasonalityMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Forecasting seasonality mode.
    """

    #: Seasonality to be determined automatically.
    AUTO = "Auto"
    #: Use the custom seasonality value.
    CUSTOM = "Custom"

class SecretsType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the datastore secrets type.
    """

    ACCOUNT_KEY = "AccountKey"
    CERTIFICATE = "Certificate"
    SAS = "Sas"
    SERVICE_PRINCIPAL = "ServicePrincipal"

class ServerlessEndpointState(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """State of the Serverless Endpoint.
    """

    UNKNOWN = "Unknown"
    CREATING = "Creating"
    DELETING = "Deleting"
    SUSPENDING = "Suspending"
    REINSTATING = "Reinstating"
    ONLINE = "Online"
    SUSPENDED = "Suspended"
    CREATION_FAILED = "CreationFailed"
    DELETION_FAILED = "DeletionFailed"

class ServerlessInferenceEndpointAuthMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    KEY = "Key"

class ServiceAccountKeyName(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    KEY1 = "Key1"
    KEY2 = "Key2"

class ServiceDataAccessAuthIdentity(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    #: Do not use any identity for service data access.
    NONE = "None"
    #: Use the system assigned managed identity of the Workspace to authenticate service data access.
    WORKSPACE_SYSTEM_ASSIGNED_IDENTITY = "WorkspaceSystemAssignedIdentity"
    #: Use the user assigned managed identity of the Workspace to authenticate service data access.
    WORKSPACE_USER_ASSIGNED_IDENTITY = "WorkspaceUserAssignedIdentity"

class ShortSeriesHandlingConfiguration(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The parameter defining how if AutoML should handle short time series.
    """

    #: Represents no/null value.
    NONE = "None"
    #: Short series will be padded if there are no long series, otherwise short series will be
    #: dropped.
    AUTO = "Auto"
    #: All the short series will be padded.
    PAD = "Pad"
    #: All the short series will be dropped.
    DROP = "Drop"

class SkuScaleType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Node scaling setting for the compute sku.
    """

    #: Automatically scales node count.
    AUTOMATIC = "Automatic"
    #: Node count scaled upon user request.
    MANUAL = "Manual"
    #: Fixed set of nodes.
    NONE = "None"

class SkuTier(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """This field is required to be implemented by the Resource Provider if the service has more than
    one tier, but is not required on a PUT.
    """

    FREE = "Free"
    BASIC = "Basic"
    STANDARD = "Standard"
    PREMIUM = "Premium"

class SourceType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Data source type.
    """

    DATASET = "Dataset"
    DATASTORE = "Datastore"
    URI = "URI"

class SparkJobEntryType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    SPARK_JOB_PYTHON_ENTRY = "SparkJobPythonEntry"
    SPARK_JOB_SCALA_ENTRY = "SparkJobScalaEntry"

class SshPublicAccess(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """State of the public SSH port. Possible values are: Disabled - Indicates that the public ssh
    port is closed on this instance. Enabled - Indicates that the public ssh port is open and
    accessible according to the VNet/subnet policy if applicable.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"

class SslConfigStatus(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enable or disable ssl for scoring
    """

    DISABLED = "Disabled"
    ENABLED = "Enabled"
    AUTO = "Auto"

class StackMetaLearnerType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The meta-learner is a model trained on the output of the individual heterogeneous models.
    Default meta-learners are LogisticRegression for classification tasks (or LogisticRegressionCV
    if cross-validation is enabled) and ElasticNet for regression/forecasting tasks (or
    ElasticNetCV if cross-validation is enabled).
    This parameter can be one of the following strings: LogisticRegression, LogisticRegressionCV,
    LightGBMClassifier, ElasticNet, ElasticNetCV, LightGBMRegressor, or LinearRegression
    """

    NONE = "None"
    #: Default meta-learners are LogisticRegression for classification tasks.
    LOGISTIC_REGRESSION = "LogisticRegression"
    #: Default meta-learners are LogisticRegression for classification task when CV is on.
    LOGISTIC_REGRESSION_CV = "LogisticRegressionCV"
    LIGHT_GBM_CLASSIFIER = "LightGBMClassifier"
    #: Default meta-learners are LogisticRegression for regression task.
    ELASTIC_NET = "ElasticNet"
    #: Default meta-learners are LogisticRegression for regression task when CV is on.
    ELASTIC_NET_CV = "ElasticNetCV"
    LIGHT_GBM_REGRESSOR = "LightGBMRegressor"
    LINEAR_REGRESSION = "LinearRegression"

class Status(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Status of update workspace quota.
    """

    UNDEFINED = "Undefined"
    SUCCESS = "Success"
    FAILURE = "Failure"
    INVALID_QUOTA_BELOW_CLUSTER_MINIMUM = "InvalidQuotaBelowClusterMinimum"
    INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT = "InvalidQuotaExceedsSubscriptionLimit"
    INVALID_VM_FAMILY_NAME = "InvalidVMFamilyName"
    OPERATION_NOT_SUPPORTED_FOR_SKU = "OperationNotSupportedForSku"
    OPERATION_NOT_ENABLED_FOR_REGION = "OperationNotEnabledForRegion"

class StochasticOptimizer(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Stochastic optimizer for image models.
    """

    #: No optimizer selected.
    NONE = "None"
    #: Stochastic Gradient Descent optimizer.
    SGD = "Sgd"
    #: Adam is algorithm the optimizes stochastic objective functions based on adaptive estimates of
    #: moments.
    ADAM = "Adam"
    #: AdamW is a variant of the optimizer Adam that has an improved implementation of weight decay.
    ADAMW = "Adamw"

class StorageAccountType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """type of this storage account.
    """

    STANDARD_LRS = "Standard_LRS"
    PREMIUM_LRS = "Premium_LRS"

class TargetAggregationFunction(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Target aggregate function.
    """

    #: Represent no value set.
    NONE = "None"
    SUM = "Sum"
    MAX = "Max"
    MIN = "Min"
    MEAN = "Mean"

class TargetLagsMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Target lags selection modes.
    """

    #: Target lags to be determined automatically.
    AUTO = "Auto"
    #: Use the custom target lags.
    CUSTOM = "Custom"

class TargetRollingWindowSizeMode(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Target rolling windows size mode.
    """

    #: Determine rolling windows size automatically.
    AUTO = "Auto"
    #: Use the specified rolling window size.
    CUSTOM = "Custom"

class TaskType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """AutoMLJob Task type.
    """

    #: Classification in machine learning and statistics is a supervised learning approach in which
    #: the computer program learns from the data given to it and make new observations or
    #: classifications.
    CLASSIFICATION = "Classification"
    #: Regression means to predict the value using the input data. Regression models are used to
    #: predict a continuous value.
    REGRESSION = "Regression"
    #: Forecasting is a special kind of regression task that deals with time-series data and creates
    #: forecasting model
    #: that can be used to predict the near future values based on the inputs.
    FORECASTING = "Forecasting"
    #: Image Classification. Multi-class image classification is used when an image is classified with
    #: only a single label
    #: from a set of classes - e.g. each image is classified as either an image of a 'cat' or a 'dog'
    #: or a 'duck'.
    IMAGE_CLASSIFICATION = "ImageClassification"
    #: Image Classification Multilabel. Multi-label image classification is used when an image could
    #: have one or more labels
    #: from a set of labels - e.g. an image could be labeled with both 'cat' and 'dog'.
    IMAGE_CLASSIFICATION_MULTILABEL = "ImageClassificationMultilabel"
    #: Image Object Detection. Object detection is used to identify objects in an image and locate
    #: each object with a
    #: bounding box e.g. locate all dogs and cats in an image and draw a bounding box around each.
    IMAGE_OBJECT_DETECTION = "ImageObjectDetection"
    #: Image Instance Segmentation. Instance segmentation is used to identify objects in an image at
    #: the pixel level,
    #: drawing a polygon around each object in the image.
    IMAGE_INSTANCE_SEGMENTATION = "ImageInstanceSegmentation"
    #: Text classification (also known as text tagging or text categorization) is the process of
    #: sorting texts into categories.
    #: Categories are mutually exclusive.
    TEXT_CLASSIFICATION = "TextClassification"
    #: Multilabel classification task assigns each sample to a group (zero or more) of target labels.
    TEXT_CLASSIFICATION_MULTILABEL = "TextClassificationMultilabel"
    #: Text Named Entity Recognition a.k.a. TextNER.
    #: Named Entity Recognition (NER) is the ability to take free-form text and identify the
    #: occurrences of entities such as people, locations, organizations, and more.
    TEXT_NER = "TextNER"

class TriggerType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    RECURRENCE = "Recurrence"
    CRON = "Cron"

class UnderlyingResourceAction(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):

    DELETE = "Delete"
    DETACH = "Detach"

class UnitOfMeasure(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The unit of time measurement for the specified VM price. Example: OneHour
    """

    ONE_HOUR = "OneHour"

class UsageUnit(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """An enum describing the unit of usage measurement.
    """

    COUNT = "Count"

class UseStl(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Configure STL Decomposition of the time-series target column.
    """

    #: No stl decomposition.
    NONE = "None"
    SEASON = "Season"
    SEASON_TREND = "SeasonTrend"

class ValidationMetricType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Metric computation method to use for validation metrics in image tasks.
    """

    #: No metric.
    NONE = "None"
    #: Coco metric.
    COCO = "Coco"
    #: Voc metric.
    VOC = "Voc"
    #: CocoVoc metric.
    COCO_VOC = "CocoVoc"

class VMPriceOSType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Operating system type used by the VM.
    """

    LINUX = "Linux"
    WINDOWS = "Windows"

class VmPriority(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Virtual Machine priority
    """

    DEDICATED = "Dedicated"
    LOW_PRIORITY = "LowPriority"

class VMTier(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """The type of the VM.
    """

    STANDARD = "Standard"
    LOW_PRIORITY = "LowPriority"
    SPOT = "Spot"

class VolumeDefinitionType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Type of Volume Definition. Possible Values: bind,volume,tmpfs,npipe
    """

    BIND = "bind"
    VOLUME = "volume"
    TMPFS = "tmpfs"
    NPIPE = "npipe"

class WebhookType(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum to determine the webhook callback service type.
    """

    AZURE_DEV_OPS = "AzureDevOps"

class WeekDay(with_metaclass(CaseInsensitiveEnumMeta, str, Enum)):
    """Enum of weekday
    """

    #: Monday weekday.
    MONDAY = "Monday"
    #: Tuesday weekday.
    TUESDAY = "Tuesday"
    #: Wednesday weekday.
    WEDNESDAY = "Wednesday"
    #: Thursday weekday.
    THURSDAY = "Thursday"
    #: Friday weekday.
    FRIDAY = "Friday"
    #: Saturday weekday.
    SATURDAY = "Saturday"
    #: Sunday weekday.
    SUNDAY = "Sunday"
