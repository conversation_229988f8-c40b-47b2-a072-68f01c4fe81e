# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import msrest.serialization


class ComputeRuntimeDto(msrest.serialization.Model):
    """ComputeRuntimeDto.

    :ivar spark_runtime_version:
    :vartype spark_runtime_version: str
    """

    _attribute_map = {
        'spark_runtime_version': {'key': 'sparkRuntimeVersion', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword spark_runtime_version:
        :paramtype spark_runtime_version: str
        """
        super(ComputeRuntimeDto, self).__init__(**kwargs)
        self.spark_runtime_version = kwargs.get('spark_runtime_version', None)


class CosmosDbSettings(msrest.serialization.Model):
    """CosmosDbSettings.

    :ivar collections_throughput:
    :vartype collections_throughput: int
    """

    _attribute_map = {
        'collections_throughput': {'key': 'collectionsThroughput', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword collections_throughput:
        :paramtype collections_throughput: int
        """
        super(CosmosDbSettings, self).__init__(**kwargs)
        self.collections_throughput = kwargs.get('collections_throughput', None)


class EncryptionProperty(msrest.serialization.Model):
    """EncryptionProperty.

    All required parameters must be populated in order to send to Azure.

    :ivar cosmos_db_resource_id: The byok cosmosdb account that customer brings to store customer's
     data
     with encryption.
    :vartype cosmos_db_resource_id: str
    :ivar identity: Identity to be used with the keyVault.
    :vartype identity: ~azure.mgmt.machinelearningservices.models.IdentityForCmk
    :ivar key_vault_properties: Required. KeyVault details to do the encryption.
    :vartype key_vault_properties: ~azure.mgmt.machinelearningservices.models.KeyVaultProperties
    :ivar search_account_resource_id: The byok search account that customer brings to store
     customer's data
     with encryption.
    :vartype search_account_resource_id: str
    :ivar status: Required. Possible values include: "Enabled", "Disabled".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.EncryptionStatus
    :ivar storage_account_resource_id: The byok storage account that customer brings to store
     customer's data
     with encryption.
    :vartype storage_account_resource_id: str
    """

    _validation = {
        'key_vault_properties': {'required': True},
        'status': {'required': True},
    }

    _attribute_map = {
        'cosmos_db_resource_id': {'key': 'cosmosDbResourceId', 'type': 'str'},
        'identity': {'key': 'identity', 'type': 'IdentityForCmk'},
        'key_vault_properties': {'key': 'keyVaultProperties', 'type': 'KeyVaultProperties'},
        'search_account_resource_id': {'key': 'searchAccountResourceId', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'storage_account_resource_id': {'key': 'storageAccountResourceId', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword cosmos_db_resource_id: The byok cosmosdb account that customer brings to store
         customer's data
         with encryption.
        :paramtype cosmos_db_resource_id: str
        :keyword identity: Identity to be used with the keyVault.
        :paramtype identity: ~azure.mgmt.machinelearningservices.models.IdentityForCmk
        :keyword key_vault_properties: Required. KeyVault details to do the encryption.
        :paramtype key_vault_properties: ~azure.mgmt.machinelearningservices.models.KeyVaultProperties
        :keyword search_account_resource_id: The byok search account that customer brings to store
         customer's data
         with encryption.
        :paramtype search_account_resource_id: str
        :keyword status: Required. Possible values include: "Enabled", "Disabled".
        :paramtype status: str or ~azure.mgmt.machinelearningservices.models.EncryptionStatus
        :keyword storage_account_resource_id: The byok storage account that customer brings to store
         customer's data
         with encryption.
        :paramtype storage_account_resource_id: str
        """
        super(EncryptionProperty, self).__init__(**kwargs)
        self.cosmos_db_resource_id = kwargs.get('cosmos_db_resource_id', None)
        self.identity = kwargs.get('identity', None)
        self.key_vault_properties = kwargs['key_vault_properties']
        self.search_account_resource_id = kwargs.get('search_account_resource_id', None)
        self.status = kwargs['status']
        self.storage_account_resource_id = kwargs.get('storage_account_resource_id', None)


class FeatureStoreSettings(msrest.serialization.Model):
    """FeatureStoreSettings.

    :ivar compute_runtime:
    :vartype compute_runtime: ~azure.mgmt.machinelearningservices.models.ComputeRuntimeDto
    :ivar offline_store_connection_name:
    :vartype offline_store_connection_name: str
    :ivar online_store_connection_name:
    :vartype online_store_connection_name: str
    """

    _attribute_map = {
        'compute_runtime': {'key': 'computeRuntime', 'type': 'ComputeRuntimeDto'},
        'offline_store_connection_name': {'key': 'offlineStoreConnectionName', 'type': 'str'},
        'online_store_connection_name': {'key': 'onlineStoreConnectionName', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword compute_runtime:
        :paramtype compute_runtime: ~azure.mgmt.machinelearningservices.models.ComputeRuntimeDto
        :keyword offline_store_connection_name:
        :paramtype offline_store_connection_name: str
        :keyword online_store_connection_name:
        :paramtype online_store_connection_name: str
        """
        super(FeatureStoreSettings, self).__init__(**kwargs)
        self.compute_runtime = kwargs.get('compute_runtime', None)
        self.offline_store_connection_name = kwargs.get('offline_store_connection_name', None)
        self.online_store_connection_name = kwargs.get('online_store_connection_name', None)


class IdentityForCmk(msrest.serialization.Model):
    """IdentityForCmk.

    :ivar user_assigned_identity: UserAssignedIdentity to be used to fetch the encryption key from
     keyVault.
    :vartype user_assigned_identity: str
    """

    _attribute_map = {
        'user_assigned_identity': {'key': 'userAssignedIdentity', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword user_assigned_identity: UserAssignedIdentity to be used to fetch the encryption key
         from keyVault.
        :paramtype user_assigned_identity: str
        """
        super(IdentityForCmk, self).__init__(**kwargs)
        self.user_assigned_identity = kwargs.get('user_assigned_identity', None)


class KeyVaultProperties(msrest.serialization.Model):
    """KeyVaultProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar identity_client_id: Currently, we support only SystemAssigned MSI.
     We need this when we support UserAssignedIdentities.
    :vartype identity_client_id: str
    :ivar key_identifier: Required. KeyVault key identifier to encrypt the data.
    :vartype key_identifier: str
    :ivar key_vault_arm_id: Required. KeyVault Arm Id that contains the data encryption key.
    :vartype key_vault_arm_id: str
    """

    _validation = {
        'key_identifier': {'required': True, 'min_length': 1, 'pattern': r'[a-zA-Z0-9_]'},
        'key_vault_arm_id': {'required': True, 'min_length': 1, 'pattern': r'[a-zA-Z0-9_]'},
    }

    _attribute_map = {
        'identity_client_id': {'key': 'identityClientId', 'type': 'str'},
        'key_identifier': {'key': 'keyIdentifier', 'type': 'str'},
        'key_vault_arm_id': {'key': 'keyVaultArmId', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword identity_client_id: Currently, we support only SystemAssigned MSI.
         We need this when we support UserAssignedIdentities.
        :paramtype identity_client_id: str
        :keyword key_identifier: Required. KeyVault key identifier to encrypt the data.
        :paramtype key_identifier: str
        :keyword key_vault_arm_id: Required. KeyVault Arm Id that contains the data encryption key.
        :paramtype key_vault_arm_id: str
        """
        super(KeyVaultProperties, self).__init__(**kwargs)
        self.identity_client_id = kwargs.get('identity_client_id', None)
        self.key_identifier = kwargs['key_identifier']
        self.key_vault_arm_id = kwargs['key_vault_arm_id']


class ManagedServiceIdentity(msrest.serialization.Model):
    """Managed service identity (system assigned and/or user assigned identities).

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar principal_id: The service principal ID of the system assigned identity. This property
     will only be provided for a system assigned identity.
    :vartype principal_id: str
    :ivar tenant_id: The tenant ID of the system assigned identity. This property will only be
     provided for a system assigned identity.
    :vartype tenant_id: str
    :ivar type: Required. Type of managed service identity (where both SystemAssigned and
     UserAssigned types are allowed). Possible values include: "None", "SystemAssigned",
     "UserAssigned", "SystemAssigned,UserAssigned".
    :vartype type: str or ~azure.mgmt.machinelearningservices.models.ManagedServiceIdentityType
    :ivar user_assigned_identities: The set of user assigned identities associated with the
     resource. The userAssignedIdentities dictionary keys will be ARM resource ids in the form:
     '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}.
     The dictionary values can be empty objects ({}) in requests.
    :vartype user_assigned_identities: dict[str,
     ~azure.mgmt.machinelearningservices.models.UserAssignedIdentity]
    """

    _validation = {
        'principal_id': {'readonly': True},
        'tenant_id': {'readonly': True},
        'type': {'required': True},
    }

    _attribute_map = {
        'principal_id': {'key': 'principalId', 'type': 'str'},
        'tenant_id': {'key': 'tenantId', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
        'user_assigned_identities': {'key': 'userAssignedIdentities', 'type': '{UserAssignedIdentity}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword type: Required. Type of managed service identity (where both SystemAssigned and
         UserAssigned types are allowed). Possible values include: "None", "SystemAssigned",
         "UserAssigned", "SystemAssigned,UserAssigned".
        :paramtype type: str or ~azure.mgmt.machinelearningservices.models.ManagedServiceIdentityType
        :keyword user_assigned_identities: The set of user assigned identities associated with the
         resource. The userAssignedIdentities dictionary keys will be ARM resource ids in the form:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}.
         The dictionary values can be empty objects ({}) in requests.
        :paramtype user_assigned_identities: dict[str,
         ~azure.mgmt.machinelearningservices.models.UserAssignedIdentity]
        """
        super(ManagedServiceIdentity, self).__init__(**kwargs)
        self.principal_id = None
        self.tenant_id = None
        self.type = kwargs['type']
        self.user_assigned_identities = kwargs.get('user_assigned_identities', None)


class NotebookPreparationError(msrest.serialization.Model):
    """NotebookPreparationError.

    :ivar error_message:
    :vartype error_message: str
    :ivar status_code:
    :vartype status_code: int
    """

    _attribute_map = {
        'error_message': {'key': 'errorMessage', 'type': 'str'},
        'status_code': {'key': 'statusCode', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword error_message:
        :paramtype error_message: str
        :keyword status_code:
        :paramtype status_code: int
        """
        super(NotebookPreparationError, self).__init__(**kwargs)
        self.error_message = kwargs.get('error_message', None)
        self.status_code = kwargs.get('status_code', None)


class NotebookResourceInfo(msrest.serialization.Model):
    """NotebookResourceInfo.

    :ivar fqdn:
    :vartype fqdn: str
    :ivar is_private_link_enabled:
    :vartype is_private_link_enabled: bool
    :ivar notebook_preparation_error: The error that occurs when preparing notebook.
    :vartype notebook_preparation_error:
     ~azure.mgmt.machinelearningservices.models.NotebookPreparationError
    :ivar resource_id: the data plane resourceId that used to initialize notebook component.
    :vartype resource_id: str
    """

    _attribute_map = {
        'fqdn': {'key': 'fqdn', 'type': 'str'},
        'is_private_link_enabled': {'key': 'isPrivateLinkEnabled', 'type': 'bool'},
        'notebook_preparation_error': {'key': 'notebookPreparationError', 'type': 'NotebookPreparationError'},
        'resource_id': {'key': 'resourceId', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword fqdn:
        :paramtype fqdn: str
        :keyword is_private_link_enabled:
        :paramtype is_private_link_enabled: bool
        :keyword notebook_preparation_error: The error that occurs when preparing notebook.
        :paramtype notebook_preparation_error:
         ~azure.mgmt.machinelearningservices.models.NotebookPreparationError
        :keyword resource_id: the data plane resourceId that used to initialize notebook component.
        :paramtype resource_id: str
        """
        super(NotebookResourceInfo, self).__init__(**kwargs)
        self.fqdn = kwargs.get('fqdn', None)
        self.is_private_link_enabled = kwargs.get('is_private_link_enabled', None)
        self.notebook_preparation_error = kwargs.get('notebook_preparation_error', None)
        self.resource_id = kwargs.get('resource_id', None)


class Resource(msrest.serialization.Model):
    """Common fields that are returned in the response for all Azure Resource Manager resources.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'readonly': True},
        'type': {'readonly': True},
        'system_data': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
        'system_data': {'key': 'systemData', 'type': 'SystemData'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        """
        super(Resource, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.system_data = None


class PrivateEndpointConnection(Resource):
    """PrivateEndpointConnection.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar identity: Managed service identity (system assigned and/or user assigned identities).
    :vartype identity: ~azure.mgmt.machinelearningservices.models.ManagedServiceIdentity
    :ivar location: Same as workspace location.
    :vartype location: str
    :ivar sku: Optional. This field is required to be implemented by the RP because AML is
     supporting more than one tier.
    :vartype sku: ~azure.mgmt.machinelearningservices.models.Sku
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar private_endpoint:
    :vartype private_endpoint:
     ~azure.mgmt.machinelearningservices.models.WorkspacePrivateEndpointResource
    :ivar private_link_service_connection_state: The connection state.
    :vartype private_link_service_connection_state:
     ~azure.mgmt.machinelearningservices.models.PrivateLinkServiceConnectionState
    :ivar provisioning_state: Connection status of the service consumer with the service provider
     Possible state transitions
     Pending -> Approved (Service provider approves the connection request)
     Pending -> Rejected (Service provider rejects the connection request)
     Pending -> Disconnected (Service provider deletes the connection)
     Approved -> Rejected (Service provider rejects the approved connection)
     Approved -> Disconnected (Service provider deletes the connection)
     Rejected -> Pending (Service consumer re-initiates the connection request that was rejected)
     Rejected -> Disconnected (Service provider deletes the connection). Possible values include:
     "Approved", "Pending", "Rejected", "Disconnected", "Timeout".
    :vartype provisioning_state: str or
     ~azure.mgmt.machinelearningservices.models.EndpointServiceConnectionStatus
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'readonly': True},
        'type': {'readonly': True},
        'system_data': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
        'system_data': {'key': 'systemData', 'type': 'SystemData'},
        'identity': {'key': 'identity', 'type': 'ManagedServiceIdentity'},
        'location': {'key': 'location', 'type': 'str'},
        'sku': {'key': 'sku', 'type': 'Sku'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'private_endpoint': {'key': 'properties.privateEndpoint', 'type': 'WorkspacePrivateEndpointResource'},
        'private_link_service_connection_state': {'key': 'properties.privateLinkServiceConnectionState', 'type': 'PrivateLinkServiceConnectionState'},
        'provisioning_state': {'key': 'properties.provisioningState', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword identity: Managed service identity (system assigned and/or user assigned identities).
        :paramtype identity: ~azure.mgmt.machinelearningservices.models.ManagedServiceIdentity
        :keyword location: Same as workspace location.
        :paramtype location: str
        :keyword sku: Optional. This field is required to be implemented by the RP because AML is
         supporting more than one tier.
        :paramtype sku: ~azure.mgmt.machinelearningservices.models.Sku
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword private_endpoint:
        :paramtype private_endpoint:
         ~azure.mgmt.machinelearningservices.models.WorkspacePrivateEndpointResource
        :keyword private_link_service_connection_state: The connection state.
        :paramtype private_link_service_connection_state:
         ~azure.mgmt.machinelearningservices.models.PrivateLinkServiceConnectionState
        :keyword provisioning_state: Connection status of the service consumer with the service
         provider
         Possible state transitions
         Pending -> Approved (Service provider approves the connection request)
         Pending -> Rejected (Service provider rejects the connection request)
         Pending -> Disconnected (Service provider deletes the connection)
         Approved -> Rejected (Service provider rejects the approved connection)
         Approved -> Disconnected (Service provider deletes the connection)
         Rejected -> Pending (Service consumer re-initiates the connection request that was rejected)
         Rejected -> Disconnected (Service provider deletes the connection). Possible values include:
         "Approved", "Pending", "Rejected", "Disconnected", "Timeout".
        :paramtype provisioning_state: str or
         ~azure.mgmt.machinelearningservices.models.EndpointServiceConnectionStatus
        """
        super(PrivateEndpointConnection, self).__init__(**kwargs)
        self.identity = kwargs.get('identity', None)
        self.location = kwargs.get('location', None)
        self.sku = kwargs.get('sku', None)
        self.tags = kwargs.get('tags', None)
        self.private_endpoint = kwargs.get('private_endpoint', None)
        self.private_link_service_connection_state = kwargs.get('private_link_service_connection_state', None)
        self.provisioning_state = kwargs.get('provisioning_state', None)


class PrivateLinkServiceConnectionState(msrest.serialization.Model):
    """PrivateLinkServiceConnectionState.

    :ivar actions_required: Some RP chose "None". Other RPs use this for region expansion.
    :vartype actions_required: str
    :ivar description: User-defined message that, per NRP doc, may be used for approval-related
     message.
    :vartype description: str
    :ivar status: Connection status of the service consumer with the service provider
     Possible state transitions
     Pending -> Approved (Service provider approves the connection request)
     Pending -> Rejected (Service provider rejects the connection request)
     Pending -> Disconnected (Service provider deletes the connection)
     Approved -> Rejected (Service provider rejects the approved connection)
     Approved -> Disconnected (Service provider deletes the connection)
     Rejected -> Pending (Service consumer re-initiates the connection request that was rejected)
     Rejected -> Disconnected (Service provider deletes the connection). Possible values include:
     "Approved", "Pending", "Rejected", "Disconnected", "Timeout".
    :vartype status: str or
     ~azure.mgmt.machinelearningservices.models.EndpointServiceConnectionStatus
    """

    _attribute_map = {
        'actions_required': {'key': 'actionsRequired', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword actions_required: Some RP chose "None". Other RPs use this for region expansion.
        :paramtype actions_required: str
        :keyword description: User-defined message that, per NRP doc, may be used for approval-related
         message.
        :paramtype description: str
        :keyword status: Connection status of the service consumer with the service provider
         Possible state transitions
         Pending -> Approved (Service provider approves the connection request)
         Pending -> Rejected (Service provider rejects the connection request)
         Pending -> Disconnected (Service provider deletes the connection)
         Approved -> Rejected (Service provider rejects the approved connection)
         Approved -> Disconnected (Service provider deletes the connection)
         Rejected -> Pending (Service consumer re-initiates the connection request that was rejected)
         Rejected -> Disconnected (Service provider deletes the connection). Possible values include:
         "Approved", "Pending", "Rejected", "Disconnected", "Timeout".
        :paramtype status: str or
         ~azure.mgmt.machinelearningservices.models.EndpointServiceConnectionStatus
        """
        super(PrivateLinkServiceConnectionState, self).__init__(**kwargs)
        self.actions_required = kwargs.get('actions_required', None)
        self.description = kwargs.get('description', None)
        self.status = kwargs.get('status', None)


class ServiceManagedResourcesSettings(msrest.serialization.Model):
    """ServiceManagedResourcesSettings.

    :ivar cosmos_db:
    :vartype cosmos_db: ~azure.mgmt.machinelearningservices.models.CosmosDbSettings
    """

    _attribute_map = {
        'cosmos_db': {'key': 'cosmosDb', 'type': 'CosmosDbSettings'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword cosmos_db:
        :paramtype cosmos_db: ~azure.mgmt.machinelearningservices.models.CosmosDbSettings
        """
        super(ServiceManagedResourcesSettings, self).__init__(**kwargs)
        self.cosmos_db = kwargs.get('cosmos_db', None)


class SharedPrivateLinkResource(msrest.serialization.Model):
    """SharedPrivateLinkResource.

    :ivar name: Unique name of the private link.
    :vartype name: str
    :ivar group_id: group id of the private link.
    :vartype group_id: str
    :ivar private_link_resource_id: the resource id that private link links to.
    :vartype private_link_resource_id: str
    :ivar request_message: Request message.
    :vartype request_message: str
    :ivar status: Connection status of the service consumer with the service provider
     Possible state transitions
     Pending -> Approved (Service provider approves the connection request)
     Pending -> Rejected (Service provider rejects the connection request)
     Pending -> Disconnected (Service provider deletes the connection)
     Approved -> Rejected (Service provider rejects the approved connection)
     Approved -> Disconnected (Service provider deletes the connection)
     Rejected -> Pending (Service consumer re-initiates the connection request that was rejected)
     Rejected -> Disconnected (Service provider deletes the connection). Possible values include:
     "Approved", "Pending", "Rejected", "Disconnected", "Timeout".
    :vartype status: str or
     ~azure.mgmt.machinelearningservices.models.EndpointServiceConnectionStatus
    """

    _attribute_map = {
        'name': {'key': 'name', 'type': 'str'},
        'group_id': {'key': 'properties.groupId', 'type': 'str'},
        'private_link_resource_id': {'key': 'properties.privateLinkResourceId', 'type': 'str'},
        'request_message': {'key': 'properties.requestMessage', 'type': 'str'},
        'status': {'key': 'properties.status', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword name: Unique name of the private link.
        :paramtype name: str
        :keyword group_id: group id of the private link.
        :paramtype group_id: str
        :keyword private_link_resource_id: the resource id that private link links to.
        :paramtype private_link_resource_id: str
        :keyword request_message: Request message.
        :paramtype request_message: str
        :keyword status: Connection status of the service consumer with the service provider
         Possible state transitions
         Pending -> Approved (Service provider approves the connection request)
         Pending -> Rejected (Service provider rejects the connection request)
         Pending -> Disconnected (Service provider deletes the connection)
         Approved -> Rejected (Service provider rejects the approved connection)
         Approved -> Disconnected (Service provider deletes the connection)
         Rejected -> Pending (Service consumer re-initiates the connection request that was rejected)
         Rejected -> Disconnected (Service provider deletes the connection). Possible values include:
         "Approved", "Pending", "Rejected", "Disconnected", "Timeout".
        :paramtype status: str or
         ~azure.mgmt.machinelearningservices.models.EndpointServiceConnectionStatus
        """
        super(SharedPrivateLinkResource, self).__init__(**kwargs)
        self.name = kwargs.get('name', None)
        self.group_id = kwargs.get('group_id', None)
        self.private_link_resource_id = kwargs.get('private_link_resource_id', None)
        self.request_message = kwargs.get('request_message', None)
        self.status = kwargs.get('status', None)


class Sku(msrest.serialization.Model):
    """The resource model definition representing SKU.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required. The name of the SKU. Ex - P3. It is typically a letter+number code.
    :vartype name: str
    :ivar tier: This field is required to be implemented by the Resource Provider if the service
     has more than one tier, but is not required on a PUT. Possible values include: "Free", "Basic",
     "Standard", "Premium".
    :vartype tier: str or ~azure.mgmt.machinelearningservices.models.SkuTier
    :ivar size: The SKU size. When the name field is the combination of tier and some other value,
     this would be the standalone code.
    :vartype size: str
    :ivar family: If the service has different generations of hardware, for the same SKU, then that
     can be captured here.
    :vartype family: str
    :ivar capacity: If the SKU supports scale out/in then the capacity integer should be included.
     If scale out/in is not possible for the resource this may be omitted.
    :vartype capacity: int
    """

    _validation = {
        'name': {'required': True},
    }

    _attribute_map = {
        'name': {'key': 'name', 'type': 'str'},
        'tier': {'key': 'tier', 'type': 'str'},
        'size': {'key': 'size', 'type': 'str'},
        'family': {'key': 'family', 'type': 'str'},
        'capacity': {'key': 'capacity', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword name: Required. The name of the SKU. Ex - P3. It is typically a letter+number code.
        :paramtype name: str
        :keyword tier: This field is required to be implemented by the Resource Provider if the service
         has more than one tier, but is not required on a PUT. Possible values include: "Free", "Basic",
         "Standard", "Premium".
        :paramtype tier: str or ~azure.mgmt.machinelearningservices.models.SkuTier
        :keyword size: The SKU size. When the name field is the combination of tier and some other
         value, this would be the standalone code.
        :paramtype size: str
        :keyword family: If the service has different generations of hardware, for the same SKU, then
         that can be captured here.
        :paramtype family: str
        :keyword capacity: If the SKU supports scale out/in then the capacity integer should be
         included. If scale out/in is not possible for the resource this may be omitted.
        :paramtype capacity: int
        """
        super(Sku, self).__init__(**kwargs)
        self.name = kwargs['name']
        self.tier = kwargs.get('tier', None)
        self.size = kwargs.get('size', None)
        self.family = kwargs.get('family', None)
        self.capacity = kwargs.get('capacity', None)


class SystemData(msrest.serialization.Model):
    """Metadata pertaining to creation and last modification of the resource.

    :ivar created_by: The identity that created the resource.
    :vartype created_by: str
    :ivar created_by_type: The type of identity that created the resource. Possible values include:
     "User", "Application", "ManagedIdentity", "Key".
    :vartype created_by_type: str or ~azure.mgmt.machinelearningservices.models.CreatedByType
    :ivar created_at: The timestamp of resource creation (UTC).
    :vartype created_at: ~datetime.datetime
    :ivar last_modified_by: The identity that last modified the resource.
    :vartype last_modified_by: str
    :ivar last_modified_by_type: The type of identity that last modified the resource. Possible
     values include: "User", "Application", "ManagedIdentity", "Key".
    :vartype last_modified_by_type: str or ~azure.mgmt.machinelearningservices.models.CreatedByType
    :ivar last_modified_at: The timestamp of resource last modification (UTC).
    :vartype last_modified_at: ~datetime.datetime
    """

    _attribute_map = {
        'created_by': {'key': 'createdBy', 'type': 'str'},
        'created_by_type': {'key': 'createdByType', 'type': 'str'},
        'created_at': {'key': 'createdAt', 'type': 'iso-8601'},
        'last_modified_by': {'key': 'lastModifiedBy', 'type': 'str'},
        'last_modified_by_type': {'key': 'lastModifiedByType', 'type': 'str'},
        'last_modified_at': {'key': 'lastModifiedAt', 'type': 'iso-8601'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword created_by: The identity that created the resource.
        :paramtype created_by: str
        :keyword created_by_type: The type of identity that created the resource. Possible values
         include: "User", "Application", "ManagedIdentity", "Key".
        :paramtype created_by_type: str or ~azure.mgmt.machinelearningservices.models.CreatedByType
        :keyword created_at: The timestamp of resource creation (UTC).
        :paramtype created_at: ~datetime.datetime
        :keyword last_modified_by: The identity that last modified the resource.
        :paramtype last_modified_by: str
        :keyword last_modified_by_type: The type of identity that last modified the resource. Possible
         values include: "User", "Application", "ManagedIdentity", "Key".
        :paramtype last_modified_by_type: str or
         ~azure.mgmt.machinelearningservices.models.CreatedByType
        :keyword last_modified_at: The timestamp of resource last modification (UTC).
        :paramtype last_modified_at: ~datetime.datetime
        """
        super(SystemData, self).__init__(**kwargs)
        self.created_by = kwargs.get('created_by', None)
        self.created_by_type = kwargs.get('created_by_type', None)
        self.created_at = kwargs.get('created_at', None)
        self.last_modified_by = kwargs.get('last_modified_by', None)
        self.last_modified_by_type = kwargs.get('last_modified_by_type', None)
        self.last_modified_at = kwargs.get('last_modified_at', None)


class UserAssignedIdentity(msrest.serialization.Model):
    """User assigned identity properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar principal_id: The principal ID of the assigned identity.
    :vartype principal_id: str
    :ivar client_id: The client ID of the assigned identity.
    :vartype client_id: str
    """

    _validation = {
        'principal_id': {'readonly': True},
        'client_id': {'readonly': True},
    }

    _attribute_map = {
        'principal_id': {'key': 'principalId', 'type': 'str'},
        'client_id': {'key': 'clientId', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        """
        super(UserAssignedIdentity, self).__init__(**kwargs)
        self.principal_id = None
        self.client_id = None


class Workspace(Resource):
    """Workspace.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Fully qualified resource ID for the resource. Ex -
     /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}.
    :vartype id: str
    :ivar name: The name of the resource.
    :vartype name: str
    :ivar type: The type of the resource. E.g. "Microsoft.Compute/virtualMachines" or
     "Microsoft.Storage/storageAccounts".
    :vartype type: str
    :ivar system_data: Azure Resource Manager metadata containing createdBy and modifiedBy
     information.
    :vartype system_data: ~azure.mgmt.machinelearningservices.models.SystemData
    :ivar identity: Managed service identity (system assigned and/or user assigned identities).
    :vartype identity: ~azure.mgmt.machinelearningservices.models.ManagedServiceIdentity
    :ivar kind:
    :vartype kind: str
    :ivar location:
    :vartype location: str
    :ivar sku: Optional. This field is required to be implemented by the RP because AML is
     supporting more than one tier.
    :vartype sku: ~azure.mgmt.machinelearningservices.models.Sku
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar allow_public_access_when_behind_vnet: The flag to indicate whether to allow public access
     when behind VNet.
    :vartype allow_public_access_when_behind_vnet: bool
    :ivar application_insights: ARM id of the application insights associated with this workspace.
    :vartype application_insights: str
    :ivar associated_workspaces:
    :vartype associated_workspaces: list[str]
    :ivar container_registries:
    :vartype container_registries: list[str]
    :ivar container_registry: ARM id of the container registry associated with this workspace.
    :vartype container_registry: str
    :ivar description: The description of this workspace.
    :vartype description: str
    :ivar discovery_url: Url for the discovery service to identify regional endpoints for machine
     learning experimentation services.
    :vartype discovery_url: str
    :ivar enable_data_isolation:
    :vartype enable_data_isolation: bool
    :ivar encryption:
    :vartype encryption: ~azure.mgmt.machinelearningservices.models.EncryptionProperty
    :ivar existing_workspaces:
    :vartype existing_workspaces: list[str]
    :ivar feature_store_settings: Settings for feature store type workspace.
    :vartype feature_store_settings:
     ~azure.mgmt.machinelearningservices.models.FeatureStoreSettings
    :ivar friendly_name: The friendly name for this workspace. This name in mutable.
    :vartype friendly_name: str
    :ivar hbi_workspace: The flag to signal HBI data in the workspace and reduce diagnostic data
     collected by the service.
    :vartype hbi_workspace: bool
    :ivar hub_resource_id:
    :vartype hub_resource_id: str
    :ivar image_build_compute: The compute name for image build.
    :vartype image_build_compute: str
    :ivar key_vault: ARM id of the key vault associated with this workspace. This cannot be changed
     once the workspace has been created.
    :vartype key_vault: str
    :ivar key_vaults:
    :vartype key_vaults: list[str]
    :ivar managed_network: Anything.
    :vartype managed_network: any
    :ivar ml_flow_tracking_uri: The URI associated with this workspace that machine learning flow
     must point at to set up tracking.
    :vartype ml_flow_tracking_uri: str
    :ivar notebook_info: The notebook info of Azure ML workspace.
    :vartype notebook_info: ~azure.mgmt.machinelearningservices.models.NotebookResourceInfo
    :ivar primary_user_assigned_identity: The user assigned identity resource id that represents
     the workspace identity.
    :vartype primary_user_assigned_identity: str
    :ivar private_endpoint_connections: The list of private endpoint connections in the workspace.
    :vartype private_endpoint_connections:
     list[~azure.mgmt.machinelearningservices.models.PrivateEndpointConnection]
    :ivar private_link_count: Count of private connections in the workspace.
    :vartype private_link_count: int
    :ivar provisioning_state: The current deployment state of workspace resource. The
     provisioningState is to indicate states for resource provisioning. Possible values include:
     "Unknown", "Updating", "Creating", "Deleting", "Succeeded", "Failed", "Canceled".
    :vartype provisioning_state: str or
     ~azure.mgmt.machinelearningservices.models.ProvisioningState
    :ivar public_network_access: Whether requests from Public Network are allowed. Possible values
     include: "Enabled", "Disabled".
    :vartype public_network_access: str or
     ~azure.mgmt.machinelearningservices.models.PublicNetworkAccessType
    :ivar service_managed_resources_settings: The service managed resource settings.
    :vartype service_managed_resources_settings:
     ~azure.mgmt.machinelearningservices.models.ServiceManagedResourcesSettings
    :ivar service_provisioned_resource_group: The name of the managed resource group created by
     workspace RP in customer subscription if the workspace is CMK workspace.
    :vartype service_provisioned_resource_group: str
    :ivar shared_private_link_resources: The list of shared private link resources in this
     workspace.
    :vartype shared_private_link_resources:
     list[~azure.mgmt.machinelearningservices.models.SharedPrivateLinkResource]
    :ivar soft_delete_retention_in_days: Retention time in days after workspace get soft deleted.
    :vartype soft_delete_retention_in_days: int
    :ivar storage_account: ARM id of the storage account associated with this workspace. This
     cannot be changed once the workspace has been created.
    :vartype storage_account: str
    :ivar storage_accounts:
    :vartype storage_accounts: list[str]
    :ivar storage_hns_enabled: If the storage associated with the workspace has hierarchical
     namespace(HNS) enabled.
    :vartype storage_hns_enabled: bool
    :ivar system_datastores_auth_mode: The auth mode used for accessing the system datastores of
     the workspace.
    :vartype system_datastores_auth_mode: str
    :ivar tenant_id: The tenant id associated with this workspace.
    :vartype tenant_id: str
    :ivar v1_legacy_mode: Enabling v1_legacy_mode may prevent you from using features provided by
     the v2 API.
    :vartype v1_legacy_mode: bool
    :ivar workspace_hub_config:
    :vartype workspace_hub_config: ~azure.mgmt.machinelearningservices.models.WorkspaceHubConfig
    :ivar workspace_id: The immutable id associated with this workspace.
    :vartype workspace_id: str
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'readonly': True},
        'type': {'readonly': True},
        'system_data': {'readonly': True},
        'ml_flow_tracking_uri': {'readonly': True},
        'notebook_info': {'readonly': True},
        'private_endpoint_connections': {'readonly': True},
        'private_link_count': {'readonly': True},
        'provisioning_state': {'readonly': True},
        'service_provisioned_resource_group': {'readonly': True},
        'storage_hns_enabled': {'readonly': True},
        'tenant_id': {'readonly': True},
        'workspace_id': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
        'system_data': {'key': 'systemData', 'type': 'SystemData'},
        'identity': {'key': 'identity', 'type': 'ManagedServiceIdentity'},
        'kind': {'key': 'kind', 'type': 'str'},
        'location': {'key': 'location', 'type': 'str'},
        'sku': {'key': 'sku', 'type': 'Sku'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'allow_public_access_when_behind_vnet': {'key': 'properties.allowPublicAccessWhenBehindVnet', 'type': 'bool'},
        'application_insights': {'key': 'properties.applicationInsights', 'type': 'str'},
        'associated_workspaces': {'key': 'properties.associatedWorkspaces', 'type': '[str]'},
        'container_registries': {'key': 'properties.containerRegistries', 'type': '[str]'},
        'container_registry': {'key': 'properties.containerRegistry', 'type': 'str'},
        'description': {'key': 'properties.description', 'type': 'str'},
        'discovery_url': {'key': 'properties.discoveryUrl', 'type': 'str'},
        'enable_data_isolation': {'key': 'properties.enableDataIsolation', 'type': 'bool'},
        'encryption': {'key': 'properties.encryption', 'type': 'EncryptionProperty'},
        'existing_workspaces': {'key': 'properties.existingWorkspaces', 'type': '[str]'},
        'feature_store_settings': {'key': 'properties.featureStoreSettings', 'type': 'FeatureStoreSettings'},
        'friendly_name': {'key': 'properties.friendlyName', 'type': 'str'},
        'hbi_workspace': {'key': 'properties.hbiWorkspace', 'type': 'bool'},
        'hub_resource_id': {'key': 'properties.hubResourceId', 'type': 'str'},
        'image_build_compute': {'key': 'properties.imageBuildCompute', 'type': 'str'},
        'key_vault': {'key': 'properties.keyVault', 'type': 'str'},
        'key_vaults': {'key': 'properties.keyVaults', 'type': '[str]'},
        'managed_network': {'key': 'properties.managedNetwork', 'type': 'object'},
        'ml_flow_tracking_uri': {'key': 'properties.mlFlowTrackingUri', 'type': 'str'},
        'notebook_info': {'key': 'properties.notebookInfo', 'type': 'NotebookResourceInfo'},
        'primary_user_assigned_identity': {'key': 'properties.primaryUserAssignedIdentity', 'type': 'str'},
        'private_endpoint_connections': {'key': 'properties.privateEndpointConnections', 'type': '[PrivateEndpointConnection]'},
        'private_link_count': {'key': 'properties.privateLinkCount', 'type': 'int'},
        'provisioning_state': {'key': 'properties.provisioningState', 'type': 'str'},
        'public_network_access': {'key': 'properties.publicNetworkAccess', 'type': 'str'},
        'service_managed_resources_settings': {'key': 'properties.serviceManagedResourcesSettings', 'type': 'ServiceManagedResourcesSettings'},
        'service_provisioned_resource_group': {'key': 'properties.serviceProvisionedResourceGroup', 'type': 'str'},
        'shared_private_link_resources': {'key': 'properties.sharedPrivateLinkResources', 'type': '[SharedPrivateLinkResource]'},
        'soft_delete_retention_in_days': {'key': 'properties.softDeleteRetentionInDays', 'type': 'int'},
        'storage_account': {'key': 'properties.storageAccount', 'type': 'str'},
        'storage_accounts': {'key': 'properties.storageAccounts', 'type': '[str]'},
        'storage_hns_enabled': {'key': 'properties.storageHnsEnabled', 'type': 'bool'},
        'system_datastores_auth_mode': {'key': 'properties.systemDatastoresAuthMode', 'type': 'str'},
        'tenant_id': {'key': 'properties.tenantId', 'type': 'str'},
        'v1_legacy_mode': {'key': 'properties.v1LegacyMode', 'type': 'bool'},
        'workspace_hub_config': {'key': 'properties.workspaceHubConfig', 'type': 'WorkspaceHubConfig'},
        'workspace_id': {'key': 'properties.workspaceId', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword identity: Managed service identity (system assigned and/or user assigned identities).
        :paramtype identity: ~azure.mgmt.machinelearningservices.models.ManagedServiceIdentity
        :keyword kind:
        :paramtype kind: str
        :keyword location:
        :paramtype location: str
        :keyword sku: Optional. This field is required to be implemented by the RP because AML is
         supporting more than one tier.
        :paramtype sku: ~azure.mgmt.machinelearningservices.models.Sku
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword allow_public_access_when_behind_vnet: The flag to indicate whether to allow public
         access when behind VNet.
        :paramtype allow_public_access_when_behind_vnet: bool
        :keyword application_insights: ARM id of the application insights associated with this
         workspace.
        :paramtype application_insights: str
        :keyword associated_workspaces:
        :paramtype associated_workspaces: list[str]
        :keyword container_registries:
        :paramtype container_registries: list[str]
        :keyword container_registry: ARM id of the container registry associated with this workspace.
        :paramtype container_registry: str
        :keyword description: The description of this workspace.
        :paramtype description: str
        :keyword discovery_url: Url for the discovery service to identify regional endpoints for
         machine learning experimentation services.
        :paramtype discovery_url: str
        :keyword enable_data_isolation:
        :paramtype enable_data_isolation: bool
        :keyword encryption:
        :paramtype encryption: ~azure.mgmt.machinelearningservices.models.EncryptionProperty
        :keyword existing_workspaces:
        :paramtype existing_workspaces: list[str]
        :keyword feature_store_settings: Settings for feature store type workspace.
        :paramtype feature_store_settings:
         ~azure.mgmt.machinelearningservices.models.FeatureStoreSettings
        :keyword friendly_name: The friendly name for this workspace. This name in mutable.
        :paramtype friendly_name: str
        :keyword hbi_workspace: The flag to signal HBI data in the workspace and reduce diagnostic data
         collected by the service.
        :paramtype hbi_workspace: bool
        :keyword hub_resource_id:
        :paramtype hub_resource_id: str
        :keyword image_build_compute: The compute name for image build.
        :paramtype image_build_compute: str
        :keyword key_vault: ARM id of the key vault associated with this workspace. This cannot be
         changed once the workspace has been created.
        :paramtype key_vault: str
        :keyword key_vaults:
        :paramtype key_vaults: list[str]
        :keyword managed_network: Anything.
        :paramtype managed_network: any
        :keyword primary_user_assigned_identity: The user assigned identity resource id that represents
         the workspace identity.
        :paramtype primary_user_assigned_identity: str
        :keyword public_network_access: Whether requests from Public Network are allowed. Possible
         values include: "Enabled", "Disabled".
        :paramtype public_network_access: str or
         ~azure.mgmt.machinelearningservices.models.PublicNetworkAccessType
        :keyword service_managed_resources_settings: The service managed resource settings.
        :paramtype service_managed_resources_settings:
         ~azure.mgmt.machinelearningservices.models.ServiceManagedResourcesSettings
        :keyword shared_private_link_resources: The list of shared private link resources in this
         workspace.
        :paramtype shared_private_link_resources:
         list[~azure.mgmt.machinelearningservices.models.SharedPrivateLinkResource]
        :keyword soft_delete_retention_in_days: Retention time in days after workspace get soft
         deleted.
        :paramtype soft_delete_retention_in_days: int
        :keyword storage_account: ARM id of the storage account associated with this workspace. This
         cannot be changed once the workspace has been created.
        :paramtype storage_account: str
        :keyword storage_accounts:
        :paramtype storage_accounts: list[str]
        :keyword system_datastores_auth_mode: The auth mode used for accessing the system datastores of
         the workspace.
        :paramtype system_datastores_auth_mode: str
        :keyword v1_legacy_mode: Enabling v1_legacy_mode may prevent you from using features provided
         by the v2 API.
        :paramtype v1_legacy_mode: bool
        :keyword workspace_hub_config:
        :paramtype workspace_hub_config: ~azure.mgmt.machinelearningservices.models.WorkspaceHubConfig
        """
        super(Workspace, self).__init__(**kwargs)
        self.identity = kwargs.get('identity', None)
        self.kind = kwargs.get('kind', None)
        self.location = kwargs.get('location', None)
        self.sku = kwargs.get('sku', None)
        self.tags = kwargs.get('tags', None)
        self.allow_public_access_when_behind_vnet = kwargs.get('allow_public_access_when_behind_vnet', None)
        self.application_insights = kwargs.get('application_insights', None)
        self.associated_workspaces = kwargs.get('associated_workspaces', None)
        self.container_registries = kwargs.get('container_registries', None)
        self.container_registry = kwargs.get('container_registry', None)
        self.description = kwargs.get('description', None)
        self.discovery_url = kwargs.get('discovery_url', None)
        self.enable_data_isolation = kwargs.get('enable_data_isolation', None)
        self.encryption = kwargs.get('encryption', None)
        self.existing_workspaces = kwargs.get('existing_workspaces', None)
        self.feature_store_settings = kwargs.get('feature_store_settings', None)
        self.friendly_name = kwargs.get('friendly_name', None)
        self.hbi_workspace = kwargs.get('hbi_workspace', None)
        self.hub_resource_id = kwargs.get('hub_resource_id', None)
        self.image_build_compute = kwargs.get('image_build_compute', None)
        self.key_vault = kwargs.get('key_vault', None)
        self.key_vaults = kwargs.get('key_vaults', None)
        self.managed_network = kwargs.get('managed_network', None)
        self.ml_flow_tracking_uri = None
        self.notebook_info = None
        self.primary_user_assigned_identity = kwargs.get('primary_user_assigned_identity', None)
        self.private_endpoint_connections = None
        self.private_link_count = None
        self.provisioning_state = None
        self.public_network_access = kwargs.get('public_network_access', None)
        self.service_managed_resources_settings = kwargs.get('service_managed_resources_settings', None)
        self.service_provisioned_resource_group = None
        self.shared_private_link_resources = kwargs.get('shared_private_link_resources', None)
        self.soft_delete_retention_in_days = kwargs.get('soft_delete_retention_in_days', None)
        self.storage_account = kwargs.get('storage_account', None)
        self.storage_accounts = kwargs.get('storage_accounts', None)
        self.storage_hns_enabled = None
        self.system_datastores_auth_mode = kwargs.get('system_datastores_auth_mode', None)
        self.tenant_id = None
        self.v1_legacy_mode = kwargs.get('v1_legacy_mode', None)
        self.workspace_hub_config = kwargs.get('workspace_hub_config', None)
        self.workspace_id = None


class WorkspaceHubConfig(msrest.serialization.Model):
    """WorkspaceHubConfig.

    :ivar additional_workspace_storage_accounts:
    :vartype additional_workspace_storage_accounts: list[str]
    :ivar default_workspace_resource_group:
    :vartype default_workspace_resource_group: str
    """

    _attribute_map = {
        'additional_workspace_storage_accounts': {'key': 'additionalWorkspaceStorageAccounts', 'type': '[str]'},
        'default_workspace_resource_group': {'key': 'defaultWorkspaceResourceGroup', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword additional_workspace_storage_accounts:
        :paramtype additional_workspace_storage_accounts: list[str]
        :keyword default_workspace_resource_group:
        :paramtype default_workspace_resource_group: str
        """
        super(WorkspaceHubConfig, self).__init__(**kwargs)
        self.additional_workspace_storage_accounts = kwargs.get('additional_workspace_storage_accounts', None)
        self.default_workspace_resource_group = kwargs.get('default_workspace_resource_group', None)


class WorkspacePrivateEndpointResource(msrest.serialization.Model):
    """WorkspacePrivateEndpointResource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: e.g.
     /subscriptions/{networkSubscriptionId}/resourceGroups/{rgName}/providers/Microsoft.Network/privateEndpoints/{privateEndpointName}.
    :vartype id: str
    :ivar subnet_arm_id: The subnetId that the private endpoint is connected to.
    :vartype subnet_arm_id: str
    """

    _validation = {
        'id': {'readonly': True},
        'subnet_arm_id': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'subnet_arm_id': {'key': 'subnetArmId', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        """
        super(WorkspacePrivateEndpointResource, self).__init__(**kwargs)
        self.id = None
        self.subnet_arm_id = None
