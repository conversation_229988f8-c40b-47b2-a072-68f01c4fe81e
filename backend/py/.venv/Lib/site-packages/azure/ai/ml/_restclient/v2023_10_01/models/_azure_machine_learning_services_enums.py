# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from enum import Enum

from azure.core import CaseInsensitiveEnumMeta


class ActionType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum. Indicates the action type. "Internal" refers to actions that are for internal only APIs."""

    INTERNAL = "Internal"


class AllocationState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Allocation state of the compute. Possible values are: steady - Indicates that the compute is
    not resizing. There are no changes to the number of compute nodes in the compute in progress. A
    compute enters this state when it is created and when no operations are being performed on the
    compute to change the number of compute nodes. resizing - Indicates that the compute is
    resizing; that is, compute nodes are being added to or removed from the compute.
    """

    STEADY = "Steady"
    RESIZING = "Resizing"


class ApplicationSharingPolicy(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Policy for sharing applications on this compute instance among users of parent workspace. If
    Personal, only the creator can access applications on this compute instance. When Shared, any
    workspace user can access applications on this instance depending on his/her assigned role.
    """

    PERSONAL = "Personal"
    SHARED = "Shared"


class AssetProvisioningState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Provisioning state of registry asset."""

    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"
    CREATING = "Creating"
    UPDATING = "Updating"
    DELETING = "Deleting"


class AutoRebuildSetting(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """AutoRebuild setting for the derived image."""

    DISABLED = "Disabled"
    ON_BASE_IMAGE_UPDATE = "OnBaseImageUpdate"


class Autosave(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Auto save settings."""

    NONE = "None"
    LOCAL = "Local"
    REMOTE = "Remote"


class BatchLoggingLevel(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Log verbosity for batch inferencing.
    Increasing verbosity order for logging is : Warning, Info and Debug.
    The default value is Info.
    """

    INFO = "Info"
    WARNING = "Warning"
    DEBUG = "Debug"


class BatchOutputAction(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine how batch inferencing will handle output."""

    SUMMARY_ONLY = "SummaryOnly"
    APPEND_ROW = "AppendRow"


class BillingCurrency(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Three lettered code specifying the currency of the VM price. Example: USD."""

    USD = "USD"


class BlockedTransformers(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum for all classification models supported by AutoML."""

    TEXT_TARGET_ENCODER = "TextTargetEncoder"
    """Target encoding for text data."""
    ONE_HOT_ENCODER = "OneHotEncoder"
    """Ohe hot encoding creates a binary feature transformation."""
    CAT_TARGET_ENCODER = "CatTargetEncoder"
    """Target encoding for categorical data."""
    TF_IDF = "TfIdf"
    """Tf-Idf stands for, term-frequency times inverse document-frequency. This is a common term
    #: weighting scheme for identifying information from documents."""
    WO_E_TARGET_ENCODER = "WoETargetEncoder"
    """Weight of Evidence encoding is a technique used to encode categorical variables. It uses the
    #: natural log of the P(1)/P(0) to create weights."""
    LABEL_ENCODER = "LabelEncoder"
    """Label encoder converts labels/categorical variables in a numerical form."""
    WORD_EMBEDDING = "WordEmbedding"
    """Word embedding helps represents words or phrases as a vector, or a series of numbers."""
    NAIVE_BAYES = "NaiveBayes"
    """Naive Bayes is a classified that is used for classification of discrete features that are
    #: categorically distributed."""
    COUNT_VECTORIZER = "CountVectorizer"
    """Count Vectorizer converts a collection of text documents to a matrix of token counts."""
    HASH_ONE_HOT_ENCODER = "HashOneHotEncoder"
    """Hashing One Hot Encoder can turn categorical variables into a limited number of new features.
    #: This is often used for high-cardinality categorical features."""


class Caching(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Caching type of Data Disk."""

    NONE = "None"
    READ_ONLY = "ReadOnly"
    READ_WRITE = "ReadWrite"


class CategoricalDataDriftMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """CategoricalDataDriftMetric."""

    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    """The Jensen Shannon Distance (JSD) metric."""
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    """The Population Stability Index (PSI) metric."""
    PEARSONS_CHI_SQUARED_TEST = "PearsonsChiSquaredTest"
    """The Pearsons Chi Squared Test metric."""


class CategoricalDataQualityMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """CategoricalDataQualityMetric."""

    NULL_VALUE_RATE = "NullValueRate"
    """Calculates the rate of null values."""
    DATA_TYPE_ERROR_RATE = "DataTypeErrorRate"
    """Calculates the rate of data type errors."""
    OUT_OF_BOUNDS_RATE = "OutOfBoundsRate"
    """Calculates the rate values are out of bounds."""


class CategoricalPredictionDriftMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """CategoricalPredictionDriftMetric."""

    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    """The Jensen Shannon Distance (JSD) metric."""
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    """The Population Stability Index (PSI) metric."""
    PEARSONS_CHI_SQUARED_TEST = "PearsonsChiSquaredTest"
    """The Pearsons Chi Squared Test metric."""


class ClassificationModels(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum for all classification models supported by AutoML."""

    LOGISTIC_REGRESSION = "LogisticRegression"
    """Logistic regression is a fundamental classification technique.
    #: It belongs to the group of linear classifiers and is somewhat similar to polynomial and linear
    #: regression.
    #: Logistic regression is fast and relatively uncomplicated, and it's convenient for you to
    #: interpret the results.
    #: Although it's essentially a method for binary classification, it can also be applied to
    #: multiclass problems."""
    SGD = "SGD"
    """SGD: Stochastic gradient descent is an optimization algorithm often used in machine learning
    #: applications
    #: to find the model parameters that correspond to the best fit between predicted and actual
    #: outputs."""
    MULTINOMIAL_NAIVE_BAYES = "MultinomialNaiveBayes"
    """The multinomial Naive Bayes classifier is suitable for classification with discrete features
    #: (e.g., word counts for text classification).
    #: The multinomial distribution normally requires integer feature counts. However, in practice,
    #: fractional counts such as tf-idf may also work."""
    BERNOULLI_NAIVE_BAYES = "BernoulliNaiveBayes"
    """Naive Bayes classifier for multivariate Bernoulli models."""
    SVM = "SVM"
    """A support vector machine (SVM) is a supervised machine learning model that uses classification
    #: algorithms for two-group classification problems.
    #: After giving an SVM model sets of labeled training data for each category, they're able to
    #: categorize new text."""
    LINEAR_SVM = "LinearSVM"
    """A support vector machine (SVM) is a supervised machine learning model that uses classification
    #: algorithms for two-group classification problems.
    #: After giving an SVM model sets of labeled training data for each category, they're able to
    #: categorize new text.
    #: Linear SVM performs best when input data is linear, i.e., data can be easily classified by
    #: drawing the straight line between classified values on a plotted graph."""
    KNN = "KNN"
    """K-nearest neighbors (KNN) algorithm uses 'feature similarity' to predict the values of new
    #: datapoints
    #: which further means that the new data point will be assigned a value based on how closely it
    #: matches the points in the training set."""
    DECISION_TREE = "DecisionTree"
    """Decision Trees are a non-parametric supervised learning method used for both classification and
    #: regression tasks.
    #: The goal is to create a model that predicts the value of a target variable by learning simple
    #: decision rules inferred from the data features."""
    RANDOM_FOREST = "RandomForest"
    r"""Random forest is a supervised learning algorithm.
    #: The "forest"\  it builds, is an ensemble of decision trees, usually trained with the “bagging”\
    #: method.
    #: The general idea of the bagging method is that a combination of learning models increases the
    #: overall result."""
    EXTREME_RANDOM_TREES = "ExtremeRandomTrees"
    """Extreme Trees is an ensemble machine learning algorithm that combines the predictions from many
    #: decision trees. It is related to the widely used random forest algorithm."""
    LIGHT_GBM = "LightGBM"
    """LightGBM is a gradient boosting framework that uses tree based learning algorithms."""
    GRADIENT_BOOSTING = "GradientBoosting"
    """The technique of transiting week learners into a strong learner is called Boosting. The
    #: gradient boosting algorithm process works on this theory of execution."""
    XG_BOOST_CLASSIFIER = "XGBoostClassifier"
    """XGBoost: Extreme Gradient Boosting Algorithm. This algorithm is used for structured data where
    #: target column values can be divided into distinct class values."""


class ClassificationMultilabelPrimaryMetrics(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Primary metrics for classification multilabel tasks."""

    AUC_WEIGHTED = "AUCWeighted"
    """AUC is the Area under the curve.
    #: This metric represents arithmetic mean of the score for each class,
    #: weighted by the number of true instances in each class."""
    ACCURACY = "Accuracy"
    """Accuracy is the ratio of predictions that exactly match the true class labels."""
    NORM_MACRO_RECALL = "NormMacroRecall"
    """Normalized macro recall is recall macro-averaged and normalized, so that random
    #: performance has a score of 0, and perfect performance has a score of 1."""
    AVERAGE_PRECISION_SCORE_WEIGHTED = "AveragePrecisionScoreWeighted"
    """The arithmetic mean of the average precision score for each class, weighted by
    #: the number of true instances in each class."""
    PRECISION_SCORE_WEIGHTED = "PrecisionScoreWeighted"
    """The arithmetic mean of precision for each class, weighted by number of true instances in each
    #: class."""
    IOU = "IOU"
    """Intersection Over Union. Intersection of predictions divided by union of predictions."""


class ClassificationPrimaryMetrics(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Primary metrics for classification tasks."""

    AUC_WEIGHTED = "AUCWeighted"
    """AUC is the Area under the curve.
    #: This metric represents arithmetic mean of the score for each class,
    #: weighted by the number of true instances in each class."""
    ACCURACY = "Accuracy"
    """Accuracy is the ratio of predictions that exactly match the true class labels."""
    NORM_MACRO_RECALL = "NormMacroRecall"
    """Normalized macro recall is recall macro-averaged and normalized, so that random
    #: performance has a score of 0, and perfect performance has a score of 1."""
    AVERAGE_PRECISION_SCORE_WEIGHTED = "AveragePrecisionScoreWeighted"
    """The arithmetic mean of the average precision score for each class, weighted by
    #: the number of true instances in each class."""
    PRECISION_SCORE_WEIGHTED = "PrecisionScoreWeighted"
    """The arithmetic mean of precision for each class, weighted by number of true instances in each
    #: class."""


class ClusterPurpose(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Intended usage of the cluster."""

    FAST_PROD = "FastProd"
    DENSE_PROD = "DenseProd"
    DEV_TEST = "DevTest"


class ComputeInstanceAuthorizationType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The Compute Instance Authorization type. Available values are personal (default)."""

    PERSONAL = "personal"


class ComputeInstanceState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Current state of an ComputeInstance."""

    CREATING = "Creating"
    CREATE_FAILED = "CreateFailed"
    DELETING = "Deleting"
    RUNNING = "Running"
    RESTARTING = "Restarting"
    JOB_RUNNING = "JobRunning"
    SETTING_UP = "SettingUp"
    SETUP_FAILED = "SetupFailed"
    STARTING = "Starting"
    STOPPED = "Stopped"
    STOPPING = "Stopping"
    USER_SETTING_UP = "UserSettingUp"
    USER_SETUP_FAILED = "UserSetupFailed"
    UNKNOWN = "Unknown"
    UNUSABLE = "Unusable"


class ComputePowerAction(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The compute power action."""

    START = "Start"
    STOP = "Stop"


class ComputeRecurrenceFrequency(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to describe the frequency of a compute recurrence schedule."""

    MINUTE = "Minute"
    """Minute frequency"""
    HOUR = "Hour"
    """Hour frequency"""
    DAY = "Day"
    """Day frequency"""
    WEEK = "Week"
    """Week frequency"""
    MONTH = "Month"
    """Month frequency"""


class ComputeTriggerType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Is the trigger type recurrence or cron."""

    RECURRENCE = "Recurrence"
    CRON = "Cron"


class ComputeType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of compute."""

    AKS = "AKS"
    KUBERNETES = "Kubernetes"
    AML_COMPUTE = "AmlCompute"
    COMPUTE_INSTANCE = "ComputeInstance"
    DATA_FACTORY = "DataFactory"
    VIRTUAL_MACHINE = "VirtualMachine"
    HD_INSIGHT = "HDInsight"
    DATABRICKS = "Databricks"
    DATA_LAKE_ANALYTICS = "DataLakeAnalytics"
    SYNAPSE_SPARK = "SynapseSpark"


class ComputeWeekDay(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum of weekday."""

    MONDAY = "Monday"
    """Monday weekday"""
    TUESDAY = "Tuesday"
    """Tuesday weekday"""
    WEDNESDAY = "Wednesday"
    """Wednesday weekday"""
    THURSDAY = "Thursday"
    """Thursday weekday"""
    FRIDAY = "Friday"
    """Friday weekday"""
    SATURDAY = "Saturday"
    """Saturday weekday"""
    SUNDAY = "Sunday"
    """Sunday weekday"""


class ConnectionAuthType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Authentication type of the connection target."""

    PAT = "PAT"
    MANAGED_IDENTITY = "ManagedIdentity"
    USERNAME_PASSWORD = "UsernamePassword"
    NONE = "None"
    SAS = "SAS"


class ConnectionCategory(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Category of the connection."""

    PYTHON_FEED = "PythonFeed"
    CONTAINER_REGISTRY = "ContainerRegistry"
    GIT = "Git"


class ContainerType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ContainerType."""

    STORAGE_INITIALIZER = "StorageInitializer"
    INFERENCE_SERVER = "InferenceServer"


class CreatedByType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of identity that created the resource."""

    USER = "User"
    APPLICATION = "Application"
    MANAGED_IDENTITY = "ManagedIdentity"
    KEY = "Key"


class CredentialsType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the datastore credentials type."""

    ACCOUNT_KEY = "AccountKey"
    CERTIFICATE = "Certificate"
    NONE = "None"
    SAS = "Sas"
    SERVICE_PRINCIPAL = "ServicePrincipal"


class DataAvailabilityStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """DataAvailabilityStatus."""

    NONE = "None"
    PENDING = "Pending"
    INCOMPLETE = "Incomplete"
    COMPLETE = "Complete"


class DataReferenceCredentialType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the DataReference credentials type."""

    SAS = "SAS"
    DOCKER_CREDENTIALS = "DockerCredentials"
    MANAGED_IDENTITY = "ManagedIdentity"
    NO_CREDENTIALS = "NoCredentials"


class DatastoreType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the datastore contents type."""

    AZURE_BLOB = "AzureBlob"
    AZURE_DATA_LAKE_GEN1 = "AzureDataLakeGen1"
    AZURE_DATA_LAKE_GEN2 = "AzureDataLakeGen2"
    AZURE_FILE = "AzureFile"


class DataType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the type of data."""

    URI_FILE = "uri_file"
    URI_FOLDER = "uri_folder"
    MLTABLE = "mltable"


class DeploymentProvisioningState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Possible values for DeploymentProvisioningState."""

    CREATING = "Creating"
    DELETING = "Deleting"
    SCALING = "Scaling"
    UPDATING = "Updating"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"


class DiagnoseResultLevel(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Level of workspace setup error."""

    WARNING = "Warning"
    ERROR = "Error"
    INFORMATION = "Information"


class DistributionType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the job distribution type."""

    PY_TORCH = "PyTorch"
    TENSOR_FLOW = "TensorFlow"
    MPI = "Mpi"


class EarlyTerminationPolicyType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """EarlyTerminationPolicyType."""

    BANDIT = "Bandit"
    MEDIAN_STOPPING = "MedianStopping"
    TRUNCATION_SELECTION = "TruncationSelection"


class EgressPublicNetworkAccessType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine whether PublicNetworkAccess is Enabled or Disabled for egress of a
    deployment.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"


class EmailNotificationEnableType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the email notification type."""

    JOB_COMPLETED = "JobCompleted"
    JOB_FAILED = "JobFailed"
    JOB_CANCELLED = "JobCancelled"


class EncryptionStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Indicates whether or not the encryption is enabled for the workspace."""

    ENABLED = "Enabled"
    DISABLED = "Disabled"


class EndpointAuthMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine endpoint authentication mode."""

    AML_TOKEN = "AMLToken"
    KEY = "Key"
    AAD_TOKEN = "AADToken"


class EndpointComputeType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine endpoint compute type."""

    MANAGED = "Managed"
    KUBERNETES = "Kubernetes"
    AZURE_ML_COMPUTE = "AzureMLCompute"


class EndpointProvisioningState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """State of endpoint provisioning."""

    CREATING = "Creating"
    DELETING = "Deleting"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    UPDATING = "Updating"
    CANCELED = "Canceled"


class EndpointServiceConnectionStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Connection status of the service consumer with the service provider."""

    APPROVED = "Approved"
    PENDING = "Pending"
    REJECTED = "Rejected"
    DISCONNECTED = "Disconnected"


class EnvironmentType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Environment type is either user created or curated by Azure ML service."""

    CURATED = "Curated"
    USER_CREATED = "UserCreated"


class EnvironmentVariableType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of the Environment Variable. Possible values are: local - For local variable."""

    LOCAL = "local"


class FeatureAttributionMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """FeatureAttributionMetric."""

    NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN = "NormalizedDiscountedCumulativeGain"
    """The Normalized Discounted Cumulative Gain metric."""


class FeatureDataType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """FeatureDataType."""

    STRING = "String"
    INTEGER = "Integer"
    LONG = "Long"
    FLOAT = "Float"
    DOUBLE = "Double"
    BINARY = "Binary"
    DATETIME = "Datetime"
    BOOLEAN = "Boolean"


class FeatureImportanceMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The mode of operation for computing feature importance."""

    DISABLED = "Disabled"
    """Disables computing feature importance within a signal."""
    ENABLED = "Enabled"
    """Enables computing feature importance within a signal."""


class FeatureLags(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Flag for generating lags for the numeric features."""

    NONE = "None"
    """No feature lags generated."""
    AUTO = "Auto"
    """System auto-generates feature lags."""


class FeaturizationMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Featurization mode - determines data featurization mode."""

    AUTO = "Auto"
    """Auto mode, system performs featurization without any custom featurization inputs."""
    CUSTOM = "Custom"
    """Custom featurization."""
    OFF = "Off"
    """Featurization off. 'Forecasting' task cannot use this value."""


class ForecastHorizonMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine forecast horizon selection mode."""

    AUTO = "Auto"
    """Forecast horizon to be determined automatically."""
    CUSTOM = "Custom"
    """Use the custom forecast horizon."""


class ForecastingModels(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum for all forecasting models supported by AutoML."""

    AUTO_ARIMA = "AutoArima"
    """Auto-Autoregressive Integrated Moving Average (ARIMA) model uses time-series data and
    #: statistical analysis to interpret the data and make future predictions.
    #: This model aims to explain data by using time series data on its past values and uses linear
    #: regression to make predictions."""
    PROPHET = "Prophet"
    """Prophet is a procedure for forecasting time series data based on an additive model where
    #: non-linear trends are fit with yearly, weekly, and daily seasonality, plus holiday effects.
    #: It works best with time series that have strong seasonal effects and several seasons of
    #: historical data. Prophet is robust to missing data and shifts in the trend, and typically
    #: handles outliers well."""
    NAIVE = "Naive"
    """The Naive forecasting model makes predictions by carrying forward the latest target value for
    #: each time-series in the training data."""
    SEASONAL_NAIVE = "SeasonalNaive"
    """The Seasonal Naive forecasting model makes predictions by carrying forward the latest season of
    #: target values for each time-series in the training data."""
    AVERAGE = "Average"
    """The Average forecasting model makes predictions by carrying forward the average of the target
    #: values for each time-series in the training data."""
    SEASONAL_AVERAGE = "SeasonalAverage"
    """The Seasonal Average forecasting model makes predictions by carrying forward the average value
    #: of the latest season of data for each time-series in the training data."""
    EXPONENTIAL_SMOOTHING = "ExponentialSmoothing"
    """Exponential smoothing is a time series forecasting method for univariate data that can be
    #: extended to support data with a systematic trend or seasonal component."""
    ARIMAX = "Arimax"
    """An Autoregressive Integrated Moving Average with Explanatory Variable (ARIMAX) model can be
    #: viewed as a multiple regression model with one or more autoregressive (AR) terms and/or one or
    #: more moving average (MA) terms.
    #: This method is suitable for forecasting when data is stationary/non stationary, and
    #: multivariate with any type of data pattern, i.e., level/trend /seasonality/cyclicity."""
    TCN_FORECASTER = "TCNForecaster"
    """TCNForecaster: Temporal Convolutional Networks Forecaster. //TODO: Ask forecasting team for
    #: brief intro."""
    ELASTIC_NET = "ElasticNet"
    """Elastic net is a popular type of regularized linear regression that combines two popular
    #: penalties, specifically the L1 and L2 penalty functions."""
    GRADIENT_BOOSTING = "GradientBoosting"
    """The technique of transiting week learners into a strong learner is called Boosting. The
    #: gradient boosting algorithm process works on this theory of execution."""
    DECISION_TREE = "DecisionTree"
    """Decision Trees are a non-parametric supervised learning method used for both classification and
    #: regression tasks.
    #: The goal is to create a model that predicts the value of a target variable by learning simple
    #: decision rules inferred from the data features."""
    KNN = "KNN"
    """K-nearest neighbors (KNN) algorithm uses 'feature similarity' to predict the values of new
    #: datapoints
    #: which further means that the new data point will be assigned a value based on how closely it
    #: matches the points in the training set."""
    LASSO_LARS = "LassoLars"
    """Lasso model fit with Least Angle Regression a.k.a. Lars. It is a Linear Model trained with an
    #: L1 prior as regularizer."""
    SGD = "SGD"
    """SGD: Stochastic gradient descent is an optimization algorithm often used in machine learning
    #: applications
    #: to find the model parameters that correspond to the best fit between predicted and actual
    #: outputs.
    #: It's an inexact but powerful technique."""
    RANDOM_FOREST = "RandomForest"
    """Random forest is a supervised learning algorithm.
    #: The "forest" it builds, is an ensemble of decision trees, usually trained with the “bagging”
    #: method.
    #: The general idea of the bagging method is that a combination of learning models increases the
    #: overall result."""
    EXTREME_RANDOM_TREES = "ExtremeRandomTrees"
    """Extreme Trees is an ensemble machine learning algorithm that combines the predictions from many
    #: decision trees. It is related to the widely used random forest algorithm."""
    LIGHT_GBM = "LightGBM"
    """LightGBM is a gradient boosting framework that uses tree based learning algorithms."""
    XG_BOOST_REGRESSOR = "XGBoostRegressor"
    """XGBoostRegressor: Extreme Gradient Boosting Regressor is a supervised machine learning model
    #: using ensemble of base learners."""


class ForecastingPrimaryMetrics(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Primary metrics for Forecasting task."""

    SPEARMAN_CORRELATION = "SpearmanCorrelation"
    """The Spearman's rank coefficient of correlation is a non-parametric measure of rank correlation."""
    NORMALIZED_ROOT_MEAN_SQUARED_ERROR = "NormalizedRootMeanSquaredError"
    """The Normalized Root Mean Squared Error (NRMSE) the RMSE facilitates the comparison between
    #: models with different scales."""
    R2_SCORE = "R2Score"
    """The R2 score is one of the performance evaluation measures for forecasting-based machine
    #: learning models."""
    NORMALIZED_MEAN_ABSOLUTE_ERROR = "NormalizedMeanAbsoluteError"
    """The Normalized Mean Absolute Error (NMAE) is a validation metric to compare the Mean Absolute
    #: Error (MAE) of (time) series with different scales."""


class Goal(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Defines supported metric goals for hyperparameter tuning."""

    MINIMIZE = "Minimize"
    MAXIMIZE = "Maximize"


class IdentityConfigurationType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine identity framework."""

    MANAGED = "Managed"
    AML_TOKEN = "AMLToken"
    USER_IDENTITY = "UserIdentity"


class ImageType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of the image. Possible values are: docker - For docker images. azureml - For AzureML
    images.
    """

    DOCKER = "docker"
    AZUREML = "azureml"


class InputDeliveryMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the input data delivery mode."""

    READ_ONLY_MOUNT = "ReadOnlyMount"
    READ_WRITE_MOUNT = "ReadWriteMount"
    DOWNLOAD = "Download"
    DIRECT = "Direct"
    EVAL_MOUNT = "EvalMount"
    EVAL_DOWNLOAD = "EvalDownload"


class InstanceSegmentationPrimaryMetrics(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Primary metrics for InstanceSegmentation tasks."""

    MEAN_AVERAGE_PRECISION = "MeanAveragePrecision"
    """Mean Average Precision (MAP) is the average of AP (Average Precision).
    #: AP is calculated for each class and averaged to get the MAP."""


class IsolationMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Isolation mode for the managed network of a machine learning workspace."""

    DISABLED = "Disabled"
    ALLOW_INTERNET_OUTBOUND = "AllowInternetOutbound"
    ALLOW_ONLY_APPROVED_OUTBOUND = "AllowOnlyApprovedOutbound"


class JobInputType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the Job Input Type."""

    LITERAL = "literal"
    URI_FILE = "uri_file"
    URI_FOLDER = "uri_folder"
    MLTABLE = "mltable"
    CUSTOM_MODEL = "custom_model"
    MLFLOW_MODEL = "mlflow_model"
    TRITON_MODEL = "triton_model"


class JobLimitsType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """JobLimitsType."""

    COMMAND = "Command"
    SWEEP = "Sweep"


class JobOutputType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the Job Output Type."""

    URI_FILE = "uri_file"
    URI_FOLDER = "uri_folder"
    MLTABLE = "mltable"
    CUSTOM_MODEL = "custom_model"
    MLFLOW_MODEL = "mlflow_model"
    TRITON_MODEL = "triton_model"


class JobStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The status of a job."""

    NOT_STARTED = "NotStarted"
    """Run hasn't started yet."""
    STARTING = "Starting"
    """Run has started. The user has a run ID."""
    PROVISIONING = "Provisioning"
    """(Not used currently) It will be used if ES is creating the compute target."""
    PREPARING = "Preparing"
    """The run environment is being prepared."""
    QUEUED = "Queued"
    """The job is queued in the compute target. For example, in BatchAI the job is in queued state,
    #: while waiting for all required nodes to be ready."""
    RUNNING = "Running"
    """The job started to run in the compute target."""
    FINALIZING = "Finalizing"
    """Job is completed in the target. It is in output collection state now."""
    CANCEL_REQUESTED = "CancelRequested"
    """Cancellation has been requested for the job."""
    COMPLETED = "Completed"
    """Job completed successfully. This reflects that both the job itself and output collection states
    #: completed successfully"""
    FAILED = "Failed"
    """Job failed."""
    CANCELED = "Canceled"
    """Following cancellation request, the job is now successfully canceled."""
    NOT_RESPONDING = "NotResponding"
    """When heartbeat is enabled, if the run isn't updating any information to RunHistory then the run
    #: goes to NotResponding state.
    #: NotResponding is the only state that is exempt from strict transition orders. A run can go from
    #: NotResponding to any of the previous states."""
    PAUSED = "Paused"
    """The job is paused by users. Some adjustment to labeling jobs can be made only in paused state."""
    UNKNOWN = "Unknown"
    """Default job status if not mapped to all other statuses"""


class JobTier(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the job tier."""

    NULL = "Null"
    SPOT = "Spot"
    BASIC = "Basic"
    STANDARD = "Standard"
    PREMIUM = "Premium"


class JobType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the type of job."""

    AUTO_ML = "AutoML"
    COMMAND = "Command"
    SWEEP = "Sweep"
    PIPELINE = "Pipeline"


class KeyType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """KeyType."""

    PRIMARY = "Primary"
    SECONDARY = "Secondary"


class LearningRateScheduler(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Learning rate scheduler enum."""

    NONE = "None"
    """No learning rate scheduler selected."""
    WARMUP_COSINE = "WarmupCosine"
    """Cosine Annealing With Warmup."""
    STEP = "Step"
    """Step learning rate scheduler."""


class ListViewType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ListViewType."""

    ACTIVE_ONLY = "ActiveOnly"
    ARCHIVED_ONLY = "ArchivedOnly"
    ALL = "All"


class LoadBalancerType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Load Balancer Type."""

    PUBLIC_IP = "PublicIp"
    INTERNAL_LOAD_BALANCER = "InternalLoadBalancer"


class LogVerbosity(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum for setting log verbosity."""

    NOT_SET = "NotSet"
    """No logs emitted."""
    DEBUG = "Debug"
    """Debug and above log statements logged."""
    INFO = "Info"
    """Info and above log statements logged."""
    WARNING = "Warning"
    """Warning and above log statements logged."""
    ERROR = "Error"
    """Error and above log statements logged."""
    CRITICAL = "Critical"
    """Only critical statements logged."""


class ManagedNetworkStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Status for the managed network of a machine learning workspace."""

    INACTIVE = "Inactive"
    ACTIVE = "Active"


class ManagedServiceIdentityType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of managed service identity (where both SystemAssigned and UserAssigned types are
    allowed).
    """

    NONE = "None"
    SYSTEM_ASSIGNED = "SystemAssigned"
    USER_ASSIGNED = "UserAssigned"
    SYSTEM_ASSIGNED_USER_ASSIGNED = "SystemAssigned,UserAssigned"


class MaterializationStoreType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """MaterializationStoreType."""

    NONE = "None"
    ONLINE = "Online"
    OFFLINE = "Offline"
    ONLINE_AND_OFFLINE = "OnlineAndOffline"


class ModelSize(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Image model size."""

    NONE = "None"
    """No value selected."""
    SMALL = "Small"
    """Small size."""
    MEDIUM = "Medium"
    """Medium size."""
    LARGE = "Large"
    """Large size."""
    EXTRA_LARGE = "ExtraLarge"
    """Extra large size."""


class ModelTaskType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Model task type enum."""

    CLASSIFICATION = "Classification"
    REGRESSION = "Regression"


class MonitorComputeIdentityType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Monitor compute identity type enum."""

    AML_TOKEN = "AmlToken"
    """Authenticates through user's AML token."""
    MANAGED_IDENTITY = "ManagedIdentity"
    """Authenticates through a user-provided managed identity."""


class MonitorComputeType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Monitor compute type enum."""

    SERVERLESS_SPARK = "ServerlessSpark"
    """Serverless Spark compute."""


class MonitoringFeatureDataType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """MonitoringFeatureDataType."""

    NUMERICAL = "Numerical"
    """Used for features of numerical data type."""
    CATEGORICAL = "Categorical"
    """Used for features of categorical data type."""


class MonitoringFeatureFilterType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """MonitoringFeatureFilterType."""

    ALL_FEATURES = "AllFeatures"
    """Includes all features."""
    TOP_N_BY_ATTRIBUTION = "TopNByAttribution"
    """Only includes the top contributing features, measured by feature attribution."""
    FEATURE_SUBSET = "FeatureSubset"
    """Includes a user-defined subset of features."""


class MonitoringInputDataType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Monitoring input data type enum."""

    STATIC = "Static"
    """An input data with a fixed window size."""
    ROLLING = "Rolling"
    """An input data which rolls relatively to the monitor's current run time."""
    FIXED = "Fixed"
    """An input data with tabular format which doesn't require preprocessing."""


class MonitoringNotificationType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """MonitoringNotificationType."""

    AML_NOTIFICATION = "AmlNotification"
    """Enables email notifications through AML notifications."""


class MonitoringSignalType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """MonitoringSignalType."""

    DATA_DRIFT = "DataDrift"
    """Tracks model input data distribution change, comparing against training data or past production
    #: data."""
    PREDICTION_DRIFT = "PredictionDrift"
    """Tracks prediction result data distribution change, comparing against validation/test label data
    #: or past production data."""
    DATA_QUALITY = "DataQuality"
    """Tracks model input data integrity."""
    FEATURE_ATTRIBUTION_DRIFT = "FeatureAttributionDrift"
    """Tracks feature importance change in production, comparing against feature importance at
    #: training time."""
    CUSTOM = "Custom"
    """Tracks a custom signal provided by users."""


class MountAction(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Mount Action."""

    MOUNT = "Mount"
    UNMOUNT = "Unmount"


class MountState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Mount state."""

    MOUNT_REQUESTED = "MountRequested"
    MOUNTED = "Mounted"
    MOUNT_FAILED = "MountFailed"
    UNMOUNT_REQUESTED = "UnmountRequested"
    UNMOUNT_FAILED = "UnmountFailed"
    UNMOUNTED = "Unmounted"


class NCrossValidationsMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Determines how N-Cross validations value is determined."""

    AUTO = "Auto"
    """Determine N-Cross validations value automatically. Supported only for 'Forecasting' AutoML
    #: task."""
    CUSTOM = "Custom"
    """Use custom N-Cross validations value."""


class Network(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """network of this container."""

    BRIDGE = "Bridge"
    HOST = "Host"


class NodeState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """State of the compute node. Values are idle, running, preparing, unusable, leaving and
    preempted.
    """

    IDLE = "idle"
    RUNNING = "running"
    PREPARING = "preparing"
    UNUSABLE = "unusable"
    LEAVING = "leaving"
    PREEMPTED = "preempted"


class NodesValueType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The enumerated types for the nodes value."""

    ALL = "All"


class NumericalDataDriftMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """NumericalDataDriftMetric."""

    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    """The Jensen Shannon Distance (JSD) metric."""
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    """The Population Stability Index (PSI) metric."""
    NORMALIZED_WASSERSTEIN_DISTANCE = "NormalizedWassersteinDistance"
    """The Normalized Wasserstein Distance metric."""
    TWO_SAMPLE_KOLMOGOROV_SMIRNOV_TEST = "TwoSampleKolmogorovSmirnovTest"
    """The Two Sample Kolmogorov-Smirnov Test (two-sample K–S) metric."""


class NumericalDataQualityMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """NumericalDataQualityMetric."""

    NULL_VALUE_RATE = "NullValueRate"
    """Calculates the rate of null values."""
    DATA_TYPE_ERROR_RATE = "DataTypeErrorRate"
    """Calculates the rate of data type errors."""
    OUT_OF_BOUNDS_RATE = "OutOfBoundsRate"
    """Calculates the rate values are out of bounds."""


class NumericalPredictionDriftMetric(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """NumericalPredictionDriftMetric."""

    JENSEN_SHANNON_DISTANCE = "JensenShannonDistance"
    """The Jensen Shannon Distance (JSD) metric."""
    POPULATION_STABILITY_INDEX = "PopulationStabilityIndex"
    """The Population Stability Index (PSI) metric."""
    NORMALIZED_WASSERSTEIN_DISTANCE = "NormalizedWassersteinDistance"
    """The Normalized Wasserstein Distance metric."""
    TWO_SAMPLE_KOLMOGOROV_SMIRNOV_TEST = "TwoSampleKolmogorovSmirnovTest"
    """The Two Sample Kolmogorov-Smirnov Test (two-sample K–S) metric."""


class ObjectDetectionPrimaryMetrics(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Primary metrics for Image ObjectDetection task."""

    MEAN_AVERAGE_PRECISION = "MeanAveragePrecision"
    """Mean Average Precision (MAP) is the average of AP (Average Precision).
    #: AP is calculated for each class and averaged to get the MAP."""


class OperatingSystemType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of operating system."""

    LINUX = "Linux"
    WINDOWS = "Windows"


class OperationName(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Name of the last operation."""

    CREATE = "Create"
    START = "Start"
    STOP = "Stop"
    RESTART = "Restart"
    REIMAGE = "Reimage"
    DELETE = "Delete"


class OperationStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Operation status."""

    IN_PROGRESS = "InProgress"
    SUCCEEDED = "Succeeded"
    CREATE_FAILED = "CreateFailed"
    START_FAILED = "StartFailed"
    STOP_FAILED = "StopFailed"
    RESTART_FAILED = "RestartFailed"
    REIMAGE_FAILED = "ReimageFailed"
    DELETE_FAILED = "DeleteFailed"


class OperationTrigger(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Trigger of operation."""

    USER = "User"
    SCHEDULE = "Schedule"
    IDLE_SHUTDOWN = "IdleShutdown"


class OrderString(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """OrderString."""

    CREATED_AT_DESC = "CreatedAtDesc"
    CREATED_AT_ASC = "CreatedAtAsc"
    UPDATED_AT_DESC = "UpdatedAtDesc"
    UPDATED_AT_ASC = "UpdatedAtAsc"


class Origin(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The intended executor of the operation; as in Resource Based Access Control (RBAC) and audit
    logs UX. Default value is "user,system".
    """

    USER = "user"
    SYSTEM = "system"
    USER_SYSTEM = "user,system"


class OsType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Compute OS Type."""

    LINUX = "Linux"
    WINDOWS = "Windows"


class OutputDeliveryMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Output data delivery mode enums."""

    READ_WRITE_MOUNT = "ReadWriteMount"
    UPLOAD = "Upload"


class PendingUploadCredentialType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the PendingUpload credentials type."""

    SAS = "SAS"


class PendingUploadType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of storage to use for the pending upload location."""

    NONE = "None"
    TEMPORARY_BLOB_REFERENCE = "TemporaryBlobReference"


class PrivateEndpointConnectionProvisioningState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The current provisioning state."""

    SUCCEEDED = "Succeeded"
    CREATING = "Creating"
    DELETING = "Deleting"
    FAILED = "Failed"


class PrivateEndpointServiceConnectionStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The private endpoint connection status."""

    PENDING = "Pending"
    APPROVED = "Approved"
    REJECTED = "Rejected"
    DISCONNECTED = "Disconnected"
    TIMEOUT = "Timeout"


class Protocol(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Protocol over which communication will happen over this endpoint."""

    TCP = "tcp"
    UDP = "udp"
    HTTP = "http"


class ProvisioningState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The current deployment state of workspace resource. The provisioningState is to indicate states
    for resource provisioning.
    """

    UNKNOWN = "Unknown"
    UPDATING = "Updating"
    CREATING = "Creating"
    DELETING = "Deleting"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"


class ProvisioningStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The current deployment state of schedule."""

    COMPLETED = "Completed"
    PROVISIONING = "Provisioning"
    FAILED = "Failed"


class PublicNetworkAccess(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Whether requests from Public Network are allowed."""

    ENABLED = "Enabled"
    DISABLED = "Disabled"


class PublicNetworkAccessType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine whether PublicNetworkAccess is Enabled or Disabled."""

    ENABLED = "Enabled"
    DISABLED = "Disabled"


class QuotaUnit(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """An enum describing the unit of quota measurement."""

    COUNT = "Count"


class RandomSamplingAlgorithmRule(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The specific type of random algorithm."""

    RANDOM = "Random"
    SOBOL = "Sobol"


class RecurrenceFrequency(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to describe the frequency of a recurrence schedule."""

    MINUTE = "Minute"
    """Minute frequency"""
    HOUR = "Hour"
    """Hour frequency"""
    DAY = "Day"
    """Day frequency"""
    WEEK = "Week"
    """Week frequency"""
    MONTH = "Month"
    """Month frequency"""


class ReferenceType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine which reference method to use for an asset."""

    ID = "Id"
    DATA_PATH = "DataPath"
    OUTPUT_PATH = "OutputPath"


class RegressionModels(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum for all Regression models supported by AutoML."""

    ELASTIC_NET = "ElasticNet"
    """Elastic net is a popular type of regularized linear regression that combines two popular
    #: penalties, specifically the L1 and L2 penalty functions."""
    GRADIENT_BOOSTING = "GradientBoosting"
    """The technique of transiting week learners into a strong learner is called Boosting. The
    #: gradient boosting algorithm process works on this theory of execution."""
    DECISION_TREE = "DecisionTree"
    """Decision Trees are a non-parametric supervised learning method used for both classification and
    #: regression tasks.
    #: The goal is to create a model that predicts the value of a target variable by learning simple
    #: decision rules inferred from the data features."""
    KNN = "KNN"
    """K-nearest neighbors (KNN) algorithm uses 'feature similarity' to predict the values of new
    #: datapoints
    #: which further means that the new data point will be assigned a value based on how closely it
    #: matches the points in the training set."""
    LASSO_LARS = "LassoLars"
    """Lasso model fit with Least Angle Regression a.k.a. Lars. It is a Linear Model trained with an
    #: L1 prior as regularizer."""
    SGD = "SGD"
    """SGD: Stochastic gradient descent is an optimization algorithm often used in machine learning
    #: applications
    #: to find the model parameters that correspond to the best fit between predicted and actual
    #: outputs.
    #: It's an inexact but powerful technique."""
    RANDOM_FOREST = "RandomForest"
    r"""Random forest is a supervised learning algorithm.
    #: The "forest"\  it builds, is an ensemble of decision trees, usually trained with the “bagging”\
    #: method.
    #: The general idea of the bagging method is that a combination of learning models increases the
    #: overall result."""
    EXTREME_RANDOM_TREES = "ExtremeRandomTrees"
    """Extreme Trees is an ensemble machine learning algorithm that combines the predictions from many
    #: decision trees. It is related to the widely used random forest algorithm."""
    LIGHT_GBM = "LightGBM"
    """LightGBM is a gradient boosting framework that uses tree based learning algorithms."""
    XG_BOOST_REGRESSOR = "XGBoostRegressor"
    """XGBoostRegressor: Extreme Gradient Boosting Regressor is a supervised machine learning model
    #: using ensemble of base learners."""


class RegressionPrimaryMetrics(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Primary metrics for Regression task."""

    SPEARMAN_CORRELATION = "SpearmanCorrelation"
    """The Spearman's rank coefficient of correlation is a nonparametric measure of rank correlation."""
    NORMALIZED_ROOT_MEAN_SQUARED_ERROR = "NormalizedRootMeanSquaredError"
    """The Normalized Root Mean Squared Error (NRMSE) the RMSE facilitates the comparison between
    #: models with different scales."""
    R2_SCORE = "R2Score"
    """The R2 score is one of the performance evaluation measures for forecasting-based machine
    #: learning models."""
    NORMALIZED_MEAN_ABSOLUTE_ERROR = "NormalizedMeanAbsoluteError"
    """The Normalized Mean Absolute Error (NMAE) is a validation metric to compare the Mean Absolute
    #: Error (MAE) of (time) series with different scales."""


class RemoteLoginPortPublicAccess(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """State of the public SSH port. Possible values are: Disabled - Indicates that the public ssh
    port is closed on all nodes of the cluster. Enabled - Indicates that the public ssh port is
    open on all nodes of the cluster. NotSpecified - Indicates that the public ssh port is closed
    on all nodes of the cluster if VNet is defined, else is open all public nodes. It can be
    default only during cluster creation time, after creation it will be either enabled or
    disabled.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"
    NOT_SPECIFIED = "NotSpecified"


class RuleAction(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The action enum for networking rule."""

    ALLOW = "Allow"
    DENY = "Deny"


class RuleCategory(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Category of a managed network Outbound Rule of a machine learning workspace."""

    REQUIRED = "Required"
    RECOMMENDED = "Recommended"
    USER_DEFINED = "UserDefined"


class RuleStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of a managed network Outbound Rule of a machine learning workspace."""

    INACTIVE = "Inactive"
    ACTIVE = "Active"


class RuleType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of a managed network Outbound Rule of a machine learning workspace."""

    FQDN = "FQDN"
    PRIVATE_ENDPOINT = "PrivateEndpoint"
    SERVICE_TAG = "ServiceTag"


class SamplingAlgorithmType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """SamplingAlgorithmType."""

    GRID = "Grid"
    RANDOM = "Random"
    BAYESIAN = "Bayesian"


class ScaleType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ScaleType."""

    DEFAULT = "Default"
    TARGET_UTILIZATION = "TargetUtilization"


class ScheduleActionType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ScheduleActionType."""

    CREATE_JOB = "CreateJob"
    INVOKE_BATCH_ENDPOINT = "InvokeBatchEndpoint"
    CREATE_MONITOR = "CreateMonitor"


class ScheduleListViewType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ScheduleListViewType."""

    ENABLED_ONLY = "EnabledOnly"
    DISABLED_ONLY = "DisabledOnly"
    ALL = "All"


class ScheduleProvisioningState(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The current deployment state of schedule."""

    COMPLETED = "Completed"
    PROVISIONING = "Provisioning"
    FAILED = "Failed"


class ScheduleProvisioningStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ScheduleProvisioningStatus."""

    CREATING = "Creating"
    UPDATING = "Updating"
    DELETING = "Deleting"
    SUCCEEDED = "Succeeded"
    FAILED = "Failed"
    CANCELED = "Canceled"


class ScheduleStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Is the schedule enabled or disabled?."""

    ENABLED = "Enabled"
    DISABLED = "Disabled"


class SeasonalityMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Forecasting seasonality mode."""

    AUTO = "Auto"
    """Seasonality to be determined automatically."""
    CUSTOM = "Custom"
    """Use the custom seasonality value."""


class SecretsType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the datastore secrets type."""

    ACCOUNT_KEY = "AccountKey"
    CERTIFICATE = "Certificate"
    SAS = "Sas"
    SERVICE_PRINCIPAL = "ServicePrincipal"


class ServiceDataAccessAuthIdentity(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """ServiceDataAccessAuthIdentity."""

    NONE = "None"
    """Do not use any identity for service data access."""
    WORKSPACE_SYSTEM_ASSIGNED_IDENTITY = "WorkspaceSystemAssignedIdentity"
    """Use the system assigned managed identity of the Workspace to authenticate service data access."""
    WORKSPACE_USER_ASSIGNED_IDENTITY = "WorkspaceUserAssignedIdentity"
    """Use the user assigned managed identity of the Workspace to authenticate service data access."""


class ShortSeriesHandlingConfiguration(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The parameter defining how if AutoML should handle short time series."""

    NONE = "None"
    """Represents no/null value."""
    AUTO = "Auto"
    """Short series will be padded if there are no long series, otherwise short series will be
    #: dropped."""
    PAD = "Pad"
    """All the short series will be padded."""
    DROP = "Drop"
    """All the short series will be dropped."""


class SkuScaleType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Node scaling setting for the compute sku."""

    AUTOMATIC = "Automatic"
    """Automatically scales node count."""
    MANUAL = "Manual"
    """Node count scaled upon user request."""
    NONE = "None"
    """Fixed set of nodes."""


class SkuTier(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """This field is required to be implemented by the Resource Provider if the service has more than
    one tier, but is not required on a PUT.
    """

    FREE = "Free"
    BASIC = "Basic"
    STANDARD = "Standard"
    PREMIUM = "Premium"


class SourceType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Data source type."""

    DATASET = "Dataset"
    DATASTORE = "Datastore"
    URI = "URI"


class SshPublicAccess(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """State of the public SSH port. Possible values are: Disabled - Indicates that the public ssh
    port is closed on this instance. Enabled - Indicates that the public ssh port is open and
    accessible according to the VNet/subnet policy if applicable.
    """

    ENABLED = "Enabled"
    DISABLED = "Disabled"


class SslConfigStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enable or disable ssl for scoring."""

    DISABLED = "Disabled"
    ENABLED = "Enabled"
    AUTO = "Auto"


class StackMetaLearnerType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The meta-learner is a model trained on the output of the individual heterogeneous models.
    Default meta-learners are LogisticRegression for classification tasks (or LogisticRegressionCV
    if cross-validation is enabled) and ElasticNet for regression/forecasting tasks (or
    ElasticNetCV if cross-validation is enabled).
    This parameter can be one of the following strings: LogisticRegression, LogisticRegressionCV,
    LightGBMClassifier, ElasticNet, ElasticNetCV, LightGBMRegressor, or LinearRegression.
    """

    NONE = "None"
    LOGISTIC_REGRESSION = "LogisticRegression"
    """Default meta-learners are LogisticRegression for classification tasks."""
    LOGISTIC_REGRESSION_CV = "LogisticRegressionCV"
    """Default meta-learners are LogisticRegression for classification task when CV is on."""
    LIGHT_GBM_CLASSIFIER = "LightGBMClassifier"
    ELASTIC_NET = "ElasticNet"
    """Default meta-learners are LogisticRegression for regression task."""
    ELASTIC_NET_CV = "ElasticNetCV"
    """Default meta-learners are LogisticRegression for regression task when CV is on."""
    LIGHT_GBM_REGRESSOR = "LightGBMRegressor"
    LINEAR_REGRESSION = "LinearRegression"


class Status(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Status of update workspace quota."""

    UNDEFINED = "Undefined"
    SUCCESS = "Success"
    FAILURE = "Failure"
    INVALID_QUOTA_BELOW_CLUSTER_MINIMUM = "InvalidQuotaBelowClusterMinimum"
    INVALID_QUOTA_EXCEEDS_SUBSCRIPTION_LIMIT = "InvalidQuotaExceedsSubscriptionLimit"
    INVALID_VM_FAMILY_NAME = "InvalidVMFamilyName"
    OPERATION_NOT_SUPPORTED_FOR_SKU = "OperationNotSupportedForSku"
    OPERATION_NOT_ENABLED_FOR_REGION = "OperationNotEnabledForRegion"


class StochasticOptimizer(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Stochastic optimizer for image models."""

    NONE = "None"
    """No optimizer selected."""
    SGD = "Sgd"
    """Stochastic Gradient Descent optimizer."""
    ADAM = "Adam"
    """Adam is algorithm the optimizes stochastic objective functions based on adaptive estimates of
    #: moments"""
    ADAMW = "Adamw"
    """AdamW is a variant of the optimizer Adam that has an improved implementation of weight decay."""


class StorageAccountType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """type of this storage account."""

    STANDARD_LRS = "Standard_LRS"
    PREMIUM_LRS = "Premium_LRS"


class TargetAggregationFunction(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Target aggregate function."""

    NONE = "None"
    """Represent no value set."""
    SUM = "Sum"
    MAX = "Max"
    MIN = "Min"
    MEAN = "Mean"


class TargetLagsMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Target lags selection modes."""

    AUTO = "Auto"
    """Target lags to be determined automatically."""
    CUSTOM = "Custom"
    """Use the custom target lags."""


class TargetRollingWindowSizeMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Target rolling windows size mode."""

    AUTO = "Auto"
    """Determine rolling windows size automatically."""
    CUSTOM = "Custom"
    """Use the specified rolling window size."""


class TaskType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """AutoMLJob Task type."""

    CLASSIFICATION = "Classification"
    """Classification in machine learning and statistics is a supervised learning approach in which
    #: the computer program learns from the data given to it and make new observations or
    #: classifications."""
    REGRESSION = "Regression"
    """Regression means to predict the value using the input data. Regression models are used to
    #: predict a continuous value."""
    FORECASTING = "Forecasting"
    """Forecasting is a special kind of regression task that deals with time-series data and creates
    #: forecasting model
    #: that can be used to predict the near future values based on the inputs."""
    IMAGE_CLASSIFICATION = "ImageClassification"
    """Image Classification. Multi-class image classification is used when an image is classified with
    #: only a single label
    #: from a set of classes - e.g. each image is classified as either an image of a 'cat' or a 'dog'
    #: or a 'duck'."""
    IMAGE_CLASSIFICATION_MULTILABEL = "ImageClassificationMultilabel"
    """Image Classification Multilabel. Multi-label image classification is used when an image could
    #: have one or more labels
    #: from a set of labels - e.g. an image could be labeled with both 'cat' and 'dog'."""
    IMAGE_OBJECT_DETECTION = "ImageObjectDetection"
    """Image Object Detection. Object detection is used to identify objects in an image and locate
    #: each object with a
    #: bounding box e.g. locate all dogs and cats in an image and draw a bounding box around each."""
    IMAGE_INSTANCE_SEGMENTATION = "ImageInstanceSegmentation"
    """Image Instance Segmentation. Instance segmentation is used to identify objects in an image at
    #: the pixel level,
    #: drawing a polygon around each object in the image."""
    TEXT_CLASSIFICATION = "TextClassification"
    """Text classification (also known as text tagging or text categorization) is the process of
    #: sorting texts into categories.
    #: Categories are mutually exclusive."""
    TEXT_CLASSIFICATION_MULTILABEL = "TextClassificationMultilabel"
    """Multilabel classification task assigns each sample to a group (zero or more) of target labels."""
    TEXT_NER = "TextNER"
    """Text Named Entity Recognition a.k.a. TextNER.
    #: Named Entity Recognition (NER) is the ability to take free-form text and identify the
    #: occurrences of entities such as people, locations, organizations, and more."""


class TriggerType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """TriggerType."""

    RECURRENCE = "Recurrence"
    CRON = "Cron"


class UnderlyingResourceAction(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """UnderlyingResourceAction."""

    DELETE = "Delete"
    DETACH = "Detach"


class UnitOfMeasure(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The unit of time measurement for the specified VM price. Example: OneHour."""

    ONE_HOUR = "OneHour"


class UsageUnit(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """An enum describing the unit of usage measurement."""

    COUNT = "Count"


class UseStl(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Configure STL Decomposition of the time-series target column."""

    NONE = "None"
    """No stl decomposition."""
    SEASON = "Season"
    SEASON_TREND = "SeasonTrend"


class ValidationMetricType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Metric computation method to use for validation metrics in image tasks."""

    NONE = "None"
    """No metric."""
    COCO = "Coco"
    """Coco metric."""
    VOC = "Voc"
    """Voc metric."""
    COCO_VOC = "CocoVoc"
    """CocoVoc metric."""


class ValueFormat(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """format for the workspace connection value."""

    JSON = "JSON"


class VMPriceOSType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Operating system type used by the VM."""

    LINUX = "Linux"
    WINDOWS = "Windows"


class VmPriority(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Virtual Machine priority."""

    DEDICATED = "Dedicated"
    LOW_PRIORITY = "LowPriority"


class VMTier(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of the VM."""

    STANDARD = "Standard"
    LOW_PRIORITY = "LowPriority"
    SPOT = "Spot"


class VolumeDefinitionType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of Volume Definition. Possible Values: bind,volume,tmpfs,npipe."""

    BIND = "bind"
    VOLUME = "volume"
    TMPFS = "tmpfs"
    NPIPE = "npipe"


class WebhookType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum to determine the webhook callback service type."""

    AZURE_DEV_OPS = "AzureDevOps"


class WeekDay(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Enum of weekday."""

    MONDAY = "Monday"
    """Monday weekday"""
    TUESDAY = "Tuesday"
    """Tuesday weekday"""
    WEDNESDAY = "Wednesday"
    """Wednesday weekday"""
    THURSDAY = "Thursday"
    """Thursday weekday"""
    FRIDAY = "Friday"
    """Friday weekday"""
    SATURDAY = "Saturday"
    """Saturday weekday"""
    SUNDAY = "Sunday"
    """Sunday weekday"""
