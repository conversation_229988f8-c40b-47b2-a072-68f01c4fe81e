# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

try:
    from ._models_py3 import AKS
    from ._models_py3 import AKSSchema
    from ._models_py3 import AKSSchemaProperties
    from ._models_py3 import AccessKeyAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import AccountKeyDatastoreCredentials
    from ._models_py3 import AccountKeyDatastoreSecrets
    from ._models_py3 import AcrDetails
    from ._models_py3 import ActualCapacityInfo
    from ._models_py3 import AksComputeSecrets
    from ._models_py3 import AksComputeSecretsProperties
    from ._models_py3 import AksNetworkingConfiguration
    from ._models_py3 import AllFeatures
    from ._models_py3 import AllNodes
    from ._models_py3 import AmlCompute
    from ._models_py3 import AmlComputeNodeInformation
    from ._models_py3 import AmlComputeNodesInformation
    from ._models_py3 import AmlComputeProperties
    from ._models_py3 import AmlComputeSchema
    from ._models_py3 import AmlToken
    from ._models_py3 import AmlTokenComputeIdentity
    from ._models_py3 import AmlUserFeature
    from ._models_py3 import ApiKeyAuthWorkspaceConnectionProperties
    from ._models_py3 import ArmResourceId
    from ._models_py3 import AssetBase
    from ._models_py3 import AssetContainer
    from ._models_py3 import AssetJobInput
    from ._models_py3 import AssetJobOutput
    from ._models_py3 import AssetReferenceBase
    from ._models_py3 import AssignedUser
    from ._models_py3 import AutoDeleteSetting
    from ._models_py3 import AutoForecastHorizon
    from ._models_py3 import AutoMLJob
    from ._models_py3 import AutoMLVertical
    from ._models_py3 import AutoNCrossValidations
    from ._models_py3 import AutoPauseProperties
    from ._models_py3 import AutoScaleProperties
    from ._models_py3 import AutoSeasonality
    from ._models_py3 import AutoTargetLags
    from ._models_py3 import AutoTargetRollingWindowSize
    from ._models_py3 import AutologgerSettings
    from ._models_py3 import AzureBlobDatastore
    from ._models_py3 import AzureDataLakeGen1Datastore
    from ._models_py3 import AzureDataLakeGen2Datastore
    from ._models_py3 import AzureDatastore
    from ._models_py3 import AzureDevOpsWebhook
    from ._models_py3 import AzureFileDatastore
    from ._models_py3 import AzureMLBatchInferencingServer
    from ._models_py3 import AzureMLOnlineInferencingServer
    from ._models_py3 import BanditPolicy
    from ._models_py3 import BaseEnvironmentId
    from ._models_py3 import BaseEnvironmentSource
    from ._models_py3 import BatchDeployment
    from ._models_py3 import BatchDeploymentConfiguration
    from ._models_py3 import BatchDeploymentProperties
    from ._models_py3 import BatchDeploymentTrackedResourceArmPaginatedResult
    from ._models_py3 import BatchEndpoint
    from ._models_py3 import BatchEndpointDefaults
    from ._models_py3 import BatchEndpointProperties
    from ._models_py3 import BatchEndpointTrackedResourceArmPaginatedResult
    from ._models_py3 import BatchPipelineComponentDeploymentConfiguration
    from ._models_py3 import BatchRetrySettings
    from ._models_py3 import BayesianSamplingAlgorithm
    from ._models_py3 import BindOptions
    from ._models_py3 import BlobReferenceForConsumptionDto
    from ._models_py3 import BuildContext
    from ._models_py3 import CapacityReservationGroup
    from ._models_py3 import CapacityReservationGroupProperties
    from ._models_py3 import CapacityReservationGroupTrackedResourceArmPaginatedResult
    from ._models_py3 import CategoricalDataDriftMetricThreshold
    from ._models_py3 import CategoricalDataQualityMetricThreshold
    from ._models_py3 import CategoricalPredictionDriftMetricThreshold
    from ._models_py3 import CertificateDatastoreCredentials
    from ._models_py3 import CertificateDatastoreSecrets
    from ._models_py3 import Classification
    from ._models_py3 import ClassificationModelPerformanceMetricThreshold
    from ._models_py3 import ClassificationTrainingSettings
    from ._models_py3 import ClusterUpdateParameters
    from ._models_py3 import CocoExportSummary
    from ._models_py3 import CodeConfiguration
    from ._models_py3 import CodeContainer
    from ._models_py3 import CodeContainerProperties
    from ._models_py3 import CodeContainerResourceArmPaginatedResult
    from ._models_py3 import CodeVersion
    from ._models_py3 import CodeVersionProperties
    from ._models_py3 import CodeVersionResourceArmPaginatedResult
    from ._models_py3 import Collection
    from ._models_py3 import ColumnTransformer
    from ._models_py3 import CommandJob
    from ._models_py3 import CommandJobLimits
    from ._models_py3 import ComponentConfiguration
    from ._models_py3 import ComponentContainer
    from ._models_py3 import ComponentContainerProperties
    from ._models_py3 import ComponentContainerResourceArmPaginatedResult
    from ._models_py3 import ComponentVersion
    from ._models_py3 import ComponentVersionProperties
    from ._models_py3 import ComponentVersionResourceArmPaginatedResult
    from ._models_py3 import Compute
    from ._models_py3 import ComputeInstance
    from ._models_py3 import ComputeInstanceApplication
    from ._models_py3 import ComputeInstanceAutologgerSettings
    from ._models_py3 import ComputeInstanceConnectivityEndpoints
    from ._models_py3 import ComputeInstanceContainer
    from ._models_py3 import ComputeInstanceCreatedBy
    from ._models_py3 import ComputeInstanceDataDisk
    from ._models_py3 import ComputeInstanceDataMount
    from ._models_py3 import ComputeInstanceEnvironmentInfo
    from ._models_py3 import ComputeInstanceLastOperation
    from ._models_py3 import ComputeInstanceProperties
    from ._models_py3 import ComputeInstanceSchema
    from ._models_py3 import ComputeInstanceSshSettings
    from ._models_py3 import ComputeInstanceVersion
    from ._models_py3 import ComputeRecurrenceSchedule
    from ._models_py3 import ComputeResource
    from ._models_py3 import ComputeResourceSchema
    from ._models_py3 import ComputeRuntimeDto
    from ._models_py3 import ComputeSchedules
    from ._models_py3 import ComputeSecrets
    from ._models_py3 import ComputeStartStopSchedule
    from ._models_py3 import ContainerResourceRequirements
    from ._models_py3 import ContainerResourceSettings
    from ._models_py3 import CosmosDbSettings
    from ._models_py3 import CreateMonitorAction
    from ._models_py3 import Cron
    from ._models_py3 import CronTrigger
    from ._models_py3 import CsvExportSummary
    from ._models_py3 import CustomForecastHorizon
    from ._models_py3 import CustomInferencingServer
    from ._models_py3 import CustomKeys
    from ._models_py3 import CustomKeysWorkspaceConnectionProperties
    from ._models_py3 import CustomMetricThreshold
    from ._models_py3 import CustomModelJobInput
    from ._models_py3 import CustomModelJobOutput
    from ._models_py3 import CustomMonitoringSignal
    from ._models_py3 import CustomNCrossValidations
    from ._models_py3 import CustomSeasonality
    from ._models_py3 import CustomService
    from ._models_py3 import CustomTargetLags
    from ._models_py3 import CustomTargetRollingWindowSize
    from ._models_py3 import DataCollector
    from ._models_py3 import DataContainer
    from ._models_py3 import DataContainerProperties
    from ._models_py3 import DataContainerResourceArmPaginatedResult
    from ._models_py3 import DataDriftMetricThresholdBase
    from ._models_py3 import DataDriftMonitoringSignal
    from ._models_py3 import DataFactory
    from ._models_py3 import DataImport
    from ._models_py3 import DataImportSource
    from ._models_py3 import DataLakeAnalytics
    from ._models_py3 import DataLakeAnalyticsSchema
    from ._models_py3 import DataLakeAnalyticsSchemaProperties
    from ._models_py3 import DataPathAssetReference
    from ._models_py3 import DataQualityMetricThresholdBase
    from ._models_py3 import DataQualityMonitoringSignal
    from ._models_py3 import DataVersionBase
    from ._models_py3 import DataVersionBaseProperties
    from ._models_py3 import DataVersionBaseResourceArmPaginatedResult
    from ._models_py3 import DatabaseSource
    from ._models_py3 import Databricks
    from ._models_py3 import DatabricksComputeSecrets
    from ._models_py3 import DatabricksComputeSecretsProperties
    from ._models_py3 import DatabricksProperties
    from ._models_py3 import DatabricksSchema
    from ._models_py3 import DatasetExportSummary
    from ._models_py3 import Datastore
    from ._models_py3 import DatastoreCredentials
    from ._models_py3 import DatastoreProperties
    from ._models_py3 import DatastoreResourceArmPaginatedResult
    from ._models_py3 import DatastoreSecrets
    from ._models_py3 import DefaultScaleSettings
    from ._models_py3 import DeploymentLogs
    from ._models_py3 import DeploymentLogsRequest
    from ._models_py3 import DeploymentResourceConfiguration
    from ._models_py3 import DiagnoseRequestProperties
    from ._models_py3 import DiagnoseResponseResult
    from ._models_py3 import DiagnoseResponseResultValue
    from ._models_py3 import DiagnoseResult
    from ._models_py3 import DiagnoseWorkspaceParameters
    from ._models_py3 import DistributionConfiguration
    from ._models_py3 import Docker
    from ._models_py3 import EarlyTerminationPolicy
    from ._models_py3 import EncryptionKeyVaultUpdateProperties
    from ._models_py3 import EncryptionProperty
    from ._models_py3 import EncryptionUpdateProperties
    from ._models_py3 import Endpoint
    from ._models_py3 import EndpointAuthKeys
    from ._models_py3 import EndpointAuthToken
    from ._models_py3 import EndpointDeploymentPropertiesBase
    from ._models_py3 import EndpointPropertiesBase
    from ._models_py3 import EndpointScheduleAction
    from ._models_py3 import EnvironmentContainer
    from ._models_py3 import EnvironmentContainerProperties
    from ._models_py3 import EnvironmentContainerResourceArmPaginatedResult
    from ._models_py3 import EnvironmentVariable
    from ._models_py3 import EnvironmentVersion
    from ._models_py3 import EnvironmentVersionProperties
    from ._models_py3 import EnvironmentVersionResourceArmPaginatedResult
    from ._models_py3 import ErrorAdditionalInfo
    from ._models_py3 import ErrorDetail
    from ._models_py3 import ErrorResponse
    from ._models_py3 import EstimatedVMPrice
    from ._models_py3 import EstimatedVMPrices
    from ._models_py3 import ExportSummary
    from ._models_py3 import ExternalFQDNResponse
    from ._models_py3 import FQDNEndpoint
    from ._models_py3 import FQDNEndpointDetail
    from ._models_py3 import FQDNEndpoints
    from ._models_py3 import FQDNEndpointsPropertyBag
    from ._models_py3 import Feature
    from ._models_py3 import FeatureAttributionDriftMonitoringSignal
    from ._models_py3 import FeatureAttributionMetricThreshold
    from ._models_py3 import FeatureImportanceSettings
    from ._models_py3 import FeatureProperties
    from ._models_py3 import FeatureResourceArmPaginatedResult
    from ._models_py3 import FeatureStoreSettings
    from ._models_py3 import FeatureSubset
    from ._models_py3 import FeatureWindow
    from ._models_py3 import FeaturesetContainer
    from ._models_py3 import FeaturesetContainerProperties
    from ._models_py3 import FeaturesetContainerResourceArmPaginatedResult
    from ._models_py3 import FeaturesetSpecification
    from ._models_py3 import FeaturesetVersion
    from ._models_py3 import FeaturesetVersionBackfillRequest
    from ._models_py3 import FeaturesetVersionBackfillResponse
    from ._models_py3 import FeaturesetVersionProperties
    from ._models_py3 import FeaturesetVersionResourceArmPaginatedResult
    from ._models_py3 import FeaturestoreEntityContainer
    from ._models_py3 import FeaturestoreEntityContainerProperties
    from ._models_py3 import FeaturestoreEntityContainerResourceArmPaginatedResult
    from ._models_py3 import FeaturestoreEntityVersion
    from ._models_py3 import FeaturestoreEntityVersionProperties
    from ._models_py3 import FeaturestoreEntityVersionResourceArmPaginatedResult
    from ._models_py3 import FeaturizationSettings
    from ._models_py3 import FileSystemSource
    from ._models_py3 import FixedInputData
    from ._models_py3 import FlavorData
    from ._models_py3 import ForecastHorizon
    from ._models_py3 import Forecasting
    from ._models_py3 import ForecastingSettings
    from ._models_py3 import ForecastingTrainingSettings
    from ._models_py3 import FqdnOutboundRule
    from ._models_py3 import GenerationSafetyQualityMetricThreshold
    from ._models_py3 import GenerationSafetyQualityMonitoringSignal
    from ._models_py3 import GenerationTokenUsageMetricThreshold
    from ._models_py3 import GenerationTokenUsageSignal
    from ._models_py3 import GridSamplingAlgorithm
    from ._models_py3 import GroupStatus
    from ._models_py3 import HDInsight
    from ._models_py3 import HDInsightProperties
    from ._models_py3 import HDInsightSchema
    from ._models_py3 import HdfsDatastore
    from ._models_py3 import IdAssetReference
    from ._models_py3 import IdentityConfiguration
    from ._models_py3 import IdentityForCmk
    from ._models_py3 import IdleShutdownSetting
    from ._models_py3 import Image
    from ._models_py3 import ImageClassification
    from ._models_py3 import ImageClassificationBase
    from ._models_py3 import ImageClassificationMultilabel
    from ._models_py3 import ImageInstanceSegmentation
    from ._models_py3 import ImageLimitSettings
    from ._models_py3 import ImageMetadata
    from ._models_py3 import ImageModelDistributionSettings
    from ._models_py3 import ImageModelDistributionSettingsClassification
    from ._models_py3 import ImageModelDistributionSettingsObjectDetection
    from ._models_py3 import ImageModelSettings
    from ._models_py3 import ImageModelSettingsClassification
    from ._models_py3 import ImageModelSettingsObjectDetection
    from ._models_py3 import ImageObjectDetection
    from ._models_py3 import ImageObjectDetectionBase
    from ._models_py3 import ImageSweepSettings
    from ._models_py3 import ImageVertical
    from ._models_py3 import ImportDataAction
    from ._models_py3 import IndexColumn
    from ._models_py3 import InferenceContainerProperties
    from ._models_py3 import InferenceEndpoint
    from ._models_py3 import InferenceEndpointProperties
    from ._models_py3 import InferenceEndpointTrackedResourceArmPaginatedResult
    from ._models_py3 import InferenceGroup
    from ._models_py3 import InferenceGroupProperties
    from ._models_py3 import InferenceGroupTrackedResourceArmPaginatedResult
    from ._models_py3 import InferencePool
    from ._models_py3 import InferencePoolProperties
    from ._models_py3 import InferencePoolTrackedResourceArmPaginatedResult
    from ._models_py3 import InferencingServer
    from ._models_py3 import InstanceTypeSchema
    from ._models_py3 import InstanceTypeSchemaResources
    from ._models_py3 import IntellectualProperty
    from ._models_py3 import JobBase
    from ._models_py3 import JobBaseProperties
    from ._models_py3 import JobBaseResourceArmPaginatedResult
    from ._models_py3 import JobInput
    from ._models_py3 import JobLimits
    from ._models_py3 import JobOutput
    from ._models_py3 import JobResourceConfiguration
    from ._models_py3 import JobScheduleAction
    from ._models_py3 import JobService
    from ._models_py3 import JupyterKernelConfig
    from ._models_py3 import KerberosCredentials
    from ._models_py3 import KerberosKeytabCredentials
    from ._models_py3 import KerberosKeytabSecrets
    from ._models_py3 import KerberosPasswordCredentials
    from ._models_py3 import KerberosPasswordSecrets
    from ._models_py3 import KeyVaultProperties
    from ._models_py3 import Kubernetes
    from ._models_py3 import KubernetesOnlineDeployment
    from ._models_py3 import KubernetesProperties
    from ._models_py3 import KubernetesSchema
    from ._models_py3 import LabelCategory
    from ._models_py3 import LabelClass
    from ._models_py3 import LabelingDataConfiguration
    from ._models_py3 import LabelingJob
    from ._models_py3 import LabelingJobImageProperties
    from ._models_py3 import LabelingJobInstructions
    from ._models_py3 import LabelingJobMediaProperties
    from ._models_py3 import LabelingJobProperties
    from ._models_py3 import LabelingJobResourceArmPaginatedResult
    from ._models_py3 import LabelingJobTextProperties
    from ._models_py3 import LakeHouseArtifact
    from ._models_py3 import ListAmlUserFeatureResult
    from ._models_py3 import ListNotebookKeysResult
    from ._models_py3 import ListStorageAccountKeysResult
    from ._models_py3 import ListUsagesResult
    from ._models_py3 import ListWorkspaceKeysResult
    from ._models_py3 import ListWorkspaceQuotas
    from ._models_py3 import LiteralJobInput
    from ._models_py3 import MLAssistConfiguration
    from ._models_py3 import MLAssistConfigurationDisabled
    from ._models_py3 import MLAssistConfigurationEnabled
    from ._models_py3 import MLFlowModelJobInput
    from ._models_py3 import MLFlowModelJobOutput
    from ._models_py3 import MLTableData
    from ._models_py3 import MLTableJobInput
    from ._models_py3 import MLTableJobOutput
    from ._models_py3 import ManagedComputeIdentity
    from ._models_py3 import ManagedIdentity
    from ._models_py3 import ManagedIdentityAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import ManagedNetworkProvisionOptions
    from ._models_py3 import ManagedNetworkProvisionStatus
    from ._models_py3 import ManagedNetworkSettings
    from ._models_py3 import ManagedOnlineDeployment
    from ._models_py3 import ManagedServiceIdentity
    from ._models_py3 import MaterializationComputeResource
    from ._models_py3 import MaterializationSettings
    from ._models_py3 import MedianStoppingPolicy
    from ._models_py3 import ModelConfiguration
    from ._models_py3 import ModelContainer
    from ._models_py3 import ModelContainerProperties
    from ._models_py3 import ModelContainerResourceArmPaginatedResult
    from ._models_py3 import ModelPackageInput
    from ._models_py3 import ModelPerformanceMetricThresholdBase
    from ._models_py3 import ModelPerformanceSignal
    from ._models_py3 import ModelVersion
    from ._models_py3 import ModelVersionProperties
    from ._models_py3 import ModelVersionResourceArmPaginatedResult
    from ._models_py3 import MonitorComputeConfigurationBase
    from ._models_py3 import MonitorComputeIdentityBase
    from ._models_py3 import MonitorDefinition
    from ._models_py3 import MonitorEmailNotificationSettings
    from ._models_py3 import MonitorNotificationSettings
    from ._models_py3 import MonitorServerlessSparkCompute
    from ._models_py3 import MonitoringDataSegment
    from ._models_py3 import MonitoringFeatureFilterBase
    from ._models_py3 import MonitoringInputDataBase
    from ._models_py3 import MonitoringSignalBase
    from ._models_py3 import MonitoringTarget
    from ._models_py3 import MonitoringThreshold
    from ._models_py3 import MonitoringWorkspaceConnection
    from ._models_py3 import Mpi
    from ._models_py3 import NCrossValidations
    from ._models_py3 import NlpFixedParameters
    from ._models_py3 import NlpParameterSubspace
    from ._models_py3 import NlpSweepSettings
    from ._models_py3 import NlpVertical
    from ._models_py3 import NlpVerticalFeaturizationSettings
    from ._models_py3 import NlpVerticalLimitSettings
    from ._models_py3 import NodeStateCounts
    from ._models_py3 import Nodes
    from ._models_py3 import NoneAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import NoneDatastoreCredentials
    from ._models_py3 import NotebookAccessTokenResult
    from ._models_py3 import NotebookPreparationError
    from ._models_py3 import NotebookResourceInfo
    from ._models_py3 import NotificationSetting
    from ._models_py3 import NumericalDataDriftMetricThreshold
    from ._models_py3 import NumericalDataQualityMetricThreshold
    from ._models_py3 import NumericalPredictionDriftMetricThreshold
    from ._models_py3 import Objective
    from ._models_py3 import OneLakeArtifact
    from ._models_py3 import OneLakeDatastore
    from ._models_py3 import OnlineDeployment
    from ._models_py3 import OnlineDeploymentProperties
    from ._models_py3 import OnlineDeploymentTrackedResourceArmPaginatedResult
    from ._models_py3 import OnlineEndpoint
    from ._models_py3 import OnlineEndpointProperties
    from ._models_py3 import OnlineEndpointTrackedResourceArmPaginatedResult
    from ._models_py3 import OnlineInferenceConfiguration
    from ._models_py3 import OnlineRequestSettings
    from ._models_py3 import OnlineScaleSettings
    from ._models_py3 import Operation
    from ._models_py3 import OperationDisplay
    from ._models_py3 import OperationListResult
    from ._models_py3 import OsPatchingStatus
    from ._models_py3 import OutboundRule
    from ._models_py3 import OutboundRuleBasicResource
    from ._models_py3 import OutboundRuleListResult
    from ._models_py3 import OutputPathAssetReference
    from ._models_py3 import PATAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import PackageInputPathBase
    from ._models_py3 import PackageInputPathId
    from ._models_py3 import PackageInputPathUrl
    from ._models_py3 import PackageInputPathVersion
    from ._models_py3 import PackageRequest
    from ._models_py3 import PackageResponse
    from ._models_py3 import PaginatedComputeResourcesList
    from ._models_py3 import PartialBatchDeployment
    from ._models_py3 import PartialBatchDeploymentPartialMinimalTrackedResourceWithProperties
    from ._models_py3 import PartialJobBase
    from ._models_py3 import PartialJobBasePartialResource
    from ._models_py3 import PartialManagedServiceIdentity
    from ._models_py3 import PartialMinimalTrackedResource
    from ._models_py3 import PartialMinimalTrackedResourceWithIdentity
    from ._models_py3 import PartialMinimalTrackedResourceWithSku
    from ._models_py3 import PartialMinimalTrackedResourceWithSkuAndIdentity
    from ._models_py3 import PartialNotificationSetting
    from ._models_py3 import PartialRegistryPartialTrackedResource
    from ._models_py3 import PartialSku
    from ._models_py3 import Password
    from ._models_py3 import PendingUploadCredentialDto
    from ._models_py3 import PendingUploadRequestDto
    from ._models_py3 import PendingUploadResponseDto
    from ._models_py3 import PersonalComputeInstanceSettings
    from ._models_py3 import PipelineJob
    from ._models_py3 import PoolEnvironmentConfiguration
    from ._models_py3 import PoolModelConfiguration
    from ._models_py3 import PoolStatus
    from ._models_py3 import PredictionDriftMetricThresholdBase
    from ._models_py3 import PredictionDriftMonitoringSignal
    from ._models_py3 import PrivateEndpoint
    from ._models_py3 import PrivateEndpointConnection
    from ._models_py3 import PrivateEndpointConnectionListResult
    from ._models_py3 import PrivateEndpointDestination
    from ._models_py3 import PrivateEndpointOutboundRule
    from ._models_py3 import PrivateEndpointResource
    from ._models_py3 import PrivateLinkResource
    from ._models_py3 import PrivateLinkResourceListResult
    from ._models_py3 import PrivateLinkServiceConnectionState
    from ._models_py3 import ProbeSettings
    from ._models_py3 import ProgressMetrics
    from ._models_py3 import PropertiesBase
    from ._models_py3 import ProxyResource
    from ._models_py3 import PyTorch
    from ._models_py3 import QueueSettings
    from ._models_py3 import QuotaBaseProperties
    from ._models_py3 import QuotaUpdateParameters
    from ._models_py3 import RandomSamplingAlgorithm
    from ._models_py3 import Ray
    from ._models_py3 import Recurrence
    from ._models_py3 import RecurrenceSchedule
    from ._models_py3 import RecurrenceTrigger
    from ._models_py3 import RegenerateEndpointKeysRequest
    from ._models_py3 import Registry
    from ._models_py3 import RegistryListCredentialsResult
    from ._models_py3 import RegistryPartialManagedServiceIdentity
    from ._models_py3 import RegistryPrivateEndpointConnection
    from ._models_py3 import RegistryPrivateLinkServiceConnectionState
    from ._models_py3 import RegistryRegionArmDetails
    from ._models_py3 import RegistryTrackedResourceArmPaginatedResult
    from ._models_py3 import Regression
    from ._models_py3 import RegressionModelPerformanceMetricThreshold
    from ._models_py3 import RegressionTrainingSettings
    from ._models_py3 import RequestConfiguration
    from ._models_py3 import RequestLogging
    from ._models_py3 import ResizeSchema
    from ._models_py3 import Resource
    from ._models_py3 import ResourceBase
    from ._models_py3 import ResourceConfiguration
    from ._models_py3 import ResourceId
    from ._models_py3 import ResourceName
    from ._models_py3 import ResourceQuota
    from ._models_py3 import RollingInputData
    from ._models_py3 import Route
    from ._models_py3 import SASAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import SASCredentialDto
    from ._models_py3 import SamplingAlgorithm
    from ._models_py3 import SasDatastoreCredentials
    from ._models_py3 import SasDatastoreSecrets
    from ._models_py3 import ScaleSettings
    from ._models_py3 import ScaleSettingsInformation
    from ._models_py3 import Schedule
    from ._models_py3 import ScheduleActionBase
    from ._models_py3 import ScheduleBase
    from ._models_py3 import ScheduleProperties
    from ._models_py3 import ScheduleResourceArmPaginatedResult
    from ._models_py3 import ScriptReference
    from ._models_py3 import ScriptsToExecute
    from ._models_py3 import Seasonality
    from ._models_py3 import SecretConfiguration
    from ._models_py3 import ServerlessComputeSettings
    from ._models_py3 import ServerlessEndpoint
    from ._models_py3 import ServerlessEndpointCapacityReservation
    from ._models_py3 import ServerlessEndpointProperties
    from ._models_py3 import ServerlessEndpointStatus
    from ._models_py3 import ServerlessEndpointTrackedResourceArmPaginatedResult
    from ._models_py3 import ServerlessInferenceEndpoint
    from ._models_py3 import ServerlessOffer
    from ._models_py3 import ServiceManagedResourcesSettings
    from ._models_py3 import ServicePrincipalAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import ServicePrincipalDatastoreCredentials
    from ._models_py3 import ServicePrincipalDatastoreSecrets
    from ._models_py3 import ServiceTagDestination
    from ._models_py3 import ServiceTagOutboundRule
    from ._models_py3 import SetupScripts
    from ._models_py3 import SharedPrivateLinkResource
    from ._models_py3 import Sku
    from ._models_py3 import SkuCapacity
    from ._models_py3 import SkuResource
    from ._models_py3 import SkuResourceArmPaginatedResult
    from ._models_py3 import SkuSetting
    from ._models_py3 import SparkJob
    from ._models_py3 import SparkJobEntry
    from ._models_py3 import SparkJobPythonEntry
    from ._models_py3 import SparkJobScalaEntry
    from ._models_py3 import SparkResourceConfiguration
    from ._models_py3 import SslConfiguration
    from ._models_py3 import StackEnsembleSettings
    from ._models_py3 import StaticInputData
    from ._models_py3 import StatusMessage
    from ._models_py3 import StorageAccountDetails
    from ._models_py3 import SweepJob
    from ._models_py3 import SweepJobLimits
    from ._models_py3 import SynapseSpark
    from ._models_py3 import SynapseSparkProperties
    from ._models_py3 import SystemCreatedAcrAccount
    from ._models_py3 import SystemCreatedStorageAccount
    from ._models_py3 import SystemData
    from ._models_py3 import SystemService
    from ._models_py3 import TableFixedParameters
    from ._models_py3 import TableParameterSubspace
    from ._models_py3 import TableSweepSettings
    from ._models_py3 import TableVertical
    from ._models_py3 import TableVerticalFeaturizationSettings
    from ._models_py3 import TableVerticalLimitSettings
    from ._models_py3 import TargetLags
    from ._models_py3 import TargetRollingWindowSize
    from ._models_py3 import TargetUtilizationScaleSettings
    from ._models_py3 import TensorFlow
    from ._models_py3 import TextClassification
    from ._models_py3 import TextClassificationMultilabel
    from ._models_py3 import TextNer
    from ._models_py3 import TmpfsOptions
    from ._models_py3 import TopNFeaturesByAttribution
    from ._models_py3 import TrackedResource
    from ._models_py3 import TrainingSettings
    from ._models_py3 import TrialComponent
    from ._models_py3 import TriggerBase
    from ._models_py3 import TritonInferencingServer
    from ._models_py3 import TritonModelJobInput
    from ._models_py3 import TritonModelJobOutput
    from ._models_py3 import TruncationSelectionPolicy
    from ._models_py3 import UpdateWorkspaceQuotas
    from ._models_py3 import UpdateWorkspaceQuotasResult
    from ._models_py3 import UriFileDataVersion
    from ._models_py3 import UriFileJobInput
    from ._models_py3 import UriFileJobOutput
    from ._models_py3 import UriFolderDataVersion
    from ._models_py3 import UriFolderJobInput
    from ._models_py3 import UriFolderJobOutput
    from ._models_py3 import Usage
    from ._models_py3 import UsageName
    from ._models_py3 import UserAccountCredentials
    from ._models_py3 import UserAssignedIdentity
    from ._models_py3 import UserCreatedAcrAccount
    from ._models_py3 import UserCreatedStorageAccount
    from ._models_py3 import UserIdentity
    from ._models_py3 import UsernamePasswordAuthTypeWorkspaceConnectionProperties
    from ._models_py3 import VirtualMachine
    from ._models_py3 import VirtualMachineImage
    from ._models_py3 import VirtualMachineSchema
    from ._models_py3 import VirtualMachineSchemaProperties
    from ._models_py3 import VirtualMachineSecrets
    from ._models_py3 import VirtualMachineSecretsSchema
    from ._models_py3 import VirtualMachineSize
    from ._models_py3 import VirtualMachineSizeListResult
    from ._models_py3 import VirtualMachineSshCredentials
    from ._models_py3 import VolumeDefinition
    from ._models_py3 import VolumeOptions
    from ._models_py3 import Webhook
    from ._models_py3 import Workspace
    from ._models_py3 import WorkspaceConnectionAccessKey
    from ._models_py3 import WorkspaceConnectionApiKey
    from ._models_py3 import WorkspaceConnectionManagedIdentity
    from ._models_py3 import WorkspaceConnectionPersonalAccessToken
    from ._models_py3 import WorkspaceConnectionPropertiesV2
    from ._models_py3 import WorkspaceConnectionPropertiesV2BasicResource
    from ._models_py3 import WorkspaceConnectionPropertiesV2BasicResourceArmPaginatedResult
    from ._models_py3 import WorkspaceConnectionServicePrincipal
    from ._models_py3 import WorkspaceConnectionSharedAccessSignature
    from ._models_py3 import WorkspaceConnectionUpdateParameter
    from ._models_py3 import WorkspaceConnectionUsernamePassword
    from ._models_py3 import WorkspaceHubConfig
    from ._models_py3 import WorkspaceListResult
    from ._models_py3 import WorkspacePrivateEndpointResource
    from ._models_py3 import WorkspaceUpdateParameters
except (SyntaxError, ImportError):
    from ._models import AKS  # type: ignore
    from ._models import AKSSchema  # type: ignore
    from ._models import AKSSchemaProperties  # type: ignore
    from ._models import AccessKeyAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import AccountKeyDatastoreCredentials  # type: ignore
    from ._models import AccountKeyDatastoreSecrets  # type: ignore
    from ._models import AcrDetails  # type: ignore
    from ._models import ActualCapacityInfo  # type: ignore
    from ._models import AksComputeSecrets  # type: ignore
    from ._models import AksComputeSecretsProperties  # type: ignore
    from ._models import AksNetworkingConfiguration  # type: ignore
    from ._models import AllFeatures  # type: ignore
    from ._models import AllNodes  # type: ignore
    from ._models import AmlCompute  # type: ignore
    from ._models import AmlComputeNodeInformation  # type: ignore
    from ._models import AmlComputeNodesInformation  # type: ignore
    from ._models import AmlComputeProperties  # type: ignore
    from ._models import AmlComputeSchema  # type: ignore
    from ._models import AmlToken  # type: ignore
    from ._models import AmlTokenComputeIdentity  # type: ignore
    from ._models import AmlUserFeature  # type: ignore
    from ._models import ApiKeyAuthWorkspaceConnectionProperties  # type: ignore
    from ._models import ArmResourceId  # type: ignore
    from ._models import AssetBase  # type: ignore
    from ._models import AssetContainer  # type: ignore
    from ._models import AssetJobInput  # type: ignore
    from ._models import AssetJobOutput  # type: ignore
    from ._models import AssetReferenceBase  # type: ignore
    from ._models import AssignedUser  # type: ignore
    from ._models import AutoDeleteSetting  # type: ignore
    from ._models import AutoForecastHorizon  # type: ignore
    from ._models import AutoMLJob  # type: ignore
    from ._models import AutoMLVertical  # type: ignore
    from ._models import AutoNCrossValidations  # type: ignore
    from ._models import AutoPauseProperties  # type: ignore
    from ._models import AutoScaleProperties  # type: ignore
    from ._models import AutoSeasonality  # type: ignore
    from ._models import AutoTargetLags  # type: ignore
    from ._models import AutoTargetRollingWindowSize  # type: ignore
    from ._models import AutologgerSettings  # type: ignore
    from ._models import AzureBlobDatastore  # type: ignore
    from ._models import AzureDataLakeGen1Datastore  # type: ignore
    from ._models import AzureDataLakeGen2Datastore  # type: ignore
    from ._models import AzureDatastore  # type: ignore
    from ._models import AzureDevOpsWebhook  # type: ignore
    from ._models import AzureFileDatastore  # type: ignore
    from ._models import AzureMLBatchInferencingServer  # type: ignore
    from ._models import AzureMLOnlineInferencingServer  # type: ignore
    from ._models import BanditPolicy  # type: ignore
    from ._models import BaseEnvironmentId  # type: ignore
    from ._models import BaseEnvironmentSource  # type: ignore
    from ._models import BatchDeployment  # type: ignore
    from ._models import BatchDeploymentConfiguration  # type: ignore
    from ._models import BatchDeploymentProperties  # type: ignore
    from ._models import BatchDeploymentTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import BatchEndpoint  # type: ignore
    from ._models import BatchEndpointDefaults  # type: ignore
    from ._models import BatchEndpointProperties  # type: ignore
    from ._models import BatchEndpointTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import BatchPipelineComponentDeploymentConfiguration  # type: ignore
    from ._models import BatchRetrySettings  # type: ignore
    from ._models import BayesianSamplingAlgorithm  # type: ignore
    from ._models import BindOptions  # type: ignore
    from ._models import BlobReferenceForConsumptionDto  # type: ignore
    from ._models import BuildContext  # type: ignore
    from ._models import CapacityReservationGroup  # type: ignore
    from ._models import CapacityReservationGroupProperties  # type: ignore
    from ._models import CapacityReservationGroupTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import CategoricalDataDriftMetricThreshold  # type: ignore
    from ._models import CategoricalDataQualityMetricThreshold  # type: ignore
    from ._models import CategoricalPredictionDriftMetricThreshold  # type: ignore
    from ._models import CertificateDatastoreCredentials  # type: ignore
    from ._models import CertificateDatastoreSecrets  # type: ignore
    from ._models import Classification  # type: ignore
    from ._models import ClassificationModelPerformanceMetricThreshold  # type: ignore
    from ._models import ClassificationTrainingSettings  # type: ignore
    from ._models import ClusterUpdateParameters  # type: ignore
    from ._models import CocoExportSummary  # type: ignore
    from ._models import CodeConfiguration  # type: ignore
    from ._models import CodeContainer  # type: ignore
    from ._models import CodeContainerProperties  # type: ignore
    from ._models import CodeContainerResourceArmPaginatedResult  # type: ignore
    from ._models import CodeVersion  # type: ignore
    from ._models import CodeVersionProperties  # type: ignore
    from ._models import CodeVersionResourceArmPaginatedResult  # type: ignore
    from ._models import Collection  # type: ignore
    from ._models import ColumnTransformer  # type: ignore
    from ._models import CommandJob  # type: ignore
    from ._models import CommandJobLimits  # type: ignore
    from ._models import ComponentConfiguration  # type: ignore
    from ._models import ComponentContainer  # type: ignore
    from ._models import ComponentContainerProperties  # type: ignore
    from ._models import ComponentContainerResourceArmPaginatedResult  # type: ignore
    from ._models import ComponentVersion  # type: ignore
    from ._models import ComponentVersionProperties  # type: ignore
    from ._models import ComponentVersionResourceArmPaginatedResult  # type: ignore
    from ._models import Compute  # type: ignore
    from ._models import ComputeInstance  # type: ignore
    from ._models import ComputeInstanceApplication  # type: ignore
    from ._models import ComputeInstanceAutologgerSettings  # type: ignore
    from ._models import ComputeInstanceConnectivityEndpoints  # type: ignore
    from ._models import ComputeInstanceContainer  # type: ignore
    from ._models import ComputeInstanceCreatedBy  # type: ignore
    from ._models import ComputeInstanceDataDisk  # type: ignore
    from ._models import ComputeInstanceDataMount  # type: ignore
    from ._models import ComputeInstanceEnvironmentInfo  # type: ignore
    from ._models import ComputeInstanceLastOperation  # type: ignore
    from ._models import ComputeInstanceProperties  # type: ignore
    from ._models import ComputeInstanceSchema  # type: ignore
    from ._models import ComputeInstanceSshSettings  # type: ignore
    from ._models import ComputeInstanceVersion  # type: ignore
    from ._models import ComputeRecurrenceSchedule  # type: ignore
    from ._models import ComputeResource  # type: ignore
    from ._models import ComputeResourceSchema  # type: ignore
    from ._models import ComputeRuntimeDto  # type: ignore
    from ._models import ComputeSchedules  # type: ignore
    from ._models import ComputeSecrets  # type: ignore
    from ._models import ComputeStartStopSchedule  # type: ignore
    from ._models import ContainerResourceRequirements  # type: ignore
    from ._models import ContainerResourceSettings  # type: ignore
    from ._models import CosmosDbSettings  # type: ignore
    from ._models import CreateMonitorAction  # type: ignore
    from ._models import Cron  # type: ignore
    from ._models import CronTrigger  # type: ignore
    from ._models import CsvExportSummary  # type: ignore
    from ._models import CustomForecastHorizon  # type: ignore
    from ._models import CustomInferencingServer  # type: ignore
    from ._models import CustomKeys  # type: ignore
    from ._models import CustomKeysWorkspaceConnectionProperties  # type: ignore
    from ._models import CustomMetricThreshold  # type: ignore
    from ._models import CustomModelJobInput  # type: ignore
    from ._models import CustomModelJobOutput  # type: ignore
    from ._models import CustomMonitoringSignal  # type: ignore
    from ._models import CustomNCrossValidations  # type: ignore
    from ._models import CustomSeasonality  # type: ignore
    from ._models import CustomService  # type: ignore
    from ._models import CustomTargetLags  # type: ignore
    from ._models import CustomTargetRollingWindowSize  # type: ignore
    from ._models import DataCollector  # type: ignore
    from ._models import DataContainer  # type: ignore
    from ._models import DataContainerProperties  # type: ignore
    from ._models import DataContainerResourceArmPaginatedResult  # type: ignore
    from ._models import DataDriftMetricThresholdBase  # type: ignore
    from ._models import DataDriftMonitoringSignal  # type: ignore
    from ._models import DataFactory  # type: ignore
    from ._models import DataImport  # type: ignore
    from ._models import DataImportSource  # type: ignore
    from ._models import DataLakeAnalytics  # type: ignore
    from ._models import DataLakeAnalyticsSchema  # type: ignore
    from ._models import DataLakeAnalyticsSchemaProperties  # type: ignore
    from ._models import DataPathAssetReference  # type: ignore
    from ._models import DataQualityMetricThresholdBase  # type: ignore
    from ._models import DataQualityMonitoringSignal  # type: ignore
    from ._models import DataVersionBase  # type: ignore
    from ._models import DataVersionBaseProperties  # type: ignore
    from ._models import DataVersionBaseResourceArmPaginatedResult  # type: ignore
    from ._models import DatabaseSource  # type: ignore
    from ._models import Databricks  # type: ignore
    from ._models import DatabricksComputeSecrets  # type: ignore
    from ._models import DatabricksComputeSecretsProperties  # type: ignore
    from ._models import DatabricksProperties  # type: ignore
    from ._models import DatabricksSchema  # type: ignore
    from ._models import DatasetExportSummary  # type: ignore
    from ._models import Datastore  # type: ignore
    from ._models import DatastoreCredentials  # type: ignore
    from ._models import DatastoreProperties  # type: ignore
    from ._models import DatastoreResourceArmPaginatedResult  # type: ignore
    from ._models import DatastoreSecrets  # type: ignore
    from ._models import DefaultScaleSettings  # type: ignore
    from ._models import DeploymentLogs  # type: ignore
    from ._models import DeploymentLogsRequest  # type: ignore
    from ._models import DeploymentResourceConfiguration  # type: ignore
    from ._models import DiagnoseRequestProperties  # type: ignore
    from ._models import DiagnoseResponseResult  # type: ignore
    from ._models import DiagnoseResponseResultValue  # type: ignore
    from ._models import DiagnoseResult  # type: ignore
    from ._models import DiagnoseWorkspaceParameters  # type: ignore
    from ._models import DistributionConfiguration  # type: ignore
    from ._models import Docker  # type: ignore
    from ._models import EarlyTerminationPolicy  # type: ignore
    from ._models import EncryptionKeyVaultUpdateProperties  # type: ignore
    from ._models import EncryptionProperty  # type: ignore
    from ._models import EncryptionUpdateProperties  # type: ignore
    from ._models import Endpoint  # type: ignore
    from ._models import EndpointAuthKeys  # type: ignore
    from ._models import EndpointAuthToken  # type: ignore
    from ._models import EndpointDeploymentPropertiesBase  # type: ignore
    from ._models import EndpointPropertiesBase  # type: ignore
    from ._models import EndpointScheduleAction  # type: ignore
    from ._models import EnvironmentContainer  # type: ignore
    from ._models import EnvironmentContainerProperties  # type: ignore
    from ._models import EnvironmentContainerResourceArmPaginatedResult  # type: ignore
    from ._models import EnvironmentVariable  # type: ignore
    from ._models import EnvironmentVersion  # type: ignore
    from ._models import EnvironmentVersionProperties  # type: ignore
    from ._models import EnvironmentVersionResourceArmPaginatedResult  # type: ignore
    from ._models import ErrorAdditionalInfo  # type: ignore
    from ._models import ErrorDetail  # type: ignore
    from ._models import ErrorResponse  # type: ignore
    from ._models import EstimatedVMPrice  # type: ignore
    from ._models import EstimatedVMPrices  # type: ignore
    from ._models import ExportSummary  # type: ignore
    from ._models import ExternalFQDNResponse  # type: ignore
    from ._models import FQDNEndpoint  # type: ignore
    from ._models import FQDNEndpointDetail  # type: ignore
    from ._models import FQDNEndpoints  # type: ignore
    from ._models import FQDNEndpointsPropertyBag  # type: ignore
    from ._models import Feature  # type: ignore
    from ._models import FeatureAttributionDriftMonitoringSignal  # type: ignore
    from ._models import FeatureAttributionMetricThreshold  # type: ignore
    from ._models import FeatureImportanceSettings  # type: ignore
    from ._models import FeatureProperties  # type: ignore
    from ._models import FeatureResourceArmPaginatedResult  # type: ignore
    from ._models import FeatureStoreSettings  # type: ignore
    from ._models import FeatureSubset  # type: ignore
    from ._models import FeatureWindow  # type: ignore
    from ._models import FeaturesetContainer  # type: ignore
    from ._models import FeaturesetContainerProperties  # type: ignore
    from ._models import FeaturesetContainerResourceArmPaginatedResult  # type: ignore
    from ._models import FeaturesetSpecification  # type: ignore
    from ._models import FeaturesetVersion  # type: ignore
    from ._models import FeaturesetVersionBackfillRequest  # type: ignore
    from ._models import FeaturesetVersionBackfillResponse  # type: ignore
    from ._models import FeaturesetVersionProperties  # type: ignore
    from ._models import FeaturesetVersionResourceArmPaginatedResult  # type: ignore
    from ._models import FeaturestoreEntityContainer  # type: ignore
    from ._models import FeaturestoreEntityContainerProperties  # type: ignore
    from ._models import FeaturestoreEntityContainerResourceArmPaginatedResult  # type: ignore
    from ._models import FeaturestoreEntityVersion  # type: ignore
    from ._models import FeaturestoreEntityVersionProperties  # type: ignore
    from ._models import FeaturestoreEntityVersionResourceArmPaginatedResult  # type: ignore
    from ._models import FeaturizationSettings  # type: ignore
    from ._models import FileSystemSource  # type: ignore
    from ._models import FixedInputData  # type: ignore
    from ._models import FlavorData  # type: ignore
    from ._models import ForecastHorizon  # type: ignore
    from ._models import Forecasting  # type: ignore
    from ._models import ForecastingSettings  # type: ignore
    from ._models import ForecastingTrainingSettings  # type: ignore
    from ._models import FqdnOutboundRule  # type: ignore
    from ._models import GenerationSafetyQualityMetricThreshold  # type: ignore
    from ._models import GenerationSafetyQualityMonitoringSignal  # type: ignore
    from ._models import GenerationTokenUsageMetricThreshold  # type: ignore
    from ._models import GenerationTokenUsageSignal  # type: ignore
    from ._models import GridSamplingAlgorithm  # type: ignore
    from ._models import GroupStatus  # type: ignore
    from ._models import HDInsight  # type: ignore
    from ._models import HDInsightProperties  # type: ignore
    from ._models import HDInsightSchema  # type: ignore
    from ._models import HdfsDatastore  # type: ignore
    from ._models import IdAssetReference  # type: ignore
    from ._models import IdentityConfiguration  # type: ignore
    from ._models import IdentityForCmk  # type: ignore
    from ._models import IdleShutdownSetting  # type: ignore
    from ._models import Image  # type: ignore
    from ._models import ImageClassification  # type: ignore
    from ._models import ImageClassificationBase  # type: ignore
    from ._models import ImageClassificationMultilabel  # type: ignore
    from ._models import ImageInstanceSegmentation  # type: ignore
    from ._models import ImageLimitSettings  # type: ignore
    from ._models import ImageMetadata  # type: ignore
    from ._models import ImageModelDistributionSettings  # type: ignore
    from ._models import ImageModelDistributionSettingsClassification  # type: ignore
    from ._models import ImageModelDistributionSettingsObjectDetection  # type: ignore
    from ._models import ImageModelSettings  # type: ignore
    from ._models import ImageModelSettingsClassification  # type: ignore
    from ._models import ImageModelSettingsObjectDetection  # type: ignore
    from ._models import ImageObjectDetection  # type: ignore
    from ._models import ImageObjectDetectionBase  # type: ignore
    from ._models import ImageSweepSettings  # type: ignore
    from ._models import ImageVertical  # type: ignore
    from ._models import ImportDataAction  # type: ignore
    from ._models import IndexColumn  # type: ignore
    from ._models import InferenceContainerProperties  # type: ignore
    from ._models import InferenceEndpoint  # type: ignore
    from ._models import InferenceEndpointProperties  # type: ignore
    from ._models import InferenceEndpointTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import InferenceGroup  # type: ignore
    from ._models import InferenceGroupProperties  # type: ignore
    from ._models import InferenceGroupTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import InferencePool  # type: ignore
    from ._models import InferencePoolProperties  # type: ignore
    from ._models import InferencePoolTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import InferencingServer  # type: ignore
    from ._models import InstanceTypeSchema  # type: ignore
    from ._models import InstanceTypeSchemaResources  # type: ignore
    from ._models import IntellectualProperty  # type: ignore
    from ._models import JobBase  # type: ignore
    from ._models import JobBaseProperties  # type: ignore
    from ._models import JobBaseResourceArmPaginatedResult  # type: ignore
    from ._models import JobInput  # type: ignore
    from ._models import JobLimits  # type: ignore
    from ._models import JobOutput  # type: ignore
    from ._models import JobResourceConfiguration  # type: ignore
    from ._models import JobScheduleAction  # type: ignore
    from ._models import JobService  # type: ignore
    from ._models import JupyterKernelConfig  # type: ignore
    from ._models import KerberosCredentials  # type: ignore
    from ._models import KerberosKeytabCredentials  # type: ignore
    from ._models import KerberosKeytabSecrets  # type: ignore
    from ._models import KerberosPasswordCredentials  # type: ignore
    from ._models import KerberosPasswordSecrets  # type: ignore
    from ._models import KeyVaultProperties  # type: ignore
    from ._models import Kubernetes  # type: ignore
    from ._models import KubernetesOnlineDeployment  # type: ignore
    from ._models import KubernetesProperties  # type: ignore
    from ._models import KubernetesSchema  # type: ignore
    from ._models import LabelCategory  # type: ignore
    from ._models import LabelClass  # type: ignore
    from ._models import LabelingDataConfiguration  # type: ignore
    from ._models import LabelingJob  # type: ignore
    from ._models import LabelingJobImageProperties  # type: ignore
    from ._models import LabelingJobInstructions  # type: ignore
    from ._models import LabelingJobMediaProperties  # type: ignore
    from ._models import LabelingJobProperties  # type: ignore
    from ._models import LabelingJobResourceArmPaginatedResult  # type: ignore
    from ._models import LabelingJobTextProperties  # type: ignore
    from ._models import LakeHouseArtifact  # type: ignore
    from ._models import ListAmlUserFeatureResult  # type: ignore
    from ._models import ListNotebookKeysResult  # type: ignore
    from ._models import ListStorageAccountKeysResult  # type: ignore
    from ._models import ListUsagesResult  # type: ignore
    from ._models import ListWorkspaceKeysResult  # type: ignore
    from ._models import ListWorkspaceQuotas  # type: ignore
    from ._models import LiteralJobInput  # type: ignore
    from ._models import MLAssistConfiguration  # type: ignore
    from ._models import MLAssistConfigurationDisabled  # type: ignore
    from ._models import MLAssistConfigurationEnabled  # type: ignore
    from ._models import MLFlowModelJobInput  # type: ignore
    from ._models import MLFlowModelJobOutput  # type: ignore
    from ._models import MLTableData  # type: ignore
    from ._models import MLTableJobInput  # type: ignore
    from ._models import MLTableJobOutput  # type: ignore
    from ._models import ManagedComputeIdentity  # type: ignore
    from ._models import ManagedIdentity  # type: ignore
    from ._models import ManagedIdentityAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import ManagedNetworkProvisionOptions  # type: ignore
    from ._models import ManagedNetworkProvisionStatus  # type: ignore
    from ._models import ManagedNetworkSettings  # type: ignore
    from ._models import ManagedOnlineDeployment  # type: ignore
    from ._models import ManagedServiceIdentity  # type: ignore
    from ._models import MaterializationComputeResource  # type: ignore
    from ._models import MaterializationSettings  # type: ignore
    from ._models import MedianStoppingPolicy  # type: ignore
    from ._models import ModelConfiguration  # type: ignore
    from ._models import ModelContainer  # type: ignore
    from ._models import ModelContainerProperties  # type: ignore
    from ._models import ModelContainerResourceArmPaginatedResult  # type: ignore
    from ._models import ModelPackageInput  # type: ignore
    from ._models import ModelPerformanceMetricThresholdBase  # type: ignore
    from ._models import ModelPerformanceSignal  # type: ignore
    from ._models import ModelVersion  # type: ignore
    from ._models import ModelVersionProperties  # type: ignore
    from ._models import ModelVersionResourceArmPaginatedResult  # type: ignore
    from ._models import MonitorComputeConfigurationBase  # type: ignore
    from ._models import MonitorComputeIdentityBase  # type: ignore
    from ._models import MonitorDefinition  # type: ignore
    from ._models import MonitorEmailNotificationSettings  # type: ignore
    from ._models import MonitorNotificationSettings  # type: ignore
    from ._models import MonitorServerlessSparkCompute  # type: ignore
    from ._models import MonitoringDataSegment  # type: ignore
    from ._models import MonitoringFeatureFilterBase  # type: ignore
    from ._models import MonitoringInputDataBase  # type: ignore
    from ._models import MonitoringSignalBase  # type: ignore
    from ._models import MonitoringTarget  # type: ignore
    from ._models import MonitoringThreshold  # type: ignore
    from ._models import MonitoringWorkspaceConnection  # type: ignore
    from ._models import Mpi  # type: ignore
    from ._models import NCrossValidations  # type: ignore
    from ._models import NlpFixedParameters  # type: ignore
    from ._models import NlpParameterSubspace  # type: ignore
    from ._models import NlpSweepSettings  # type: ignore
    from ._models import NlpVertical  # type: ignore
    from ._models import NlpVerticalFeaturizationSettings  # type: ignore
    from ._models import NlpVerticalLimitSettings  # type: ignore
    from ._models import NodeStateCounts  # type: ignore
    from ._models import Nodes  # type: ignore
    from ._models import NoneAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import NoneDatastoreCredentials  # type: ignore
    from ._models import NotebookAccessTokenResult  # type: ignore
    from ._models import NotebookPreparationError  # type: ignore
    from ._models import NotebookResourceInfo  # type: ignore
    from ._models import NotificationSetting  # type: ignore
    from ._models import NumericalDataDriftMetricThreshold  # type: ignore
    from ._models import NumericalDataQualityMetricThreshold  # type: ignore
    from ._models import NumericalPredictionDriftMetricThreshold  # type: ignore
    from ._models import Objective  # type: ignore
    from ._models import OneLakeArtifact  # type: ignore
    from ._models import OneLakeDatastore  # type: ignore
    from ._models import OnlineDeployment  # type: ignore
    from ._models import OnlineDeploymentProperties  # type: ignore
    from ._models import OnlineDeploymentTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import OnlineEndpoint  # type: ignore
    from ._models import OnlineEndpointProperties  # type: ignore
    from ._models import OnlineEndpointTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import OnlineInferenceConfiguration  # type: ignore
    from ._models import OnlineRequestSettings  # type: ignore
    from ._models import OnlineScaleSettings  # type: ignore
    from ._models import Operation  # type: ignore
    from ._models import OperationDisplay  # type: ignore
    from ._models import OperationListResult  # type: ignore
    from ._models import OsPatchingStatus  # type: ignore
    from ._models import OutboundRule  # type: ignore
    from ._models import OutboundRuleBasicResource  # type: ignore
    from ._models import OutboundRuleListResult  # type: ignore
    from ._models import OutputPathAssetReference  # type: ignore
    from ._models import PATAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import PackageInputPathBase  # type: ignore
    from ._models import PackageInputPathId  # type: ignore
    from ._models import PackageInputPathUrl  # type: ignore
    from ._models import PackageInputPathVersion  # type: ignore
    from ._models import PackageRequest  # type: ignore
    from ._models import PackageResponse  # type: ignore
    from ._models import PaginatedComputeResourcesList  # type: ignore
    from ._models import PartialBatchDeployment  # type: ignore
    from ._models import PartialBatchDeploymentPartialMinimalTrackedResourceWithProperties  # type: ignore
    from ._models import PartialJobBase  # type: ignore
    from ._models import PartialJobBasePartialResource  # type: ignore
    from ._models import PartialManagedServiceIdentity  # type: ignore
    from ._models import PartialMinimalTrackedResource  # type: ignore
    from ._models import PartialMinimalTrackedResourceWithIdentity  # type: ignore
    from ._models import PartialMinimalTrackedResourceWithSku  # type: ignore
    from ._models import PartialMinimalTrackedResourceWithSkuAndIdentity  # type: ignore
    from ._models import PartialNotificationSetting  # type: ignore
    from ._models import PartialRegistryPartialTrackedResource  # type: ignore
    from ._models import PartialSku  # type: ignore
    from ._models import Password  # type: ignore
    from ._models import PendingUploadCredentialDto  # type: ignore
    from ._models import PendingUploadRequestDto  # type: ignore
    from ._models import PendingUploadResponseDto  # type: ignore
    from ._models import PersonalComputeInstanceSettings  # type: ignore
    from ._models import PipelineJob  # type: ignore
    from ._models import PoolEnvironmentConfiguration  # type: ignore
    from ._models import PoolModelConfiguration  # type: ignore
    from ._models import PoolStatus  # type: ignore
    from ._models import PredictionDriftMetricThresholdBase  # type: ignore
    from ._models import PredictionDriftMonitoringSignal  # type: ignore
    from ._models import PrivateEndpoint  # type: ignore
    from ._models import PrivateEndpointConnection  # type: ignore
    from ._models import PrivateEndpointConnectionListResult  # type: ignore
    from ._models import PrivateEndpointDestination  # type: ignore
    from ._models import PrivateEndpointOutboundRule  # type: ignore
    from ._models import PrivateEndpointResource  # type: ignore
    from ._models import PrivateLinkResource  # type: ignore
    from ._models import PrivateLinkResourceListResult  # type: ignore
    from ._models import PrivateLinkServiceConnectionState  # type: ignore
    from ._models import ProbeSettings  # type: ignore
    from ._models import ProgressMetrics  # type: ignore
    from ._models import PropertiesBase  # type: ignore
    from ._models import ProxyResource  # type: ignore
    from ._models import PyTorch  # type: ignore
    from ._models import QueueSettings  # type: ignore
    from ._models import QuotaBaseProperties  # type: ignore
    from ._models import QuotaUpdateParameters  # type: ignore
    from ._models import RandomSamplingAlgorithm  # type: ignore
    from ._models import Ray  # type: ignore
    from ._models import Recurrence  # type: ignore
    from ._models import RecurrenceSchedule  # type: ignore
    from ._models import RecurrenceTrigger  # type: ignore
    from ._models import RegenerateEndpointKeysRequest  # type: ignore
    from ._models import Registry  # type: ignore
    from ._models import RegistryListCredentialsResult  # type: ignore
    from ._models import RegistryPartialManagedServiceIdentity  # type: ignore
    from ._models import RegistryPrivateEndpointConnection  # type: ignore
    from ._models import RegistryPrivateLinkServiceConnectionState  # type: ignore
    from ._models import RegistryRegionArmDetails  # type: ignore
    from ._models import RegistryTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import Regression  # type: ignore
    from ._models import RegressionModelPerformanceMetricThreshold  # type: ignore
    from ._models import RegressionTrainingSettings  # type: ignore
    from ._models import RequestConfiguration  # type: ignore
    from ._models import RequestLogging  # type: ignore
    from ._models import ResizeSchema  # type: ignore
    from ._models import Resource  # type: ignore
    from ._models import ResourceBase  # type: ignore
    from ._models import ResourceConfiguration  # type: ignore
    from ._models import ResourceId  # type: ignore
    from ._models import ResourceName  # type: ignore
    from ._models import ResourceQuota  # type: ignore
    from ._models import RollingInputData  # type: ignore
    from ._models import Route  # type: ignore
    from ._models import SASAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import SASCredentialDto  # type: ignore
    from ._models import SamplingAlgorithm  # type: ignore
    from ._models import SasDatastoreCredentials  # type: ignore
    from ._models import SasDatastoreSecrets  # type: ignore
    from ._models import ScaleSettings  # type: ignore
    from ._models import ScaleSettingsInformation  # type: ignore
    from ._models import Schedule  # type: ignore
    from ._models import ScheduleActionBase  # type: ignore
    from ._models import ScheduleBase  # type: ignore
    from ._models import ScheduleProperties  # type: ignore
    from ._models import ScheduleResourceArmPaginatedResult  # type: ignore
    from ._models import ScriptReference  # type: ignore
    from ._models import ScriptsToExecute  # type: ignore
    from ._models import Seasonality  # type: ignore
    from ._models import SecretConfiguration  # type: ignore
    from ._models import ServerlessComputeSettings  # type: ignore
    from ._models import ServerlessEndpoint  # type: ignore
    from ._models import ServerlessEndpointCapacityReservation  # type: ignore
    from ._models import ServerlessEndpointProperties  # type: ignore
    from ._models import ServerlessEndpointStatus  # type: ignore
    from ._models import ServerlessEndpointTrackedResourceArmPaginatedResult  # type: ignore
    from ._models import ServerlessInferenceEndpoint  # type: ignore
    from ._models import ServerlessOffer  # type: ignore
    from ._models import ServiceManagedResourcesSettings  # type: ignore
    from ._models import ServicePrincipalAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import ServicePrincipalDatastoreCredentials  # type: ignore
    from ._models import ServicePrincipalDatastoreSecrets  # type: ignore
    from ._models import ServiceTagDestination  # type: ignore
    from ._models import ServiceTagOutboundRule  # type: ignore
    from ._models import SetupScripts  # type: ignore
    from ._models import SharedPrivateLinkResource  # type: ignore
    from ._models import Sku  # type: ignore
    from ._models import SkuCapacity  # type: ignore
    from ._models import SkuResource  # type: ignore
    from ._models import SkuResourceArmPaginatedResult  # type: ignore
    from ._models import SkuSetting  # type: ignore
    from ._models import SparkJob  # type: ignore
    from ._models import SparkJobEntry  # type: ignore
    from ._models import SparkJobPythonEntry  # type: ignore
    from ._models import SparkJobScalaEntry  # type: ignore
    from ._models import SparkResourceConfiguration  # type: ignore
    from ._models import SslConfiguration  # type: ignore
    from ._models import StackEnsembleSettings  # type: ignore
    from ._models import StaticInputData  # type: ignore
    from ._models import StatusMessage  # type: ignore
    from ._models import StorageAccountDetails  # type: ignore
    from ._models import SweepJob  # type: ignore
    from ._models import SweepJobLimits  # type: ignore
    from ._models import SynapseSpark  # type: ignore
    from ._models import SynapseSparkProperties  # type: ignore
    from ._models import SystemCreatedAcrAccount  # type: ignore
    from ._models import SystemCreatedStorageAccount  # type: ignore
    from ._models import SystemData  # type: ignore
    from ._models import SystemService  # type: ignore
    from ._models import TableFixedParameters  # type: ignore
    from ._models import TableParameterSubspace  # type: ignore
    from ._models import TableSweepSettings  # type: ignore
    from ._models import TableVertical  # type: ignore
    from ._models import TableVerticalFeaturizationSettings  # type: ignore
    from ._models import TableVerticalLimitSettings  # type: ignore
    from ._models import TargetLags  # type: ignore
    from ._models import TargetRollingWindowSize  # type: ignore
    from ._models import TargetUtilizationScaleSettings  # type: ignore
    from ._models import TensorFlow  # type: ignore
    from ._models import TextClassification  # type: ignore
    from ._models import TextClassificationMultilabel  # type: ignore
    from ._models import TextNer  # type: ignore
    from ._models import TmpfsOptions  # type: ignore
    from ._models import TopNFeaturesByAttribution  # type: ignore
    from ._models import TrackedResource  # type: ignore
    from ._models import TrainingSettings  # type: ignore
    from ._models import TrialComponent  # type: ignore
    from ._models import TriggerBase  # type: ignore
    from ._models import TritonInferencingServer  # type: ignore
    from ._models import TritonModelJobInput  # type: ignore
    from ._models import TritonModelJobOutput  # type: ignore
    from ._models import TruncationSelectionPolicy  # type: ignore
    from ._models import UpdateWorkspaceQuotas  # type: ignore
    from ._models import UpdateWorkspaceQuotasResult  # type: ignore
    from ._models import UriFileDataVersion  # type: ignore
    from ._models import UriFileJobInput  # type: ignore
    from ._models import UriFileJobOutput  # type: ignore
    from ._models import UriFolderDataVersion  # type: ignore
    from ._models import UriFolderJobInput  # type: ignore
    from ._models import UriFolderJobOutput  # type: ignore
    from ._models import Usage  # type: ignore
    from ._models import UsageName  # type: ignore
    from ._models import UserAccountCredentials  # type: ignore
    from ._models import UserAssignedIdentity  # type: ignore
    from ._models import UserCreatedAcrAccount  # type: ignore
    from ._models import UserCreatedStorageAccount  # type: ignore
    from ._models import UserIdentity  # type: ignore
    from ._models import UsernamePasswordAuthTypeWorkspaceConnectionProperties  # type: ignore
    from ._models import VirtualMachine  # type: ignore
    from ._models import VirtualMachineImage  # type: ignore
    from ._models import VirtualMachineSchema  # type: ignore
    from ._models import VirtualMachineSchemaProperties  # type: ignore
    from ._models import VirtualMachineSecrets  # type: ignore
    from ._models import VirtualMachineSecretsSchema  # type: ignore
    from ._models import VirtualMachineSize  # type: ignore
    from ._models import VirtualMachineSizeListResult  # type: ignore
    from ._models import VirtualMachineSshCredentials  # type: ignore
    from ._models import VolumeDefinition  # type: ignore
    from ._models import VolumeOptions  # type: ignore
    from ._models import Webhook  # type: ignore
    from ._models import Workspace  # type: ignore
    from ._models import WorkspaceConnectionAccessKey  # type: ignore
    from ._models import WorkspaceConnectionApiKey  # type: ignore
    from ._models import WorkspaceConnectionManagedIdentity  # type: ignore
    from ._models import WorkspaceConnectionPersonalAccessToken  # type: ignore
    from ._models import WorkspaceConnectionPropertiesV2  # type: ignore
    from ._models import WorkspaceConnectionPropertiesV2BasicResource  # type: ignore
    from ._models import WorkspaceConnectionPropertiesV2BasicResourceArmPaginatedResult  # type: ignore
    from ._models import WorkspaceConnectionServicePrincipal  # type: ignore
    from ._models import WorkspaceConnectionSharedAccessSignature  # type: ignore
    from ._models import WorkspaceConnectionUpdateParameter  # type: ignore
    from ._models import WorkspaceConnectionUsernamePassword  # type: ignore
    from ._models import WorkspaceHubConfig  # type: ignore
    from ._models import WorkspaceListResult  # type: ignore
    from ._models import WorkspacePrivateEndpointResource  # type: ignore
    from ._models import WorkspaceUpdateParameters  # type: ignore

from ._azure_machine_learning_workspaces_enums import (
    ActionType,
    AllocationState,
    ApplicationSharingPolicy,
    AssetProvisioningState,
    AuthMode,
    AutoDeleteCondition,
    AutoRebuildSetting,
    Autosave,
    BaseEnvironmentSourceType,
    BatchDeploymentConfigurationType,
    BatchLoggingLevel,
    BatchOutputAction,
    BillingCurrency,
    BlockedTransformers,
    Caching,
    CategoricalDataDriftMetric,
    CategoricalDataQualityMetric,
    CategoricalPredictionDriftMetric,
    ClassificationModelPerformanceMetric,
    ClassificationModels,
    ClassificationMultilabelPrimaryMetrics,
    ClassificationPrimaryMetrics,
    ClusterPurpose,
    ComputeInstanceAuthorizationType,
    ComputeInstanceState,
    ComputePowerAction,
    ComputeRecurrenceFrequency,
    ComputeTriggerType,
    ComputeType,
    ComputeWeekDay,
    ConnectionAuthType,
    ConnectionCategory,
    ContainerType,
    CreatedByType,
    CredentialsType,
    DataAvailabilityStatus,
    DataCollectionMode,
    DataImportSourceType,
    DataType,
    DatastoreType,
    DeploymentProvisioningState,
    DiagnoseResultLevel,
    DistributionType,
    EarlyTerminationPolicyType,
    EgressPublicNetworkAccessType,
    EmailNotificationEnableType,
    EncryptionStatus,
    EndpointAuthMode,
    EndpointComputeType,
    EndpointProvisioningState,
    EndpointServiceConnectionStatus,
    EnvironmentType,
    EnvironmentVariableType,
    ExportFormatType,
    FeatureAttributionMetric,
    FeatureDataType,
    FeatureImportanceMode,
    FeatureLags,
    FeaturizationMode,
    ForecastHorizonMode,
    ForecastingModels,
    ForecastingPrimaryMetrics,
    GenerationSafetyQualityMetric,
    GenerationTokenUsageMetric,
    Goal,
    IdentityConfigurationType,
    ImageAnnotationType,
    ImageType,
    IncrementalDataRefresh,
    InferencingServerType,
    InputDeliveryMode,
    InputPathType,
    InstanceSegmentationPrimaryMetrics,
    IsolationMode,
    JobInputType,
    JobLimitsType,
    JobOutputType,
    JobProvisioningState,
    JobStatus,
    JobTier,
    JobType,
    KeyType,
    LearningRateScheduler,
    ListViewType,
    LoadBalancerType,
    LogTrainingMetrics,
    LogValidationLoss,
    LogVerbosity,
    MLAssistConfigurationType,
    MLFlowAutologgerState,
    ManagedNetworkStatus,
    ManagedServiceIdentityType,
    MaterializationStoreType,
    MediaType,
    MlflowAutologger,
    ModelSize,
    ModelTaskType,
    MonitorComputeIdentityType,
    MonitorComputeType,
    MonitoringFeatureDataType,
    MonitoringFeatureFilterType,
    MonitoringInputDataType,
    MonitoringModelType,
    MonitoringNotificationType,
    MonitoringSignalType,
    MountAction,
    MountState,
    MultiSelect,
    NCrossValidationsMode,
    Network,
    NlpLearningRateScheduler,
    NodeState,
    NodesValueType,
    NumericalDataDriftMetric,
    NumericalDataQualityMetric,
    NumericalPredictionDriftMetric,
    ObjectDetectionPrimaryMetrics,
    OneLakeArtifactType,
    OperatingSystemType,
    OperationName,
    OperationStatus,
    OperationTrigger,
    OrderString,
    Origin,
    OsType,
    OutputDeliveryMode,
    PackageBuildState,
    PackageInputDeliveryMode,
    PackageInputType,
    PatchStatus,
    PendingUploadCredentialType,
    PendingUploadType,
    PoolProvisioningState,
    PrivateEndpointConnectionProvisioningState,
    ProtectionLevel,
    Protocol,
    ProvisioningState,
    ProvisioningStatus,
    PublicNetworkAccessType,
    QuotaUnit,
    RandomSamplingAlgorithmRule,
    RecurrenceFrequency,
    ReferenceType,
    RegressionModelPerformanceMetric,
    RegressionModels,
    RegressionPrimaryMetrics,
    RemoteLoginPortPublicAccess,
    RollingRateType,
    RuleAction,
    RuleCategory,
    RuleStatus,
    RuleType,
    SamplingAlgorithmType,
    ScaleType,
    ScheduleActionType,
    ScheduleListViewType,
    ScheduleProvisioningState,
    ScheduleProvisioningStatus,
    ScheduleStatus,
    SeasonalityMode,
    SecretsType,
    ServerlessInferenceEndpointAuthMode,
    ServiceDataAccessAuthIdentity,
    ShortSeriesHandlingConfiguration,
    SkuScaleType,
    SkuTier,
    SourceType,
    SparkJobEntryType,
    SshPublicAccess,
    SslConfigStatus,
    StackMetaLearnerType,
    Status,
    StatusMessageLevel,
    StochasticOptimizer,
    StorageAccountType,
    TargetAggregationFunction,
    TargetLagsMode,
    TargetRollingWindowSizeMode,
    TaskType,
    TextAnnotationType,
    TrainingMode,
    TriggerType,
    UnderlyingResourceAction,
    UnitOfMeasure,
    UsageUnit,
    UseStl,
    VMPriceOSType,
    VMTier,
    ValidationMetricType,
    VmPriority,
    VolumeDefinitionType,
    WebhookType,
    WeekDay,
)

__all__ = [
    'AKS',
    'AKSSchema',
    'AKSSchemaProperties',
    'AccessKeyAuthTypeWorkspaceConnectionProperties',
    'AccountKeyDatastoreCredentials',
    'AccountKeyDatastoreSecrets',
    'AcrDetails',
    'ActualCapacityInfo',
    'AksComputeSecrets',
    'AksComputeSecretsProperties',
    'AksNetworkingConfiguration',
    'AllFeatures',
    'AllNodes',
    'AmlCompute',
    'AmlComputeNodeInformation',
    'AmlComputeNodesInformation',
    'AmlComputeProperties',
    'AmlComputeSchema',
    'AmlToken',
    'AmlTokenComputeIdentity',
    'AmlUserFeature',
    'ApiKeyAuthWorkspaceConnectionProperties',
    'ArmResourceId',
    'AssetBase',
    'AssetContainer',
    'AssetJobInput',
    'AssetJobOutput',
    'AssetReferenceBase',
    'AssignedUser',
    'AutoDeleteSetting',
    'AutoForecastHorizon',
    'AutoMLJob',
    'AutoMLVertical',
    'AutoNCrossValidations',
    'AutoPauseProperties',
    'AutoScaleProperties',
    'AutoSeasonality',
    'AutoTargetLags',
    'AutoTargetRollingWindowSize',
    'AutologgerSettings',
    'AzureBlobDatastore',
    'AzureDataLakeGen1Datastore',
    'AzureDataLakeGen2Datastore',
    'AzureDatastore',
    'AzureDevOpsWebhook',
    'AzureFileDatastore',
    'AzureMLBatchInferencingServer',
    'AzureMLOnlineInferencingServer',
    'BanditPolicy',
    'BaseEnvironmentId',
    'BaseEnvironmentSource',
    'BatchDeployment',
    'BatchDeploymentConfiguration',
    'BatchDeploymentProperties',
    'BatchDeploymentTrackedResourceArmPaginatedResult',
    'BatchEndpoint',
    'BatchEndpointDefaults',
    'BatchEndpointProperties',
    'BatchEndpointTrackedResourceArmPaginatedResult',
    'BatchPipelineComponentDeploymentConfiguration',
    'BatchRetrySettings',
    'BayesianSamplingAlgorithm',
    'BindOptions',
    'BlobReferenceForConsumptionDto',
    'BuildContext',
    'CapacityReservationGroup',
    'CapacityReservationGroupProperties',
    'CapacityReservationGroupTrackedResourceArmPaginatedResult',
    'CategoricalDataDriftMetricThreshold',
    'CategoricalDataQualityMetricThreshold',
    'CategoricalPredictionDriftMetricThreshold',
    'CertificateDatastoreCredentials',
    'CertificateDatastoreSecrets',
    'Classification',
    'ClassificationModelPerformanceMetricThreshold',
    'ClassificationTrainingSettings',
    'ClusterUpdateParameters',
    'CocoExportSummary',
    'CodeConfiguration',
    'CodeContainer',
    'CodeContainerProperties',
    'CodeContainerResourceArmPaginatedResult',
    'CodeVersion',
    'CodeVersionProperties',
    'CodeVersionResourceArmPaginatedResult',
    'Collection',
    'ColumnTransformer',
    'CommandJob',
    'CommandJobLimits',
    'ComponentConfiguration',
    'ComponentContainer',
    'ComponentContainerProperties',
    'ComponentContainerResourceArmPaginatedResult',
    'ComponentVersion',
    'ComponentVersionProperties',
    'ComponentVersionResourceArmPaginatedResult',
    'Compute',
    'ComputeInstance',
    'ComputeInstanceApplication',
    'ComputeInstanceAutologgerSettings',
    'ComputeInstanceConnectivityEndpoints',
    'ComputeInstanceContainer',
    'ComputeInstanceCreatedBy',
    'ComputeInstanceDataDisk',
    'ComputeInstanceDataMount',
    'ComputeInstanceEnvironmentInfo',
    'ComputeInstanceLastOperation',
    'ComputeInstanceProperties',
    'ComputeInstanceSchema',
    'ComputeInstanceSshSettings',
    'ComputeInstanceVersion',
    'ComputeRecurrenceSchedule',
    'ComputeResource',
    'ComputeResourceSchema',
    'ComputeRuntimeDto',
    'ComputeSchedules',
    'ComputeSecrets',
    'ComputeStartStopSchedule',
    'ContainerResourceRequirements',
    'ContainerResourceSettings',
    'CosmosDbSettings',
    'CreateMonitorAction',
    'Cron',
    'CronTrigger',
    'CsvExportSummary',
    'CustomForecastHorizon',
    'CustomInferencingServer',
    'CustomKeys',
    'CustomKeysWorkspaceConnectionProperties',
    'CustomMetricThreshold',
    'CustomModelJobInput',
    'CustomModelJobOutput',
    'CustomMonitoringSignal',
    'CustomNCrossValidations',
    'CustomSeasonality',
    'CustomService',
    'CustomTargetLags',
    'CustomTargetRollingWindowSize',
    'DataCollector',
    'DataContainer',
    'DataContainerProperties',
    'DataContainerResourceArmPaginatedResult',
    'DataDriftMetricThresholdBase',
    'DataDriftMonitoringSignal',
    'DataFactory',
    'DataImport',
    'DataImportSource',
    'DataLakeAnalytics',
    'DataLakeAnalyticsSchema',
    'DataLakeAnalyticsSchemaProperties',
    'DataPathAssetReference',
    'DataQualityMetricThresholdBase',
    'DataQualityMonitoringSignal',
    'DataVersionBase',
    'DataVersionBaseProperties',
    'DataVersionBaseResourceArmPaginatedResult',
    'DatabaseSource',
    'Databricks',
    'DatabricksComputeSecrets',
    'DatabricksComputeSecretsProperties',
    'DatabricksProperties',
    'DatabricksSchema',
    'DatasetExportSummary',
    'Datastore',
    'DatastoreCredentials',
    'DatastoreProperties',
    'DatastoreResourceArmPaginatedResult',
    'DatastoreSecrets',
    'DefaultScaleSettings',
    'DeploymentLogs',
    'DeploymentLogsRequest',
    'DeploymentResourceConfiguration',
    'DiagnoseRequestProperties',
    'DiagnoseResponseResult',
    'DiagnoseResponseResultValue',
    'DiagnoseResult',
    'DiagnoseWorkspaceParameters',
    'DistributionConfiguration',
    'Docker',
    'EarlyTerminationPolicy',
    'EncryptionKeyVaultUpdateProperties',
    'EncryptionProperty',
    'EncryptionUpdateProperties',
    'Endpoint',
    'EndpointAuthKeys',
    'EndpointAuthToken',
    'EndpointDeploymentPropertiesBase',
    'EndpointPropertiesBase',
    'EndpointScheduleAction',
    'EnvironmentContainer',
    'EnvironmentContainerProperties',
    'EnvironmentContainerResourceArmPaginatedResult',
    'EnvironmentVariable',
    'EnvironmentVersion',
    'EnvironmentVersionProperties',
    'EnvironmentVersionResourceArmPaginatedResult',
    'ErrorAdditionalInfo',
    'ErrorDetail',
    'ErrorResponse',
    'EstimatedVMPrice',
    'EstimatedVMPrices',
    'ExportSummary',
    'ExternalFQDNResponse',
    'FQDNEndpoint',
    'FQDNEndpointDetail',
    'FQDNEndpoints',
    'FQDNEndpointsPropertyBag',
    'Feature',
    'FeatureAttributionDriftMonitoringSignal',
    'FeatureAttributionMetricThreshold',
    'FeatureImportanceSettings',
    'FeatureProperties',
    'FeatureResourceArmPaginatedResult',
    'FeatureStoreSettings',
    'FeatureSubset',
    'FeatureWindow',
    'FeaturesetContainer',
    'FeaturesetContainerProperties',
    'FeaturesetContainerResourceArmPaginatedResult',
    'FeaturesetSpecification',
    'FeaturesetVersion',
    'FeaturesetVersionBackfillRequest',
    'FeaturesetVersionBackfillResponse',
    'FeaturesetVersionProperties',
    'FeaturesetVersionResourceArmPaginatedResult',
    'FeaturestoreEntityContainer',
    'FeaturestoreEntityContainerProperties',
    'FeaturestoreEntityContainerResourceArmPaginatedResult',
    'FeaturestoreEntityVersion',
    'FeaturestoreEntityVersionProperties',
    'FeaturestoreEntityVersionResourceArmPaginatedResult',
    'FeaturizationSettings',
    'FileSystemSource',
    'FixedInputData',
    'FlavorData',
    'ForecastHorizon',
    'Forecasting',
    'ForecastingSettings',
    'ForecastingTrainingSettings',
    'FqdnOutboundRule',
    'GenerationSafetyQualityMetricThreshold',
    'GenerationSafetyQualityMonitoringSignal',
    'GenerationTokenUsageMetricThreshold',
    'GenerationTokenUsageSignal',
    'GridSamplingAlgorithm',
    'GroupStatus',
    'HDInsight',
    'HDInsightProperties',
    'HDInsightSchema',
    'HdfsDatastore',
    'IdAssetReference',
    'IdentityConfiguration',
    'IdentityForCmk',
    'IdleShutdownSetting',
    'Image',
    'ImageClassification',
    'ImageClassificationBase',
    'ImageClassificationMultilabel',
    'ImageInstanceSegmentation',
    'ImageLimitSettings',
    'ImageMetadata',
    'ImageModelDistributionSettings',
    'ImageModelDistributionSettingsClassification',
    'ImageModelDistributionSettingsObjectDetection',
    'ImageModelSettings',
    'ImageModelSettingsClassification',
    'ImageModelSettingsObjectDetection',
    'ImageObjectDetection',
    'ImageObjectDetectionBase',
    'ImageSweepSettings',
    'ImageVertical',
    'ImportDataAction',
    'IndexColumn',
    'InferenceContainerProperties',
    'InferenceEndpoint',
    'InferenceEndpointProperties',
    'InferenceEndpointTrackedResourceArmPaginatedResult',
    'InferenceGroup',
    'InferenceGroupProperties',
    'InferenceGroupTrackedResourceArmPaginatedResult',
    'InferencePool',
    'InferencePoolProperties',
    'InferencePoolTrackedResourceArmPaginatedResult',
    'InferencingServer',
    'InstanceTypeSchema',
    'InstanceTypeSchemaResources',
    'IntellectualProperty',
    'JobBase',
    'JobBaseProperties',
    'JobBaseResourceArmPaginatedResult',
    'JobInput',
    'JobLimits',
    'JobOutput',
    'JobResourceConfiguration',
    'JobScheduleAction',
    'JobService',
    'JupyterKernelConfig',
    'KerberosCredentials',
    'KerberosKeytabCredentials',
    'KerberosKeytabSecrets',
    'KerberosPasswordCredentials',
    'KerberosPasswordSecrets',
    'KeyVaultProperties',
    'Kubernetes',
    'KubernetesOnlineDeployment',
    'KubernetesProperties',
    'KubernetesSchema',
    'LabelCategory',
    'LabelClass',
    'LabelingDataConfiguration',
    'LabelingJob',
    'LabelingJobImageProperties',
    'LabelingJobInstructions',
    'LabelingJobMediaProperties',
    'LabelingJobProperties',
    'LabelingJobResourceArmPaginatedResult',
    'LabelingJobTextProperties',
    'LakeHouseArtifact',
    'ListAmlUserFeatureResult',
    'ListNotebookKeysResult',
    'ListStorageAccountKeysResult',
    'ListUsagesResult',
    'ListWorkspaceKeysResult',
    'ListWorkspaceQuotas',
    'LiteralJobInput',
    'MLAssistConfiguration',
    'MLAssistConfigurationDisabled',
    'MLAssistConfigurationEnabled',
    'MLFlowModelJobInput',
    'MLFlowModelJobOutput',
    'MLTableData',
    'MLTableJobInput',
    'MLTableJobOutput',
    'ManagedComputeIdentity',
    'ManagedIdentity',
    'ManagedIdentityAuthTypeWorkspaceConnectionProperties',
    'ManagedNetworkProvisionOptions',
    'ManagedNetworkProvisionStatus',
    'ManagedNetworkSettings',
    'ManagedOnlineDeployment',
    'ManagedServiceIdentity',
    'MaterializationComputeResource',
    'MaterializationSettings',
    'MedianStoppingPolicy',
    'ModelConfiguration',
    'ModelContainer',
    'ModelContainerProperties',
    'ModelContainerResourceArmPaginatedResult',
    'ModelPackageInput',
    'ModelPerformanceMetricThresholdBase',
    'ModelPerformanceSignal',
    'ModelVersion',
    'ModelVersionProperties',
    'ModelVersionResourceArmPaginatedResult',
    'MonitorComputeConfigurationBase',
    'MonitorComputeIdentityBase',
    'MonitorDefinition',
    'MonitorEmailNotificationSettings',
    'MonitorNotificationSettings',
    'MonitorServerlessSparkCompute',
    'MonitoringDataSegment',
    'MonitoringFeatureFilterBase',
    'MonitoringInputDataBase',
    'MonitoringSignalBase',
    'MonitoringTarget',
    'MonitoringThreshold',
    'MonitoringWorkspaceConnection',
    'Mpi',
    'NCrossValidations',
    'NlpFixedParameters',
    'NlpParameterSubspace',
    'NlpSweepSettings',
    'NlpVertical',
    'NlpVerticalFeaturizationSettings',
    'NlpVerticalLimitSettings',
    'NodeStateCounts',
    'Nodes',
    'NoneAuthTypeWorkspaceConnectionProperties',
    'NoneDatastoreCredentials',
    'NotebookAccessTokenResult',
    'NotebookPreparationError',
    'NotebookResourceInfo',
    'NotificationSetting',
    'NumericalDataDriftMetricThreshold',
    'NumericalDataQualityMetricThreshold',
    'NumericalPredictionDriftMetricThreshold',
    'Objective',
    'OneLakeArtifact',
    'OneLakeDatastore',
    'OnlineDeployment',
    'OnlineDeploymentProperties',
    'OnlineDeploymentTrackedResourceArmPaginatedResult',
    'OnlineEndpoint',
    'OnlineEndpointProperties',
    'OnlineEndpointTrackedResourceArmPaginatedResult',
    'OnlineInferenceConfiguration',
    'OnlineRequestSettings',
    'OnlineScaleSettings',
    'Operation',
    'OperationDisplay',
    'OperationListResult',
    'OsPatchingStatus',
    'OutboundRule',
    'OutboundRuleBasicResource',
    'OutboundRuleListResult',
    'OutputPathAssetReference',
    'PATAuthTypeWorkspaceConnectionProperties',
    'PackageInputPathBase',
    'PackageInputPathId',
    'PackageInputPathUrl',
    'PackageInputPathVersion',
    'PackageRequest',
    'PackageResponse',
    'PaginatedComputeResourcesList',
    'PartialBatchDeployment',
    'PartialBatchDeploymentPartialMinimalTrackedResourceWithProperties',
    'PartialJobBase',
    'PartialJobBasePartialResource',
    'PartialManagedServiceIdentity',
    'PartialMinimalTrackedResource',
    'PartialMinimalTrackedResourceWithIdentity',
    'PartialMinimalTrackedResourceWithSku',
    'PartialMinimalTrackedResourceWithSkuAndIdentity',
    'PartialNotificationSetting',
    'PartialRegistryPartialTrackedResource',
    'PartialSku',
    'Password',
    'PendingUploadCredentialDto',
    'PendingUploadRequestDto',
    'PendingUploadResponseDto',
    'PersonalComputeInstanceSettings',
    'PipelineJob',
    'PoolEnvironmentConfiguration',
    'PoolModelConfiguration',
    'PoolStatus',
    'PredictionDriftMetricThresholdBase',
    'PredictionDriftMonitoringSignal',
    'PrivateEndpoint',
    'PrivateEndpointConnection',
    'PrivateEndpointConnectionListResult',
    'PrivateEndpointDestination',
    'PrivateEndpointOutboundRule',
    'PrivateEndpointResource',
    'PrivateLinkResource',
    'PrivateLinkResourceListResult',
    'PrivateLinkServiceConnectionState',
    'ProbeSettings',
    'ProgressMetrics',
    'PropertiesBase',
    'ProxyResource',
    'PyTorch',
    'QueueSettings',
    'QuotaBaseProperties',
    'QuotaUpdateParameters',
    'RandomSamplingAlgorithm',
    'Ray',
    'Recurrence',
    'RecurrenceSchedule',
    'RecurrenceTrigger',
    'RegenerateEndpointKeysRequest',
    'Registry',
    'RegistryListCredentialsResult',
    'RegistryPartialManagedServiceIdentity',
    'RegistryPrivateEndpointConnection',
    'RegistryPrivateLinkServiceConnectionState',
    'RegistryRegionArmDetails',
    'RegistryTrackedResourceArmPaginatedResult',
    'Regression',
    'RegressionModelPerformanceMetricThreshold',
    'RegressionTrainingSettings',
    'RequestConfiguration',
    'RequestLogging',
    'ResizeSchema',
    'Resource',
    'ResourceBase',
    'ResourceConfiguration',
    'ResourceId',
    'ResourceName',
    'ResourceQuota',
    'RollingInputData',
    'Route',
    'SASAuthTypeWorkspaceConnectionProperties',
    'SASCredentialDto',
    'SamplingAlgorithm',
    'SasDatastoreCredentials',
    'SasDatastoreSecrets',
    'ScaleSettings',
    'ScaleSettingsInformation',
    'Schedule',
    'ScheduleActionBase',
    'ScheduleBase',
    'ScheduleProperties',
    'ScheduleResourceArmPaginatedResult',
    'ScriptReference',
    'ScriptsToExecute',
    'Seasonality',
    'SecretConfiguration',
    'ServerlessComputeSettings',
    'ServerlessEndpoint',
    'ServerlessEndpointCapacityReservation',
    'ServerlessEndpointProperties',
    'ServerlessEndpointStatus',
    'ServerlessEndpointTrackedResourceArmPaginatedResult',
    'ServerlessInferenceEndpoint',
    'ServerlessOffer',
    'ServiceManagedResourcesSettings',
    'ServicePrincipalAuthTypeWorkspaceConnectionProperties',
    'ServicePrincipalDatastoreCredentials',
    'ServicePrincipalDatastoreSecrets',
    'ServiceTagDestination',
    'ServiceTagOutboundRule',
    'SetupScripts',
    'SharedPrivateLinkResource',
    'Sku',
    'SkuCapacity',
    'SkuResource',
    'SkuResourceArmPaginatedResult',
    'SkuSetting',
    'SparkJob',
    'SparkJobEntry',
    'SparkJobPythonEntry',
    'SparkJobScalaEntry',
    'SparkResourceConfiguration',
    'SslConfiguration',
    'StackEnsembleSettings',
    'StaticInputData',
    'StatusMessage',
    'StorageAccountDetails',
    'SweepJob',
    'SweepJobLimits',
    'SynapseSpark',
    'SynapseSparkProperties',
    'SystemCreatedAcrAccount',
    'SystemCreatedStorageAccount',
    'SystemData',
    'SystemService',
    'TableFixedParameters',
    'TableParameterSubspace',
    'TableSweepSettings',
    'TableVertical',
    'TableVerticalFeaturizationSettings',
    'TableVerticalLimitSettings',
    'TargetLags',
    'TargetRollingWindowSize',
    'TargetUtilizationScaleSettings',
    'TensorFlow',
    'TextClassification',
    'TextClassificationMultilabel',
    'TextNer',
    'TmpfsOptions',
    'TopNFeaturesByAttribution',
    'TrackedResource',
    'TrainingSettings',
    'TrialComponent',
    'TriggerBase',
    'TritonInferencingServer',
    'TritonModelJobInput',
    'TritonModelJobOutput',
    'TruncationSelectionPolicy',
    'UpdateWorkspaceQuotas',
    'UpdateWorkspaceQuotasResult',
    'UriFileDataVersion',
    'UriFileJobInput',
    'UriFileJobOutput',
    'UriFolderDataVersion',
    'UriFolderJobInput',
    'UriFolderJobOutput',
    'Usage',
    'UsageName',
    'UserAccountCredentials',
    'UserAssignedIdentity',
    'UserCreatedAcrAccount',
    'UserCreatedStorageAccount',
    'UserIdentity',
    'UsernamePasswordAuthTypeWorkspaceConnectionProperties',
    'VirtualMachine',
    'VirtualMachineImage',
    'VirtualMachineSchema',
    'VirtualMachineSchemaProperties',
    'VirtualMachineSecrets',
    'VirtualMachineSecretsSchema',
    'VirtualMachineSize',
    'VirtualMachineSizeListResult',
    'VirtualMachineSshCredentials',
    'VolumeDefinition',
    'VolumeOptions',
    'Webhook',
    'Workspace',
    'WorkspaceConnectionAccessKey',
    'WorkspaceConnectionApiKey',
    'WorkspaceConnectionManagedIdentity',
    'WorkspaceConnectionPersonalAccessToken',
    'WorkspaceConnectionPropertiesV2',
    'WorkspaceConnectionPropertiesV2BasicResource',
    'WorkspaceConnectionPropertiesV2BasicResourceArmPaginatedResult',
    'WorkspaceConnectionServicePrincipal',
    'WorkspaceConnectionSharedAccessSignature',
    'WorkspaceConnectionUpdateParameter',
    'WorkspaceConnectionUsernamePassword',
    'WorkspaceHubConfig',
    'WorkspaceListResult',
    'WorkspacePrivateEndpointResource',
    'WorkspaceUpdateParameters',
    'ActionType',
    'AllocationState',
    'ApplicationSharingPolicy',
    'AssetProvisioningState',
    'AuthMode',
    'AutoDeleteCondition',
    'AutoRebuildSetting',
    'Autosave',
    'BaseEnvironmentSourceType',
    'BatchDeploymentConfigurationType',
    'BatchLoggingLevel',
    'BatchOutputAction',
    'BillingCurrency',
    'BlockedTransformers',
    'Caching',
    'CategoricalDataDriftMetric',
    'CategoricalDataQualityMetric',
    'CategoricalPredictionDriftMetric',
    'ClassificationModelPerformanceMetric',
    'ClassificationModels',
    'ClassificationMultilabelPrimaryMetrics',
    'ClassificationPrimaryMetrics',
    'ClusterPurpose',
    'ComputeInstanceAuthorizationType',
    'ComputeInstanceState',
    'ComputePowerAction',
    'ComputeRecurrenceFrequency',
    'ComputeTriggerType',
    'ComputeType',
    'ComputeWeekDay',
    'ConnectionAuthType',
    'ConnectionCategory',
    'ContainerType',
    'CreatedByType',
    'CredentialsType',
    'DataAvailabilityStatus',
    'DataCollectionMode',
    'DataImportSourceType',
    'DataType',
    'DatastoreType',
    'DeploymentProvisioningState',
    'DiagnoseResultLevel',
    'DistributionType',
    'EarlyTerminationPolicyType',
    'EgressPublicNetworkAccessType',
    'EmailNotificationEnableType',
    'EncryptionStatus',
    'EndpointAuthMode',
    'EndpointComputeType',
    'EndpointProvisioningState',
    'EndpointServiceConnectionStatus',
    'EnvironmentType',
    'EnvironmentVariableType',
    'ExportFormatType',
    'FeatureAttributionMetric',
    'FeatureDataType',
    'FeatureImportanceMode',
    'FeatureLags',
    'FeaturizationMode',
    'ForecastHorizonMode',
    'ForecastingModels',
    'ForecastingPrimaryMetrics',
    'GenerationSafetyQualityMetric',
    'GenerationTokenUsageMetric',
    'Goal',
    'IdentityConfigurationType',
    'ImageAnnotationType',
    'ImageType',
    'IncrementalDataRefresh',
    'InferencingServerType',
    'InputDeliveryMode',
    'InputPathType',
    'InstanceSegmentationPrimaryMetrics',
    'IsolationMode',
    'JobInputType',
    'JobLimitsType',
    'JobOutputType',
    'JobProvisioningState',
    'JobStatus',
    'JobTier',
    'JobType',
    'KeyType',
    'LearningRateScheduler',
    'ListViewType',
    'LoadBalancerType',
    'LogTrainingMetrics',
    'LogValidationLoss',
    'LogVerbosity',
    'MLAssistConfigurationType',
    'MLFlowAutologgerState',
    'ManagedNetworkStatus',
    'ManagedServiceIdentityType',
    'MaterializationStoreType',
    'MediaType',
    'MlflowAutologger',
    'ModelSize',
    'ModelTaskType',
    'MonitorComputeIdentityType',
    'MonitorComputeType',
    'MonitoringFeatureDataType',
    'MonitoringFeatureFilterType',
    'MonitoringInputDataType',
    'MonitoringModelType',
    'MonitoringNotificationType',
    'MonitoringSignalType',
    'MountAction',
    'MountState',
    'MultiSelect',
    'NCrossValidationsMode',
    'Network',
    'NlpLearningRateScheduler',
    'NodeState',
    'NodesValueType',
    'NumericalDataDriftMetric',
    'NumericalDataQualityMetric',
    'NumericalPredictionDriftMetric',
    'ObjectDetectionPrimaryMetrics',
    'OneLakeArtifactType',
    'OperatingSystemType',
    'OperationName',
    'OperationStatus',
    'OperationTrigger',
    'OrderString',
    'Origin',
    'OsType',
    'OutputDeliveryMode',
    'PackageBuildState',
    'PackageInputDeliveryMode',
    'PackageInputType',
    'PatchStatus',
    'PendingUploadCredentialType',
    'PendingUploadType',
    'PoolProvisioningState',
    'PrivateEndpointConnectionProvisioningState',
    'ProtectionLevel',
    'Protocol',
    'ProvisioningState',
    'ProvisioningStatus',
    'PublicNetworkAccessType',
    'QuotaUnit',
    'RandomSamplingAlgorithmRule',
    'RecurrenceFrequency',
    'ReferenceType',
    'RegressionModelPerformanceMetric',
    'RegressionModels',
    'RegressionPrimaryMetrics',
    'RemoteLoginPortPublicAccess',
    'RollingRateType',
    'RuleAction',
    'RuleCategory',
    'RuleStatus',
    'RuleType',
    'SamplingAlgorithmType',
    'ScaleType',
    'ScheduleActionType',
    'ScheduleListViewType',
    'ScheduleProvisioningState',
    'ScheduleProvisioningStatus',
    'ScheduleStatus',
    'SeasonalityMode',
    'SecretsType',
    'ServerlessInferenceEndpointAuthMode',
    'ServiceDataAccessAuthIdentity',
    'ShortSeriesHandlingConfiguration',
    'SkuScaleType',
    'SkuTier',
    'SourceType',
    'SparkJobEntryType',
    'SshPublicAccess',
    'SslConfigStatus',
    'StackMetaLearnerType',
    'Status',
    'StatusMessageLevel',
    'StochasticOptimizer',
    'StorageAccountType',
    'TargetAggregationFunction',
    'TargetLagsMode',
    'TargetRollingWindowSizeMode',
    'TaskType',
    'TextAnnotationType',
    'TrainingMode',
    'TriggerType',
    'UnderlyingResourceAction',
    'UnitOfMeasure',
    'UsageUnit',
    'UseStl',
    'VMPriceOSType',
    'VMTier',
    'ValidationMetricType',
    'VmPriority',
    'VolumeDefinitionType',
    'WebhookType',
    'WeekDay',
]
