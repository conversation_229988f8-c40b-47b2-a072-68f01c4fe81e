# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from azure.core.exceptions import HttpResponseError
import msrest.serialization


class AddOrModifyRunServiceInstancesRequest(msrest.serialization.Model):
    """AddOrModifyRunServiceInstancesRequest.

    :ivar instances: Dictionary of :code:`<ServiceInstance>`.
    :vartype instances: dict[str, ~azure.mgmt.machinelearningservices.models.ServiceInstance]
    """

    _attribute_map = {
        'instances': {'key': 'instances', 'type': '{ServiceInstance}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword instances: Dictionary of :code:`<ServiceInstance>`.
        :paramtype instances: dict[str, ~azure.mgmt.machinelearningservices.models.ServiceInstance]
        """
        super(AddOrModifyRunServiceInstancesRequest, self).__init__(**kwargs)
        self.instances = kwargs.get('instances', None)


class Artifact(msrest.serialization.Model):
    """Details of an Artifact.

    All required parameters must be populated in order to send to Azure.

    :ivar artifact_id: The identifier of an Artifact. Format of ArtifactId -
     {Origin}/{Container}/{Path}.
    :vartype artifact_id: str
    :ivar origin: Required. The origin of the Artifact creation request. Available origins are
     'ExperimentRun', 'LocalUpload', 'WebUpload', 'Dataset' and 'Unknown'.
    :vartype origin: str
    :ivar container: Required. The name of container. Artifacts can be grouped by container.
    :vartype container: str
    :ivar path: Required. The path to the Artifact in a container.
    :vartype path: str
    :ivar etag: The Etag of the Artifact.
    :vartype etag: str
    :ivar created_time: The Date and Time at which the Artifact is created. The DateTime is in UTC.
    :vartype created_time: ~datetime.datetime
    :ivar data_path:
    :vartype data_path: ~azure.mgmt.machinelearningservices.models.ArtifactDataPath
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        'origin': {'required': True},
        'container': {'required': True},
        'path': {'required': True},
    }

    _attribute_map = {
        'artifact_id': {'key': 'artifactId', 'type': 'str'},
        'origin': {'key': 'origin', 'type': 'str'},
        'container': {'key': 'container', 'type': 'str'},
        'path': {'key': 'path', 'type': 'str'},
        'etag': {'key': 'etag', 'type': 'str'},
        'created_time': {'key': 'createdTime', 'type': 'iso-8601'},
        'data_path': {'key': 'dataPath', 'type': 'ArtifactDataPath'},
        'tags': {'key': 'tags', 'type': '{str}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword artifact_id: The identifier of an Artifact. Format of ArtifactId -
         {Origin}/{Container}/{Path}.
        :paramtype artifact_id: str
        :keyword origin: Required. The origin of the Artifact creation request. Available origins are
         'ExperimentRun', 'LocalUpload', 'WebUpload', 'Dataset' and 'Unknown'.
        :paramtype origin: str
        :keyword container: Required. The name of container. Artifacts can be grouped by container.
        :paramtype container: str
        :keyword path: Required. The path to the Artifact in a container.
        :paramtype path: str
        :keyword etag: The Etag of the Artifact.
        :paramtype etag: str
        :keyword created_time: The Date and Time at which the Artifact is created. The DateTime is in
         UTC.
        :paramtype created_time: ~datetime.datetime
        :keyword data_path:
        :paramtype data_path: ~azure.mgmt.machinelearningservices.models.ArtifactDataPath
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(Artifact, self).__init__(**kwargs)
        self.artifact_id = kwargs.get('artifact_id', None)
        self.origin = kwargs['origin']
        self.container = kwargs['container']
        self.path = kwargs['path']
        self.etag = kwargs.get('etag', None)
        self.created_time = kwargs.get('created_time', None)
        self.data_path = kwargs.get('data_path', None)
        self.tags = kwargs.get('tags', None)


class ArtifactContentInformation(msrest.serialization.Model):
    """Details of an Artifact Content Information.

    :ivar content_uri: The URI of the content.
    :vartype content_uri: str
    :ivar origin: The origin of the Artifact creation request. Available origins are
     'ExperimentRun', 'LocalUpload', 'WebUpload', 'Dataset', 'ComputeRecord', 'Metric', and
     'Unknown'.
    :vartype origin: str
    :ivar container: The name of container. Artifacts can be grouped by container.
    :vartype container: str
    :ivar path: The path to the Artifact in a container.
    :vartype path: str
    :ivar tags: A set of tags. The tags on the artifact.
    :vartype tags: dict[str, str]
    """

    _attribute_map = {
        'content_uri': {'key': 'contentUri', 'type': 'str'},
        'origin': {'key': 'origin', 'type': 'str'},
        'container': {'key': 'container', 'type': 'str'},
        'path': {'key': 'path', 'type': 'str'},
        'tags': {'key': 'tags', 'type': '{str}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword content_uri: The URI of the content.
        :paramtype content_uri: str
        :keyword origin: The origin of the Artifact creation request. Available origins are
         'ExperimentRun', 'LocalUpload', 'WebUpload', 'Dataset', 'ComputeRecord', 'Metric', and
         'Unknown'.
        :paramtype origin: str
        :keyword container: The name of container. Artifacts can be grouped by container.
        :paramtype container: str
        :keyword path: The path to the Artifact in a container.
        :paramtype path: str
        :keyword tags: A set of tags. The tags on the artifact.
        :paramtype tags: dict[str, str]
        """
        super(ArtifactContentInformation, self).__init__(**kwargs)
        self.content_uri = kwargs.get('content_uri', None)
        self.origin = kwargs.get('origin', None)
        self.container = kwargs.get('container', None)
        self.path = kwargs.get('path', None)
        self.tags = kwargs.get('tags', None)


class ArtifactDataPath(msrest.serialization.Model):
    """ArtifactDataPath.

    :ivar data_store_name:
    :vartype data_store_name: str
    :ivar relative_path:
    :vartype relative_path: str
    :ivar sql_data_path:
    :vartype sql_data_path: ~azure.mgmt.machinelearningservices.models.SqlDataPath
    """

    _attribute_map = {
        'data_store_name': {'key': 'dataStoreName', 'type': 'str'},
        'relative_path': {'key': 'relativePath', 'type': 'str'},
        'sql_data_path': {'key': 'sqlDataPath', 'type': 'SqlDataPath'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword data_store_name:
        :paramtype data_store_name: str
        :keyword relative_path:
        :paramtype relative_path: str
        :keyword sql_data_path:
        :paramtype sql_data_path: ~azure.mgmt.machinelearningservices.models.SqlDataPath
        """
        super(ArtifactDataPath, self).__init__(**kwargs)
        self.data_store_name = kwargs.get('data_store_name', None)
        self.relative_path = kwargs.get('relative_path', None)
        self.sql_data_path = kwargs.get('sql_data_path', None)


class ArtifactPath(msrest.serialization.Model):
    """Details of an Artifact Path.

    All required parameters must be populated in order to send to Azure.

    :ivar path: Required. The path to the Artifact in a container.
    :vartype path: str
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        'path': {'required': True},
    }

    _attribute_map = {
        'path': {'key': 'path', 'type': 'str'},
        'tags': {'key': 'tags', 'type': '{str}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword path: Required. The path to the Artifact in a container.
        :paramtype path: str
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(ArtifactPath, self).__init__(**kwargs)
        self.path = kwargs['path']
        self.tags = kwargs.get('tags', None)


class ArtifactPathList(msrest.serialization.Model):
    """Contains list of Artifact Paths.

    All required parameters must be populated in order to send to Azure.

    :ivar paths: Required. List of Artifact Paths.
    :vartype paths: list[~azure.mgmt.machinelearningservices.models.ArtifactPath]
    """

    _validation = {
        'paths': {'required': True},
    }

    _attribute_map = {
        'paths': {'key': 'paths', 'type': '[ArtifactPath]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword paths: Required. List of Artifact Paths.
        :paramtype paths: list[~azure.mgmt.machinelearningservices.models.ArtifactPath]
        """
        super(ArtifactPathList, self).__init__(**kwargs)
        self.paths = kwargs['paths']


class BaseEvent(msrest.serialization.Model):
    """Base event is the envelope used to post event data to the Event controller.

    :ivar timestamp:
    :vartype timestamp: ~datetime.datetime
    :ivar name:
    :vartype name: str
    :ivar data: Anything.
    :vartype data: any
    """

    _attribute_map = {
        'timestamp': {'key': 'timestamp', 'type': 'iso-8601'},
        'name': {'key': 'name', 'type': 'str'},
        'data': {'key': 'data', 'type': 'object'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword timestamp:
        :paramtype timestamp: ~datetime.datetime
        :keyword name:
        :paramtype name: str
        :keyword data: Anything.
        :paramtype data: any
        """
        super(BaseEvent, self).__init__(**kwargs)
        self.timestamp = kwargs.get('timestamp', None)
        self.name = kwargs.get('name', None)
        self.data = kwargs.get('data', None)


class BatchAddOrModifyRunRequest(msrest.serialization.Model):
    """BatchAddOrModifyRunRequest.

    :ivar runs:
    :vartype runs: list[~azure.mgmt.machinelearningservices.models.CreateRun]
    """

    _attribute_map = {
        'runs': {'key': 'runs', 'type': '[CreateRun]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword runs:
        :paramtype runs: list[~azure.mgmt.machinelearningservices.models.CreateRun]
        """
        super(BatchAddOrModifyRunRequest, self).__init__(**kwargs)
        self.runs = kwargs.get('runs', None)


class BatchArtifactContentInformationResult(msrest.serialization.Model):
    """Results of the Batch Artifact Content Information request.

    :ivar artifacts: Artifact details of the Artifact Ids requested.
    :vartype artifacts: dict[str, ~azure.mgmt.machinelearningservices.models.Artifact]
    :ivar artifact_content_information: Artifact Content Information details of the Artifact Ids
     requested.
    :vartype artifact_content_information: dict[str,
     ~azure.mgmt.machinelearningservices.models.ArtifactContentInformation]
    :ivar errors: Errors occurred while fetching the requested Artifact Ids.
    :vartype errors: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
    """

    _attribute_map = {
        'artifacts': {'key': 'artifacts', 'type': '{Artifact}'},
        'artifact_content_information': {'key': 'artifactContentInformation', 'type': '{ArtifactContentInformation}'},
        'errors': {'key': 'errors', 'type': '{ErrorResponse}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword artifacts: Artifact details of the Artifact Ids requested.
        :paramtype artifacts: dict[str, ~azure.mgmt.machinelearningservices.models.Artifact]
        :keyword artifact_content_information: Artifact Content Information details of the Artifact Ids
         requested.
        :paramtype artifact_content_information: dict[str,
         ~azure.mgmt.machinelearningservices.models.ArtifactContentInformation]
        :keyword errors: Errors occurred while fetching the requested Artifact Ids.
        :paramtype errors: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
        """
        super(BatchArtifactContentInformationResult, self).__init__(**kwargs)
        self.artifacts = kwargs.get('artifacts', None)
        self.artifact_content_information = kwargs.get('artifact_content_information', None)
        self.errors = kwargs.get('errors', None)


class BatchEventCommand(msrest.serialization.Model):
    """BatchEventCommand.

    :ivar events:
    :vartype events: list[~azure.mgmt.machinelearningservices.models.BaseEvent]
    """

    _attribute_map = {
        'events': {'key': 'events', 'type': '[BaseEvent]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword events:
        :paramtype events: list[~azure.mgmt.machinelearningservices.models.BaseEvent]
        """
        super(BatchEventCommand, self).__init__(**kwargs)
        self.events = kwargs.get('events', None)


class BatchEventCommandResult(msrest.serialization.Model):
    """BatchEventCommandResult.

    :ivar errors:
    :vartype errors:
     list[~azure.mgmt.machinelearningservices.models.KeyValuePairBaseEventErrorResponse]
    :ivar successes:
    :vartype successes: list[str]
    """

    _attribute_map = {
        'errors': {'key': 'errors', 'type': '[KeyValuePairBaseEventErrorResponse]'},
        'successes': {'key': 'successes', 'type': '[str]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword errors:
        :paramtype errors:
         list[~azure.mgmt.machinelearningservices.models.KeyValuePairBaseEventErrorResponse]
        :keyword successes:
        :paramtype successes: list[str]
        """
        super(BatchEventCommandResult, self).__init__(**kwargs)
        self.errors = kwargs.get('errors', None)
        self.successes = kwargs.get('successes', None)


class BatchIMetricV2(msrest.serialization.Model):
    """BatchIMetricV2.

    :ivar values:
    :vartype values: list[~azure.mgmt.machinelearningservices.models.IMetricV2]
    :ivar report_errors:
    :vartype report_errors: bool
    """

    _attribute_map = {
        'values': {'key': 'values', 'type': '[IMetricV2]'},
        'report_errors': {'key': 'reportErrors', 'type': 'bool'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword values:
        :paramtype values: list[~azure.mgmt.machinelearningservices.models.IMetricV2]
        :keyword report_errors:
        :paramtype report_errors: bool
        """
        super(BatchIMetricV2, self).__init__(**kwargs)
        self.values = kwargs.get('values', None)
        self.report_errors = kwargs.get('report_errors', None)


class BatchRequest1(msrest.serialization.Model):
    """BatchRequest1.

    :ivar requests: Dictionary of :code:`<GetRunDataRequest>`.
    :vartype requests: dict[str, ~azure.mgmt.machinelearningservices.models.GetRunDataRequest]
    """

    _attribute_map = {
        'requests': {'key': 'requests', 'type': '{GetRunDataRequest}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword requests: Dictionary of :code:`<GetRunDataRequest>`.
        :paramtype requests: dict[str, ~azure.mgmt.machinelearningservices.models.GetRunDataRequest]
        """
        super(BatchRequest1, self).__init__(**kwargs)
        self.requests = kwargs.get('requests', None)


class BatchResult1(msrest.serialization.Model):
    """BatchResult1.

    :ivar successful_results: Dictionary of :code:`<GetRunDataResult>`.
    :vartype successful_results: dict[str,
     ~azure.mgmt.machinelearningservices.models.GetRunDataResult]
    :ivar failed_results: Dictionary of :code:`<ErrorResponse>`.
    :vartype failed_results: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
    """

    _attribute_map = {
        'successful_results': {'key': 'successfulResults', 'type': '{GetRunDataResult}'},
        'failed_results': {'key': 'failedResults', 'type': '{ErrorResponse}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword successful_results: Dictionary of :code:`<GetRunDataResult>`.
        :paramtype successful_results: dict[str,
         ~azure.mgmt.machinelearningservices.models.GetRunDataResult]
        :keyword failed_results: Dictionary of :code:`<ErrorResponse>`.
        :paramtype failed_results: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
        """
        super(BatchResult1, self).__init__(**kwargs)
        self.successful_results = kwargs.get('successful_results', None)
        self.failed_results = kwargs.get('failed_results', None)


class BatchRunResult(msrest.serialization.Model):
    """BatchRunResult.

    :ivar runs: Dictionary of :code:`<Run>`.
    :vartype runs: dict[str, ~azure.mgmt.machinelearningservices.models.Run]
    :ivar errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype errors: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
    """

    _attribute_map = {
        'runs': {'key': 'runs', 'type': '{Run}'},
        'errors': {'key': 'errors', 'type': '{ErrorResponse}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword runs: Dictionary of :code:`<Run>`.
        :paramtype runs: dict[str, ~azure.mgmt.machinelearningservices.models.Run]
        :keyword errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype errors: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
        """
        super(BatchRunResult, self).__init__(**kwargs)
        self.runs = kwargs.get('runs', None)
        self.errors = kwargs.get('errors', None)


class Compute(msrest.serialization.Model):
    """Compute.

    :ivar target:
    :vartype target: str
    :ivar target_type:
    :vartype target_type: str
    :ivar vm_size:
    :vartype vm_size: str
    :ivar instance_count:
    :vartype instance_count: int
    :ivar gpu_count:
    :vartype gpu_count: int
    :ivar priority:
    :vartype priority: str
    :ivar region:
    :vartype region: str
    """

    _attribute_map = {
        'target': {'key': 'target', 'type': 'str'},
        'target_type': {'key': 'targetType', 'type': 'str'},
        'vm_size': {'key': 'vmSize', 'type': 'str'},
        'instance_count': {'key': 'instanceCount', 'type': 'int'},
        'gpu_count': {'key': 'gpuCount', 'type': 'int'},
        'priority': {'key': 'priority', 'type': 'str'},
        'region': {'key': 'region', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword target:
        :paramtype target: str
        :keyword target_type:
        :paramtype target_type: str
        :keyword vm_size:
        :paramtype vm_size: str
        :keyword instance_count:
        :paramtype instance_count: int
        :keyword gpu_count:
        :paramtype gpu_count: int
        :keyword priority:
        :paramtype priority: str
        :keyword region:
        :paramtype region: str
        """
        super(Compute, self).__init__(**kwargs)
        self.target = kwargs.get('target', None)
        self.target_type = kwargs.get('target_type', None)
        self.vm_size = kwargs.get('vm_size', None)
        self.instance_count = kwargs.get('instance_count', None)
        self.gpu_count = kwargs.get('gpu_count', None)
        self.priority = kwargs.get('priority', None)
        self.region = kwargs.get('region', None)


class ComputeRequest(msrest.serialization.Model):
    """ComputeRequest.

    :ivar node_count:
    :vartype node_count: int
    :ivar gpu_count:
    :vartype gpu_count: int
    """

    _attribute_map = {
        'node_count': {'key': 'nodeCount', 'type': 'int'},
        'gpu_count': {'key': 'gpuCount', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword node_count:
        :paramtype node_count: int
        :keyword gpu_count:
        :paramtype gpu_count: int
        """
        super(ComputeRequest, self).__init__(**kwargs)
        self.node_count = kwargs.get('node_count', None)
        self.gpu_count = kwargs.get('gpu_count', None)


class CreatedFrom(msrest.serialization.Model):
    """CreatedFrom.

    :ivar type:  The only acceptable values to pass in are None and "Notebook". The default value
     is None.
    :vartype type: str
    :ivar location_type:  The only acceptable values to pass in are None and "ArtifactId". The
     default value is None.
    :vartype location_type: str
    :ivar location:
    :vartype location: str
    """

    _attribute_map = {
        'type': {'key': 'type', 'type': 'str'},
        'location_type': {'key': 'locationType', 'type': 'str'},
        'location': {'key': 'location', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword type:  The only acceptable values to pass in are None and "Notebook". The default
         value is None.
        :paramtype type: str
        :keyword location_type:  The only acceptable values to pass in are None and "ArtifactId". The
         default value is None.
        :paramtype location_type: str
        :keyword location:
        :paramtype location: str
        """
        super(CreatedFrom, self).__init__(**kwargs)
        self.type = kwargs.get('type', None)
        self.location_type = kwargs.get('location_type', None)
        self.location = kwargs.get('location', None)


class CreateRun(msrest.serialization.Model):
    """CreateRun.

    :ivar run_id: The identifier for the run. Run IDs must be less than 256 characters and contain
     only alphanumeric characters with dashes and underscores.
    :vartype run_id: str
    :ivar parent_run_id: The parent of the run if the run is hierarchical; otherwise, Null.
    :vartype parent_run_id: str
    :ivar experiment_id: The Id of the experiment that created this run.
    :vartype experiment_id: str
    :ivar status: The status of the run. The Status string value maps to the RunStatus Enum.
    :vartype status: str
    :ivar start_time_utc: The start time of the run in UTC.
    :vartype start_time_utc: ~datetime.datetime
    :ivar end_time_utc: The end time of the run in UTC.
    :vartype end_time_utc: ~datetime.datetime
    :ivar options:
    :vartype options: ~azure.mgmt.machinelearningservices.models.RunOptions
    :ivar is_virtual: A virtual run can set an active child run that will override the virtual run
     status and properties.
    :vartype is_virtual: bool
    :ivar display_name:
    :vartype display_name: str
    :ivar name:
    :vartype name: str
    :ivar data_container_id:
    :vartype data_container_id: str
    :ivar description:
    :vartype description: str
    :ivar hidden:
    :vartype hidden: bool
    :ivar run_type:
    :vartype run_type: str
    :ivar run_type_v2:
    :vartype run_type_v2: ~azure.mgmt.machinelearningservices.models.RunTypeV2
    :ivar properties: Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar parameters: Dictionary of :code:`<any>`.
    :vartype parameters: dict[str, any]
    :ivar action_uris: Dictionary of :code:`<string>`.
    :vartype action_uris: dict[str, str]
    :ivar script_name:
    :vartype script_name: str
    :ivar target:
    :vartype target: str
    :ivar unique_child_run_compute_targets:
    :vartype unique_child_run_compute_targets: list[str]
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar settings: Dictionary of :code:`<string>`.
    :vartype settings: dict[str, str]
    :ivar services: Dictionary of :code:`<EndpointSetting>`.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.EndpointSetting]
    :ivar input_datasets:
    :vartype input_datasets: list[~azure.mgmt.machinelearningservices.models.DatasetLineage]
    :ivar output_datasets:
    :vartype output_datasets: list[~azure.mgmt.machinelearningservices.models.OutputDatasetLineage]
    :ivar run_definition: Anything.
    :vartype run_definition: any
    :ivar job_specification: Anything.
    :vartype job_specification: any
    :ivar primary_metric_name:
    :vartype primary_metric_name: str
    :ivar created_from:
    :vartype created_from: ~azure.mgmt.machinelearningservices.models.CreatedFrom
    :ivar cancel_uri:
    :vartype cancel_uri: str
    :ivar complete_uri:
    :vartype complete_uri: str
    :ivar diagnostics_uri:
    :vartype diagnostics_uri: str
    :ivar compute_request:
    :vartype compute_request: ~azure.mgmt.machinelearningservices.models.ComputeRequest
    :ivar compute:
    :vartype compute: ~azure.mgmt.machinelearningservices.models.Compute
    :ivar retain_for_lifetime_of_workspace:
    :vartype retain_for_lifetime_of_workspace: bool
    :ivar queueing_info:
    :vartype queueing_info: ~azure.mgmt.machinelearningservices.models.QueueingInfo
    :ivar active_child_run_id: The RunId of the active child on a virtual run.
    :vartype active_child_run_id: str
    :ivar inputs: Dictionary of :code:`<TypedAssetReference>`.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
    :ivar outputs: Dictionary of :code:`<TypedAssetReference>`.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
    """

    _validation = {
        'unique_child_run_compute_targets': {'unique': True},
        'input_datasets': {'unique': True},
        'output_datasets': {'unique': True},
    }

    _attribute_map = {
        'run_id': {'key': 'runId', 'type': 'str'},
        'parent_run_id': {'key': 'parentRunId', 'type': 'str'},
        'experiment_id': {'key': 'experimentId', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'start_time_utc': {'key': 'startTimeUtc', 'type': 'iso-8601'},
        'end_time_utc': {'key': 'endTimeUtc', 'type': 'iso-8601'},
        'options': {'key': 'options', 'type': 'RunOptions'},
        'is_virtual': {'key': 'isVirtual', 'type': 'bool'},
        'display_name': {'key': 'displayName', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'data_container_id': {'key': 'dataContainerId', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'hidden': {'key': 'hidden', 'type': 'bool'},
        'run_type': {'key': 'runType', 'type': 'str'},
        'run_type_v2': {'key': 'runTypeV2', 'type': 'RunTypeV2'},
        'properties': {'key': 'properties', 'type': '{str}'},
        'parameters': {'key': 'parameters', 'type': '{object}'},
        'action_uris': {'key': 'actionUris', 'type': '{str}'},
        'script_name': {'key': 'scriptName', 'type': 'str'},
        'target': {'key': 'target', 'type': 'str'},
        'unique_child_run_compute_targets': {'key': 'uniqueChildRunComputeTargets', 'type': '[str]'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'settings': {'key': 'settings', 'type': '{str}'},
        'services': {'key': 'services', 'type': '{EndpointSetting}'},
        'input_datasets': {'key': 'inputDatasets', 'type': '[DatasetLineage]'},
        'output_datasets': {'key': 'outputDatasets', 'type': '[OutputDatasetLineage]'},
        'run_definition': {'key': 'runDefinition', 'type': 'object'},
        'job_specification': {'key': 'jobSpecification', 'type': 'object'},
        'primary_metric_name': {'key': 'primaryMetricName', 'type': 'str'},
        'created_from': {'key': 'createdFrom', 'type': 'CreatedFrom'},
        'cancel_uri': {'key': 'cancelUri', 'type': 'str'},
        'complete_uri': {'key': 'completeUri', 'type': 'str'},
        'diagnostics_uri': {'key': 'diagnosticsUri', 'type': 'str'},
        'compute_request': {'key': 'computeRequest', 'type': 'ComputeRequest'},
        'compute': {'key': 'compute', 'type': 'Compute'},
        'retain_for_lifetime_of_workspace': {'key': 'retainForLifetimeOfWorkspace', 'type': 'bool'},
        'queueing_info': {'key': 'queueingInfo', 'type': 'QueueingInfo'},
        'active_child_run_id': {'key': 'activeChildRunId', 'type': 'str'},
        'inputs': {'key': 'inputs', 'type': '{TypedAssetReference}'},
        'outputs': {'key': 'outputs', 'type': '{TypedAssetReference}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_id: The identifier for the run. Run IDs must be less than 256 characters and
         contain only alphanumeric characters with dashes and underscores.
        :paramtype run_id: str
        :keyword parent_run_id: The parent of the run if the run is hierarchical; otherwise, Null.
        :paramtype parent_run_id: str
        :keyword experiment_id: The Id of the experiment that created this run.
        :paramtype experiment_id: str
        :keyword status: The status of the run. The Status string value maps to the RunStatus Enum.
        :paramtype status: str
        :keyword start_time_utc: The start time of the run in UTC.
        :paramtype start_time_utc: ~datetime.datetime
        :keyword end_time_utc: The end time of the run in UTC.
        :paramtype end_time_utc: ~datetime.datetime
        :keyword options:
        :paramtype options: ~azure.mgmt.machinelearningservices.models.RunOptions
        :keyword is_virtual: A virtual run can set an active child run that will override the virtual
         run status and properties.
        :paramtype is_virtual: bool
        :keyword display_name:
        :paramtype display_name: str
        :keyword name:
        :paramtype name: str
        :keyword data_container_id:
        :paramtype data_container_id: str
        :keyword description:
        :paramtype description: str
        :keyword hidden:
        :paramtype hidden: bool
        :keyword run_type:
        :paramtype run_type: str
        :keyword run_type_v2:
        :paramtype run_type_v2: ~azure.mgmt.machinelearningservices.models.RunTypeV2
        :keyword properties: Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword parameters: Dictionary of :code:`<any>`.
        :paramtype parameters: dict[str, any]
        :keyword action_uris: Dictionary of :code:`<string>`.
        :paramtype action_uris: dict[str, str]
        :keyword script_name:
        :paramtype script_name: str
        :keyword target:
        :paramtype target: str
        :keyword unique_child_run_compute_targets:
        :paramtype unique_child_run_compute_targets: list[str]
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword settings: Dictionary of :code:`<string>`.
        :paramtype settings: dict[str, str]
        :keyword services: Dictionary of :code:`<EndpointSetting>`.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.EndpointSetting]
        :keyword input_datasets:
        :paramtype input_datasets: list[~azure.mgmt.machinelearningservices.models.DatasetLineage]
        :keyword output_datasets:
        :paramtype output_datasets:
         list[~azure.mgmt.machinelearningservices.models.OutputDatasetLineage]
        :keyword run_definition: Anything.
        :paramtype run_definition: any
        :keyword job_specification: Anything.
        :paramtype job_specification: any
        :keyword primary_metric_name:
        :paramtype primary_metric_name: str
        :keyword created_from:
        :paramtype created_from: ~azure.mgmt.machinelearningservices.models.CreatedFrom
        :keyword cancel_uri:
        :paramtype cancel_uri: str
        :keyword complete_uri:
        :paramtype complete_uri: str
        :keyword diagnostics_uri:
        :paramtype diagnostics_uri: str
        :keyword compute_request:
        :paramtype compute_request: ~azure.mgmt.machinelearningservices.models.ComputeRequest
        :keyword compute:
        :paramtype compute: ~azure.mgmt.machinelearningservices.models.Compute
        :keyword retain_for_lifetime_of_workspace:
        :paramtype retain_for_lifetime_of_workspace: bool
        :keyword queueing_info:
        :paramtype queueing_info: ~azure.mgmt.machinelearningservices.models.QueueingInfo
        :keyword active_child_run_id: The RunId of the active child on a virtual run.
        :paramtype active_child_run_id: str
        :keyword inputs: Dictionary of :code:`<TypedAssetReference>`.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
        :keyword outputs: Dictionary of :code:`<TypedAssetReference>`.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
        """
        super(CreateRun, self).__init__(**kwargs)
        self.run_id = kwargs.get('run_id', None)
        self.parent_run_id = kwargs.get('parent_run_id', None)
        self.experiment_id = kwargs.get('experiment_id', None)
        self.status = kwargs.get('status', None)
        self.start_time_utc = kwargs.get('start_time_utc', None)
        self.end_time_utc = kwargs.get('end_time_utc', None)
        self.options = kwargs.get('options', None)
        self.is_virtual = kwargs.get('is_virtual', None)
        self.display_name = kwargs.get('display_name', None)
        self.name = kwargs.get('name', None)
        self.data_container_id = kwargs.get('data_container_id', None)
        self.description = kwargs.get('description', None)
        self.hidden = kwargs.get('hidden', None)
        self.run_type = kwargs.get('run_type', None)
        self.run_type_v2 = kwargs.get('run_type_v2', None)
        self.properties = kwargs.get('properties', None)
        self.parameters = kwargs.get('parameters', None)
        self.action_uris = kwargs.get('action_uris', None)
        self.script_name = kwargs.get('script_name', None)
        self.target = kwargs.get('target', None)
        self.unique_child_run_compute_targets = kwargs.get('unique_child_run_compute_targets', None)
        self.tags = kwargs.get('tags', None)
        self.settings = kwargs.get('settings', None)
        self.services = kwargs.get('services', None)
        self.input_datasets = kwargs.get('input_datasets', None)
        self.output_datasets = kwargs.get('output_datasets', None)
        self.run_definition = kwargs.get('run_definition', None)
        self.job_specification = kwargs.get('job_specification', None)
        self.primary_metric_name = kwargs.get('primary_metric_name', None)
        self.created_from = kwargs.get('created_from', None)
        self.cancel_uri = kwargs.get('cancel_uri', None)
        self.complete_uri = kwargs.get('complete_uri', None)
        self.diagnostics_uri = kwargs.get('diagnostics_uri', None)
        self.compute_request = kwargs.get('compute_request', None)
        self.compute = kwargs.get('compute', None)
        self.retain_for_lifetime_of_workspace = kwargs.get('retain_for_lifetime_of_workspace', None)
        self.queueing_info = kwargs.get('queueing_info', None)
        self.active_child_run_id = kwargs.get('active_child_run_id', None)
        self.inputs = kwargs.get('inputs', None)
        self.outputs = kwargs.get('outputs', None)


class DatasetIdentifier(msrest.serialization.Model):
    """DatasetIdentifier.

    :ivar saved_id:
    :vartype saved_id: str
    :ivar registered_id:
    :vartype registered_id: str
    :ivar registered_version:
    :vartype registered_version: str
    """

    _attribute_map = {
        'saved_id': {'key': 'savedId', 'type': 'str'},
        'registered_id': {'key': 'registeredId', 'type': 'str'},
        'registered_version': {'key': 'registeredVersion', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword saved_id:
        :paramtype saved_id: str
        :keyword registered_id:
        :paramtype registered_id: str
        :keyword registered_version:
        :paramtype registered_version: str
        """
        super(DatasetIdentifier, self).__init__(**kwargs)
        self.saved_id = kwargs.get('saved_id', None)
        self.registered_id = kwargs.get('registered_id', None)
        self.registered_version = kwargs.get('registered_version', None)


class DatasetInputDetails(msrest.serialization.Model):
    """DatasetInputDetails.

    :ivar input_name:
    :vartype input_name: str
    :ivar mechanism: Possible values include: "Direct", "Mount", "Download", "Hdfs".
    :vartype mechanism: str or ~azure.mgmt.machinelearningservices.models.DatasetDeliveryMechanism
    :ivar path_on_compute:
    :vartype path_on_compute: str
    """

    _attribute_map = {
        'input_name': {'key': 'inputName', 'type': 'str'},
        'mechanism': {'key': 'mechanism', 'type': 'str'},
        'path_on_compute': {'key': 'pathOnCompute', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword input_name:
        :paramtype input_name: str
        :keyword mechanism: Possible values include: "Direct", "Mount", "Download", "Hdfs".
        :paramtype mechanism: str or
         ~azure.mgmt.machinelearningservices.models.DatasetDeliveryMechanism
        :keyword path_on_compute:
        :paramtype path_on_compute: str
        """
        super(DatasetInputDetails, self).__init__(**kwargs)
        self.input_name = kwargs.get('input_name', None)
        self.mechanism = kwargs.get('mechanism', None)
        self.path_on_compute = kwargs.get('path_on_compute', None)


class DatasetLineage(msrest.serialization.Model):
    """DatasetLineage.

    :ivar identifier:
    :vartype identifier: ~azure.mgmt.machinelearningservices.models.DatasetIdentifier
    :ivar consumption_type: Possible values include: "RunInput", "Reference".
    :vartype consumption_type: str or
     ~azure.mgmt.machinelearningservices.models.DatasetConsumptionType
    :ivar input_details:
    :vartype input_details: ~azure.mgmt.machinelearningservices.models.DatasetInputDetails
    """

    _attribute_map = {
        'identifier': {'key': 'identifier', 'type': 'DatasetIdentifier'},
        'consumption_type': {'key': 'consumptionType', 'type': 'str'},
        'input_details': {'key': 'inputDetails', 'type': 'DatasetInputDetails'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword identifier:
        :paramtype identifier: ~azure.mgmt.machinelearningservices.models.DatasetIdentifier
        :keyword consumption_type: Possible values include: "RunInput", "Reference".
        :paramtype consumption_type: str or
         ~azure.mgmt.machinelearningservices.models.DatasetConsumptionType
        :keyword input_details:
        :paramtype input_details: ~azure.mgmt.machinelearningservices.models.DatasetInputDetails
        """
        super(DatasetLineage, self).__init__(**kwargs)
        self.identifier = kwargs.get('identifier', None)
        self.consumption_type = kwargs.get('consumption_type', None)
        self.input_details = kwargs.get('input_details', None)


class DatasetOutputDetails(msrest.serialization.Model):
    """DatasetOutputDetails.

    :ivar output_name:
    :vartype output_name: str
    """

    _attribute_map = {
        'output_name': {'key': 'outputName', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword output_name:
        :paramtype output_name: str
        """
        super(DatasetOutputDetails, self).__init__(**kwargs)
        self.output_name = kwargs.get('output_name', None)


class DeleteConfiguration(msrest.serialization.Model):
    """DeleteConfiguration.

    :ivar workspace_id:
    :vartype workspace_id: str
    :ivar is_enabled:
    :vartype is_enabled: bool
    :ivar cutoff_days:
    :vartype cutoff_days: int
    """

    _attribute_map = {
        'workspace_id': {'key': 'workspaceId', 'type': 'str'},
        'is_enabled': {'key': 'isEnabled', 'type': 'bool'},
        'cutoff_days': {'key': 'cutoffDays', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword workspace_id:
        :paramtype workspace_id: str
        :keyword is_enabled:
        :paramtype is_enabled: bool
        :keyword cutoff_days:
        :paramtype cutoff_days: int
        """
        super(DeleteConfiguration, self).__init__(**kwargs)
        self.workspace_id = kwargs.get('workspace_id', None)
        self.is_enabled = kwargs.get('is_enabled', None)
        self.cutoff_days = kwargs.get('cutoff_days', None)


class DeleteExperimentTagsResult(msrest.serialization.Model):
    """DeleteExperimentTagsResult.

    :ivar errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype errors: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
    """

    _attribute_map = {
        'errors': {'key': 'errors', 'type': '{ErrorResponse}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype errors: dict[str, ~azure.mgmt.machinelearningservices.models.ErrorResponse]
        """
        super(DeleteExperimentTagsResult, self).__init__(**kwargs)
        self.errors = kwargs.get('errors', None)


class DeleteOrModifyTags(msrest.serialization.Model):
    """The Tags to modify or delete.

    :ivar tags_to_modify: The KV pairs of tags to modify.
    :vartype tags_to_modify: dict[str, str]
    :ivar tags_to_delete: The list of tags to delete.
    :vartype tags_to_delete: list[str]
    """

    _attribute_map = {
        'tags_to_modify': {'key': 'tagsToModify', 'type': '{str}'},
        'tags_to_delete': {'key': 'tagsToDelete', 'type': '[str]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword tags_to_modify: The KV pairs of tags to modify.
        :paramtype tags_to_modify: dict[str, str]
        :keyword tags_to_delete: The list of tags to delete.
        :paramtype tags_to_delete: list[str]
        """
        super(DeleteOrModifyTags, self).__init__(**kwargs)
        self.tags_to_modify = kwargs.get('tags_to_modify', None)
        self.tags_to_delete = kwargs.get('tags_to_delete', None)


class DeleteRunServices(msrest.serialization.Model):
    """The Services to delete.

    :ivar services_to_delete: The list of Services to delete.
    :vartype services_to_delete: list[str]
    """

    _attribute_map = {
        'services_to_delete': {'key': 'servicesToDelete', 'type': '[str]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword services_to_delete: The list of Services to delete.
        :paramtype services_to_delete: list[str]
        """
        super(DeleteRunServices, self).__init__(**kwargs)
        self.services_to_delete = kwargs.get('services_to_delete', None)


class DeleteTagsCommand(msrest.serialization.Model):
    """DeleteTagsCommand.

    :ivar tags: A set of tags.
    :vartype tags: list[str]
    """

    _attribute_map = {
        'tags': {'key': 'tags', 'type': '[str]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword tags: A set of tags.
        :paramtype tags: list[str]
        """
        super(DeleteTagsCommand, self).__init__(**kwargs)
        self.tags = kwargs.get('tags', None)


class DerivedMetricKey(msrest.serialization.Model):
    """DerivedMetricKey.

    :ivar namespace:
    :vartype namespace: str
    :ivar name:
    :vartype name: str
    :ivar labels:
    :vartype labels: list[str]
    :ivar column_names:
    :vartype column_names: list[str]
    """

    _validation = {
        'labels': {'unique': True},
        'column_names': {'unique': True},
    }

    _attribute_map = {
        'namespace': {'key': 'namespace', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'labels': {'key': 'labels', 'type': '[str]'},
        'column_names': {'key': 'columnNames', 'type': '[str]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword namespace:
        :paramtype namespace: str
        :keyword name:
        :paramtype name: str
        :keyword labels:
        :paramtype labels: list[str]
        :keyword column_names:
        :paramtype column_names: list[str]
        """
        super(DerivedMetricKey, self).__init__(**kwargs)
        self.namespace = kwargs.get('namespace', None)
        self.name = kwargs.get('name', None)
        self.labels = kwargs.get('labels', None)
        self.column_names = kwargs.get('column_names', None)


class EndpointSetting(msrest.serialization.Model):
    """EndpointSetting.

    :ivar type:
    :vartype type: str
    :ivar port:
    :vartype port: int
    :ivar ssl_thumbprint:
    :vartype ssl_thumbprint: str
    :ivar endpoint:
    :vartype endpoint: str
    :ivar proxy_endpoint:
    :vartype proxy_endpoint: str
    :ivar status:
    :vartype status: str
    :ivar error_message:
    :vartype error_message: str
    :ivar enabled:
    :vartype enabled: bool
    :ivar properties: Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    """

    _attribute_map = {
        'type': {'key': 'type', 'type': 'str'},
        'port': {'key': 'port', 'type': 'int'},
        'ssl_thumbprint': {'key': 'sslThumbprint', 'type': 'str'},
        'endpoint': {'key': 'endpoint', 'type': 'str'},
        'proxy_endpoint': {'key': 'proxyEndpoint', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'error_message': {'key': 'errorMessage', 'type': 'str'},
        'enabled': {'key': 'enabled', 'type': 'bool'},
        'properties': {'key': 'properties', 'type': '{str}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword type:
        :paramtype type: str
        :keyword port:
        :paramtype port: int
        :keyword ssl_thumbprint:
        :paramtype ssl_thumbprint: str
        :keyword endpoint:
        :paramtype endpoint: str
        :keyword proxy_endpoint:
        :paramtype proxy_endpoint: str
        :keyword status:
        :paramtype status: str
        :keyword error_message:
        :paramtype error_message: str
        :keyword enabled:
        :paramtype enabled: bool
        :keyword properties: Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        """
        super(EndpointSetting, self).__init__(**kwargs)
        self.type = kwargs.get('type', None)
        self.port = kwargs.get('port', None)
        self.ssl_thumbprint = kwargs.get('ssl_thumbprint', None)
        self.endpoint = kwargs.get('endpoint', None)
        self.proxy_endpoint = kwargs.get('proxy_endpoint', None)
        self.status = kwargs.get('status', None)
        self.error_message = kwargs.get('error_message', None)
        self.enabled = kwargs.get('enabled', None)
        self.properties = kwargs.get('properties', None)


class ErrorAdditionalInfo(msrest.serialization.Model):
    """The resource management error additional info.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: any
    """

    _attribute_map = {
        'type': {'key': 'type', 'type': 'str'},
        'info': {'key': 'info', 'type': 'object'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword type: The additional info type.
        :paramtype type: str
        :keyword info: The additional info.
        :paramtype info: any
        """
        super(ErrorAdditionalInfo, self).__init__(**kwargs)
        self.type = kwargs.get('type', None)
        self.info = kwargs.get('info', None)


class ErrorResponse(msrest.serialization.Model):
    """The error response.

    :ivar error: The root error.
    :vartype error: ~azure.mgmt.machinelearningservices.models.RootError
    :ivar correlation: Dictionary containing correlation details for the error.
    :vartype correlation: dict[str, str]
    :ivar environment: The hosting environment.
    :vartype environment: str
    :ivar location: The Azure region.
    :vartype location: str
    :ivar time: The time in UTC.
    :vartype time: ~datetime.datetime
    :ivar component_name: Component name where error originated/encountered.
    :vartype component_name: str
    """

    _attribute_map = {
        'error': {'key': 'error', 'type': 'RootError'},
        'correlation': {'key': 'correlation', 'type': '{str}'},
        'environment': {'key': 'environment', 'type': 'str'},
        'location': {'key': 'location', 'type': 'str'},
        'time': {'key': 'time', 'type': 'iso-8601'},
        'component_name': {'key': 'componentName', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword error: The root error.
        :paramtype error: ~azure.mgmt.machinelearningservices.models.RootError
        :keyword correlation: Dictionary containing correlation details for the error.
        :paramtype correlation: dict[str, str]
        :keyword environment: The hosting environment.
        :paramtype environment: str
        :keyword location: The Azure region.
        :paramtype location: str
        :keyword time: The time in UTC.
        :paramtype time: ~datetime.datetime
        :keyword component_name: Component name where error originated/encountered.
        :paramtype component_name: str
        """
        super(ErrorResponse, self).__init__(**kwargs)
        self.error = kwargs.get('error', None)
        self.correlation = kwargs.get('correlation', None)
        self.environment = kwargs.get('environment', None)
        self.location = kwargs.get('location', None)
        self.time = kwargs.get('time', None)
        self.component_name = kwargs.get('component_name', None)


class Event(msrest.serialization.Model):
    """Event.

    :ivar name: Gets the Microsoft.MachineLearning.RunHistory.Contracts.Event name.
    :vartype name: str
    :ivar timestamp: Gets the Microsoft.MachineLearning.RunHistory.Contracts.Event timestamp.
    :vartype timestamp: ~datetime.datetime
    :ivar attributes: Gets the System.Collections.Generic.IDictionary`2 collection of attributes
     associated with the event.
    :vartype attributes: dict[str, any]
    """

    _attribute_map = {
        'name': {'key': 'name', 'type': 'str'},
        'timestamp': {'key': 'timestamp', 'type': 'iso-8601'},
        'attributes': {'key': 'attributes', 'type': '{object}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword name: Gets the Microsoft.MachineLearning.RunHistory.Contracts.Event name.
        :paramtype name: str
        :keyword timestamp: Gets the Microsoft.MachineLearning.RunHistory.Contracts.Event timestamp.
        :paramtype timestamp: ~datetime.datetime
        :keyword attributes: Gets the System.Collections.Generic.IDictionary`2 collection of attributes
         associated with the event.
        :paramtype attributes: dict[str, any]
        """
        super(Event, self).__init__(**kwargs)
        self.name = kwargs.get('name', None)
        self.timestamp = kwargs.get('timestamp', None)
        self.attributes = kwargs.get('attributes', None)


class Experiment(msrest.serialization.Model):
    """Experiment.

    :ivar experiment_id:
    :vartype experiment_id: str
    :ivar name:
    :vartype name: str
    :ivar description:
    :vartype description: str
    :ivar created_utc:
    :vartype created_utc: ~datetime.datetime
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar archived_time:
    :vartype archived_time: ~datetime.datetime
    :ivar retain_for_lifetime_of_workspace:
    :vartype retain_for_lifetime_of_workspace: bool
    :ivar artifact_location:
    :vartype artifact_location: str
    """

    _attribute_map = {
        'experiment_id': {'key': 'experimentId', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'created_utc': {'key': 'createdUtc', 'type': 'iso-8601'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'archived_time': {'key': 'archivedTime', 'type': 'iso-8601'},
        'retain_for_lifetime_of_workspace': {'key': 'retainForLifetimeOfWorkspace', 'type': 'bool'},
        'artifact_location': {'key': 'artifactLocation', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword experiment_id:
        :paramtype experiment_id: str
        :keyword name:
        :paramtype name: str
        :keyword description:
        :paramtype description: str
        :keyword created_utc:
        :paramtype created_utc: ~datetime.datetime
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword archived_time:
        :paramtype archived_time: ~datetime.datetime
        :keyword retain_for_lifetime_of_workspace:
        :paramtype retain_for_lifetime_of_workspace: bool
        :keyword artifact_location:
        :paramtype artifact_location: str
        """
        super(Experiment, self).__init__(**kwargs)
        self.experiment_id = kwargs.get('experiment_id', None)
        self.name = kwargs.get('name', None)
        self.description = kwargs.get('description', None)
        self.created_utc = kwargs.get('created_utc', None)
        self.tags = kwargs.get('tags', None)
        self.archived_time = kwargs.get('archived_time', None)
        self.retain_for_lifetime_of_workspace = kwargs.get('retain_for_lifetime_of_workspace', None)
        self.artifact_location = kwargs.get('artifact_location', None)


class ExperimentQueryParams(msrest.serialization.Model):
    """Extends Query Params DTO for ViewType.

    :ivar view_type: ViewType filters experiments by their archived state. Default is ActiveOnly.
     Possible values include: "Default", "All", "ActiveOnly", "ArchivedOnly".
    :vartype view_type: str or ~azure.mgmt.machinelearningservices.models.ExperimentViewType
    :ivar filter: Allows for filtering the collection of resources.
     The expression specified is evaluated for each resource in the collection, and only items
     where the expression evaluates to true are included in the response.
     See https://learn.microsoft.com/azure/search/query-odata-filter-orderby-syntax for
     details on the expression syntax.
    :vartype filter: str
    :ivar continuation_token: The continuation token to use for getting the next set of resources.
    :vartype continuation_token: str
    :ivar order_by: The comma separated list of resource properties to use for sorting the
     requested resources.
     Optionally, can be followed by either 'asc' or 'desc'.
    :vartype order_by: str
    :ivar top: The maximum number of items in the resource collection to be included in the result.
     If not specified, all items are returned.
    :vartype top: int
    """

    _attribute_map = {
        'view_type': {'key': 'viewType', 'type': 'str'},
        'filter': {'key': 'filter', 'type': 'str'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'order_by': {'key': 'orderBy', 'type': 'str'},
        'top': {'key': 'top', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword view_type: ViewType filters experiments by their archived state. Default is
         ActiveOnly. Possible values include: "Default", "All", "ActiveOnly", "ArchivedOnly".
        :paramtype view_type: str or ~azure.mgmt.machinelearningservices.models.ExperimentViewType
        :keyword filter: Allows for filtering the collection of resources.
         The expression specified is evaluated for each resource in the collection, and only items
         where the expression evaluates to true are included in the response.
         See https://learn.microsoft.com/azure/search/query-odata-filter-orderby-syntax for
         details on the expression syntax.
        :paramtype filter: str
        :keyword continuation_token: The continuation token to use for getting the next set of
         resources.
        :paramtype continuation_token: str
        :keyword order_by: The comma separated list of resource properties to use for sorting the
         requested resources.
         Optionally, can be followed by either 'asc' or 'desc'.
        :paramtype order_by: str
        :keyword top: The maximum number of items in the resource collection to be included in the
         result.
         If not specified, all items are returned.
        :paramtype top: int
        """
        super(ExperimentQueryParams, self).__init__(**kwargs)
        self.view_type = kwargs.get('view_type', None)
        self.filter = kwargs.get('filter', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.order_by = kwargs.get('order_by', None)
        self.top = kwargs.get('top', None)


class GetRunDataRequest(msrest.serialization.Model):
    """GetRunDataRequest.

    :ivar run_id:
    :vartype run_id: str
    :ivar select_run_metadata:
    :vartype select_run_metadata: bool
    :ivar select_run_definition:
    :vartype select_run_definition: bool
    :ivar select_job_specification:
    :vartype select_job_specification: bool
    """

    _attribute_map = {
        'run_id': {'key': 'runId', 'type': 'str'},
        'select_run_metadata': {'key': 'selectRunMetadata', 'type': 'bool'},
        'select_run_definition': {'key': 'selectRunDefinition', 'type': 'bool'},
        'select_job_specification': {'key': 'selectJobSpecification', 'type': 'bool'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_id:
        :paramtype run_id: str
        :keyword select_run_metadata:
        :paramtype select_run_metadata: bool
        :keyword select_run_definition:
        :paramtype select_run_definition: bool
        :keyword select_job_specification:
        :paramtype select_job_specification: bool
        """
        super(GetRunDataRequest, self).__init__(**kwargs)
        self.run_id = kwargs.get('run_id', None)
        self.select_run_metadata = kwargs.get('select_run_metadata', None)
        self.select_run_definition = kwargs.get('select_run_definition', None)
        self.select_job_specification = kwargs.get('select_job_specification', None)


class GetRunDataResult(msrest.serialization.Model):
    """GetRunDataResult.

    :ivar run_metadata: The definition of a Run.
    :vartype run_metadata: ~azure.mgmt.machinelearningservices.models.Run
    :ivar run_definition: Anything.
    :vartype run_definition: any
    :ivar job_specification: Anything.
    :vartype job_specification: any
    """

    _attribute_map = {
        'run_metadata': {'key': 'runMetadata', 'type': 'Run'},
        'run_definition': {'key': 'runDefinition', 'type': 'object'},
        'job_specification': {'key': 'jobSpecification', 'type': 'object'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_metadata: The definition of a Run.
        :paramtype run_metadata: ~azure.mgmt.machinelearningservices.models.Run
        :keyword run_definition: Anything.
        :paramtype run_definition: any
        :keyword job_specification: Anything.
        :paramtype job_specification: any
        """
        super(GetRunDataResult, self).__init__(**kwargs)
        self.run_metadata = kwargs.get('run_metadata', None)
        self.run_definition = kwargs.get('run_definition', None)
        self.job_specification = kwargs.get('job_specification', None)


class GetRunsByIds(msrest.serialization.Model):
    """GetRunsByIds.

    :ivar run_ids:
    :vartype run_ids: list[str]
    """

    _attribute_map = {
        'run_ids': {'key': 'runIds', 'type': '[str]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_ids:
        :paramtype run_ids: list[str]
        """
        super(GetRunsByIds, self).__init__(**kwargs)
        self.run_ids = kwargs.get('run_ids', None)


class GetSampledMetricRequest(msrest.serialization.Model):
    """GetSampledMetricRequest.

    :ivar metric_name:
    :vartype metric_name: str
    :ivar metric_namespace:
    :vartype metric_namespace: str
    """

    _attribute_map = {
        'metric_name': {'key': 'metricName', 'type': 'str'},
        'metric_namespace': {'key': 'metricNamespace', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword metric_name:
        :paramtype metric_name: str
        :keyword metric_namespace:
        :paramtype metric_namespace: str
        """
        super(GetSampledMetricRequest, self).__init__(**kwargs)
        self.metric_name = kwargs.get('metric_name', None)
        self.metric_namespace = kwargs.get('metric_namespace', None)


class IMetricV2(msrest.serialization.Model):
    """Sequence of one or many values sharing a common  DataContainerId, Name, and Schema. Used only for Post Metrics.

    :ivar data_container_id: Data container to which this Metric belongs.
    :vartype data_container_id: str
    :ivar name: Name identifying this Metric within the Data Container.
    :vartype name: str
    :ivar columns: Schema shared by all values under this Metric
     Columns.Keys define the column names which are required for each MetricValue
     Columns.Values define the type of the associated object for each column.
    :vartype columns: dict[str, str or ~azure.mgmt.machinelearningservices.models.MetricValueType]
    :ivar namespace: Namespace for this Metric.
    :vartype namespace: str
    :ivar standard_schema_id:
    :vartype standard_schema_id: str
    :ivar value: The list of values.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.MetricV2Value]
    """

    _attribute_map = {
        'data_container_id': {'key': 'dataContainerId', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'columns': {'key': 'columns', 'type': '{str}'},
        'namespace': {'key': 'namespace', 'type': 'str'},
        'standard_schema_id': {'key': 'standardSchemaId', 'type': 'str'},
        'value': {'key': 'value', 'type': '[MetricV2Value]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword data_container_id: Data container to which this Metric belongs.
        :paramtype data_container_id: str
        :keyword name: Name identifying this Metric within the Data Container.
        :paramtype name: str
        :keyword columns: Schema shared by all values under this Metric
         Columns.Keys define the column names which are required for each MetricValue
         Columns.Values define the type of the associated object for each column.
        :paramtype columns: dict[str, str or
         ~azure.mgmt.machinelearningservices.models.MetricValueType]
        :keyword namespace: Namespace for this Metric.
        :paramtype namespace: str
        :keyword standard_schema_id:
        :paramtype standard_schema_id: str
        :keyword value: The list of values.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.MetricV2Value]
        """
        super(IMetricV2, self).__init__(**kwargs)
        self.data_container_id = kwargs.get('data_container_id', None)
        self.name = kwargs.get('name', None)
        self.columns = kwargs.get('columns', None)
        self.namespace = kwargs.get('namespace', None)
        self.standard_schema_id = kwargs.get('standard_schema_id', None)
        self.value = kwargs.get('value', None)


class InnerErrorResponse(msrest.serialization.Model):
    """A nested structure of errors.

    :ivar code: The error code.
    :vartype code: str
    :ivar inner_error: A nested structure of errors.
    :vartype inner_error: ~azure.mgmt.machinelearningservices.models.InnerErrorResponse
    """

    _attribute_map = {
        'code': {'key': 'code', 'type': 'str'},
        'inner_error': {'key': 'innerError', 'type': 'InnerErrorResponse'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword code: The error code.
        :paramtype code: str
        :keyword inner_error: A nested structure of errors.
        :paramtype inner_error: ~azure.mgmt.machinelearningservices.models.InnerErrorResponse
        """
        super(InnerErrorResponse, self).__init__(**kwargs)
        self.code = kwargs.get('code', None)
        self.inner_error = kwargs.get('inner_error', None)


class JobCost(msrest.serialization.Model):
    """JobCost.

    :ivar charged_cpu_core_seconds:
    :vartype charged_cpu_core_seconds: float
    :ivar charged_cpu_memory_megabyte_seconds:
    :vartype charged_cpu_memory_megabyte_seconds: float
    :ivar charged_gpu_seconds:
    :vartype charged_gpu_seconds: float
    :ivar charged_node_utilization_seconds:
    :vartype charged_node_utilization_seconds: float
    """

    _attribute_map = {
        'charged_cpu_core_seconds': {'key': 'chargedCpuCoreSeconds', 'type': 'float'},
        'charged_cpu_memory_megabyte_seconds': {'key': 'chargedCpuMemoryMegabyteSeconds', 'type': 'float'},
        'charged_gpu_seconds': {'key': 'chargedGpuSeconds', 'type': 'float'},
        'charged_node_utilization_seconds': {'key': 'chargedNodeUtilizationSeconds', 'type': 'float'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword charged_cpu_core_seconds:
        :paramtype charged_cpu_core_seconds: float
        :keyword charged_cpu_memory_megabyte_seconds:
        :paramtype charged_cpu_memory_megabyte_seconds: float
        :keyword charged_gpu_seconds:
        :paramtype charged_gpu_seconds: float
        :keyword charged_node_utilization_seconds:
        :paramtype charged_node_utilization_seconds: float
        """
        super(JobCost, self).__init__(**kwargs)
        self.charged_cpu_core_seconds = kwargs.get('charged_cpu_core_seconds', None)
        self.charged_cpu_memory_megabyte_seconds = kwargs.get('charged_cpu_memory_megabyte_seconds', None)
        self.charged_gpu_seconds = kwargs.get('charged_gpu_seconds', None)
        self.charged_node_utilization_seconds = kwargs.get('charged_node_utilization_seconds', None)


class KeyValuePairBaseEventErrorResponse(msrest.serialization.Model):
    """KeyValuePairBaseEventErrorResponse.

    :ivar key: Base event is the envelope used to post event data to the Event controller.
    :vartype key: ~azure.mgmt.machinelearningservices.models.BaseEvent
    :ivar value: The error response.
    :vartype value: ~azure.mgmt.machinelearningservices.models.ErrorResponse
    """

    _attribute_map = {
        'key': {'key': 'key', 'type': 'BaseEvent'},
        'value': {'key': 'value', 'type': 'ErrorResponse'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword key: Base event is the envelope used to post event data to the Event controller.
        :paramtype key: ~azure.mgmt.machinelearningservices.models.BaseEvent
        :keyword value: The error response.
        :paramtype value: ~azure.mgmt.machinelearningservices.models.ErrorResponse
        """
        super(KeyValuePairBaseEventErrorResponse, self).__init__(**kwargs)
        self.key = kwargs.get('key', None)
        self.value = kwargs.get('value', None)


class KeyValuePairString(msrest.serialization.Model):
    """KeyValuePairString.

    :ivar key:
    :vartype key: str
    :ivar value:
    :vartype value: str
    """

    _attribute_map = {
        'key': {'key': 'key', 'type': 'str'},
        'value': {'key': 'value', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword key:
        :paramtype key: str
        :keyword value:
        :paramtype value: str
        """
        super(KeyValuePairString, self).__init__(**kwargs)
        self.key = kwargs.get('key', None)
        self.value = kwargs.get('value', None)


class KeyValuePairStringJToken(msrest.serialization.Model):
    """KeyValuePairStringJToken.

    :ivar key:
    :vartype key: str
    :ivar value: Anything.
    :vartype value: any
    """

    _attribute_map = {
        'key': {'key': 'key', 'type': 'str'},
        'value': {'key': 'value', 'type': 'object'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword key:
        :paramtype key: str
        :keyword value: Anything.
        :paramtype value: any
        """
        super(KeyValuePairStringJToken, self).__init__(**kwargs)
        self.key = kwargs.get('key', None)
        self.value = kwargs.get('value', None)


class Link(msrest.serialization.Model):
    """Link.

    :ivar context:
    :vartype context: ~azure.mgmt.machinelearningservices.models.SpanContext
    :ivar attributes: Gets the collection of attributes associated with the link.
    :vartype attributes: dict[str, any]
    """

    _attribute_map = {
        'context': {'key': 'context', 'type': 'SpanContext'},
        'attributes': {'key': 'attributes', 'type': '{object}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword context:
        :paramtype context: ~azure.mgmt.machinelearningservices.models.SpanContext
        :keyword attributes: Gets the collection of attributes associated with the link.
        :paramtype attributes: dict[str, any]
        """
        super(Link, self).__init__(**kwargs)
        self.context = kwargs.get('context', None)
        self.attributes = kwargs.get('attributes', None)


class ListGenericResourceMetrics(msrest.serialization.Model):
    """ListGenericResourceMetrics.

    :ivar resource_id:
    :vartype resource_id: str
    :ivar metric_names:
    :vartype metric_names: list[str]
    :ivar label_filters: Dictionary of :code:`<string>`.
    :vartype label_filters: dict[str, str]
    :ivar metric_namespace:
    :vartype metric_namespace: str
    :ivar continuation_token:
    :vartype continuation_token: str
    """

    _attribute_map = {
        'resource_id': {'key': 'resourceId', 'type': 'str'},
        'metric_names': {'key': 'metricNames', 'type': '[str]'},
        'label_filters': {'key': 'labelFilters', 'type': '{str}'},
        'metric_namespace': {'key': 'metricNamespace', 'type': 'str'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword resource_id:
        :paramtype resource_id: str
        :keyword metric_names:
        :paramtype metric_names: list[str]
        :keyword label_filters: Dictionary of :code:`<string>`.
        :paramtype label_filters: dict[str, str]
        :keyword metric_namespace:
        :paramtype metric_namespace: str
        :keyword continuation_token:
        :paramtype continuation_token: str
        """
        super(ListGenericResourceMetrics, self).__init__(**kwargs)
        self.resource_id = kwargs.get('resource_id', None)
        self.metric_names = kwargs.get('metric_names', None)
        self.label_filters = kwargs.get('label_filters', None)
        self.metric_namespace = kwargs.get('metric_namespace', None)
        self.continuation_token = kwargs.get('continuation_token', None)


class ListMetrics(msrest.serialization.Model):
    """ListMetrics.

    :ivar metric_namespace:
    :vartype metric_namespace: str
    :ivar continuation_token:
    :vartype continuation_token: str
    """

    _attribute_map = {
        'metric_namespace': {'key': 'metricNamespace', 'type': 'str'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword metric_namespace:
        :paramtype metric_namespace: str
        :keyword continuation_token:
        :paramtype continuation_token: str
        """
        super(ListMetrics, self).__init__(**kwargs)
        self.metric_namespace = kwargs.get('metric_namespace', None)
        self.continuation_token = kwargs.get('continuation_token', None)


class MetricDefinition(msrest.serialization.Model):
    """MetricDefinition.

    :ivar metric_key:
    :vartype metric_key: ~azure.mgmt.machinelearningservices.models.DerivedMetricKey
    :ivar columns: Dictionary of :code:`<MetricValueType>`.
    :vartype columns: dict[str, str or ~azure.mgmt.machinelearningservices.models.MetricValueType]
    :ivar properties:
    :vartype properties: ~azure.mgmt.machinelearningservices.models.MetricProperties
    """

    _attribute_map = {
        'metric_key': {'key': 'metricKey', 'type': 'DerivedMetricKey'},
        'columns': {'key': 'columns', 'type': '{str}'},
        'properties': {'key': 'properties', 'type': 'MetricProperties'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword metric_key:
        :paramtype metric_key: ~azure.mgmt.machinelearningservices.models.DerivedMetricKey
        :keyword columns: Dictionary of :code:`<MetricValueType>`.
        :paramtype columns: dict[str, str or
         ~azure.mgmt.machinelearningservices.models.MetricValueType]
        :keyword properties:
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.MetricProperties
        """
        super(MetricDefinition, self).__init__(**kwargs)
        self.metric_key = kwargs.get('metric_key', None)
        self.columns = kwargs.get('columns', None)
        self.properties = kwargs.get('properties', None)


class MetricProperties(msrest.serialization.Model):
    """MetricProperties.

    :ivar ux_metric_type: String value UX uses to decide how to render your metrics
     Ex: azureml.v1.scalar or azureml.v1.table.
    :vartype ux_metric_type: str
    """

    _attribute_map = {
        'ux_metric_type': {'key': 'uxMetricType', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword ux_metric_type: String value UX uses to decide how to render your metrics
         Ex: azureml.v1.scalar or azureml.v1.table.
        :paramtype ux_metric_type: str
        """
        super(MetricProperties, self).__init__(**kwargs)
        self.ux_metric_type = kwargs.get('ux_metric_type', None)


class MetricSample(msrest.serialization.Model):
    """MetricSample.

    :ivar derived_label_values: Dictionary of :code:`<string>`.
    :vartype derived_label_values: dict[str, str]
    :ivar is_partial_result:
    :vartype is_partial_result: bool
    :ivar num_values_logged:
    :vartype num_values_logged: long
    :ivar data_container_id: Data container to which this Metric belongs.
    :vartype data_container_id: str
    :ivar name: Name identifying this Metric within the Data Container.
    :vartype name: str
    :ivar columns: Schema shared by all values under this Metric
     Columns.Keys define the column names which are required for each MetricValue
     Columns.Values define the type of the associated object for each column.
    :vartype columns: dict[str, str or ~azure.mgmt.machinelearningservices.models.MetricValueType]
    :ivar properties:
    :vartype properties: ~azure.mgmt.machinelearningservices.models.MetricProperties
    :ivar namespace: Namespace for this Metric.
    :vartype namespace: str
    :ivar standard_schema_id:
    :vartype standard_schema_id: str
    :ivar value:
    :vartype value: list[~azure.mgmt.machinelearningservices.models.MetricV2Value]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'derived_label_values': {'key': 'derivedLabelValues', 'type': '{str}'},
        'is_partial_result': {'key': 'isPartialResult', 'type': 'bool'},
        'num_values_logged': {'key': 'numValuesLogged', 'type': 'long'},
        'data_container_id': {'key': 'dataContainerId', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'columns': {'key': 'columns', 'type': '{str}'},
        'properties': {'key': 'properties', 'type': 'MetricProperties'},
        'namespace': {'key': 'namespace', 'type': 'str'},
        'standard_schema_id': {'key': 'standardSchemaId', 'type': 'str'},
        'value': {'key': 'value', 'type': '[MetricV2Value]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword derived_label_values: Dictionary of :code:`<string>`.
        :paramtype derived_label_values: dict[str, str]
        :keyword is_partial_result:
        :paramtype is_partial_result: bool
        :keyword num_values_logged:
        :paramtype num_values_logged: long
        :keyword data_container_id: Data container to which this Metric belongs.
        :paramtype data_container_id: str
        :keyword name: Name identifying this Metric within the Data Container.
        :paramtype name: str
        :keyword columns: Schema shared by all values under this Metric
         Columns.Keys define the column names which are required for each MetricValue
         Columns.Values define the type of the associated object for each column.
        :paramtype columns: dict[str, str or
         ~azure.mgmt.machinelearningservices.models.MetricValueType]
        :keyword properties:
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.MetricProperties
        :keyword namespace: Namespace for this Metric.
        :paramtype namespace: str
        :keyword standard_schema_id:
        :paramtype standard_schema_id: str
        :keyword value:
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.MetricV2Value]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(MetricSample, self).__init__(**kwargs)
        self.derived_label_values = kwargs.get('derived_label_values', None)
        self.is_partial_result = kwargs.get('is_partial_result', None)
        self.num_values_logged = kwargs.get('num_values_logged', None)
        self.data_container_id = kwargs.get('data_container_id', None)
        self.name = kwargs.get('name', None)
        self.columns = kwargs.get('columns', None)
        self.properties = kwargs.get('properties', None)
        self.namespace = kwargs.get('namespace', None)
        self.standard_schema_id = kwargs.get('standard_schema_id', None)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class MetricSchema(msrest.serialization.Model):
    """MetricSchema.

    :ivar num_properties:
    :vartype num_properties: int
    :ivar properties:
    :vartype properties: list[~azure.mgmt.machinelearningservices.models.MetricSchemaProperty]
    """

    _attribute_map = {
        'num_properties': {'key': 'numProperties', 'type': 'int'},
        'properties': {'key': 'properties', 'type': '[MetricSchemaProperty]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword num_properties:
        :paramtype num_properties: int
        :keyword properties:
        :paramtype properties: list[~azure.mgmt.machinelearningservices.models.MetricSchemaProperty]
        """
        super(MetricSchema, self).__init__(**kwargs)
        self.num_properties = kwargs.get('num_properties', None)
        self.properties = kwargs.get('properties', None)


class MetricSchemaProperty(msrest.serialization.Model):
    """MetricSchemaProperty.

    :ivar property_id:
    :vartype property_id: str
    :ivar name:
    :vartype name: str
    :ivar type:
    :vartype type: str
    """

    _attribute_map = {
        'property_id': {'key': 'propertyId', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword property_id:
        :paramtype property_id: str
        :keyword name:
        :paramtype name: str
        :keyword type:
        :paramtype type: str
        """
        super(MetricSchemaProperty, self).__init__(**kwargs)
        self.property_id = kwargs.get('property_id', None)
        self.name = kwargs.get('name', None)
        self.type = kwargs.get('type', None)


class MetricV2(msrest.serialization.Model):
    """Sequence of one or many values sharing a common DataContainerId, Name, and Schema.

    :ivar data_container_id: Data container to which this Metric belongs.
    :vartype data_container_id: str
    :ivar name: Name identifying this Metric within the Data Container.
    :vartype name: str
    :ivar columns: Schema shared by all values under this Metric
     Columns.Keys define the column names which are required for each MetricValue
     Columns.Values define the type of the associated object for each column.
    :vartype columns: dict[str, str or ~azure.mgmt.machinelearningservices.models.MetricValueType]
    :ivar properties:
    :vartype properties: ~azure.mgmt.machinelearningservices.models.MetricProperties
    :ivar namespace: Namespace for this Metric.
    :vartype namespace: str
    :ivar standard_schema_id:
    :vartype standard_schema_id: str
    :ivar value:
    :vartype value: list[~azure.mgmt.machinelearningservices.models.MetricV2Value]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'data_container_id': {'key': 'dataContainerId', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'columns': {'key': 'columns', 'type': '{str}'},
        'properties': {'key': 'properties', 'type': 'MetricProperties'},
        'namespace': {'key': 'namespace', 'type': 'str'},
        'standard_schema_id': {'key': 'standardSchemaId', 'type': 'str'},
        'value': {'key': 'value', 'type': '[MetricV2Value]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword data_container_id: Data container to which this Metric belongs.
        :paramtype data_container_id: str
        :keyword name: Name identifying this Metric within the Data Container.
        :paramtype name: str
        :keyword columns: Schema shared by all values under this Metric
         Columns.Keys define the column names which are required for each MetricValue
         Columns.Values define the type of the associated object for each column.
        :paramtype columns: dict[str, str or
         ~azure.mgmt.machinelearningservices.models.MetricValueType]
        :keyword properties:
        :paramtype properties: ~azure.mgmt.machinelearningservices.models.MetricProperties
        :keyword namespace: Namespace for this Metric.
        :paramtype namespace: str
        :keyword standard_schema_id:
        :paramtype standard_schema_id: str
        :keyword value:
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.MetricV2Value]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(MetricV2, self).__init__(**kwargs)
        self.data_container_id = kwargs.get('data_container_id', None)
        self.name = kwargs.get('name', None)
        self.columns = kwargs.get('columns', None)
        self.properties = kwargs.get('properties', None)
        self.namespace = kwargs.get('namespace', None)
        self.standard_schema_id = kwargs.get('standard_schema_id', None)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class MetricV2Value(msrest.serialization.Model):
    """An individual value logged within a Metric.

    :ivar metric_id: Unique Id for this metric value
     Format is either a Guid or a Guid augmented with an additional int index for cases where
     multiple metric values shared a
       MetricId in the old schema.
    :vartype metric_id: str
    :ivar created_utc: Client specified timestamp for this metric value.
    :vartype created_utc: ~datetime.datetime
    :ivar step:
    :vartype step: long
    :ivar data: Dictionary mapping column names (specified as the keys in MetricV2Dto.Columns) to
     values expressed in type associated
     with that column in the metric's schema.
    :vartype data: dict[str, any]
    """

    _attribute_map = {
        'metric_id': {'key': 'metricId', 'type': 'str'},
        'created_utc': {'key': 'createdUtc', 'type': 'iso-8601'},
        'step': {'key': 'step', 'type': 'long'},
        'data': {'key': 'data', 'type': '{object}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword metric_id: Unique Id for this metric value
         Format is either a Guid or a Guid augmented with an additional int index for cases where
         multiple metric values shared a
           MetricId in the old schema.
        :paramtype metric_id: str
        :keyword created_utc: Client specified timestamp for this metric value.
        :paramtype created_utc: ~datetime.datetime
        :keyword step:
        :paramtype step: long
        :keyword data: Dictionary mapping column names (specified as the keys in MetricV2Dto.Columns)
         to values expressed in type associated
         with that column in the metric's schema.
        :paramtype data: dict[str, any]
        """
        super(MetricV2Value, self).__init__(**kwargs)
        self.metric_id = kwargs.get('metric_id', None)
        self.created_utc = kwargs.get('created_utc', None)
        self.step = kwargs.get('step', None)
        self.data = kwargs.get('data', None)


class ModifyExperiment(msrest.serialization.Model):
    """ModifyExperiment.

    :ivar name:
    :vartype name: str
    :ivar description:
    :vartype description: str
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar archive:
    :vartype archive: bool
    :ivar retain_for_lifetime_of_workspace:
    :vartype retain_for_lifetime_of_workspace: bool
    """

    _attribute_map = {
        'name': {'key': 'name', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'archive': {'key': 'archive', 'type': 'bool'},
        'retain_for_lifetime_of_workspace': {'key': 'retainForLifetimeOfWorkspace', 'type': 'bool'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword name:
        :paramtype name: str
        :keyword description:
        :paramtype description: str
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword archive:
        :paramtype archive: bool
        :keyword retain_for_lifetime_of_workspace:
        :paramtype retain_for_lifetime_of_workspace: bool
        """
        super(ModifyExperiment, self).__init__(**kwargs)
        self.name = kwargs.get('name', None)
        self.description = kwargs.get('description', None)
        self.tags = kwargs.get('tags', None)
        self.archive = kwargs.get('archive', None)
        self.retain_for_lifetime_of_workspace = kwargs.get('retain_for_lifetime_of_workspace', None)


class OutputDatasetLineage(msrest.serialization.Model):
    """OutputDatasetLineage.

    :ivar identifier:
    :vartype identifier: ~azure.mgmt.machinelearningservices.models.DatasetIdentifier
    :ivar output_type: Possible values include: "RunOutput", "Reference".
    :vartype output_type: str or ~azure.mgmt.machinelearningservices.models.DatasetOutputType
    :ivar output_details:
    :vartype output_details: ~azure.mgmt.machinelearningservices.models.DatasetOutputDetails
    """

    _attribute_map = {
        'identifier': {'key': 'identifier', 'type': 'DatasetIdentifier'},
        'output_type': {'key': 'outputType', 'type': 'str'},
        'output_details': {'key': 'outputDetails', 'type': 'DatasetOutputDetails'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword identifier:
        :paramtype identifier: ~azure.mgmt.machinelearningservices.models.DatasetIdentifier
        :keyword output_type: Possible values include: "RunOutput", "Reference".
        :paramtype output_type: str or ~azure.mgmt.machinelearningservices.models.DatasetOutputType
        :keyword output_details:
        :paramtype output_details: ~azure.mgmt.machinelearningservices.models.DatasetOutputDetails
        """
        super(OutputDatasetLineage, self).__init__(**kwargs)
        self.identifier = kwargs.get('identifier', None)
        self.output_type = kwargs.get('output_type', None)
        self.output_details = kwargs.get('output_details', None)


class PaginatedArtifactContentInformationList(msrest.serialization.Model):
    """A paginated list of ArtifactContentInformations.

    :ivar value: An array of objects of type ArtifactContentInformation.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.ArtifactContentInformation]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'value': {'key': 'value', 'type': '[ArtifactContentInformation]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword value: An array of objects of type ArtifactContentInformation.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.ArtifactContentInformation]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(PaginatedArtifactContentInformationList, self).__init__(**kwargs)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class PaginatedArtifactList(msrest.serialization.Model):
    """A paginated list of Artifacts.

    :ivar value: An array of objects of type Artifact.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.Artifact]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'value': {'key': 'value', 'type': '[Artifact]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword value: An array of objects of type Artifact.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.Artifact]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(PaginatedArtifactList, self).__init__(**kwargs)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class PaginatedExperimentList(msrest.serialization.Model):
    """A paginated list of Experiments.

    :ivar value: An array of objects of type Experiment.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.Experiment]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'value': {'key': 'value', 'type': '[Experiment]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword value: An array of objects of type Experiment.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.Experiment]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(PaginatedExperimentList, self).__init__(**kwargs)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class PaginatedMetricDefinitionList(msrest.serialization.Model):
    """A paginated list of MetricDefinitions.

    :ivar value: An array of objects of type MetricDefinition.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.MetricDefinition]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'value': {'key': 'value', 'type': '[MetricDefinition]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword value: An array of objects of type MetricDefinition.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.MetricDefinition]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(PaginatedMetricDefinitionList, self).__init__(**kwargs)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class PaginatedRunList(msrest.serialization.Model):
    """A paginated list of Runs.

    :ivar value: An array of objects of type Run.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.Run]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'value': {'key': 'value', 'type': '[Run]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword value: An array of objects of type Run.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.Run]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(PaginatedRunList, self).__init__(**kwargs)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class PaginatedSpanDefinition1List(msrest.serialization.Model):
    """A paginated list of SpanDefinition`1s.

    :ivar value: An array of objects of type SpanDefinition`1.
    :vartype value: list[~azure.mgmt.machinelearningservices.models.SpanDefinition1]
    :ivar continuation_token: The token used in retrieving the next page. If null, there are no
     additional pages.
    :vartype continuation_token: str
    :ivar next_link: The link to the next page constructed using the continuationToken.  If null,
     there are no additional pages.
    :vartype next_link: str
    """

    _attribute_map = {
        'value': {'key': 'value', 'type': '[SpanDefinition1]'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'next_link': {'key': 'nextLink', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword value: An array of objects of type SpanDefinition`1.
        :paramtype value: list[~azure.mgmt.machinelearningservices.models.SpanDefinition1]
        :keyword continuation_token: The token used in retrieving the next page. If null, there are no
         additional pages.
        :paramtype continuation_token: str
        :keyword next_link: The link to the next page constructed using the continuationToken.  If
         null, there are no additional pages.
        :paramtype next_link: str
        """
        super(PaginatedSpanDefinition1List, self).__init__(**kwargs)
        self.value = kwargs.get('value', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.next_link = kwargs.get('next_link', None)


class PostRunMetricsError(msrest.serialization.Model):
    """PostRunMetricsError.

    :ivar metric: Sequence of one or many values sharing a common  DataContainerId, Name, and
     Schema. Used only for Post Metrics.
    :vartype metric: ~azure.mgmt.machinelearningservices.models.IMetricV2
    :ivar error_response: The error response.
    :vartype error_response: ~azure.mgmt.machinelearningservices.models.ErrorResponse
    """

    _attribute_map = {
        'metric': {'key': 'metric', 'type': 'IMetricV2'},
        'error_response': {'key': 'errorResponse', 'type': 'ErrorResponse'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword metric: Sequence of one or many values sharing a common  DataContainerId, Name, and
         Schema. Used only for Post Metrics.
        :paramtype metric: ~azure.mgmt.machinelearningservices.models.IMetricV2
        :keyword error_response: The error response.
        :paramtype error_response: ~azure.mgmt.machinelearningservices.models.ErrorResponse
        """
        super(PostRunMetricsError, self).__init__(**kwargs)
        self.metric = kwargs.get('metric', None)
        self.error_response = kwargs.get('error_response', None)


class PostRunMetricsResult(msrest.serialization.Model):
    """PostRunMetricsResult.

    :ivar errors:
    :vartype errors: list[~azure.mgmt.machinelearningservices.models.PostRunMetricsError]
    """

    _attribute_map = {
        'errors': {'key': 'errors', 'type': '[PostRunMetricsError]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword errors:
        :paramtype errors: list[~azure.mgmt.machinelearningservices.models.PostRunMetricsError]
        """
        super(PostRunMetricsResult, self).__init__(**kwargs)
        self.errors = kwargs.get('errors', None)


class QueryParams(msrest.serialization.Model):
    """The set of supported filters.

    :ivar filter: Allows for filtering the collection of resources.
     The expression specified is evaluated for each resource in the collection, and only items
     where the expression evaluates to true are included in the response.
     See https://learn.microsoft.com/azure/search/query-odata-filter-orderby-syntax for
     details on the expression syntax.
    :vartype filter: str
    :ivar continuation_token: The continuation token to use for getting the next set of resources.
    :vartype continuation_token: str
    :ivar order_by: The comma separated list of resource properties to use for sorting the
     requested resources.
     Optionally, can be followed by either 'asc' or 'desc'.
    :vartype order_by: str
    :ivar top: The maximum number of items in the resource collection to be included in the result.
     If not specified, all items are returned.
    :vartype top: int
    """

    _attribute_map = {
        'filter': {'key': 'filter', 'type': 'str'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'order_by': {'key': 'orderBy', 'type': 'str'},
        'top': {'key': 'top', 'type': 'int'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword filter: Allows for filtering the collection of resources.
         The expression specified is evaluated for each resource in the collection, and only items
         where the expression evaluates to true are included in the response.
         See https://learn.microsoft.com/azure/search/query-odata-filter-orderby-syntax for
         details on the expression syntax.
        :paramtype filter: str
        :keyword continuation_token: The continuation token to use for getting the next set of
         resources.
        :paramtype continuation_token: str
        :keyword order_by: The comma separated list of resource properties to use for sorting the
         requested resources.
         Optionally, can be followed by either 'asc' or 'desc'.
        :paramtype order_by: str
        :keyword top: The maximum number of items in the resource collection to be included in the
         result.
         If not specified, all items are returned.
        :paramtype top: int
        """
        super(QueryParams, self).__init__(**kwargs)
        self.filter = kwargs.get('filter', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.order_by = kwargs.get('order_by', None)
        self.top = kwargs.get('top', None)


class QueueingInfo(msrest.serialization.Model):
    """QueueingInfo.

    :ivar code:
    :vartype code: str
    :ivar message:
    :vartype message: str
    :ivar last_refresh_timestamp:
    :vartype last_refresh_timestamp: ~datetime.datetime
    """

    _attribute_map = {
        'code': {'key': 'code', 'type': 'str'},
        'message': {'key': 'message', 'type': 'str'},
        'last_refresh_timestamp': {'key': 'lastRefreshTimestamp', 'type': 'iso-8601'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword code:
        :paramtype code: str
        :keyword message:
        :paramtype message: str
        :keyword last_refresh_timestamp:
        :paramtype last_refresh_timestamp: ~datetime.datetime
        """
        super(QueueingInfo, self).__init__(**kwargs)
        self.code = kwargs.get('code', None)
        self.message = kwargs.get('message', None)
        self.last_refresh_timestamp = kwargs.get('last_refresh_timestamp', None)


class RetrieveFullFidelityMetricRequest(msrest.serialization.Model):
    """RetrieveFullFidelityMetricRequest.

    :ivar metric_name:
    :vartype metric_name: str
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar start_time:
    :vartype start_time: ~datetime.datetime
    :ivar end_time:
    :vartype end_time: ~datetime.datetime
    :ivar metric_namespace:
    :vartype metric_namespace: str
    """

    _attribute_map = {
        'metric_name': {'key': 'metricName', 'type': 'str'},
        'continuation_token': {'key': 'continuationToken', 'type': 'str'},
        'start_time': {'key': 'startTime', 'type': 'iso-8601'},
        'end_time': {'key': 'endTime', 'type': 'iso-8601'},
        'metric_namespace': {'key': 'metricNamespace', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword metric_name:
        :paramtype metric_name: str
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword start_time:
        :paramtype start_time: ~datetime.datetime
        :keyword end_time:
        :paramtype end_time: ~datetime.datetime
        :keyword metric_namespace:
        :paramtype metric_namespace: str
        """
        super(RetrieveFullFidelityMetricRequest, self).__init__(**kwargs)
        self.metric_name = kwargs.get('metric_name', None)
        self.continuation_token = kwargs.get('continuation_token', None)
        self.start_time = kwargs.get('start_time', None)
        self.end_time = kwargs.get('end_time', None)
        self.metric_namespace = kwargs.get('metric_namespace', None)


class RootError(msrest.serialization.Model):
    """The root error.

    :ivar code: The service-defined error code. Supported error codes: ServiceError, UserError,
     ValidationError, AzureStorageError, TransientError, RequestThrottled.
    :vartype code: str
    :ivar severity: The Severity of error.
    :vartype severity: int
    :ivar message: A human-readable representation of the error.
    :vartype message: str
    :ivar message_format: An unformatted version of the message with no variable substitution.
    :vartype message_format: str
    :ivar message_parameters: Value substitutions corresponding to the contents of MessageFormat.
    :vartype message_parameters: dict[str, str]
    :ivar reference_code: This code can optionally be set by the system generating the error.
     It should be used to classify the problem and identify the module and code area where the
     failure occured.
    :vartype reference_code: str
    :ivar details_uri: A URI which points to more details about the context of the error.
    :vartype details_uri: str
    :ivar target: The target of the error (e.g., the name of the property in error).
    :vartype target: str
    :ivar details: The related errors that occurred during the request.
    :vartype details: list[~azure.mgmt.machinelearningservices.models.RootError]
    :ivar inner_error: A nested structure of errors.
    :vartype inner_error: ~azure.mgmt.machinelearningservices.models.InnerErrorResponse
    :ivar additional_info: The error additional info.
    :vartype additional_info: list[~azure.mgmt.machinelearningservices.models.ErrorAdditionalInfo]
    """

    _attribute_map = {
        'code': {'key': 'code', 'type': 'str'},
        'severity': {'key': 'severity', 'type': 'int'},
        'message': {'key': 'message', 'type': 'str'},
        'message_format': {'key': 'messageFormat', 'type': 'str'},
        'message_parameters': {'key': 'messageParameters', 'type': '{str}'},
        'reference_code': {'key': 'referenceCode', 'type': 'str'},
        'details_uri': {'key': 'detailsUri', 'type': 'str'},
        'target': {'key': 'target', 'type': 'str'},
        'details': {'key': 'details', 'type': '[RootError]'},
        'inner_error': {'key': 'innerError', 'type': 'InnerErrorResponse'},
        'additional_info': {'key': 'additionalInfo', 'type': '[ErrorAdditionalInfo]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword code: The service-defined error code. Supported error codes: ServiceError, UserError,
         ValidationError, AzureStorageError, TransientError, RequestThrottled.
        :paramtype code: str
        :keyword severity: The Severity of error.
        :paramtype severity: int
        :keyword message: A human-readable representation of the error.
        :paramtype message: str
        :keyword message_format: An unformatted version of the message with no variable substitution.
        :paramtype message_format: str
        :keyword message_parameters: Value substitutions corresponding to the contents of
         MessageFormat.
        :paramtype message_parameters: dict[str, str]
        :keyword reference_code: This code can optionally be set by the system generating the error.
         It should be used to classify the problem and identify the module and code area where the
         failure occured.
        :paramtype reference_code: str
        :keyword details_uri: A URI which points to more details about the context of the error.
        :paramtype details_uri: str
        :keyword target: The target of the error (e.g., the name of the property in error).
        :paramtype target: str
        :keyword details: The related errors that occurred during the request.
        :paramtype details: list[~azure.mgmt.machinelearningservices.models.RootError]
        :keyword inner_error: A nested structure of errors.
        :paramtype inner_error: ~azure.mgmt.machinelearningservices.models.InnerErrorResponse
        :keyword additional_info: The error additional info.
        :paramtype additional_info:
         list[~azure.mgmt.machinelearningservices.models.ErrorAdditionalInfo]
        """
        super(RootError, self).__init__(**kwargs)
        self.code = kwargs.get('code', None)
        self.severity = kwargs.get('severity', None)
        self.message = kwargs.get('message', None)
        self.message_format = kwargs.get('message_format', None)
        self.message_parameters = kwargs.get('message_parameters', None)
        self.reference_code = kwargs.get('reference_code', None)
        self.details_uri = kwargs.get('details_uri', None)
        self.target = kwargs.get('target', None)
        self.details = kwargs.get('details', None)
        self.inner_error = kwargs.get('inner_error', None)
        self.additional_info = kwargs.get('additional_info', None)


class Run(msrest.serialization.Model):
    """The definition of a Run.

    :ivar run_number:
    :vartype run_number: int
    :ivar root_run_id:
    :vartype root_run_id: str
    :ivar created_utc: The time the run was created in UTC.
    :vartype created_utc: ~datetime.datetime
    :ivar created_by:
    :vartype created_by: ~azure.mgmt.machinelearningservices.models.User
    :ivar user_id: The Id of the user that created the run.
    :vartype user_id: str
    :ivar token: A token used for authenticating a run.
    :vartype token: str
    :ivar token_expiry_time_utc: The Token expiration time in UTC.
    :vartype token_expiry_time_utc: ~datetime.datetime
    :ivar error: The error response.
    :vartype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
    :ivar warnings: A list of warnings that occurred during the run.
    :vartype warnings: list[~azure.mgmt.machinelearningservices.models.RunDetailsWarning]
    :ivar revision:
    :vartype revision: long
    :ivar status_revision:
    :vartype status_revision: long
    :ivar run_uuid: A system generated Id for the run.
    :vartype run_uuid: str
    :ivar parent_run_uuid: A system generated Id for the run's parent.
    :vartype parent_run_uuid: str
    :ivar root_run_uuid: A system generated Id for the root of the run's hierarchy.
    :vartype root_run_uuid: str
    :ivar has_virtual_parent: Indicates if this is a child of a virtual run.
    :vartype has_virtual_parent: bool
    :ivar last_start_time_utc: The last timestamp when a run transitioned from paused to running.
     Initialized when StartTimeUtc is first set.
    :vartype last_start_time_utc: ~datetime.datetime
    :ivar current_compute_time: The cumulative time spent in an active status for an active run.
    :vartype current_compute_time: str
    :ivar compute_duration: The cumulative time spent in an active status for a terminal run.
    :vartype compute_duration: str
    :ivar effective_start_time_utc: A relative start time set as LastStartTimeUtc - ComputeTime for
     active runs. This allows sorting active runs on how long they have been active, since an actual
     active duration cannot be frequently updated.
    :vartype effective_start_time_utc: ~datetime.datetime
    :ivar last_modified_by:
    :vartype last_modified_by: ~azure.mgmt.machinelearningservices.models.User
    :ivar last_modified_utc: The time the run was created in UTC.
    :vartype last_modified_utc: ~datetime.datetime
    :ivar duration: The total duration of a run.
    :vartype duration: str
    :ivar cancelation_reason: The cancelation Reason if the run was canceled.
    :vartype cancelation_reason: str
    :ivar run_id: The identifier for the run. Run IDs must be less than 256 characters and contain
     only alphanumeric characters with dashes and underscores.
    :vartype run_id: str
    :ivar parent_run_id: The parent of the run if the run is hierarchical; otherwise, Null.
    :vartype parent_run_id: str
    :ivar experiment_id: The Id of the experiment that created this run.
    :vartype experiment_id: str
    :ivar status: The status of the run. The Status string value maps to the RunStatus Enum.
    :vartype status: str
    :ivar start_time_utc: The start time of the run in UTC.
    :vartype start_time_utc: ~datetime.datetime
    :ivar end_time_utc: The end time of the run in UTC.
    :vartype end_time_utc: ~datetime.datetime
    :ivar options:
    :vartype options: ~azure.mgmt.machinelearningservices.models.RunOptions
    :ivar is_virtual: A virtual run can set an active child run that will override the virtual run
     status and properties.
    :vartype is_virtual: bool
    :ivar display_name:
    :vartype display_name: str
    :ivar name:
    :vartype name: str
    :ivar data_container_id:
    :vartype data_container_id: str
    :ivar description:
    :vartype description: str
    :ivar hidden:
    :vartype hidden: bool
    :ivar run_type:
    :vartype run_type: str
    :ivar run_type_v2:
    :vartype run_type_v2: ~azure.mgmt.machinelearningservices.models.RunTypeV2
    :ivar properties: Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar parameters: Dictionary of :code:`<any>`.
    :vartype parameters: dict[str, any]
    :ivar action_uris: Dictionary of :code:`<string>`.
    :vartype action_uris: dict[str, str]
    :ivar script_name:
    :vartype script_name: str
    :ivar target:
    :vartype target: str
    :ivar unique_child_run_compute_targets:
    :vartype unique_child_run_compute_targets: list[str]
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar settings: Dictionary of :code:`<string>`.
    :vartype settings: dict[str, str]
    :ivar services: Dictionary of :code:`<EndpointSetting>`.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.EndpointSetting]
    :ivar input_datasets:
    :vartype input_datasets: list[~azure.mgmt.machinelearningservices.models.DatasetLineage]
    :ivar output_datasets:
    :vartype output_datasets: list[~azure.mgmt.machinelearningservices.models.OutputDatasetLineage]
    :ivar run_definition: Anything.
    :vartype run_definition: any
    :ivar job_specification: Anything.
    :vartype job_specification: any
    :ivar primary_metric_name:
    :vartype primary_metric_name: str
    :ivar created_from:
    :vartype created_from: ~azure.mgmt.machinelearningservices.models.CreatedFrom
    :ivar cancel_uri:
    :vartype cancel_uri: str
    :ivar complete_uri:
    :vartype complete_uri: str
    :ivar diagnostics_uri:
    :vartype diagnostics_uri: str
    :ivar compute_request:
    :vartype compute_request: ~azure.mgmt.machinelearningservices.models.ComputeRequest
    :ivar compute:
    :vartype compute: ~azure.mgmt.machinelearningservices.models.Compute
    :ivar retain_for_lifetime_of_workspace:
    :vartype retain_for_lifetime_of_workspace: bool
    :ivar queueing_info:
    :vartype queueing_info: ~azure.mgmt.machinelearningservices.models.QueueingInfo
    :ivar active_child_run_id: The RunId of the active child on a virtual run.
    :vartype active_child_run_id: str
    :ivar inputs: Dictionary of :code:`<TypedAssetReference>`.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
    :ivar outputs: Dictionary of :code:`<TypedAssetReference>`.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
    """

    _validation = {
        'unique_child_run_compute_targets': {'unique': True},
        'input_datasets': {'unique': True},
        'output_datasets': {'unique': True},
    }

    _attribute_map = {
        'run_number': {'key': 'runNumber', 'type': 'int'},
        'root_run_id': {'key': 'rootRunId', 'type': 'str'},
        'created_utc': {'key': 'createdUtc', 'type': 'iso-8601'},
        'created_by': {'key': 'createdBy', 'type': 'User'},
        'user_id': {'key': 'userId', 'type': 'str'},
        'token': {'key': 'token', 'type': 'str'},
        'token_expiry_time_utc': {'key': 'tokenExpiryTimeUtc', 'type': 'iso-8601'},
        'error': {'key': 'error', 'type': 'ErrorResponse'},
        'warnings': {'key': 'warnings', 'type': '[RunDetailsWarning]'},
        'revision': {'key': 'revision', 'type': 'long'},
        'status_revision': {'key': 'statusRevision', 'type': 'long'},
        'run_uuid': {'key': 'runUuid', 'type': 'str'},
        'parent_run_uuid': {'key': 'parentRunUuid', 'type': 'str'},
        'root_run_uuid': {'key': 'rootRunUuid', 'type': 'str'},
        'has_virtual_parent': {'key': 'hasVirtualParent', 'type': 'bool'},
        'last_start_time_utc': {'key': 'lastStartTimeUtc', 'type': 'iso-8601'},
        'current_compute_time': {'key': 'currentComputeTime', 'type': 'str'},
        'compute_duration': {'key': 'computeDuration', 'type': 'str'},
        'effective_start_time_utc': {'key': 'effectiveStartTimeUtc', 'type': 'iso-8601'},
        'last_modified_by': {'key': 'lastModifiedBy', 'type': 'User'},
        'last_modified_utc': {'key': 'lastModifiedUtc', 'type': 'iso-8601'},
        'duration': {'key': 'duration', 'type': 'str'},
        'cancelation_reason': {'key': 'cancelationReason', 'type': 'str'},
        'run_id': {'key': 'runId', 'type': 'str'},
        'parent_run_id': {'key': 'parentRunId', 'type': 'str'},
        'experiment_id': {'key': 'experimentId', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'start_time_utc': {'key': 'startTimeUtc', 'type': 'iso-8601'},
        'end_time_utc': {'key': 'endTimeUtc', 'type': 'iso-8601'},
        'options': {'key': 'options', 'type': 'RunOptions'},
        'is_virtual': {'key': 'isVirtual', 'type': 'bool'},
        'display_name': {'key': 'displayName', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'data_container_id': {'key': 'dataContainerId', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'hidden': {'key': 'hidden', 'type': 'bool'},
        'run_type': {'key': 'runType', 'type': 'str'},
        'run_type_v2': {'key': 'runTypeV2', 'type': 'RunTypeV2'},
        'properties': {'key': 'properties', 'type': '{str}'},
        'parameters': {'key': 'parameters', 'type': '{object}'},
        'action_uris': {'key': 'actionUris', 'type': '{str}'},
        'script_name': {'key': 'scriptName', 'type': 'str'},
        'target': {'key': 'target', 'type': 'str'},
        'unique_child_run_compute_targets': {'key': 'uniqueChildRunComputeTargets', 'type': '[str]'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'settings': {'key': 'settings', 'type': '{str}'},
        'services': {'key': 'services', 'type': '{EndpointSetting}'},
        'input_datasets': {'key': 'inputDatasets', 'type': '[DatasetLineage]'},
        'output_datasets': {'key': 'outputDatasets', 'type': '[OutputDatasetLineage]'},
        'run_definition': {'key': 'runDefinition', 'type': 'object'},
        'job_specification': {'key': 'jobSpecification', 'type': 'object'},
        'primary_metric_name': {'key': 'primaryMetricName', 'type': 'str'},
        'created_from': {'key': 'createdFrom', 'type': 'CreatedFrom'},
        'cancel_uri': {'key': 'cancelUri', 'type': 'str'},
        'complete_uri': {'key': 'completeUri', 'type': 'str'},
        'diagnostics_uri': {'key': 'diagnosticsUri', 'type': 'str'},
        'compute_request': {'key': 'computeRequest', 'type': 'ComputeRequest'},
        'compute': {'key': 'compute', 'type': 'Compute'},
        'retain_for_lifetime_of_workspace': {'key': 'retainForLifetimeOfWorkspace', 'type': 'bool'},
        'queueing_info': {'key': 'queueingInfo', 'type': 'QueueingInfo'},
        'active_child_run_id': {'key': 'activeChildRunId', 'type': 'str'},
        'inputs': {'key': 'inputs', 'type': '{TypedAssetReference}'},
        'outputs': {'key': 'outputs', 'type': '{TypedAssetReference}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_number:
        :paramtype run_number: int
        :keyword root_run_id:
        :paramtype root_run_id: str
        :keyword created_utc: The time the run was created in UTC.
        :paramtype created_utc: ~datetime.datetime
        :keyword created_by:
        :paramtype created_by: ~azure.mgmt.machinelearningservices.models.User
        :keyword user_id: The Id of the user that created the run.
        :paramtype user_id: str
        :keyword token: A token used for authenticating a run.
        :paramtype token: str
        :keyword token_expiry_time_utc: The Token expiration time in UTC.
        :paramtype token_expiry_time_utc: ~datetime.datetime
        :keyword error: The error response.
        :paramtype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
        :keyword warnings: A list of warnings that occurred during the run.
        :paramtype warnings: list[~azure.mgmt.machinelearningservices.models.RunDetailsWarning]
        :keyword revision:
        :paramtype revision: long
        :keyword status_revision:
        :paramtype status_revision: long
        :keyword run_uuid: A system generated Id for the run.
        :paramtype run_uuid: str
        :keyword parent_run_uuid: A system generated Id for the run's parent.
        :paramtype parent_run_uuid: str
        :keyword root_run_uuid: A system generated Id for the root of the run's hierarchy.
        :paramtype root_run_uuid: str
        :keyword has_virtual_parent: Indicates if this is a child of a virtual run.
        :paramtype has_virtual_parent: bool
        :keyword last_start_time_utc: The last timestamp when a run transitioned from paused to
         running. Initialized when StartTimeUtc is first set.
        :paramtype last_start_time_utc: ~datetime.datetime
        :keyword current_compute_time: The cumulative time spent in an active status for an active run.
        :paramtype current_compute_time: str
        :keyword compute_duration: The cumulative time spent in an active status for a terminal run.
        :paramtype compute_duration: str
        :keyword effective_start_time_utc: A relative start time set as LastStartTimeUtc - ComputeTime
         for active runs. This allows sorting active runs on how long they have been active, since an
         actual active duration cannot be frequently updated.
        :paramtype effective_start_time_utc: ~datetime.datetime
        :keyword last_modified_by:
        :paramtype last_modified_by: ~azure.mgmt.machinelearningservices.models.User
        :keyword last_modified_utc: The time the run was created in UTC.
        :paramtype last_modified_utc: ~datetime.datetime
        :keyword duration: The total duration of a run.
        :paramtype duration: str
        :keyword cancelation_reason: The cancelation Reason if the run was canceled.
        :paramtype cancelation_reason: str
        :keyword run_id: The identifier for the run. Run IDs must be less than 256 characters and
         contain only alphanumeric characters with dashes and underscores.
        :paramtype run_id: str
        :keyword parent_run_id: The parent of the run if the run is hierarchical; otherwise, Null.
        :paramtype parent_run_id: str
        :keyword experiment_id: The Id of the experiment that created this run.
        :paramtype experiment_id: str
        :keyword status: The status of the run. The Status string value maps to the RunStatus Enum.
        :paramtype status: str
        :keyword start_time_utc: The start time of the run in UTC.
        :paramtype start_time_utc: ~datetime.datetime
        :keyword end_time_utc: The end time of the run in UTC.
        :paramtype end_time_utc: ~datetime.datetime
        :keyword options:
        :paramtype options: ~azure.mgmt.machinelearningservices.models.RunOptions
        :keyword is_virtual: A virtual run can set an active child run that will override the virtual
         run status and properties.
        :paramtype is_virtual: bool
        :keyword display_name:
        :paramtype display_name: str
        :keyword name:
        :paramtype name: str
        :keyword data_container_id:
        :paramtype data_container_id: str
        :keyword description:
        :paramtype description: str
        :keyword hidden:
        :paramtype hidden: bool
        :keyword run_type:
        :paramtype run_type: str
        :keyword run_type_v2:
        :paramtype run_type_v2: ~azure.mgmt.machinelearningservices.models.RunTypeV2
        :keyword properties: Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword parameters: Dictionary of :code:`<any>`.
        :paramtype parameters: dict[str, any]
        :keyword action_uris: Dictionary of :code:`<string>`.
        :paramtype action_uris: dict[str, str]
        :keyword script_name:
        :paramtype script_name: str
        :keyword target:
        :paramtype target: str
        :keyword unique_child_run_compute_targets:
        :paramtype unique_child_run_compute_targets: list[str]
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword settings: Dictionary of :code:`<string>`.
        :paramtype settings: dict[str, str]
        :keyword services: Dictionary of :code:`<EndpointSetting>`.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.EndpointSetting]
        :keyword input_datasets:
        :paramtype input_datasets: list[~azure.mgmt.machinelearningservices.models.DatasetLineage]
        :keyword output_datasets:
        :paramtype output_datasets:
         list[~azure.mgmt.machinelearningservices.models.OutputDatasetLineage]
        :keyword run_definition: Anything.
        :paramtype run_definition: any
        :keyword job_specification: Anything.
        :paramtype job_specification: any
        :keyword primary_metric_name:
        :paramtype primary_metric_name: str
        :keyword created_from:
        :paramtype created_from: ~azure.mgmt.machinelearningservices.models.CreatedFrom
        :keyword cancel_uri:
        :paramtype cancel_uri: str
        :keyword complete_uri:
        :paramtype complete_uri: str
        :keyword diagnostics_uri:
        :paramtype diagnostics_uri: str
        :keyword compute_request:
        :paramtype compute_request: ~azure.mgmt.machinelearningservices.models.ComputeRequest
        :keyword compute:
        :paramtype compute: ~azure.mgmt.machinelearningservices.models.Compute
        :keyword retain_for_lifetime_of_workspace:
        :paramtype retain_for_lifetime_of_workspace: bool
        :keyword queueing_info:
        :paramtype queueing_info: ~azure.mgmt.machinelearningservices.models.QueueingInfo
        :keyword active_child_run_id: The RunId of the active child on a virtual run.
        :paramtype active_child_run_id: str
        :keyword inputs: Dictionary of :code:`<TypedAssetReference>`.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
        :keyword outputs: Dictionary of :code:`<TypedAssetReference>`.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
        """
        super(Run, self).__init__(**kwargs)
        self.run_number = kwargs.get('run_number', None)
        self.root_run_id = kwargs.get('root_run_id', None)
        self.created_utc = kwargs.get('created_utc', None)
        self.created_by = kwargs.get('created_by', None)
        self.user_id = kwargs.get('user_id', None)
        self.token = kwargs.get('token', None)
        self.token_expiry_time_utc = kwargs.get('token_expiry_time_utc', None)
        self.error = kwargs.get('error', None)
        self.warnings = kwargs.get('warnings', None)
        self.revision = kwargs.get('revision', None)
        self.status_revision = kwargs.get('status_revision', None)
        self.run_uuid = kwargs.get('run_uuid', None)
        self.parent_run_uuid = kwargs.get('parent_run_uuid', None)
        self.root_run_uuid = kwargs.get('root_run_uuid', None)
        self.has_virtual_parent = kwargs.get('has_virtual_parent', None)
        self.last_start_time_utc = kwargs.get('last_start_time_utc', None)
        self.current_compute_time = kwargs.get('current_compute_time', None)
        self.compute_duration = kwargs.get('compute_duration', None)
        self.effective_start_time_utc = kwargs.get('effective_start_time_utc', None)
        self.last_modified_by = kwargs.get('last_modified_by', None)
        self.last_modified_utc = kwargs.get('last_modified_utc', None)
        self.duration = kwargs.get('duration', None)
        self.cancelation_reason = kwargs.get('cancelation_reason', None)
        self.run_id = kwargs.get('run_id', None)
        self.parent_run_id = kwargs.get('parent_run_id', None)
        self.experiment_id = kwargs.get('experiment_id', None)
        self.status = kwargs.get('status', None)
        self.start_time_utc = kwargs.get('start_time_utc', None)
        self.end_time_utc = kwargs.get('end_time_utc', None)
        self.options = kwargs.get('options', None)
        self.is_virtual = kwargs.get('is_virtual', None)
        self.display_name = kwargs.get('display_name', None)
        self.name = kwargs.get('name', None)
        self.data_container_id = kwargs.get('data_container_id', None)
        self.description = kwargs.get('description', None)
        self.hidden = kwargs.get('hidden', None)
        self.run_type = kwargs.get('run_type', None)
        self.run_type_v2 = kwargs.get('run_type_v2', None)
        self.properties = kwargs.get('properties', None)
        self.parameters = kwargs.get('parameters', None)
        self.action_uris = kwargs.get('action_uris', None)
        self.script_name = kwargs.get('script_name', None)
        self.target = kwargs.get('target', None)
        self.unique_child_run_compute_targets = kwargs.get('unique_child_run_compute_targets', None)
        self.tags = kwargs.get('tags', None)
        self.settings = kwargs.get('settings', None)
        self.services = kwargs.get('services', None)
        self.input_datasets = kwargs.get('input_datasets', None)
        self.output_datasets = kwargs.get('output_datasets', None)
        self.run_definition = kwargs.get('run_definition', None)
        self.job_specification = kwargs.get('job_specification', None)
        self.primary_metric_name = kwargs.get('primary_metric_name', None)
        self.created_from = kwargs.get('created_from', None)
        self.cancel_uri = kwargs.get('cancel_uri', None)
        self.complete_uri = kwargs.get('complete_uri', None)
        self.diagnostics_uri = kwargs.get('diagnostics_uri', None)
        self.compute_request = kwargs.get('compute_request', None)
        self.compute = kwargs.get('compute', None)
        self.retain_for_lifetime_of_workspace = kwargs.get('retain_for_lifetime_of_workspace', None)
        self.queueing_info = kwargs.get('queueing_info', None)
        self.active_child_run_id = kwargs.get('active_child_run_id', None)
        self.inputs = kwargs.get('inputs', None)
        self.outputs = kwargs.get('outputs', None)


class RunDetails(msrest.serialization.Model):
    """The details of the run.

    :ivar run_id: The identifier for the run.
    :vartype run_id: str
    :ivar run_uuid: A system generated Id for the run.
    :vartype run_uuid: str
    :ivar parent_run_uuid: A system generated Id for the run's parent.
    :vartype parent_run_uuid: str
    :ivar root_run_uuid: A system generated Id for the root of the run's hierarchy.
    :vartype root_run_uuid: str
    :ivar target: The name of the compute target where the run is executed.
    :vartype target: str
    :ivar status: The status of the run. The Status string value maps to the RunStatus Enum.
    :vartype status: str
    :ivar parent_run_id: The parent of the run if the run is hierarchical.
    :vartype parent_run_id: str
    :ivar created_time_utc: The creation time of the run in UTC.
    :vartype created_time_utc: ~datetime.datetime
    :ivar start_time_utc: The start time of the run in UTC.
    :vartype start_time_utc: ~datetime.datetime
    :ivar end_time_utc: The end time of the run in UTC.
    :vartype end_time_utc: ~datetime.datetime
    :ivar error: The error response.
    :vartype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
    :ivar warnings: A list of warnings that occurred during the run.
    :vartype warnings: list[~azure.mgmt.machinelearningservices.models.RunDetailsWarning]
    :ivar tags: A set of tags. The tag dictionary for the run. Tags are mutable.
    :vartype tags: dict[str, str]
    :ivar properties: The properties dictionary for the run. Properties are immutable.
    :vartype properties: dict[str, str]
    :ivar parameters: The parameters dictionary for the run. Parameters are immutable.
    :vartype parameters: dict[str, any]
    :ivar services: The interactive run services for a run. Services are mutable.
    :vartype services: dict[str, ~azure.mgmt.machinelearningservices.models.EndpointSetting]
    :ivar input_datasets: A list of dataset used as input to the run.
    :vartype input_datasets: list[~azure.mgmt.machinelearningservices.models.DatasetLineage]
    :ivar output_datasets: A list of dataset used as output to the run.
    :vartype output_datasets: list[~azure.mgmt.machinelearningservices.models.OutputDatasetLineage]
    :ivar run_definition: The run definition specification.
    :vartype run_definition: any
    :ivar log_files: Dictionary of :code:`<string>`.
    :vartype log_files: dict[str, str]
    :ivar job_cost:
    :vartype job_cost: ~azure.mgmt.machinelearningservices.models.JobCost
    :ivar revision:
    :vartype revision: long
    :ivar run_type_v2:
    :vartype run_type_v2: ~azure.mgmt.machinelearningservices.models.RunTypeV2
    :ivar settings: The run settings.
    :vartype settings: dict[str, str]
    :ivar compute_request:
    :vartype compute_request: ~azure.mgmt.machinelearningservices.models.ComputeRequest
    :ivar compute:
    :vartype compute: ~azure.mgmt.machinelearningservices.models.Compute
    :ivar created_by:
    :vartype created_by: ~azure.mgmt.machinelearningservices.models.User
    :ivar compute_duration: Time spent in an active state for terminal runs.
    :vartype compute_duration: str
    :ivar effective_start_time_utc: Relative start time of active runs for ordering and computing
     active compute duration.
     Compute duration of an active run is now() - EffectiveStartTimeUtc.
    :vartype effective_start_time_utc: ~datetime.datetime
    :ivar run_number: Relative start time of active runs for ordering and computing active compute
     duration.
     Compute duration of an active run is now() - EffectiveStartTimeUtc.
    :vartype run_number: int
    :ivar root_run_id:
    :vartype root_run_id: str
    :ivar user_id: The Id of the user that created the run.
    :vartype user_id: str
    :ivar status_revision:
    :vartype status_revision: long
    :ivar has_virtual_parent: Indicates if this is a child of a virtual run.
    :vartype has_virtual_parent: bool
    :ivar current_compute_time: The cumulative time spent in an active status for an active run.
    :vartype current_compute_time: str
    :ivar last_start_time_utc: The last timestamp when a run transitioned from paused to running.
     Initialized when StartTimeUtc is first set.
    :vartype last_start_time_utc: ~datetime.datetime
    :ivar last_modified_by:
    :vartype last_modified_by: ~azure.mgmt.machinelearningservices.models.User
    :ivar last_modified_utc: The time the run was created in UTC.
    :vartype last_modified_utc: ~datetime.datetime
    :ivar duration: The total duration of a run.
    :vartype duration: str
    :ivar inputs: The inputs for the run.
    :vartype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
    :ivar outputs: The outputs for the run.
    :vartype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
    """

    _validation = {
        'input_datasets': {'unique': True},
        'output_datasets': {'unique': True},
    }

    _attribute_map = {
        'run_id': {'key': 'runId', 'type': 'str'},
        'run_uuid': {'key': 'runUuid', 'type': 'str'},
        'parent_run_uuid': {'key': 'parentRunUuid', 'type': 'str'},
        'root_run_uuid': {'key': 'rootRunUuid', 'type': 'str'},
        'target': {'key': 'target', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'parent_run_id': {'key': 'parentRunId', 'type': 'str'},
        'created_time_utc': {'key': 'createdTimeUtc', 'type': 'iso-8601'},
        'start_time_utc': {'key': 'startTimeUtc', 'type': 'iso-8601'},
        'end_time_utc': {'key': 'endTimeUtc', 'type': 'iso-8601'},
        'error': {'key': 'error', 'type': 'ErrorResponse'},
        'warnings': {'key': 'warnings', 'type': '[RunDetailsWarning]'},
        'tags': {'key': 'tags', 'type': '{str}'},
        'properties': {'key': 'properties', 'type': '{str}'},
        'parameters': {'key': 'parameters', 'type': '{object}'},
        'services': {'key': 'services', 'type': '{EndpointSetting}'},
        'input_datasets': {'key': 'inputDatasets', 'type': '[DatasetLineage]'},
        'output_datasets': {'key': 'outputDatasets', 'type': '[OutputDatasetLineage]'},
        'run_definition': {'key': 'runDefinition', 'type': 'object'},
        'log_files': {'key': 'logFiles', 'type': '{str}'},
        'job_cost': {'key': 'jobCost', 'type': 'JobCost'},
        'revision': {'key': 'revision', 'type': 'long'},
        'run_type_v2': {'key': 'runTypeV2', 'type': 'RunTypeV2'},
        'settings': {'key': 'settings', 'type': '{str}'},
        'compute_request': {'key': 'computeRequest', 'type': 'ComputeRequest'},
        'compute': {'key': 'compute', 'type': 'Compute'},
        'created_by': {'key': 'createdBy', 'type': 'User'},
        'compute_duration': {'key': 'computeDuration', 'type': 'str'},
        'effective_start_time_utc': {'key': 'effectiveStartTimeUtc', 'type': 'iso-8601'},
        'run_number': {'key': 'runNumber', 'type': 'int'},
        'root_run_id': {'key': 'rootRunId', 'type': 'str'},
        'user_id': {'key': 'userId', 'type': 'str'},
        'status_revision': {'key': 'statusRevision', 'type': 'long'},
        'has_virtual_parent': {'key': 'hasVirtualParent', 'type': 'bool'},
        'current_compute_time': {'key': 'currentComputeTime', 'type': 'str'},
        'last_start_time_utc': {'key': 'lastStartTimeUtc', 'type': 'iso-8601'},
        'last_modified_by': {'key': 'lastModifiedBy', 'type': 'User'},
        'last_modified_utc': {'key': 'lastModifiedUtc', 'type': 'iso-8601'},
        'duration': {'key': 'duration', 'type': 'str'},
        'inputs': {'key': 'inputs', 'type': '{TypedAssetReference}'},
        'outputs': {'key': 'outputs', 'type': '{TypedAssetReference}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_id: The identifier for the run.
        :paramtype run_id: str
        :keyword run_uuid: A system generated Id for the run.
        :paramtype run_uuid: str
        :keyword parent_run_uuid: A system generated Id for the run's parent.
        :paramtype parent_run_uuid: str
        :keyword root_run_uuid: A system generated Id for the root of the run's hierarchy.
        :paramtype root_run_uuid: str
        :keyword target: The name of the compute target where the run is executed.
        :paramtype target: str
        :keyword status: The status of the run. The Status string value maps to the RunStatus Enum.
        :paramtype status: str
        :keyword parent_run_id: The parent of the run if the run is hierarchical.
        :paramtype parent_run_id: str
        :keyword created_time_utc: The creation time of the run in UTC.
        :paramtype created_time_utc: ~datetime.datetime
        :keyword start_time_utc: The start time of the run in UTC.
        :paramtype start_time_utc: ~datetime.datetime
        :keyword end_time_utc: The end time of the run in UTC.
        :paramtype end_time_utc: ~datetime.datetime
        :keyword error: The error response.
        :paramtype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
        :keyword warnings: A list of warnings that occurred during the run.
        :paramtype warnings: list[~azure.mgmt.machinelearningservices.models.RunDetailsWarning]
        :keyword tags: A set of tags. The tag dictionary for the run. Tags are mutable.
        :paramtype tags: dict[str, str]
        :keyword properties: The properties dictionary for the run. Properties are immutable.
        :paramtype properties: dict[str, str]
        :keyword parameters: The parameters dictionary for the run. Parameters are immutable.
        :paramtype parameters: dict[str, any]
        :keyword services: The interactive run services for a run. Services are mutable.
        :paramtype services: dict[str, ~azure.mgmt.machinelearningservices.models.EndpointSetting]
        :keyword input_datasets: A list of dataset used as input to the run.
        :paramtype input_datasets: list[~azure.mgmt.machinelearningservices.models.DatasetLineage]
        :keyword output_datasets: A list of dataset used as output to the run.
        :paramtype output_datasets:
         list[~azure.mgmt.machinelearningservices.models.OutputDatasetLineage]
        :keyword run_definition: The run definition specification.
        :paramtype run_definition: any
        :keyword log_files: Dictionary of :code:`<string>`.
        :paramtype log_files: dict[str, str]
        :keyword job_cost:
        :paramtype job_cost: ~azure.mgmt.machinelearningservices.models.JobCost
        :keyword revision:
        :paramtype revision: long
        :keyword run_type_v2:
        :paramtype run_type_v2: ~azure.mgmt.machinelearningservices.models.RunTypeV2
        :keyword settings: The run settings.
        :paramtype settings: dict[str, str]
        :keyword compute_request:
        :paramtype compute_request: ~azure.mgmt.machinelearningservices.models.ComputeRequest
        :keyword compute:
        :paramtype compute: ~azure.mgmt.machinelearningservices.models.Compute
        :keyword created_by:
        :paramtype created_by: ~azure.mgmt.machinelearningservices.models.User
        :keyword compute_duration: Time spent in an active state for terminal runs.
        :paramtype compute_duration: str
        :keyword effective_start_time_utc: Relative start time of active runs for ordering and
         computing active compute duration.
         Compute duration of an active run is now() - EffectiveStartTimeUtc.
        :paramtype effective_start_time_utc: ~datetime.datetime
        :keyword run_number: Relative start time of active runs for ordering and computing active
         compute duration.
         Compute duration of an active run is now() - EffectiveStartTimeUtc.
        :paramtype run_number: int
        :keyword root_run_id:
        :paramtype root_run_id: str
        :keyword user_id: The Id of the user that created the run.
        :paramtype user_id: str
        :keyword status_revision:
        :paramtype status_revision: long
        :keyword has_virtual_parent: Indicates if this is a child of a virtual run.
        :paramtype has_virtual_parent: bool
        :keyword current_compute_time: The cumulative time spent in an active status for an active run.
        :paramtype current_compute_time: str
        :keyword last_start_time_utc: The last timestamp when a run transitioned from paused to
         running. Initialized when StartTimeUtc is first set.
        :paramtype last_start_time_utc: ~datetime.datetime
        :keyword last_modified_by:
        :paramtype last_modified_by: ~azure.mgmt.machinelearningservices.models.User
        :keyword last_modified_utc: The time the run was created in UTC.
        :paramtype last_modified_utc: ~datetime.datetime
        :keyword duration: The total duration of a run.
        :paramtype duration: str
        :keyword inputs: The inputs for the run.
        :paramtype inputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
        :keyword outputs: The outputs for the run.
        :paramtype outputs: dict[str, ~azure.mgmt.machinelearningservices.models.TypedAssetReference]
        """
        super(RunDetails, self).__init__(**kwargs)
        self.run_id = kwargs.get('run_id', None)
        self.run_uuid = kwargs.get('run_uuid', None)
        self.parent_run_uuid = kwargs.get('parent_run_uuid', None)
        self.root_run_uuid = kwargs.get('root_run_uuid', None)
        self.target = kwargs.get('target', None)
        self.status = kwargs.get('status', None)
        self.parent_run_id = kwargs.get('parent_run_id', None)
        self.created_time_utc = kwargs.get('created_time_utc', None)
        self.start_time_utc = kwargs.get('start_time_utc', None)
        self.end_time_utc = kwargs.get('end_time_utc', None)
        self.error = kwargs.get('error', None)
        self.warnings = kwargs.get('warnings', None)
        self.tags = kwargs.get('tags', None)
        self.properties = kwargs.get('properties', None)
        self.parameters = kwargs.get('parameters', None)
        self.services = kwargs.get('services', None)
        self.input_datasets = kwargs.get('input_datasets', None)
        self.output_datasets = kwargs.get('output_datasets', None)
        self.run_definition = kwargs.get('run_definition', None)
        self.log_files = kwargs.get('log_files', None)
        self.job_cost = kwargs.get('job_cost', None)
        self.revision = kwargs.get('revision', None)
        self.run_type_v2 = kwargs.get('run_type_v2', None)
        self.settings = kwargs.get('settings', None)
        self.compute_request = kwargs.get('compute_request', None)
        self.compute = kwargs.get('compute', None)
        self.created_by = kwargs.get('created_by', None)
        self.compute_duration = kwargs.get('compute_duration', None)
        self.effective_start_time_utc = kwargs.get('effective_start_time_utc', None)
        self.run_number = kwargs.get('run_number', None)
        self.root_run_id = kwargs.get('root_run_id', None)
        self.user_id = kwargs.get('user_id', None)
        self.status_revision = kwargs.get('status_revision', None)
        self.has_virtual_parent = kwargs.get('has_virtual_parent', None)
        self.current_compute_time = kwargs.get('current_compute_time', None)
        self.last_start_time_utc = kwargs.get('last_start_time_utc', None)
        self.last_modified_by = kwargs.get('last_modified_by', None)
        self.last_modified_utc = kwargs.get('last_modified_utc', None)
        self.duration = kwargs.get('duration', None)
        self.inputs = kwargs.get('inputs', None)
        self.outputs = kwargs.get('outputs', None)


class RunDetailsWarning(msrest.serialization.Model):
    """RunDetailsWarning.

    :ivar source:
    :vartype source: str
    :ivar message:
    :vartype message: str
    """

    _attribute_map = {
        'source': {'key': 'source', 'type': 'str'},
        'message': {'key': 'message', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword source:
        :paramtype source: str
        :keyword message:
        :paramtype message: str
        """
        super(RunDetailsWarning, self).__init__(**kwargs)
        self.source = kwargs.get('source', None)
        self.message = kwargs.get('message', None)


class RunMetric(msrest.serialization.Model):
    """RunMetric.

    :ivar run_id:
    :vartype run_id: str
    :ivar metric_id:
    :vartype metric_id: str
    :ivar data_container_id:
    :vartype data_container_id: str
    :ivar metric_type:
    :vartype metric_type: str
    :ivar created_utc:
    :vartype created_utc: ~datetime.datetime
    :ivar name:
    :vartype name: str
    :ivar description:
    :vartype description: str
    :ivar label:
    :vartype label: str
    :ivar num_cells:
    :vartype num_cells: int
    :ivar data_location:
    :vartype data_location: str
    :ivar cells:
    :vartype cells: list[dict[str, any]]
    :ivar schema:
    :vartype schema: ~azure.mgmt.machinelearningservices.models.MetricSchema
    """

    _attribute_map = {
        'run_id': {'key': 'runId', 'type': 'str'},
        'metric_id': {'key': 'metricId', 'type': 'str'},
        'data_container_id': {'key': 'dataContainerId', 'type': 'str'},
        'metric_type': {'key': 'metricType', 'type': 'str'},
        'created_utc': {'key': 'createdUtc', 'type': 'iso-8601'},
        'name': {'key': 'name', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'label': {'key': 'label', 'type': 'str'},
        'num_cells': {'key': 'numCells', 'type': 'int'},
        'data_location': {'key': 'dataLocation', 'type': 'str'},
        'cells': {'key': 'cells', 'type': '[{object}]'},
        'schema': {'key': 'schema', 'type': 'MetricSchema'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword run_id:
        :paramtype run_id: str
        :keyword metric_id:
        :paramtype metric_id: str
        :keyword data_container_id:
        :paramtype data_container_id: str
        :keyword metric_type:
        :paramtype metric_type: str
        :keyword created_utc:
        :paramtype created_utc: ~datetime.datetime
        :keyword name:
        :paramtype name: str
        :keyword description:
        :paramtype description: str
        :keyword label:
        :paramtype label: str
        :keyword num_cells:
        :paramtype num_cells: int
        :keyword data_location:
        :paramtype data_location: str
        :keyword cells:
        :paramtype cells: list[dict[str, any]]
        :keyword schema:
        :paramtype schema: ~azure.mgmt.machinelearningservices.models.MetricSchema
        """
        super(RunMetric, self).__init__(**kwargs)
        self.run_id = kwargs.get('run_id', None)
        self.metric_id = kwargs.get('metric_id', None)
        self.data_container_id = kwargs.get('data_container_id', None)
        self.metric_type = kwargs.get('metric_type', None)
        self.created_utc = kwargs.get('created_utc', None)
        self.name = kwargs.get('name', None)
        self.description = kwargs.get('description', None)
        self.label = kwargs.get('label', None)
        self.num_cells = kwargs.get('num_cells', None)
        self.data_location = kwargs.get('data_location', None)
        self.cells = kwargs.get('cells', None)
        self.schema = kwargs.get('schema', None)


class RunOptions(msrest.serialization.Model):
    """RunOptions.

    :ivar generate_data_container_id_if_not_specified:
    :vartype generate_data_container_id_if_not_specified: bool
    """

    _attribute_map = {
        'generate_data_container_id_if_not_specified': {'key': 'generateDataContainerIdIfNotSpecified', 'type': 'bool'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword generate_data_container_id_if_not_specified:
        :paramtype generate_data_container_id_if_not_specified: bool
        """
        super(RunOptions, self).__init__(**kwargs)
        self.generate_data_container_id_if_not_specified = kwargs.get('generate_data_container_id_if_not_specified', None)


class RunServiceInstances(msrest.serialization.Model):
    """RunServiceInstances.

    :ivar instances: Dictionary of :code:`<ServiceInstanceResult>`.
    :vartype instances: dict[str, ~azure.mgmt.machinelearningservices.models.ServiceInstanceResult]
    """

    _attribute_map = {
        'instances': {'key': 'instances', 'type': '{ServiceInstanceResult}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword instances: Dictionary of :code:`<ServiceInstanceResult>`.
        :paramtype instances: dict[str,
         ~azure.mgmt.machinelearningservices.models.ServiceInstanceResult]
        """
        super(RunServiceInstances, self).__init__(**kwargs)
        self.instances = kwargs.get('instances', None)


class RunStatusSpans(msrest.serialization.Model):
    """RunStatusSpans.

    :ivar spans:
    :vartype spans: list[~azure.mgmt.machinelearningservices.models.SpanDefinition1]
    """

    _attribute_map = {
        'spans': {'key': 'spans', 'type': '[SpanDefinition1]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword spans:
        :paramtype spans: list[~azure.mgmt.machinelearningservices.models.SpanDefinition1]
        """
        super(RunStatusSpans, self).__init__(**kwargs)
        self.spans = kwargs.get('spans', None)


class RunTypeV2(msrest.serialization.Model):
    """RunTypeV2.

    :ivar orchestrator:
    :vartype orchestrator: str
    :ivar traits:
    :vartype traits: list[str]
    :ivar attribution:
    :vartype attribution: str
    :ivar compute_type:
    :vartype compute_type: str
    """

    _validation = {
        'traits': {'unique': True},
    }

    _attribute_map = {
        'orchestrator': {'key': 'orchestrator', 'type': 'str'},
        'traits': {'key': 'traits', 'type': '[str]'},
        'attribution': {'key': 'attribution', 'type': 'str'},
        'compute_type': {'key': 'computeType', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword orchestrator:
        :paramtype orchestrator: str
        :keyword traits:
        :paramtype traits: list[str]
        :keyword attribution:
        :paramtype attribution: str
        :keyword compute_type:
        :paramtype compute_type: str
        """
        super(RunTypeV2, self).__init__(**kwargs)
        self.orchestrator = kwargs.get('orchestrator', None)
        self.traits = kwargs.get('traits', None)
        self.attribution = kwargs.get('attribution', None)
        self.compute_type = kwargs.get('compute_type', None)


class ServiceInstance(msrest.serialization.Model):
    """ServiceInstance.

    :ivar is_single_node:
    :vartype is_single_node: bool
    :ivar error_message:
    :vartype error_message: str
    :ivar port:
    :vartype port: int
    :ivar status:
    :vartype status: str
    :ivar error: The error response.
    :vartype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
    :ivar properties: Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    """

    _attribute_map = {
        'is_single_node': {'key': 'isSingleNode', 'type': 'bool'},
        'error_message': {'key': 'errorMessage', 'type': 'str'},
        'port': {'key': 'port', 'type': 'int'},
        'status': {'key': 'status', 'type': 'str'},
        'error': {'key': 'error', 'type': 'ErrorResponse'},
        'properties': {'key': 'properties', 'type': '{str}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword is_single_node:
        :paramtype is_single_node: bool
        :keyword error_message:
        :paramtype error_message: str
        :keyword port:
        :paramtype port: int
        :keyword status:
        :paramtype status: str
        :keyword error: The error response.
        :paramtype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
        :keyword properties: Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        """
        super(ServiceInstance, self).__init__(**kwargs)
        self.is_single_node = kwargs.get('is_single_node', None)
        self.error_message = kwargs.get('error_message', None)
        self.port = kwargs.get('port', None)
        self.status = kwargs.get('status', None)
        self.error = kwargs.get('error', None)
        self.properties = kwargs.get('properties', None)


class ServiceInstanceResult(msrest.serialization.Model):
    """ServiceInstanceResult.

    :ivar type:
    :vartype type: str
    :ivar port:
    :vartype port: int
    :ivar status:
    :vartype status: str
    :ivar error: The error response.
    :vartype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
    :ivar endpoint:
    :vartype endpoint: str
    :ivar properties: Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    """

    _attribute_map = {
        'type': {'key': 'type', 'type': 'str'},
        'port': {'key': 'port', 'type': 'int'},
        'status': {'key': 'status', 'type': 'str'},
        'error': {'key': 'error', 'type': 'ErrorResponse'},
        'endpoint': {'key': 'endpoint', 'type': 'str'},
        'properties': {'key': 'properties', 'type': '{str}'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword type:
        :paramtype type: str
        :keyword port:
        :paramtype port: int
        :keyword status:
        :paramtype status: str
        :keyword error: The error response.
        :paramtype error: ~azure.mgmt.machinelearningservices.models.ErrorResponse
        :keyword endpoint:
        :paramtype endpoint: str
        :keyword properties: Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        """
        super(ServiceInstanceResult, self).__init__(**kwargs)
        self.type = kwargs.get('type', None)
        self.port = kwargs.get('port', None)
        self.status = kwargs.get('status', None)
        self.error = kwargs.get('error', None)
        self.endpoint = kwargs.get('endpoint', None)
        self.properties = kwargs.get('properties', None)


class SpanContext(msrest.serialization.Model):
    """SpanContext.

    :ivar trace_id: Gets the TraceId associated with this
     Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.
     TODO: In actual spec, it is ActivityTraceId type. But that causes problems in
     serialization/deserialization.
    :vartype trace_id: str
    :ivar span_id: Gets the SpanId associated with this
     Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.
     TODO: In actual spec, it is ActivitySpanId type. But that causes problems in
     serialization/deserialization.
    :vartype span_id: str
    :ivar is_remote: Gets a value indicating whether this
     Microsoft.MachineLearning.RunHistory.Contracts.SpanContext
     was propagated from a remote parent.
    :vartype is_remote: bool
    :ivar is_valid: Gets a value indicating whether this
     Microsoft.MachineLearning.RunHistory.Contracts.SpanContext is valid.
    :vartype is_valid: bool
    :ivar tracestate: Gets the
     Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.Tracestate associated with this
     Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.
    :vartype tracestate: list[~azure.mgmt.machinelearningservices.models.KeyValuePairString]
    """

    _attribute_map = {
        'trace_id': {'key': 'traceId', 'type': 'str'},
        'span_id': {'key': 'spanId', 'type': 'str'},
        'is_remote': {'key': 'isRemote', 'type': 'bool'},
        'is_valid': {'key': 'isValid', 'type': 'bool'},
        'tracestate': {'key': 'tracestate', 'type': '[KeyValuePairString]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword trace_id: Gets the TraceId associated with this
         Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.
         TODO: In actual spec, it is ActivityTraceId type. But that causes problems in
         serialization/deserialization.
        :paramtype trace_id: str
        :keyword span_id: Gets the SpanId associated with this
         Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.
         TODO: In actual spec, it is ActivitySpanId type. But that causes problems in
         serialization/deserialization.
        :paramtype span_id: str
        :keyword is_remote: Gets a value indicating whether this
         Microsoft.MachineLearning.RunHistory.Contracts.SpanContext
         was propagated from a remote parent.
        :paramtype is_remote: bool
        :keyword is_valid: Gets a value indicating whether this
         Microsoft.MachineLearning.RunHistory.Contracts.SpanContext is valid.
        :paramtype is_valid: bool
        :keyword tracestate: Gets the
         Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.Tracestate associated with this
         Microsoft.MachineLearning.RunHistory.Contracts.SpanContext.
        :paramtype tracestate: list[~azure.mgmt.machinelearningservices.models.KeyValuePairString]
        """
        super(SpanContext, self).__init__(**kwargs)
        self.trace_id = kwargs.get('trace_id', None)
        self.span_id = kwargs.get('span_id', None)
        self.is_remote = kwargs.get('is_remote', None)
        self.is_valid = kwargs.get('is_valid', None)
        self.tracestate = kwargs.get('tracestate', None)


class SpanDefinition1(msrest.serialization.Model):
    """Most of the code in this class is vendored from here.
https://github.com/open-telemetry/opentelemetry-dotnet/blob/master/src/OpenTelemetry/Trace/Export/SpanData.cs
SpanData on that github link is readonly, we can't set properties on it after creation. So, just vendoring the Span
contract.
TStatus is the status enum. For runs, it is RunStatus
This is the link for span spec https://github.com/open-telemetry/opentelemetry-specification/blob/master/specification/overview.md#span.

    :ivar context:
    :vartype context: ~azure.mgmt.machinelearningservices.models.SpanContext
    :ivar name: Gets span name.
    :vartype name: str
    :ivar status: Gets span status.
     OpenTelemetry sets it to
     https://github.com/open-telemetry/opentelemetry-dotnet/blob/master/src/OpenTelemetry.Api/Trace/Status.cs
     That status enums are not very meaningful to us, so we customize this. Possible values
     include: "NotStarted", "Unapproved", "Pausing", "Paused", "Starting", "Preparing", "Queued",
     "Running", "Finalizing", "CancelRequested", "Completed", "Failed", "Canceled".
    :vartype status: str or ~azure.mgmt.machinelearningservices.models.RunStatus
    :ivar parent_span_id: Gets parent span id.
     TODO: In actual spec, it is ActivitySpanId type. But that causes problems in
     serialization/deserialization.
    :vartype parent_span_id: str
    :ivar attributes: Gets attributes.
    :vartype attributes: list[~azure.mgmt.machinelearningservices.models.KeyValuePairStringJToken]
    :ivar events: Gets events.
    :vartype events: list[~azure.mgmt.machinelearningservices.models.Event]
    :ivar links: Gets links.
    :vartype links: list[~azure.mgmt.machinelearningservices.models.Link]
    :ivar start_timestamp: Gets span start timestamp.
    :vartype start_timestamp: ~datetime.datetime
    :ivar end_timestamp: Gets span end timestamp.
    :vartype end_timestamp: ~datetime.datetime
    """

    _attribute_map = {
        'context': {'key': 'context', 'type': 'SpanContext'},
        'name': {'key': 'name', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'parent_span_id': {'key': 'parentSpanId', 'type': 'str'},
        'attributes': {'key': 'attributes', 'type': '[KeyValuePairStringJToken]'},
        'events': {'key': 'events', 'type': '[Event]'},
        'links': {'key': 'links', 'type': '[Link]'},
        'start_timestamp': {'key': 'startTimestamp', 'type': 'iso-8601'},
        'end_timestamp': {'key': 'endTimestamp', 'type': 'iso-8601'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword context:
        :paramtype context: ~azure.mgmt.machinelearningservices.models.SpanContext
        :keyword name: Gets span name.
        :paramtype name: str
        :keyword status: Gets span status.
         OpenTelemetry sets it to
         https://github.com/open-telemetry/opentelemetry-dotnet/blob/master/src/OpenTelemetry.Api/Trace/Status.cs
         That status enums are not very meaningful to us, so we customize this. Possible values
         include: "NotStarted", "Unapproved", "Pausing", "Paused", "Starting", "Preparing", "Queued",
         "Running", "Finalizing", "CancelRequested", "Completed", "Failed", "Canceled".
        :paramtype status: str or ~azure.mgmt.machinelearningservices.models.RunStatus
        :keyword parent_span_id: Gets parent span id.
         TODO: In actual spec, it is ActivitySpanId type. But that causes problems in
         serialization/deserialization.
        :paramtype parent_span_id: str
        :keyword attributes: Gets attributes.
        :paramtype attributes:
         list[~azure.mgmt.machinelearningservices.models.KeyValuePairStringJToken]
        :keyword events: Gets events.
        :paramtype events: list[~azure.mgmt.machinelearningservices.models.Event]
        :keyword links: Gets links.
        :paramtype links: list[~azure.mgmt.machinelearningservices.models.Link]
        :keyword start_timestamp: Gets span start timestamp.
        :paramtype start_timestamp: ~datetime.datetime
        :keyword end_timestamp: Gets span end timestamp.
        :paramtype end_timestamp: ~datetime.datetime
        """
        super(SpanDefinition1, self).__init__(**kwargs)
        self.context = kwargs.get('context', None)
        self.name = kwargs.get('name', None)
        self.status = kwargs.get('status', None)
        self.parent_span_id = kwargs.get('parent_span_id', None)
        self.attributes = kwargs.get('attributes', None)
        self.events = kwargs.get('events', None)
        self.links = kwargs.get('links', None)
        self.start_timestamp = kwargs.get('start_timestamp', None)
        self.end_timestamp = kwargs.get('end_timestamp', None)


class SqlDataPath(msrest.serialization.Model):
    """SqlDataPath.

    :ivar sql_table_name:
    :vartype sql_table_name: str
    :ivar sql_query:
    :vartype sql_query: str
    :ivar sql_stored_procedure_name:
    :vartype sql_stored_procedure_name: str
    :ivar sql_stored_procedure_params:
    :vartype sql_stored_procedure_params:
     list[~azure.mgmt.machinelearningservices.models.StoredProcedureParameter]
    """

    _attribute_map = {
        'sql_table_name': {'key': 'sqlTableName', 'type': 'str'},
        'sql_query': {'key': 'sqlQuery', 'type': 'str'},
        'sql_stored_procedure_name': {'key': 'sqlStoredProcedureName', 'type': 'str'},
        'sql_stored_procedure_params': {'key': 'sqlStoredProcedureParams', 'type': '[StoredProcedureParameter]'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword sql_table_name:
        :paramtype sql_table_name: str
        :keyword sql_query:
        :paramtype sql_query: str
        :keyword sql_stored_procedure_name:
        :paramtype sql_stored_procedure_name: str
        :keyword sql_stored_procedure_params:
        :paramtype sql_stored_procedure_params:
         list[~azure.mgmt.machinelearningservices.models.StoredProcedureParameter]
        """
        super(SqlDataPath, self).__init__(**kwargs)
        self.sql_table_name = kwargs.get('sql_table_name', None)
        self.sql_query = kwargs.get('sql_query', None)
        self.sql_stored_procedure_name = kwargs.get('sql_stored_procedure_name', None)
        self.sql_stored_procedure_params = kwargs.get('sql_stored_procedure_params', None)


class StoredProcedureParameter(msrest.serialization.Model):
    """StoredProcedureParameter.

    :ivar name:
    :vartype name: str
    :ivar value:
    :vartype value: str
    :ivar type: Possible values include: "String", "Int", "Decimal", "Guid", "Boolean", "Date".
    :vartype type: str or ~azure.mgmt.machinelearningservices.models.StoredProcedureParameterType
    """

    _attribute_map = {
        'name': {'key': 'name', 'type': 'str'},
        'value': {'key': 'value', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword name:
        :paramtype name: str
        :keyword value:
        :paramtype value: str
        :keyword type: Possible values include: "String", "Int", "Decimal", "Guid", "Boolean", "Date".
        :paramtype type: str or ~azure.mgmt.machinelearningservices.models.StoredProcedureParameterType
        """
        super(StoredProcedureParameter, self).__init__(**kwargs)
        self.name = kwargs.get('name', None)
        self.value = kwargs.get('value', None)
        self.type = kwargs.get('type', None)


class TypedAssetReference(msrest.serialization.Model):
    """TypedAssetReference.

    :ivar asset_id:
    :vartype asset_id: str
    :ivar type:
    :vartype type: str
    """

    _attribute_map = {
        'asset_id': {'key': 'assetId', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword asset_id:
        :paramtype asset_id: str
        :keyword type:
        :paramtype type: str
        """
        super(TypedAssetReference, self).__init__(**kwargs)
        self.asset_id = kwargs.get('asset_id', None)
        self.type = kwargs.get('type', None)


class User(msrest.serialization.Model):
    """User.

    :ivar user_object_id: A user or service principal's object ID.
     This is EUPI and may only be logged to warm path telemetry.
    :vartype user_object_id: str
    :ivar user_pu_id: A user or service principal's PuID.
     This is PII and should never be logged.
    :vartype user_pu_id: str
    :ivar user_idp: A user identity provider. Eg live.com
     This is PII and should never be logged.
    :vartype user_idp: str
    :ivar user_alt_sec_id: A user alternate sec id. This represents the user in a different
     identity provider system Eg.1:live.com:puid
     This is PII and should never be logged.
    :vartype user_alt_sec_id: str
    :ivar user_iss: The issuer which issed the token for this user.
     This is PII and should never be logged.
    :vartype user_iss: str
    :ivar user_tenant_id: A user or service principal's tenant ID.
    :vartype user_tenant_id: str
    :ivar user_name: A user's full name or a service principal's app ID.
     This is PII and should never be logged.
    :vartype user_name: str
    :ivar upn: A user's Principal name (upn)
     This is PII andshould never be logged.
    :vartype upn: str
    """

    _attribute_map = {
        'user_object_id': {'key': 'userObjectId', 'type': 'str'},
        'user_pu_id': {'key': 'userPuId', 'type': 'str'},
        'user_idp': {'key': 'userIdp', 'type': 'str'},
        'user_alt_sec_id': {'key': 'userAltSecId', 'type': 'str'},
        'user_iss': {'key': 'userIss', 'type': 'str'},
        'user_tenant_id': {'key': 'userTenantId', 'type': 'str'},
        'user_name': {'key': 'userName', 'type': 'str'},
        'upn': {'key': 'upn', 'type': 'str'},
    }

    def __init__(
        self,
        **kwargs
    ):
        """
        :keyword user_object_id: A user or service principal's object ID.
         This is EUPI and may only be logged to warm path telemetry.
        :paramtype user_object_id: str
        :keyword user_pu_id: A user or service principal's PuID.
         This is PII and should never be logged.
        :paramtype user_pu_id: str
        :keyword user_idp: A user identity provider. Eg live.com
         This is PII and should never be logged.
        :paramtype user_idp: str
        :keyword user_alt_sec_id: A user alternate sec id. This represents the user in a different
         identity provider system Eg.1:live.com:puid
         This is PII and should never be logged.
        :paramtype user_alt_sec_id: str
        :keyword user_iss: The issuer which issed the token for this user.
         This is PII and should never be logged.
        :paramtype user_iss: str
        :keyword user_tenant_id: A user or service principal's tenant ID.
        :paramtype user_tenant_id: str
        :keyword user_name: A user's full name or a service principal's app ID.
         This is PII and should never be logged.
        :paramtype user_name: str
        :keyword upn: A user's Principal name (upn)
         This is PII andshould never be logged.
        :paramtype upn: str
        """
        super(User, self).__init__(**kwargs)
        self.user_object_id = kwargs.get('user_object_id', None)
        self.user_pu_id = kwargs.get('user_pu_id', None)
        self.user_idp = kwargs.get('user_idp', None)
        self.user_alt_sec_id = kwargs.get('user_alt_sec_id', None)
        self.user_iss = kwargs.get('user_iss', None)
        self.user_tenant_id = kwargs.get('user_tenant_id', None)
        self.user_name = kwargs.get('user_name', None)
        self.upn = kwargs.get('upn', None)
