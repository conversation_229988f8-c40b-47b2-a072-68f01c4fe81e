# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
from typing import Any, AsyncIterable, Callable, Dict, Optional, TypeVar, Union

from azure.core.async_paging import AsyncItemPaged, AsyncList
from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.pipeline.transport import As<PERSON><PERSON>ttp<PERSON><PERSON>ponse, HttpRequest
from azure.core.polling import Async<PERSON><PERSON><PERSON><PERSON>, AsyncNoPolling, AsyncPollingMethod
from azure.mgmt.core.exceptions import ARMErrorFormat
from azure.mgmt.core.polling.async_arm_polling import AsyncARMPolling

from ... import models

T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, AsyncHttpResponse], T, Dict[str, Any]], Any]]


class ResourcesOperations:
    """ResourcesOperations async operations.

    You should not instantiate this class directly. Instead, you should create a Client instance that
    instantiates it for you and attaches it as an attribute.

    :ivar models: Alias to model classes used in this operation group.
    :type models: ~azure.mgmt.resource.resources.v2020_06_01.models
    :param client: Client for service requests.
    :param config: Configuration of service client.
    :param serializer: An object model serializer.
    :param deserializer: An object model deserializer.
    """

    models = models

    def __init__(self, client, config, serializer, deserializer) -> None:
        self._client = client
        self._serialize = serializer
        self._deserialize = deserializer
        self._config = config

    def list_by_resource_group(
        self,
        resource_group_name: str,
        filter: Optional[str] = None,
        expand: Optional[str] = None,
        top: Optional[int] = None,
        **kwargs
    ) -> AsyncIterable["models.ResourceListResult"]:
        """Get all the resources for a resource group.

        :param resource_group_name: The resource group with the resources to get.
        :type resource_group_name: str
        :param filter: The filter to apply on the operation.:code:`<br>`:code:`<br>`The properties you
         can use for eq (equals) or ne (not equals) are: location, resourceType, name, resourceGroup,
         identity, identity/principalId, plan, plan/publisher, plan/product, plan/name, plan/version,
         and plan/promotionCode.:code:`<br>`:code:`<br>`For example, to filter by a resource type, use:
         $filter=resourceType eq 'Microsoft.Network/virtualNetworks':code:`<br>`:code:`<br>`You can use
         substringof(value, property) in the filter. The properties you can use for substring are: name
         and resourceGroup.:code:`<br>`:code:`<br>`For example, to get all resources with 'demo'
         anywhere in the name, use: $filter=substringof('demo', name):code:`<br>`:code:`<br>`You can
         link more than one substringof together by adding and/or operators.:code:`<br>`:code:`<br>`You
         can filter by tag names and values. For example, to filter for a tag name and value, use
         $filter=tagName eq 'tag1' and tagValue eq 'Value1'. When you filter by a tag name and value,
         the tags for each resource are not returned in the results.:code:`<br>`:code:`<br>`You can use
         some properties together when filtering. The combinations you can use are: substringof and/or
         resourceType, plan and plan/publisher and plan/name, identity and identity/principalId.
        :type filter: str
        :param expand: Comma-separated list of additional properties to be included in the response.
         Valid values include ``createdTime``\ , ``changedTime`` and ``provisioningState``. For example,
         ``$expand=createdTime,changedTime``.
        :type expand: str
        :param top: The number of results to return. If null is passed, returns all resources.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either ResourceListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.ResourceListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.ResourceListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list_by_resource_group.metadata["url"]  # type: ignore
                path_format_arguments = {
                    "resourceGroupName": self._serialize.url(
                        "resource_group_name",
                        resource_group_name,
                        "str",
                        max_length=90,
                        min_length=1,
                        pattern=r"^[-\w\._\(\)]+$",
                    ),
                    "subscriptionId": self._serialize.url(
                        "self._config.subscription_id", self._config.subscription_id, "str"
                    ),
                }
                url = self._client.format_url(url, **path_format_arguments)
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if expand is not None:
                    query_parameters["$expand"] = self._serialize.query("expand", expand, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("ResourceListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_by_resource_group.metadata = {"url": "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/resources"}  # type: ignore

    async def _move_resources_initial(
        self, source_resource_group_name: str, parameters: "models.ResourcesMoveInfo", **kwargs
    ) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._move_resources_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "sourceResourceGroupName": self._serialize.url(
                "source_resource_group_name",
                source_resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ResourcesMoveInfo")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _move_resources_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourceGroups/{sourceResourceGroupName}/moveResources"}  # type: ignore

    async def begin_move_resources(
        self, source_resource_group_name: str, parameters: "models.ResourcesMoveInfo", **kwargs
    ) -> AsyncLROPoller[None]:
        """Moves resources from one resource group to another resource group.

        The resources to move must be in the same source resource group. The target resource group may
        be in a different subscription. When moving resources, both the source group and the target
        group are locked for the duration of the operation. Write and delete operations are blocked on
        the groups until the move completes.

        :param source_resource_group_name: The name of the resource group containing the resources to
         move.
        :type source_resource_group_name: str
        :param parameters: Parameters for moving resources.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ResourcesMoveInfo
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._move_resources_initial(
                source_resource_group_name=source_resource_group_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_move_resources.metadata = {"url": "/subscriptions/{subscriptionId}/resourceGroups/{sourceResourceGroupName}/moveResources"}  # type: ignore

    async def _validate_move_resources_initial(
        self, source_resource_group_name: str, parameters: "models.ResourcesMoveInfo", **kwargs
    ) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._validate_move_resources_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "sourceResourceGroupName": self._serialize.url(
                "source_resource_group_name",
                source_resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ResourcesMoveInfo")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _validate_move_resources_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourceGroups/{sourceResourceGroupName}/validateMoveResources"}  # type: ignore

    async def begin_validate_move_resources(
        self, source_resource_group_name: str, parameters: "models.ResourcesMoveInfo", **kwargs
    ) -> AsyncLROPoller[None]:
        """Validates whether resources can be moved from one resource group to another resource group.

        This operation checks whether the specified resources can be moved to the target. The resources
        to move must be in the same source resource group. The target resource group may be in a
        different subscription. If validation succeeds, it returns HTTP response code 204 (no content).
        If validation fails, it returns HTTP response code 409 (Conflict) with an error message.
        Retrieve the URL in the Location header value to check the result of the long-running
        operation.

        :param source_resource_group_name: The name of the resource group containing the resources to
         validate for move.
        :type source_resource_group_name: str
        :param parameters: Parameters for moving resources.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ResourcesMoveInfo
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._validate_move_resources_initial(
                source_resource_group_name=source_resource_group_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_validate_move_resources.metadata = {"url": "/subscriptions/{subscriptionId}/resourceGroups/{sourceResourceGroupName}/validateMoveResources"}  # type: ignore

    def list(
        self, filter: Optional[str] = None, expand: Optional[str] = None, top: Optional[int] = None, **kwargs
    ) -> AsyncIterable["models.ResourceListResult"]:
        """Get all the resources in a subscription.

        :param filter: The filter to apply on the operation.:code:`<br>`:code:`<br>`The properties you
         can use for eq (equals) or ne (not equals) are: location, resourceType, name, resourceGroup,
         identity, identity/principalId, plan, plan/publisher, plan/product, plan/name, plan/version,
         and plan/promotionCode.:code:`<br>`:code:`<br>`For example, to filter by a resource type, use:
         $filter=resourceType eq 'Microsoft.Network/virtualNetworks':code:`<br>`:code:`<br>`You can use
         substringof(value, property) in the filter. The properties you can use for substring are: name
         and resourceGroup.:code:`<br>`:code:`<br>`For example, to get all resources with 'demo'
         anywhere in the name, use: $filter=substringof('demo', name):code:`<br>`:code:`<br>`You can
         link more than one substringof together by adding and/or operators.:code:`<br>`:code:`<br>`You
         can filter by tag names and values. For example, to filter for a tag name and value, use
         $filter=tagName eq 'tag1' and tagValue eq 'Value1'. When you filter by a tag name and value,
         the tags for each resource are not returned in the results.:code:`<br>`:code:`<br>`You can use
         some properties together when filtering. The combinations you can use are: substringof and/or
         resourceType, plan and plan/publisher and plan/name, identity and identity/principalId.
        :type filter: str
        :param expand: Comma-separated list of additional properties to be included in the response.
         Valid values include ``createdTime``\ , ``changedTime`` and ``provisioningState``. For example,
         ``$expand=createdTime,changedTime``.
        :type expand: str
        :param top: The number of results to return. If null is passed, returns all resource groups.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either ResourceListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.ResourceListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.ResourceListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list.metadata["url"]  # type: ignore
                path_format_arguments = {
                    "subscriptionId": self._serialize.url(
                        "self._config.subscription_id", self._config.subscription_id, "str"
                    ),
                }
                url = self._client.format_url(url, **path_format_arguments)
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if expand is not None:
                    query_parameters["$expand"] = self._serialize.query("expand", expand, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("ResourceListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list.metadata = {"url": "/subscriptions/{subscriptionId}/resources"}  # type: ignore

    async def check_existence(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        **kwargs
    ) -> bool:
        """Checks whether a resource exists.

        :param resource_group_name: The name of the resource group containing the resource to check.
         The name is case insensitive.
        :type resource_group_name: str
        :param resource_provider_namespace: The resource provider of the resource to check.
        :type resource_provider_namespace: str
        :param parent_resource_path: The parent resource identity.
        :type parent_resource_path: str
        :param resource_type: The resource type.
        :type resource_type: str
        :param resource_name: The name of the resource to check whether it exists.
        :type resource_name: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        accept = "application/json"

        # Construct URL
        url = self.check_existence.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "resourceProviderNamespace": self._serialize.url(
                "resource_provider_namespace", resource_provider_namespace, "str"
            ),
            "parentResourcePath": self._serialize.url(
                "parent_resource_path", parent_resource_path, "str", skip_quote=True
            ),
            "resourceType": self._serialize.url("resource_type", resource_type, "str", skip_quote=True),
            "resourceName": self._serialize.url("resource_name", resource_name, "str"),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def _delete_initial(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        **kwargs
    ) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        accept = "application/json"

        # Construct URL
        url = self._delete_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "resourceProviderNamespace": self._serialize.url(
                "resource_provider_namespace", resource_provider_namespace, "str"
            ),
            "parentResourcePath": self._serialize.url(
                "parent_resource_path", parent_resource_path, "str", skip_quote=True
            ),
            "resourceType": self._serialize.url("resource_type", resource_type, "str", skip_quote=True),
            "resourceName": self._serialize.url("resource_name", resource_name, "str"),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def begin_delete(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        **kwargs
    ) -> AsyncLROPoller[None]:
        """Deletes a resource.

        :param resource_group_name: The name of the resource group that contains the resource to
         delete. The name is case insensitive.
        :type resource_group_name: str
        :param resource_provider_namespace: The namespace of the resource provider.
        :type resource_provider_namespace: str
        :param parent_resource_path: The parent resource identity.
        :type parent_resource_path: str
        :param resource_type: The resource type.
        :type resource_type: str
        :param resource_name: The name of the resource to delete.
        :type resource_name: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_initial(
                resource_group_name=resource_group_name,
                resource_provider_namespace=resource_provider_namespace,
                parent_resource_path=parent_resource_path,
                resource_type=resource_type,
                resource_name=resource_name,
                api_version=api_version,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def _create_or_update_initial(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        parameters: "models.GenericResource",
        **kwargs
    ) -> Optional["models.GenericResource"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.GenericResource"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "resourceProviderNamespace": self._serialize.url(
                "resource_provider_namespace", resource_provider_namespace, "str"
            ),
            "parentResourcePath": self._serialize.url(
                "parent_resource_path", parent_resource_path, "str", skip_quote=True
            ),
            "resourceType": self._serialize.url("resource_type", resource_type, "str", skip_quote=True),
            "resourceName": self._serialize.url("resource_name", resource_name, "str"),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "GenericResource")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("GenericResource", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("GenericResource", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def begin_create_or_update(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        parameters: "models.GenericResource",
        **kwargs
    ) -> AsyncLROPoller["models.GenericResource"]:
        """Creates a resource.

        :param resource_group_name: The name of the resource group for the resource. The name is case
         insensitive.
        :type resource_group_name: str
        :param resource_provider_namespace: The namespace of the resource provider.
        :type resource_provider_namespace: str
        :param parent_resource_path: The parent resource identity.
        :type parent_resource_path: str
        :param resource_type: The resource type of the resource to create.
        :type resource_type: str
        :param resource_name: The name of the resource to create.
        :type resource_name: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :param parameters: Parameters for creating or updating the resource.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either GenericResource or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.GenericResource"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_initial(
                resource_group_name=resource_group_name,
                resource_provider_namespace=resource_provider_namespace,
                parent_resource_path=parent_resource_path,
                resource_type=resource_type,
                resource_name=resource_name,
                api_version=api_version,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("GenericResource", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def _update_initial(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        parameters: "models.GenericResource",
        **kwargs
    ) -> Optional["models.GenericResource"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.GenericResource"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._update_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "resourceProviderNamespace": self._serialize.url(
                "resource_provider_namespace", resource_provider_namespace, "str"
            ),
            "parentResourcePath": self._serialize.url(
                "parent_resource_path", parent_resource_path, "str", skip_quote=True
            ),
            "resourceType": self._serialize.url("resource_type", resource_type, "str", skip_quote=True),
            "resourceName": self._serialize.url("resource_name", resource_name, "str"),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "GenericResource")
        body_content_kwargs["content"] = body_content
        request = self._client.patch(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("GenericResource", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _update_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def begin_update(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        parameters: "models.GenericResource",
        **kwargs
    ) -> AsyncLROPoller["models.GenericResource"]:
        """Updates a resource.

        :param resource_group_name: The name of the resource group for the resource. The name is case
         insensitive.
        :type resource_group_name: str
        :param resource_provider_namespace: The namespace of the resource provider.
        :type resource_provider_namespace: str
        :param parent_resource_path: The parent resource identity.
        :type parent_resource_path: str
        :param resource_type: The resource type of the resource to update.
        :type resource_type: str
        :param resource_name: The name of the resource to update.
        :type resource_name: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :param parameters: Parameters for updating the resource.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either GenericResource or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.GenericResource"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._update_initial(
                resource_group_name=resource_group_name,
                resource_provider_namespace=resource_provider_namespace,
                parent_resource_path=parent_resource_path,
                resource_type=resource_type,
                resource_name=resource_name,
                api_version=api_version,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("GenericResource", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_update.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def get(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        parent_resource_path: str,
        resource_type: str,
        resource_name: str,
        api_version: str,
        **kwargs
    ) -> "models.GenericResource":
        """Gets a resource.

        :param resource_group_name: The name of the resource group containing the resource to get. The
         name is case insensitive.
        :type resource_group_name: str
        :param resource_provider_namespace: The namespace of the resource provider.
        :type resource_provider_namespace: str
        :param parent_resource_path: The parent resource identity.
        :type parent_resource_path: str
        :param resource_type: The resource type of the resource.
        :type resource_type: str
        :param resource_name: The name of the resource to get.
        :type resource_name: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: GenericResource, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.GenericResource"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        accept = "application/json"

        # Construct URL
        url = self.get.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "resourceProviderNamespace": self._serialize.url(
                "resource_provider_namespace", resource_provider_namespace, "str"
            ),
            "parentResourcePath": self._serialize.url(
                "parent_resource_path", parent_resource_path, "str", skip_quote=True
            ),
            "resourceType": self._serialize.url("resource_type", resource_type, "str", skip_quote=True),
            "resourceName": self._serialize.url("resource_name", resource_name, "str"),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("GenericResource", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{parentResourcePath}/{resourceType}/{resourceName}"}  # type: ignore

    async def check_existence_by_id(self, resource_id: str, api_version: str, **kwargs) -> bool:
        """Checks by ID whether a resource exists.

        :param resource_id: The fully qualified ID of the resource, including the resource name and
         resource type. Use the format, /subscriptions/{guid}/resourceGroups/{resource-group-
         name}/{resource-provider-namespace}/{resource-type}/{resource-name}.
        :type resource_id: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        accept = "application/json"

        # Construct URL
        url = self.check_existence_by_id.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceId": self._serialize.url("resource_id", resource_id, "str", skip_quote=True),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence_by_id.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def _delete_by_id_initial(self, resource_id: str, api_version: str, **kwargs) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        accept = "application/json"

        # Construct URL
        url = self._delete_by_id_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceId": self._serialize.url("resource_id", resource_id, "str", skip_quote=True),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_by_id_initial.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def begin_delete_by_id(self, resource_id: str, api_version: str, **kwargs) -> AsyncLROPoller[None]:
        """Deletes a resource by ID.

        :param resource_id: The fully qualified ID of the resource, including the resource name and
         resource type. Use the format, /subscriptions/{guid}/resourceGroups/{resource-group-
         name}/{resource-provider-namespace}/{resource-type}/{resource-name}.
        :type resource_id: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_by_id_initial(
                resource_id=resource_id, api_version=api_version, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete_by_id.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def _create_or_update_by_id_initial(
        self, resource_id: str, api_version: str, parameters: "models.GenericResource", **kwargs
    ) -> Optional["models.GenericResource"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.GenericResource"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_by_id_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceId": self._serialize.url("resource_id", resource_id, "str", skip_quote=True),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "GenericResource")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("GenericResource", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("GenericResource", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_by_id_initial.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def begin_create_or_update_by_id(
        self, resource_id: str, api_version: str, parameters: "models.GenericResource", **kwargs
    ) -> AsyncLROPoller["models.GenericResource"]:
        """Create a resource by ID.

        :param resource_id: The fully qualified ID of the resource, including the resource name and
         resource type. Use the format, /subscriptions/{guid}/resourceGroups/{resource-group-
         name}/{resource-provider-namespace}/{resource-type}/{resource-name}.
        :type resource_id: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :param parameters: Create or update resource parameters.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either GenericResource or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.GenericResource"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_by_id_initial(
                resource_id=resource_id, api_version=api_version, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("GenericResource", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update_by_id.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def _update_by_id_initial(
        self, resource_id: str, api_version: str, parameters: "models.GenericResource", **kwargs
    ) -> Optional["models.GenericResource"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.GenericResource"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._update_by_id_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceId": self._serialize.url("resource_id", resource_id, "str", skip_quote=True),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "GenericResource")
        body_content_kwargs["content"] = body_content
        request = self._client.patch(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("GenericResource", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _update_by_id_initial.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def begin_update_by_id(
        self, resource_id: str, api_version: str, parameters: "models.GenericResource", **kwargs
    ) -> AsyncLROPoller["models.GenericResource"]:
        """Updates a resource by ID.

        :param resource_id: The fully qualified ID of the resource, including the resource name and
         resource type. Use the format, /subscriptions/{guid}/resourceGroups/{resource-group-
         name}/{resource-provider-namespace}/{resource-type}/{resource-name}.
        :type resource_id: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :param parameters: Update resource parameters.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either GenericResource or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.GenericResource"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._update_by_id_initial(
                resource_id=resource_id, api_version=api_version, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("GenericResource", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_update_by_id.metadata = {"url": "/{resourceId}"}  # type: ignore

    async def get_by_id(self, resource_id: str, api_version: str, **kwargs) -> "models.GenericResource":
        """Gets a resource by ID.

        :param resource_id: The fully qualified ID of the resource, including the resource name and
         resource type. Use the format, /subscriptions/{guid}/resourceGroups/{resource-group-
         name}/{resource-provider-namespace}/{resource-type}/{resource-name}.
        :type resource_id: str
        :param api_version: The API version to use for the operation.
        :type api_version: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: GenericResource, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.GenericResource
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.GenericResource"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        accept = "application/json"

        # Construct URL
        url = self.get_by_id.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceId": self._serialize.url("resource_id", resource_id, "str", skip_quote=True),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("GenericResource", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get_by_id.metadata = {"url": "/{resourceId}"}  # type: ignore
