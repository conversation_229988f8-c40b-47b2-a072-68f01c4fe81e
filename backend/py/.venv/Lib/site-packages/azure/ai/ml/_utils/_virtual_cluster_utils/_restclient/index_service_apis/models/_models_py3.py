# coding=utf-8
# --------------------------------------------------------------------------
# Code generated by Microsoft (R) AutoRest Code Generator (autorest: 3.6.2, generator: @autorest/python@5.12.6)
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import datetime
from typing import Any, Dict, List, Optional, Union

import msrest.serialization

from ._index_service_apis_enums import *


class BatchEndpointAnnotations(msrest.serialization.Model):
    """BatchEndpointAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar scoring_uri: Required.
    :vartype scoring_uri: str
    :ivar location: Required.
    :vartype location: str
    :ivar last_modified_time: Required.
    :vartype last_modified_time: ~datetime.datetime
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "scoring_uri": {"required": True},
        "location": {"required": True},
        "last_modified_time": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "scoring_uri": {"key": "scoringUri", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "last_modified_time": {"key": "lastModifiedTime", "type": "iso-8601"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        scoring_uri: str,
        location: str,
        last_modified_time: datetime.datetime,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword scoring_uri: Required.
        :paramtype scoring_uri: str
        :keyword location: Required.
        :paramtype location: str
        :keyword last_modified_time: Required.
        :paramtype last_modified_time: ~datetime.datetime
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(BatchEndpointAnnotations, self).__init__(**kwargs)
        self.scoring_uri = scoring_uri
        self.location = location
        self.last_modified_time = last_modified_time
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class BatchEndpointProperties(msrest.serialization.Model):
    """BatchEndpointProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "properties": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "properties": {"key": "properties", "type": "{str}"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, properties: Dict[str, str], creation_context: "CreationContext", **kwargs):
        """
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(BatchEndpointProperties, self).__init__(**kwargs)
        self.properties = properties
        self.creation_context = creation_context


class BatchendpointsUnversionedEntitiesResponse(msrest.serialization.Model):
    """BatchendpointsUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.BatchendpointsUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[BatchendpointsUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["BatchendpointsUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.BatchendpointsUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(BatchendpointsUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class BatchendpointsUnversionedEntity(msrest.serialization.Model):
    """BatchendpointsUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.BatchendpointsUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.BatchEndpointAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.BatchEndpointProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "BatchEndpointAnnotations"},
        "properties": {"key": "properties", "type": "BatchEndpointProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "BatchendpointsUnversionedEntityKind"],
        annotations: "BatchEndpointAnnotations",
        properties: "BatchEndpointProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.BatchendpointsUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.BatchEndpointAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.BatchEndpointProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(BatchendpointsUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class Compute(msrest.serialization.Model):
    """Compute.

    All required parameters must be populated in order to send to Azure.

    :ivar target: Required.
    :vartype target: str
    :ivar target_type: Required.
    :vartype target_type: str
    :ivar vm_size: Required.
    :vartype vm_size: str
    :ivar instance_type: Required.
    :vartype instance_type: str
    :ivar instance_count: Required.
    :vartype instance_count: int
    :ivar gpu_count: Required.
    :vartype gpu_count: int
    :ivar priority: Required.
    :vartype priority: str
    :ivar region: Required.
    :vartype region: str
    :ivar arm_id: Required.
    :vartype arm_id: str
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    """

    _validation = {
        "target": {"required": True},
        "target_type": {"required": True},
        "vm_size": {"required": True},
        "instance_type": {"required": True},
        "instance_count": {"required": True},
        "gpu_count": {"required": True},
        "priority": {"required": True},
        "region": {"required": True},
        "arm_id": {"required": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "target": {"key": "target", "type": "str"},
        "target_type": {"key": "targetType", "type": "str"},
        "vm_size": {"key": "vmSize", "type": "str"},
        "instance_type": {"key": "instanceType", "type": "str"},
        "instance_count": {"key": "instanceCount", "type": "int"},
        "gpu_count": {"key": "gpuCount", "type": "int"},
        "priority": {"key": "priority", "type": "str"},
        "region": {"key": "region", "type": "str"},
        "arm_id": {"key": "armId", "type": "str"},
        "properties": {"key": "properties", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        target: str,
        target_type: str,
        vm_size: str,
        instance_type: str,
        instance_count: int,
        gpu_count: int,
        priority: str,
        region: str,
        arm_id: str,
        properties: Dict[str, str],
        **kwargs
    ):
        """
        :keyword target: Required.
        :paramtype target: str
        :keyword target_type: Required.
        :paramtype target_type: str
        :keyword vm_size: Required.
        :paramtype vm_size: str
        :keyword instance_type: Required.
        :paramtype instance_type: str
        :keyword instance_count: Required.
        :paramtype instance_count: int
        :keyword gpu_count: Required.
        :paramtype gpu_count: int
        :keyword priority: Required.
        :paramtype priority: str
        :keyword region: Required.
        :paramtype region: str
        :keyword arm_id: Required.
        :paramtype arm_id: str
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        """
        super(Compute, self).__init__(**kwargs)
        self.target = target
        self.target_type = target_type
        self.vm_size = vm_size
        self.instance_type = instance_type
        self.instance_count = instance_count
        self.gpu_count = gpu_count
        self.priority = priority
        self.region = region
        self.arm_id = arm_id
        self.properties = properties


class ComputeRequest(msrest.serialization.Model):
    """ComputeRequest.

    All required parameters must be populated in order to send to Azure.

    :ivar node_count: Required.
    :vartype node_count: int
    :ivar gpu_count: Required.
    :vartype gpu_count: int
    """

    _validation = {
        "node_count": {"required": True},
        "gpu_count": {"required": True},
    }

    _attribute_map = {
        "node_count": {"key": "nodeCount", "type": "int"},
        "gpu_count": {"key": "gpuCount", "type": "int"},
    }

    def __init__(self, *, node_count: int, gpu_count: int, **kwargs):
        """
        :keyword node_count: Required.
        :paramtype node_count: int
        :keyword gpu_count: Required.
        :paramtype gpu_count: int
        """
        super(ComputeRequest, self).__init__(**kwargs)
        self.node_count = node_count
        self.gpu_count = gpu_count


class ContainerResourceRequirements(msrest.serialization.Model):
    """ContainerResourceRequirements.

    All required parameters must be populated in order to send to Azure.

    :ivar cpu: Required.
    :vartype cpu: float
    :ivar cpu_limit: Required.
    :vartype cpu_limit: float
    :ivar memory_in_gb: Required.
    :vartype memory_in_gb: float
    :ivar memory_in_gb_limit: Required.
    :vartype memory_in_gb_limit: float
    :ivar gpu_enabled: Required.
    :vartype gpu_enabled: bool
    :ivar gpu: Required.
    :vartype gpu: int
    :ivar fpga: Required.
    :vartype fpga: int
    """

    _validation = {
        "cpu": {"required": True},
        "cpu_limit": {"required": True},
        "memory_in_gb": {"required": True},
        "memory_in_gb_limit": {"required": True},
        "gpu_enabled": {"required": True},
        "gpu": {"required": True},
        "fpga": {"required": True},
    }

    _attribute_map = {
        "cpu": {"key": "cpu", "type": "float"},
        "cpu_limit": {"key": "cpuLimit", "type": "float"},
        "memory_in_gb": {"key": "memoryInGB", "type": "float"},
        "memory_in_gb_limit": {"key": "memoryInGBLimit", "type": "float"},
        "gpu_enabled": {"key": "gpuEnabled", "type": "bool"},
        "gpu": {"key": "gpu", "type": "int"},
        "fpga": {"key": "fpga", "type": "int"},
    }

    def __init__(
        self,
        *,
        cpu: float,
        cpu_limit: float,
        memory_in_gb: float,
        memory_in_gb_limit: float,
        gpu_enabled: bool,
        gpu: int,
        fpga: int,
        **kwargs
    ):
        """
        :keyword cpu: Required.
        :paramtype cpu: float
        :keyword cpu_limit: Required.
        :paramtype cpu_limit: float
        :keyword memory_in_gb: Required.
        :paramtype memory_in_gb: float
        :keyword memory_in_gb_limit: Required.
        :paramtype memory_in_gb_limit: float
        :keyword gpu_enabled: Required.
        :paramtype gpu_enabled: bool
        :keyword gpu: Required.
        :paramtype gpu: int
        :keyword fpga: Required.
        :paramtype fpga: int
        """
        super(ContainerResourceRequirements, self).__init__(**kwargs)
        self.cpu = cpu
        self.cpu_limit = cpu_limit
        self.memory_in_gb = memory_in_gb
        self.memory_in_gb_limit = memory_in_gb_limit
        self.gpu_enabled = gpu_enabled
        self.gpu = gpu
        self.fpga = fpga


class ControlOutput(msrest.serialization.Model):
    """ControlOutput.

    :ivar name:
    :vartype name: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
    }

    def __init__(self, *, name: Optional[str] = None, **kwargs):
        """
        :keyword name:
        :paramtype name: str
        """
        super(ControlOutput, self).__init__(**kwargs)
        self.name = name


class CreatedBy(msrest.serialization.Model):
    """CreatedBy.

    :ivar user_object_id:
    :vartype user_object_id: str
    :ivar user_tenant_id:
    :vartype user_tenant_id: str
    :ivar user_name:
    :vartype user_name: str
    """

    _attribute_map = {
        "user_object_id": {"key": "userObjectId", "type": "str"},
        "user_tenant_id": {"key": "userTenantId", "type": "str"},
        "user_name": {"key": "userName", "type": "str"},
    }

    def __init__(
        self,
        *,
        user_object_id: Optional[str] = None,
        user_tenant_id: Optional[str] = None,
        user_name: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword user_object_id:
        :paramtype user_object_id: str
        :keyword user_tenant_id:
        :paramtype user_tenant_id: str
        :keyword user_name:
        :paramtype user_name: str
        """
        super(CreatedBy, self).__init__(**kwargs)
        self.user_object_id = user_object_id
        self.user_tenant_id = user_tenant_id
        self.user_name = user_name


class CreatedBy1(msrest.serialization.Model):
    """CreatedBy1.

    All required parameters must be populated in order to send to Azure.

    :ivar user_object_id: Required.
    :vartype user_object_id: str
    :ivar user_tenant_id: Required.
    :vartype user_tenant_id: str
    :ivar user_name: Required.
    :vartype user_name: str
    """

    _validation = {
        "user_object_id": {"required": True},
        "user_tenant_id": {"required": True},
        "user_name": {"required": True},
    }

    _attribute_map = {
        "user_object_id": {"key": "userObjectId", "type": "str"},
        "user_tenant_id": {"key": "userTenantId", "type": "str"},
        "user_name": {"key": "userName", "type": "str"},
    }

    def __init__(self, *, user_object_id: str, user_tenant_id: str, user_name: str, **kwargs):
        """
        :keyword user_object_id: Required.
        :paramtype user_object_id: str
        :keyword user_tenant_id: Required.
        :paramtype user_tenant_id: str
        :keyword user_name: Required.
        :paramtype user_name: str
        """
        super(CreatedBy1, self).__init__(**kwargs)
        self.user_object_id = user_object_id
        self.user_tenant_id = user_tenant_id
        self.user_name = user_name


class CreationContext(msrest.serialization.Model):
    """CreationContext.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar created_time:
    :vartype created_time: ~datetime.datetime
    :ivar created_by:
    :vartype created_by: ~index_service_apis.models.CreatedBy
    :ivar creation_source:
    :vartype creation_source: str
    """

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "created_time": {"key": "createdTime", "type": "iso-8601"},
        "created_by": {"key": "createdBy", "type": "CreatedBy"},
        "creation_source": {"key": "creationSource", "type": "str"},
    }

    def __init__(
        self,
        *,
        additional_properties: Optional[Dict[str, Any]] = None,
        created_time: Optional[datetime.datetime] = None,
        created_by: Optional["CreatedBy"] = None,
        creation_source: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        :keyword created_time:
        :paramtype created_time: ~datetime.datetime
        :keyword created_by:
        :paramtype created_by: ~index_service_apis.models.CreatedBy
        :keyword creation_source:
        :paramtype creation_source: str
        """
        super(CreationContext, self).__init__(**kwargs)
        self.additional_properties = additional_properties
        self.created_time = created_time
        self.created_by = created_by
        self.creation_source = creation_source


class CronTrigger(msrest.serialization.Model):
    """CronTrigger.

    :ivar expression:
    :vartype expression: str
    """

    _attribute_map = {
        "expression": {"key": "expression", "type": "str"},
    }

    def __init__(self, *, expression: Optional[str] = None, **kwargs):
        """
        :keyword expression:
        :paramtype expression: str
        """
        super(CronTrigger, self).__init__(**kwargs)
        self.expression = expression


class CrossRegionIndexEntitiesRequest(msrest.serialization.Model):
    """CrossRegionIndexEntitiesRequest.

    :ivar resource_ids:
    :vartype resource_ids: list[~index_service_apis.models.ResourceInformation]
    :ivar index_entities_request:
    :vartype index_entities_request: ~index_service_apis.models.IndexEntitiesRequest
    """

    _attribute_map = {
        "resource_ids": {"key": "resourceIds", "type": "[ResourceInformation]"},
        "index_entities_request": {"key": "indexEntitiesRequest", "type": "IndexEntitiesRequest"},
    }

    def __init__(
        self,
        *,
        resource_ids: Optional[List["ResourceInformation"]] = None,
        index_entities_request: Optional["IndexEntitiesRequest"] = None,
        **kwargs
    ):
        """
        :keyword resource_ids:
        :paramtype resource_ids: list[~index_service_apis.models.ResourceInformation]
        :keyword index_entities_request:
        :paramtype index_entities_request: ~index_service_apis.models.IndexEntitiesRequest
        """
        super(CrossRegionIndexEntitiesRequest, self).__init__(**kwargs)
        self.resource_ids = resource_ids
        self.index_entities_request = index_entities_request


class CrossRegionIndexEntitiesResponse(msrest.serialization.Model):
    """CrossRegionIndexEntitiesResponse.

    :ivar index_entities_response:
    :vartype index_entities_response: ~index_service_apis.models.IndexEntitiesResponse
    :ivar regional_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype regional_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar shard_errors: Dictionary of
     <components·1fu9hkf·schemas·crossregionindexentitiesresponse·properties·sharderrors·additionalproperties>.
    :vartype shard_errors: dict[str, dict[str, ~index_service_apis.models.ErrorResponse]]
    :ivar number_of_resources_not_included_in_search:
    :vartype number_of_resources_not_included_in_search: int
    """

    _attribute_map = {
        "index_entities_response": {"key": "indexEntitiesResponse", "type": "IndexEntitiesResponse"},
        "regional_errors": {"key": "regionalErrors", "type": "{ErrorResponse}"},
        "shard_errors": {"key": "shardErrors", "type": "{{ErrorResponse}}"},
        "number_of_resources_not_included_in_search": {"key": "numberOfResourcesNotIncludedInSearch", "type": "int"},
    }

    def __init__(
        self,
        *,
        index_entities_response: Optional["IndexEntitiesResponse"] = None,
        regional_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        shard_errors: Optional[Dict[str, Dict[str, "ErrorResponse"]]] = None,
        number_of_resources_not_included_in_search: Optional[int] = None,
        **kwargs
    ):
        """
        :keyword index_entities_response:
        :paramtype index_entities_response: ~index_service_apis.models.IndexEntitiesResponse
        :keyword regional_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype regional_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword shard_errors: Dictionary of
         <components·1fu9hkf·schemas·crossregionindexentitiesresponse·properties·sharderrors·additionalproperties>.
        :paramtype shard_errors: dict[str, dict[str, ~index_service_apis.models.ErrorResponse]]
        :keyword number_of_resources_not_included_in_search:
        :paramtype number_of_resources_not_included_in_search: int
        """
        super(CrossRegionIndexEntitiesResponse, self).__init__(**kwargs)
        self.index_entities_response = index_entities_response
        self.regional_errors = regional_errors
        self.shard_errors = shard_errors
        self.number_of_resources_not_included_in_search = number_of_resources_not_included_in_search


class CudaAttribute(msrest.serialization.Model):
    """CudaAttribute.

    All required parameters must be populated in order to send to Azure.

    :ivar cuda_version: Required.
    :vartype cuda_version: str
    :ivar cu_dnn_version: Required.
    :vartype cu_dnn_version: str
    :ivar nccl_version: Required.
    :vartype nccl_version: str
    """

    _validation = {
        "cuda_version": {"required": True},
        "cu_dnn_version": {"required": True},
        "nccl_version": {"required": True},
    }

    _attribute_map = {
        "cuda_version": {"key": "cudaVersion", "type": "str"},
        "cu_dnn_version": {"key": "cuDnnVersion", "type": "str"},
        "nccl_version": {"key": "ncclVersion", "type": "str"},
    }

    def __init__(self, *, cuda_version: str, cu_dnn_version: str, nccl_version: str, **kwargs):
        """
        :keyword cuda_version: Required.
        :paramtype cuda_version: str
        :keyword cu_dnn_version: Required.
        :paramtype cu_dnn_version: str
        :keyword nccl_version: Required.
        :paramtype nccl_version: str
        """
        super(CudaAttribute, self).__init__(**kwargs)
        self.cuda_version = cuda_version
        self.cu_dnn_version = cu_dnn_version
        self.nccl_version = nccl_version


class DatasetAnnotations(msrest.serialization.Model):
    """DatasetAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar modified_time: Required.
    :vartype modified_time: ~datetime.datetime
    :ivar modified_by: Required.
    :vartype modified_by: ~index_service_apis.models.UserDto
    :ivar next_version_id: Required.
    :vartype next_version_id: str
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar labels: Required. Dictionary of :code:`<string>`.
    :vartype labels: dict[str, str]
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "modified_time": {"required": True},
        "modified_by": {"required": True},
        "next_version_id": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "labels": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "modified_time": {"key": "modifiedTime", "type": "iso-8601"},
        "modified_by": {"key": "modifiedBy", "type": "UserDto"},
        "next_version_id": {"key": "nextVersionId", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "labels": {"key": "labels", "type": "{str}"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        modified_time: datetime.datetime,
        modified_by: "UserDto",
        next_version_id: str,
        name: str,
        description: str,
        labels: Dict[str, str],
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword modified_time: Required.
        :paramtype modified_time: ~datetime.datetime
        :keyword modified_by: Required.
        :paramtype modified_by: ~index_service_apis.models.UserDto
        :keyword next_version_id: Required.
        :paramtype next_version_id: str
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword labels: Required. Dictionary of :code:`<string>`.
        :paramtype labels: dict[str, str]
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(DatasetAnnotations, self).__init__(**kwargs)
        self.modified_time = modified_time
        self.modified_by = modified_by
        self.next_version_id = next_version_id
        self.name = name
        self.description = description
        self.labels = labels
        self.archived = archived
        self.tags = tags


class DatasetProperties(msrest.serialization.Model):
    """DatasetProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar subtype: Required.
    :vartype subtype: str
    :ivar is_registered: Required.
    :vartype is_registered: bool
    :ivar data_type: Required.
    :vartype data_type: str
    :ivar is_v2: Required.
    :vartype is_v2: bool
    :ivar name_lower_case: Required.
    :vartype name_lower_case: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "subtype": {"required": True},
        "is_registered": {"required": True},
        "data_type": {"required": True},
        "is_v2": {"required": True},
        "name_lower_case": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "subtype": {"key": "subtype", "type": "str"},
        "is_registered": {"key": "isRegistered", "type": "bool"},
        "data_type": {"key": "dataType", "type": "str"},
        "is_v2": {"key": "isV2", "type": "bool"},
        "name_lower_case": {"key": "nameLowerCase", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(
        self,
        *,
        subtype: str,
        is_registered: bool,
        data_type: str,
        is_v2: bool,
        name_lower_case: str,
        creation_context: "CreationContext",
        **kwargs
    ):
        """
        :keyword subtype: Required.
        :paramtype subtype: str
        :keyword is_registered: Required.
        :paramtype is_registered: bool
        :keyword data_type: Required.
        :paramtype data_type: str
        :keyword is_v2: Required.
        :paramtype is_v2: bool
        :keyword name_lower_case: Required.
        :paramtype name_lower_case: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(DatasetProperties, self).__init__(**kwargs)
        self.subtype = subtype
        self.is_registered = is_registered
        self.data_type = data_type
        self.is_v2 = is_v2
        self.name_lower_case = name_lower_case
        self.creation_context = creation_context


class DatasetReference(msrest.serialization.Model):
    """DatasetReference.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required.
    :vartype name: str
    :ivar id: Required.
    :vartype id: str
    """

    _validation = {
        "name": {"required": True},
        "id": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, *, name: str, id: str, **kwargs):
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword id: Required.
        :paramtype id: str
        """
        super(DatasetReference, self).__init__(**kwargs)
        self.name = name
        self.id = id


class DatasetsVersionedEntitiesResponse(msrest.serialization.Model):
    """DatasetsVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.DatasetsVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[DatasetsVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["DatasetsVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.DatasetsVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(DatasetsVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class DatasetsVersionedEntity(msrest.serialization.Model):
    """DatasetsVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.DatasetsVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.DatasetAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.DatasetProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "DatasetAnnotations"},
        "properties": {"key": "properties", "type": "DatasetProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "DatasetsVersionedEntityKind"],
        annotations: "DatasetAnnotations",
        properties: "DatasetProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.DatasetsVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.DatasetAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.DatasetProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(DatasetsVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class DataStoreAnnotations(msrest.serialization.Model):
    """DataStoreAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar data_store_type: Required. Possible values include: "AzureBlob", "AzureFile",
     "GlusterFs", "AzureDataLake", "AzureMySql", "Custom", "Hdfs", "AzureSqlDatabase",
     "AzurePostgreSql", "DBFS", "AzureDataLakeGen2".
    :vartype data_store_type: str or ~index_service_apis.models.DataStoreAnnotationsDataStoreType
    :ivar data_store_type_name: Required.
    :vartype data_store_type_name: str
    :ivar storage_name: Required.
    :vartype storage_name: str
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "data_store_type": {"required": True},
        "data_store_type_name": {"required": True},
        "storage_name": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "data_store_type": {"key": "dataStoreType", "type": "str"},
        "data_store_type_name": {"key": "dataStoreTypeName", "type": "str"},
        "storage_name": {"key": "storageName", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        data_store_type: Union[str, "DataStoreAnnotationsDataStoreType"],
        data_store_type_name: str,
        storage_name: str,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword data_store_type: Required. Possible values include: "AzureBlob", "AzureFile",
         "GlusterFs", "AzureDataLake", "AzureMySql", "Custom", "Hdfs", "AzureSqlDatabase",
         "AzurePostgreSql", "DBFS", "AzureDataLakeGen2".
        :paramtype data_store_type: str or ~index_service_apis.models.DataStoreAnnotationsDataStoreType
        :keyword data_store_type_name: Required.
        :paramtype data_store_type_name: str
        :keyword storage_name: Required.
        :paramtype storage_name: str
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(DataStoreAnnotations, self).__init__(**kwargs)
        self.data_store_type = data_store_type
        self.data_store_type_name = data_store_type_name
        self.storage_name = storage_name
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class DataStoreProperties(msrest.serialization.Model):
    """DataStoreProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, creation_context: "CreationContext", **kwargs):
        """
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(DataStoreProperties, self).__init__(**kwargs)
        self.creation_context = creation_context


class DatastoresUnversionedEntitiesResponse(msrest.serialization.Model):
    """DatastoresUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.DatastoresUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[DatastoresUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["DatastoresUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.DatastoresUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(DatastoresUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class DatastoresUnversionedEntity(msrest.serialization.Model):
    """DatastoresUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.DatastoresUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.DataStoreAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.DataStoreProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "DataStoreAnnotations"},
        "properties": {"key": "properties", "type": "DataStoreProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "DatastoresUnversionedEntityKind"],
        annotations: "DataStoreAnnotations",
        properties: "DataStoreProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.DatastoresUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.DataStoreAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.DataStoreProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(DatastoresUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class DebugInfoResponse(msrest.serialization.Model):
    """DebugInfoResponse.

    :ivar type:
    :vartype type: str
    :ivar message:
    :vartype message: str
    :ivar stack_trace:
    :vartype stack_trace: str
    :ivar inner_exception:
    :vartype inner_exception: ~index_service_apis.models.DebugInfoResponse
    :ivar data: Dictionary of :code:`<any>`.
    :vartype data: dict[str, any]
    :ivar error_response:
    :vartype error_response: ~index_service_apis.models.ErrorResponse
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "stack_trace": {"key": "stackTrace", "type": "str"},
        "inner_exception": {"key": "innerException", "type": "DebugInfoResponse"},
        "data": {"key": "data", "type": "{object}"},
        "error_response": {"key": "errorResponse", "type": "ErrorResponse"},
    }

    def __init__(
        self,
        *,
        type: Optional[str] = None,
        message: Optional[str] = None,
        stack_trace: Optional[str] = None,
        inner_exception: Optional["DebugInfoResponse"] = None,
        data: Optional[Dict[str, Any]] = None,
        error_response: Optional["ErrorResponse"] = None,
        **kwargs
    ):
        """
        :keyword type:
        :paramtype type: str
        :keyword message:
        :paramtype message: str
        :keyword stack_trace:
        :paramtype stack_trace: str
        :keyword inner_exception:
        :paramtype inner_exception: ~index_service_apis.models.DebugInfoResponse
        :keyword data: Dictionary of :code:`<any>`.
        :paramtype data: dict[str, any]
        :keyword error_response:
        :paramtype error_response: ~index_service_apis.models.ErrorResponse
        """
        super(DebugInfoResponse, self).__init__(**kwargs)
        self.type = type
        self.message = message
        self.stack_trace = stack_trace
        self.inner_exception = inner_exception
        self.data = data
        self.error_response = error_response


class DefinitionAnnotations(msrest.serialization.Model):
    """DefinitionAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, *, description: str, archived: bool, tags: Dict[str, str], **kwargs):
        """
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(DefinitionAnnotations, self).__init__(**kwargs)
        self.description = description
        self.archived = archived
        self.tags = tags


class DefinitionProperties(msrest.serialization.Model):
    """DefinitionProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar automatically_saved: Required.
    :vartype automatically_saved: bool
    :ivar definition_hash: Required.
    :vartype definition_hash: str
    :ivar environment_attributes: Required.
    :vartype environment_attributes: ~index_service_apis.models.EnvironmentAttributes
    :ivar stage: Required.
    :vartype stage: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "automatically_saved": {"required": True},
        "definition_hash": {"required": True},
        "environment_attributes": {"required": True},
        "stage": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "automatically_saved": {"key": "automaticallySaved", "type": "bool"},
        "definition_hash": {"key": "definitionHash", "type": "str"},
        "environment_attributes": {"key": "environmentAttributes", "type": "EnvironmentAttributes"},
        "stage": {"key": "stage", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(
        self,
        *,
        automatically_saved: bool,
        definition_hash: str,
        environment_attributes: "EnvironmentAttributes",
        stage: str,
        creation_context: "CreationContext",
        **kwargs
    ):
        """
        :keyword automatically_saved: Required.
        :paramtype automatically_saved: bool
        :keyword definition_hash: Required.
        :paramtype definition_hash: str
        :keyword environment_attributes: Required.
        :paramtype environment_attributes: ~index_service_apis.models.EnvironmentAttributes
        :keyword stage: Required.
        :paramtype stage: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(DefinitionProperties, self).__init__(**kwargs)
        self.automatically_saved = automatically_saved
        self.definition_hash = definition_hash
        self.environment_attributes = environment_attributes
        self.stage = stage
        self.creation_context = creation_context


class EndpointComputeTarget(msrest.serialization.Model):
    """EndpointComputeTarget.

    All required parameters must be populated in order to send to Azure.

    :ivar namespace: Required.
    :vartype namespace: str
    :ivar compute_target_name: Required.
    :vartype compute_target_name: str
    """

    _validation = {
        "namespace": {"required": True},
        "compute_target_name": {"required": True},
    }

    _attribute_map = {
        "namespace": {"key": "namespace", "type": "str"},
        "compute_target_name": {"key": "computeTargetName", "type": "str"},
    }

    def __init__(self, *, namespace: str, compute_target_name: str, **kwargs):
        """
        :keyword namespace: Required.
        :paramtype namespace: str
        :keyword compute_target_name: Required.
        :paramtype compute_target_name: str
        """
        super(EndpointComputeTarget, self).__init__(**kwargs)
        self.namespace = namespace
        self.compute_target_name = compute_target_name


class EnvironmentAttributes(msrest.serialization.Model):
    """EnvironmentAttributes.

    All required parameters must be populated in order to send to Azure.

    :ivar machine_learning_framework: Required.
    :vartype machine_learning_framework: ~index_service_apis.models.VersionedAttribute
    :ivar mpi: Required.
    :vartype mpi: ~index_service_apis.models.VersionedAttribute
    :ivar runtime: Required.
    :vartype runtime: ~index_service_apis.models.VersionedAttribute
    :ivar cuda: Required.
    :vartype cuda: ~index_service_apis.models.CudaAttribute
    :ivar purpose: Required.
    :vartype purpose: ~index_service_apis.models.Purpose
    :ivar os: Required.
    :vartype os: ~index_service_apis.models.VersionedAttribute
    """

    _validation = {
        "machine_learning_framework": {"required": True},
        "mpi": {"required": True},
        "runtime": {"required": True},
        "cuda": {"required": True},
        "purpose": {"required": True},
        "os": {"required": True},
    }

    _attribute_map = {
        "machine_learning_framework": {"key": "machineLearningFramework", "type": "VersionedAttribute"},
        "mpi": {"key": "mpi", "type": "VersionedAttribute"},
        "runtime": {"key": "runtime", "type": "VersionedAttribute"},
        "cuda": {"key": "cuda", "type": "CudaAttribute"},
        "purpose": {"key": "purpose", "type": "Purpose"},
        "os": {"key": "os", "type": "VersionedAttribute"},
    }

    def __init__(
        self,
        *,
        machine_learning_framework: "VersionedAttribute",
        mpi: "VersionedAttribute",
        runtime: "VersionedAttribute",
        cuda: "CudaAttribute",
        purpose: "Purpose",
        os: "VersionedAttribute",
        **kwargs
    ):
        """
        :keyword machine_learning_framework: Required.
        :paramtype machine_learning_framework: ~index_service_apis.models.VersionedAttribute
        :keyword mpi: Required.
        :paramtype mpi: ~index_service_apis.models.VersionedAttribute
        :keyword runtime: Required.
        :paramtype runtime: ~index_service_apis.models.VersionedAttribute
        :keyword cuda: Required.
        :paramtype cuda: ~index_service_apis.models.CudaAttribute
        :keyword purpose: Required.
        :paramtype purpose: ~index_service_apis.models.Purpose
        :keyword os: Required.
        :paramtype os: ~index_service_apis.models.VersionedAttribute
        """
        super(EnvironmentAttributes, self).__init__(**kwargs)
        self.machine_learning_framework = machine_learning_framework
        self.mpi = mpi
        self.runtime = runtime
        self.cuda = cuda
        self.purpose = purpose
        self.os = os


class EnvironmentsVersionedEntitiesResponse(msrest.serialization.Model):
    """EnvironmentsVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.EnvironmentsVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[EnvironmentsVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["EnvironmentsVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.EnvironmentsVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(EnvironmentsVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class EnvironmentsVersionedEntity(msrest.serialization.Model):
    """EnvironmentsVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.EnvironmentsVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.DefinitionAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.DefinitionProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "DefinitionAnnotations"},
        "properties": {"key": "properties", "type": "DefinitionProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "EnvironmentsVersionedEntityKind"],
        annotations: "DefinitionAnnotations",
        properties: "DefinitionProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.EnvironmentsVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.DefinitionAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.DefinitionProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(EnvironmentsVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class ErrorAdditionalInfo(msrest.serialization.Model):
    """ErrorAdditionalInfo.

    :ivar type:
    :vartype type: str
    :ivar info: Anything.
    :vartype info: any
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, *, type: Optional[str] = None, info: Optional[Any] = None, **kwargs):
        """
        :keyword type:
        :paramtype type: str
        :keyword info: Anything.
        :paramtype info: any
        """
        super(ErrorAdditionalInfo, self).__init__(**kwargs)
        self.type = type
        self.info = info


class ErrorResponse(msrest.serialization.Model):
    """ErrorResponse.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar error:
    :vartype error: ~index_service_apis.models.RootError
    :ivar correlation: Dictionary of :code:`<string>`.
    :vartype correlation: dict[str, str]
    :ivar environment:
    :vartype environment: str
    :ivar location:
    :vartype location: str
    :ivar time:
    :vartype time: ~datetime.datetime
    :ivar component_name:
    :vartype component_name: str
    """

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "error": {"key": "error", "type": "RootError"},
        "correlation": {"key": "correlation", "type": "{str}"},
        "environment": {"key": "environment", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "time": {"key": "time", "type": "iso-8601"},
        "component_name": {"key": "componentName", "type": "str"},
    }

    def __init__(
        self,
        *,
        additional_properties: Optional[Dict[str, Any]] = None,
        error: Optional["RootError"] = None,
        correlation: Optional[Dict[str, str]] = None,
        environment: Optional[str] = None,
        location: Optional[str] = None,
        time: Optional[datetime.datetime] = None,
        component_name: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        :keyword error:
        :paramtype error: ~index_service_apis.models.RootError
        :keyword correlation: Dictionary of :code:`<string>`.
        :paramtype correlation: dict[str, str]
        :keyword environment:
        :paramtype environment: str
        :keyword location:
        :paramtype location: str
        :keyword time:
        :paramtype time: ~datetime.datetime
        :keyword component_name:
        :paramtype component_name: str
        """
        super(ErrorResponse, self).__init__(**kwargs)
        self.additional_properties = additional_properties
        self.error = error
        self.correlation = correlation
        self.environment = environment
        self.location = location
        self.time = time
        self.component_name = component_name


class ExperimentStatus(msrest.serialization.Model):
    """ExperimentStatus.

    :ivar status_code: Possible values include: "NotStarted", "Running", "Failed", "Finished",
     "Canceled", "Failing".
    :vartype status_code: str or ~index_service_apis.models.ExperimentStatusCode
    :ivar status_detail:
    :vartype status_detail: str
    :ivar creation_time:
    :vartype creation_time: ~datetime.datetime
    :ivar end_time:
    :vartype end_time: ~datetime.datetime
    :ivar run_history_error_code:
    :vartype run_history_error_code: str
    """

    _attribute_map = {
        "status_code": {"key": "statusCode", "type": "str"},
        "status_detail": {"key": "statusDetail", "type": "str"},
        "creation_time": {"key": "creationTime", "type": "iso-8601"},
        "end_time": {"key": "endTime", "type": "iso-8601"},
        "run_history_error_code": {"key": "runHistoryErrorCode", "type": "str"},
    }

    def __init__(
        self,
        *,
        status_code: Optional[Union[str, "ExperimentStatusCode"]] = None,
        status_detail: Optional[str] = None,
        creation_time: Optional[datetime.datetime] = None,
        end_time: Optional[datetime.datetime] = None,
        run_history_error_code: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword status_code: Possible values include: "NotStarted", "Running", "Failed", "Finished",
         "Canceled", "Failing".
        :paramtype status_code: str or ~index_service_apis.models.ExperimentStatusCode
        :keyword status_detail:
        :paramtype status_detail: str
        :keyword creation_time:
        :paramtype creation_time: ~datetime.datetime
        :keyword end_time:
        :paramtype end_time: ~datetime.datetime
        :keyword run_history_error_code:
        :paramtype run_history_error_code: str
        """
        super(ExperimentStatus, self).__init__(**kwargs)
        self.status_code = status_code
        self.status_detail = status_detail
        self.creation_time = creation_time
        self.end_time = end_time
        self.run_history_error_code = run_history_error_code


class Feature(msrest.serialization.Model):
    """Feature.

    All required parameters must be populated in order to send to Azure.

    :ivar feature_name: Required.
    :vartype feature_name: str
    :ivar description: Required.
    :vartype description: str
    :ivar data_type: Required. Possible values include: "String", "Integer", "Long", "Float",
     "Double", "Binary", "Datetime", "Boolean".
    :vartype data_type: str or ~index_service_apis.models.FeatureDataType
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "feature_name": {"required": True},
        "description": {"required": True},
        "data_type": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "feature_name": {"key": "featureName", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "data_type": {"key": "dataType", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        feature_name: str,
        description: str,
        data_type: Union[str, "FeatureDataType"],
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword feature_name: Required.
        :paramtype feature_name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword data_type: Required. Possible values include: "String", "Integer", "Long", "Float",
         "Double", "Binary", "Datetime", "Boolean".
        :paramtype data_type: str or ~index_service_apis.models.FeatureDataType
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(Feature, self).__init__(**kwargs)
        self.feature_name = feature_name
        self.description = description
        self.data_type = data_type
        self.tags = tags


class FeatureentitiesVersionedEntitiesResponse(msrest.serialization.Model):
    """FeatureentitiesVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.FeatureentitiesVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[FeatureentitiesVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["FeatureentitiesVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.FeatureentitiesVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(FeatureentitiesVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class FeatureentitiesVersionedEntity(msrest.serialization.Model):
    """FeatureentitiesVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.FeatureentitiesVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.FeatureEntityVersionAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.FeatureEntityVersionProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "FeatureEntityVersionAnnotations"},
        "properties": {"key": "properties", "type": "FeatureEntityVersionProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "FeatureentitiesVersionedEntityKind"],
        annotations: "FeatureEntityVersionAnnotations",
        properties: "FeatureEntityVersionProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.FeatureentitiesVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.FeatureEntityVersionAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.FeatureEntityVersionProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(FeatureentitiesVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class FeatureEntityVersionAnnotations(msrest.serialization.Model):
    """FeatureEntityVersionAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar actual_name: Required.
    :vartype actual_name: str
    :ivar index_columns: Required.
    :vartype index_columns: list[~index_service_apis.models.IndexColumnDto]
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "actual_name": {"required": True},
        "index_columns": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "actual_name": {"key": "actualName", "type": "str"},
        "index_columns": {"key": "indexColumns", "type": "[IndexColumnDto]"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        actual_name: str,
        index_columns: List["IndexColumnDto"],
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword actual_name: Required.
        :paramtype actual_name: str
        :keyword index_columns: Required.
        :paramtype index_columns: list[~index_service_apis.models.IndexColumnDto]
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(FeatureEntityVersionAnnotations, self).__init__(**kwargs)
        self.actual_name = actual_name
        self.index_columns = index_columns
        self.description = description
        self.archived = archived
        self.tags = tags


class FeatureEntityVersionProperties(msrest.serialization.Model):
    """FeatureEntityVersionProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar feature_entity_name: Required.
    :vartype feature_entity_name: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "feature_entity_name": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "feature_entity_name": {"key": "featureEntityName", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, feature_entity_name: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword feature_entity_name: Required.
        :paramtype feature_entity_name: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(FeatureEntityVersionProperties, self).__init__(**kwargs)
        self.feature_entity_name = feature_entity_name
        self.creation_context = creation_context


class FeaturesetSpecification(msrest.serialization.Model):
    """FeaturesetSpecification.

    All required parameters must be populated in order to send to Azure.

    :ivar path: Required.
    :vartype path: str
    """

    _validation = {
        "path": {"required": True},
    }

    _attribute_map = {
        "path": {"key": "path", "type": "str"},
    }

    def __init__(self, *, path: str, **kwargs):
        """
        :keyword path: Required.
        :paramtype path: str
        """
        super(FeaturesetSpecification, self).__init__(**kwargs)
        self.path = path


class FeaturesetsVersionedEntitiesResponse(msrest.serialization.Model):
    """FeaturesetsVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.FeaturesetsVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[FeaturesetsVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["FeaturesetsVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.FeaturesetsVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(FeaturesetsVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class FeaturesetsVersionedEntity(msrest.serialization.Model):
    """FeaturesetsVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.FeaturesetsVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.FeatureSetVersionAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.FeatureSetVersionProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "FeatureSetVersionAnnotations"},
        "properties": {"key": "properties", "type": "FeatureSetVersionProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "FeaturesetsVersionedEntityKind"],
        annotations: "FeatureSetVersionAnnotations",
        properties: "FeatureSetVersionProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.FeaturesetsVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.FeatureSetVersionAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.FeatureSetVersionProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(FeaturesetsVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class FeatureSetVersionAnnotations(msrest.serialization.Model):
    """FeatureSetVersionAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar actual_name: Required.
    :vartype actual_name: str
    :ivar specification: Required.
    :vartype specification: ~index_service_apis.models.FeaturesetSpecification
    :ivar entities: Required.
    :vartype entities: list[str]
    :ivar features: Required.
    :vartype features: list[~index_service_apis.models.Feature]
    :ivar stage: Required.
    :vartype stage: str
    :ivar materialization_settings: Required.
    :vartype materialization_settings: ~index_service_apis.models.MaterializationSettings
    :ivar source: Required.
    :vartype source: ~index_service_apis.models.FeatureSourceDto
    :ivar feature_transformation_code: Required.
    :vartype feature_transformation_code: ~index_service_apis.models.FeatureTransformationDto
    :ivar temporal_join_lookback: Required.
    :vartype temporal_join_lookback: ~index_service_apis.models.TimeDeltaDto
    :ivar source_lookback: Required.
    :vartype source_lookback: ~index_service_apis.models.TimeDeltaDto
    :ivar timestamp_column: Required.
    :vartype timestamp_column: ~index_service_apis.models.TimestampColumnDto
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "actual_name": {"required": True},
        "specification": {"required": True},
        "entities": {"required": True},
        "features": {"required": True},
        "stage": {"required": True},
        "materialization_settings": {"required": True},
        "source": {"required": True},
        "feature_transformation_code": {"required": True},
        "temporal_join_lookback": {"required": True},
        "source_lookback": {"required": True},
        "timestamp_column": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "actual_name": {"key": "actualName", "type": "str"},
        "specification": {"key": "specification", "type": "FeaturesetSpecification"},
        "entities": {"key": "entities", "type": "[str]"},
        "features": {"key": "features", "type": "[Feature]"},
        "stage": {"key": "stage", "type": "str"},
        "materialization_settings": {"key": "materializationSettings", "type": "MaterializationSettings"},
        "source": {"key": "source", "type": "FeatureSourceDto"},
        "feature_transformation_code": {"key": "featureTransformationCode", "type": "FeatureTransformationDto"},
        "temporal_join_lookback": {"key": "temporalJoinLookback", "type": "TimeDeltaDto"},
        "source_lookback": {"key": "sourceLookback", "type": "TimeDeltaDto"},
        "timestamp_column": {"key": "timestampColumn", "type": "TimestampColumnDto"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        actual_name: str,
        specification: "FeaturesetSpecification",
        entities: List[str],
        features: List["Feature"],
        stage: str,
        materialization_settings: "MaterializationSettings",
        source: "FeatureSourceDto",
        feature_transformation_code: "FeatureTransformationDto",
        temporal_join_lookback: "TimeDeltaDto",
        source_lookback: "TimeDeltaDto",
        timestamp_column: "TimestampColumnDto",
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword actual_name: Required.
        :paramtype actual_name: str
        :keyword specification: Required.
        :paramtype specification: ~index_service_apis.models.FeaturesetSpecification
        :keyword entities: Required.
        :paramtype entities: list[str]
        :keyword features: Required.
        :paramtype features: list[~index_service_apis.models.Feature]
        :keyword stage: Required.
        :paramtype stage: str
        :keyword materialization_settings: Required.
        :paramtype materialization_settings: ~index_service_apis.models.MaterializationSettings
        :keyword source: Required.
        :paramtype source: ~index_service_apis.models.FeatureSourceDto
        :keyword feature_transformation_code: Required.
        :paramtype feature_transformation_code: ~index_service_apis.models.FeatureTransformationDto
        :keyword temporal_join_lookback: Required.
        :paramtype temporal_join_lookback: ~index_service_apis.models.TimeDeltaDto
        :keyword source_lookback: Required.
        :paramtype source_lookback: ~index_service_apis.models.TimeDeltaDto
        :keyword timestamp_column: Required.
        :paramtype timestamp_column: ~index_service_apis.models.TimestampColumnDto
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(FeatureSetVersionAnnotations, self).__init__(**kwargs)
        self.actual_name = actual_name
        self.specification = specification
        self.entities = entities
        self.features = features
        self.stage = stage
        self.materialization_settings = materialization_settings
        self.source = source
        self.feature_transformation_code = feature_transformation_code
        self.temporal_join_lookback = temporal_join_lookback
        self.source_lookback = source_lookback
        self.timestamp_column = timestamp_column
        self.description = description
        self.archived = archived
        self.tags = tags


class FeatureSetVersionProperties(msrest.serialization.Model):
    """FeatureSetVersionProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar feature_set_name: Required.
    :vartype feature_set_name: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "feature_set_name": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "feature_set_name": {"key": "featureSetName", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, feature_set_name: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword feature_set_name: Required.
        :paramtype feature_set_name: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(FeatureSetVersionProperties, self).__init__(**kwargs)
        self.feature_set_name = feature_set_name
        self.creation_context = creation_context


class FeatureSourceDto(msrest.serialization.Model):
    """FeatureSourceDto.

    All required parameters must be populated in order to send to Azure.

    :ivar type: Required. Possible values include: "mltable", "csv", "parquet".
    :vartype type: str or ~index_service_apis.models.FeatureSourceDtoType
    :ivar path: Required.
    :vartype path: str
    :ivar timestamp_column: Required.
    :vartype timestamp_column: ~index_service_apis.models.TimestampColumnDto
    :ivar source_delay: Required.
    :vartype source_delay: ~index_service_apis.models.TimeDeltaDto
    """

    _validation = {
        "type": {"required": True},
        "path": {"required": True},
        "timestamp_column": {"required": True},
        "source_delay": {"required": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "path": {"key": "path", "type": "str"},
        "timestamp_column": {"key": "timestampColumn", "type": "TimestampColumnDto"},
        "source_delay": {"key": "sourceDelay", "type": "TimeDeltaDto"},
    }

    def __init__(
        self,
        *,
        type: Union[str, "FeatureSourceDtoType"],
        path: str,
        timestamp_column: "TimestampColumnDto",
        source_delay: "TimeDeltaDto",
        **kwargs
    ):
        """
        :keyword type: Required. Possible values include: "mltable", "csv", "parquet".
        :paramtype type: str or ~index_service_apis.models.FeatureSourceDtoType
        :keyword path: Required.
        :paramtype path: str
        :keyword timestamp_column: Required.
        :paramtype timestamp_column: ~index_service_apis.models.TimestampColumnDto
        :keyword source_delay: Required.
        :paramtype source_delay: ~index_service_apis.models.TimeDeltaDto
        """
        super(FeatureSourceDto, self).__init__(**kwargs)
        self.type = type
        self.path = path
        self.timestamp_column = timestamp_column
        self.source_delay = source_delay


class FeaturestoreentitiesVersionedEntitiesResponse(msrest.serialization.Model):
    """FeaturestoreentitiesVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.FeaturestoreentitiesVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[FeaturestoreentitiesVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["FeaturestoreentitiesVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.FeaturestoreentitiesVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(FeaturestoreentitiesVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class FeaturestoreentitiesVersionedEntity(msrest.serialization.Model):
    """FeaturestoreentitiesVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.FeaturestoreentitiesVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.FeatureEntityVersionAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.FeatureEntityVersionProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "FeatureEntityVersionAnnotations"},
        "properties": {"key": "properties", "type": "FeatureEntityVersionProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "FeaturestoreentitiesVersionedEntityKind"],
        annotations: "FeatureEntityVersionAnnotations",
        properties: "FeatureEntityVersionProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.FeaturestoreentitiesVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.FeatureEntityVersionAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.FeatureEntityVersionProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(FeaturestoreentitiesVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class FeatureTransformationDto(msrest.serialization.Model):
    """FeatureTransformationDto.

    All required parameters must be populated in order to send to Azure.

    :ivar path: Required.
    :vartype path: str
    :ivar transformer_class: Required.
    :vartype transformer_class: str
    """

    _validation = {
        "path": {"required": True},
        "transformer_class": {"required": True},
    }

    _attribute_map = {
        "path": {"key": "path", "type": "str"},
        "transformer_class": {"key": "transformerClass", "type": "str"},
    }

    def __init__(self, *, path: str, transformer_class: str, **kwargs):
        """
        :keyword path: Required.
        :paramtype path: str
        :keyword transformer_class: Required.
        :paramtype transformer_class: str
        """
        super(FeatureTransformationDto, self).__init__(**kwargs)
        self.path = path
        self.transformer_class = transformer_class


class FreeTextSearchColumn(msrest.serialization.Model):
    """FreeTextSearchColumn.

    :ivar name:
    :vartype name: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
    }

    def __init__(self, *, name: Optional[str] = None, **kwargs):
        """
        :keyword name:
        :paramtype name: str
        """
        super(FreeTextSearchColumn, self).__init__(**kwargs)
        self.name = name


class GenericTriggerAnnotations(msrest.serialization.Model):
    """GenericTriggerAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar display_name: Required.
    :vartype display_name: str
    :ivar kv_tags: Required. Dictionary of :code:`<string>`.
    :vartype kv_tags: dict[str, str]
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar provisioning_status: Required. Possible values include: "Completed", "Provisioning",
     "Failed".
    :vartype provisioning_status: str or
     ~index_service_apis.models.GenericTriggerAnnotationsProvisioningStatus
    :ivar trigger_type: Required. Possible values include: "Schedule", "Once".
    :vartype trigger_type: str or ~index_service_apis.models.GenericTriggerAnnotationsTriggerType
    :ivar schedule_method: Required. Possible values include: "Cron", "Recurrence".
    :vartype schedule_method: str or
     ~index_service_apis.models.GenericTriggerAnnotationsScheduleMethod
    :ivar schedule_action_type: Required. Possible values include: "CreateJob", "InvokeEndpoint",
     "ImportData", "ModelMonitor".
    :vartype schedule_action_type: str or
     ~index_service_apis.models.GenericTriggerAnnotationsScheduleActionType
    :ivar recurrence: Required.
    :vartype recurrence: ~index_service_apis.models.RecurrenceTrigger
    :ivar cron: Required.
    :vartype cron: ~index_service_apis.models.CronTrigger
    :ivar start_time: Required.
    :vartype start_time: str
    :ivar end_time: Required.
    :vartype end_time: str
    :ivar time_zone: Required.
    :vartype time_zone: str
    :ivar entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
    :vartype entity_status: str or ~index_service_apis.models.GenericTriggerAnnotationsEntityStatus
    :ivar last_modified_date: Required.
    :vartype last_modified_date: ~datetime.datetime
    :ivar last_updated_by: Required.
    :vartype last_updated_by: ~index_service_apis.models.CreatedBy
    :ivar definition: Required.
    :vartype definition: str
    :ivar feature_definition: Required.
    :vartype feature_definition: str
    :ivar managed_type: Required. Possible values include: "Customer", "System".
    :vartype managed_type: str or ~index_service_apis.models.GenericTriggerAnnotationsManagedType
    :ivar scenario_name: Required.
    :vartype scenario_name: str
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "display_name": {"required": True},
        "kv_tags": {"required": True},
        "properties": {"required": True},
        "provisioning_status": {"required": True},
        "trigger_type": {"required": True},
        "schedule_method": {"required": True},
        "schedule_action_type": {"required": True},
        "recurrence": {"required": True},
        "cron": {"required": True},
        "start_time": {"required": True},
        "end_time": {"required": True},
        "time_zone": {"required": True},
        "entity_status": {"required": True},
        "last_modified_date": {"required": True},
        "last_updated_by": {"required": True},
        "definition": {"required": True},
        "feature_definition": {"required": True},
        "managed_type": {"required": True},
        "scenario_name": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "display_name": {"key": "displayName", "type": "str"},
        "kv_tags": {"key": "kvTags", "type": "{str}"},
        "properties": {"key": "properties", "type": "{str}"},
        "provisioning_status": {"key": "provisioningStatus", "type": "str"},
        "trigger_type": {"key": "triggerType", "type": "str"},
        "schedule_method": {"key": "scheduleMethod", "type": "str"},
        "schedule_action_type": {"key": "scheduleActionType", "type": "str"},
        "recurrence": {"key": "recurrence", "type": "RecurrenceTrigger"},
        "cron": {"key": "cron", "type": "CronTrigger"},
        "start_time": {"key": "startTime", "type": "str"},
        "end_time": {"key": "endTime", "type": "str"},
        "time_zone": {"key": "timeZone", "type": "str"},
        "entity_status": {"key": "entityStatus", "type": "str"},
        "last_modified_date": {"key": "lastModifiedDate", "type": "iso-8601"},
        "last_updated_by": {"key": "lastUpdatedBy", "type": "CreatedBy"},
        "definition": {"key": "definition", "type": "str"},
        "feature_definition": {"key": "featureDefinition", "type": "str"},
        "managed_type": {"key": "managedType", "type": "str"},
        "scenario_name": {"key": "scenarioName", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        display_name: str,
        kv_tags: Dict[str, str],
        properties: Dict[str, str],
        provisioning_status: Union[str, "GenericTriggerAnnotationsProvisioningStatus"],
        trigger_type: Union[str, "GenericTriggerAnnotationsTriggerType"],
        schedule_method: Union[str, "GenericTriggerAnnotationsScheduleMethod"],
        schedule_action_type: Union[str, "GenericTriggerAnnotationsScheduleActionType"],
        recurrence: "RecurrenceTrigger",
        cron: "CronTrigger",
        start_time: str,
        end_time: str,
        time_zone: str,
        entity_status: Union[str, "GenericTriggerAnnotationsEntityStatus"],
        last_modified_date: datetime.datetime,
        last_updated_by: "CreatedBy",
        definition: str,
        feature_definition: str,
        managed_type: Union[str, "GenericTriggerAnnotationsManagedType"],
        scenario_name: str,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword display_name: Required.
        :paramtype display_name: str
        :keyword kv_tags: Required. Dictionary of :code:`<string>`.
        :paramtype kv_tags: dict[str, str]
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword provisioning_status: Required. Possible values include: "Completed", "Provisioning",
         "Failed".
        :paramtype provisioning_status: str or
         ~index_service_apis.models.GenericTriggerAnnotationsProvisioningStatus
        :keyword trigger_type: Required. Possible values include: "Schedule", "Once".
        :paramtype trigger_type: str or ~index_service_apis.models.GenericTriggerAnnotationsTriggerType
        :keyword schedule_method: Required. Possible values include: "Cron", "Recurrence".
        :paramtype schedule_method: str or
         ~index_service_apis.models.GenericTriggerAnnotationsScheduleMethod
        :keyword schedule_action_type: Required. Possible values include: "CreateJob",
         "InvokeEndpoint", "ImportData", "ModelMonitor".
        :paramtype schedule_action_type: str or
         ~index_service_apis.models.GenericTriggerAnnotationsScheduleActionType
        :keyword recurrence: Required.
        :paramtype recurrence: ~index_service_apis.models.RecurrenceTrigger
        :keyword cron: Required.
        :paramtype cron: ~index_service_apis.models.CronTrigger
        :keyword start_time: Required.
        :paramtype start_time: str
        :keyword end_time: Required.
        :paramtype end_time: str
        :keyword time_zone: Required.
        :paramtype time_zone: str
        :keyword entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
        :paramtype entity_status: str or
         ~index_service_apis.models.GenericTriggerAnnotationsEntityStatus
        :keyword last_modified_date: Required.
        :paramtype last_modified_date: ~datetime.datetime
        :keyword last_updated_by: Required.
        :paramtype last_updated_by: ~index_service_apis.models.CreatedBy
        :keyword definition: Required.
        :paramtype definition: str
        :keyword feature_definition: Required.
        :paramtype feature_definition: str
        :keyword managed_type: Required. Possible values include: "Customer", "System".
        :paramtype managed_type: str or ~index_service_apis.models.GenericTriggerAnnotationsManagedType
        :keyword scenario_name: Required.
        :paramtype scenario_name: str
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(GenericTriggerAnnotations, self).__init__(**kwargs)
        self.display_name = display_name
        self.kv_tags = kv_tags
        self.properties = properties
        self.provisioning_status = provisioning_status
        self.trigger_type = trigger_type
        self.schedule_method = schedule_method
        self.schedule_action_type = schedule_action_type
        self.recurrence = recurrence
        self.cron = cron
        self.start_time = start_time
        self.end_time = end_time
        self.time_zone = time_zone
        self.entity_status = entity_status
        self.last_modified_date = last_modified_date
        self.last_updated_by = last_updated_by
        self.definition = definition
        self.feature_definition = feature_definition
        self.managed_type = managed_type
        self.scenario_name = scenario_name
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class GenericTriggerProperties(msrest.serialization.Model):
    """GenericTriggerProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, id: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(GenericTriggerProperties, self).__init__(**kwargs)
        self.id = id
        self.creation_context = creation_context


class GenerictriggersUnversionedEntitiesResponse(msrest.serialization.Model):
    """GenerictriggersUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.GenerictriggersUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[GenerictriggersUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["GenerictriggersUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.GenerictriggersUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(GenerictriggersUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class GenerictriggersUnversionedEntity(msrest.serialization.Model):
    """GenerictriggersUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.GenerictriggersUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.GenericTriggerAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.GenericTriggerProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "GenericTriggerAnnotations"},
        "properties": {"key": "properties", "type": "GenericTriggerProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "GenerictriggersUnversionedEntityKind"],
        annotations: "GenericTriggerAnnotations",
        properties: "GenericTriggerProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.GenerictriggersUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.GenericTriggerAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.GenericTriggerProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(GenerictriggersUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class HighlightOptions(msrest.serialization.Model):
    """HighlightOptions.

    :ivar pre_tag:
    :vartype pre_tag: str
    :ivar post_tag:
    :vartype post_tag: str
    :ivar fields:
    :vartype fields: list[str]
    """

    _attribute_map = {
        "pre_tag": {"key": "preTag", "type": "str"},
        "post_tag": {"key": "postTag", "type": "str"},
        "fields": {"key": "fields", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        pre_tag: Optional[str] = None,
        post_tag: Optional[str] = None,
        fields: Optional[List[str]] = None,
        **kwargs
    ):
        """
        :keyword pre_tag:
        :paramtype pre_tag: str
        :keyword post_tag:
        :paramtype post_tag: str
        :keyword fields:
        :paramtype fields: list[str]
        """
        super(HighlightOptions, self).__init__(**kwargs)
        self.pre_tag = pre_tag
        self.post_tag = post_tag
        self.fields = fields


class IndependentPipelineAnnotations(msrest.serialization.Model):
    """IndependentPipelineAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar url: Required.
    :vartype url: str
    :ivar kv_tags: Required. Dictionary of :code:`<string>`.
    :vartype kv_tags: dict[str, str]
    :ivar step_tags: Required. Dictionary of :code:`<string>`.
    :vartype step_tags: dict[str, str]
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar step_properties: Required. Dictionary of :code:`<string>`.
    :vartype step_properties: dict[str, str]
    :ivar entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
    :vartype entity_status: str or
     ~index_service_apis.models.IndependentPipelineAnnotationsEntityStatus
    :ivar last_modified_date: Required.
    :vartype last_modified_date: ~datetime.datetime
    :ivar last_updated_by: Required.
    :vartype last_updated_by: ~index_service_apis.models.CreatedBy
    :ivar last_run_id: Required.
    :vartype last_run_id: str
    :ivar pipeline_type: Required.
    :vartype pipeline_type: str
    :ivar last_run_status_code: Required. Possible values include: "NotStarted", "Running",
     "Failed", "Finished", "Canceled", "Failing", "Queued", "CancelRequested".
    :vartype last_run_status_code: str or
     ~index_service_apis.models.IndependentPipelineAnnotationsLastRunStatusCode
    :ivar last_run_creation_time: Required.
    :vartype last_run_creation_time: ~datetime.datetime
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "url": {"required": True},
        "kv_tags": {"required": True},
        "step_tags": {"required": True},
        "properties": {"required": True},
        "step_properties": {"required": True},
        "entity_status": {"required": True},
        "last_modified_date": {"required": True},
        "last_updated_by": {"required": True},
        "last_run_id": {"required": True},
        "pipeline_type": {"required": True},
        "last_run_status_code": {"required": True},
        "last_run_creation_time": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "url": {"key": "url", "type": "str"},
        "kv_tags": {"key": "kvTags", "type": "{str}"},
        "step_tags": {"key": "stepTags", "type": "{str}"},
        "properties": {"key": "properties", "type": "{str}"},
        "step_properties": {"key": "stepProperties", "type": "{str}"},
        "entity_status": {"key": "entityStatus", "type": "str"},
        "last_modified_date": {"key": "lastModifiedDate", "type": "iso-8601"},
        "last_updated_by": {"key": "lastUpdatedBy", "type": "CreatedBy"},
        "last_run_id": {"key": "lastRunId", "type": "str"},
        "pipeline_type": {"key": "pipelineType", "type": "str"},
        "last_run_status_code": {"key": "lastRunStatusCode", "type": "str"},
        "last_run_creation_time": {"key": "lastRunCreationTime", "type": "iso-8601"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        url: str,
        kv_tags: Dict[str, str],
        step_tags: Dict[str, str],
        properties: Dict[str, str],
        step_properties: Dict[str, str],
        entity_status: Union[str, "IndependentPipelineAnnotationsEntityStatus"],
        last_modified_date: datetime.datetime,
        last_updated_by: "CreatedBy",
        last_run_id: str,
        pipeline_type: str,
        last_run_status_code: Union[str, "IndependentPipelineAnnotationsLastRunStatusCode"],
        last_run_creation_time: datetime.datetime,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword url: Required.
        :paramtype url: str
        :keyword kv_tags: Required. Dictionary of :code:`<string>`.
        :paramtype kv_tags: dict[str, str]
        :keyword step_tags: Required. Dictionary of :code:`<string>`.
        :paramtype step_tags: dict[str, str]
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword step_properties: Required. Dictionary of :code:`<string>`.
        :paramtype step_properties: dict[str, str]
        :keyword entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
        :paramtype entity_status: str or
         ~index_service_apis.models.IndependentPipelineAnnotationsEntityStatus
        :keyword last_modified_date: Required.
        :paramtype last_modified_date: ~datetime.datetime
        :keyword last_updated_by: Required.
        :paramtype last_updated_by: ~index_service_apis.models.CreatedBy
        :keyword last_run_id: Required.
        :paramtype last_run_id: str
        :keyword pipeline_type: Required.
        :paramtype pipeline_type: str
        :keyword last_run_status_code: Required. Possible values include: "NotStarted", "Running",
         "Failed", "Finished", "Canceled", "Failing", "Queued", "CancelRequested".
        :paramtype last_run_status_code: str or
         ~index_service_apis.models.IndependentPipelineAnnotationsLastRunStatusCode
        :keyword last_run_creation_time: Required.
        :paramtype last_run_creation_time: ~datetime.datetime
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(IndependentPipelineAnnotations, self).__init__(**kwargs)
        self.url = url
        self.kv_tags = kv_tags
        self.step_tags = step_tags
        self.properties = properties
        self.step_properties = step_properties
        self.entity_status = entity_status
        self.last_modified_date = last_modified_date
        self.last_updated_by = last_updated_by
        self.last_run_id = last_run_id
        self.pipeline_type = pipeline_type
        self.last_run_status_code = last_run_status_code
        self.last_run_creation_time = last_run_creation_time
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class IndependentPipelineProperties(msrest.serialization.Model):
    """IndependentPipelineProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, id: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(IndependentPipelineProperties, self).__init__(**kwargs)
        self.id = id
        self.creation_context = creation_context


class IndependentpipelinesUnversionedEntitiesResponse(msrest.serialization.Model):
    """IndependentpipelinesUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.IndependentpipelinesUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[IndependentpipelinesUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["IndependentpipelinesUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.IndependentpipelinesUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(IndependentpipelinesUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class IndependentpipelinesUnversionedEntity(msrest.serialization.Model):
    """IndependentpipelinesUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.IndependentpipelinesUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.IndependentPipelineAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.IndependentPipelineProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "IndependentPipelineAnnotations"},
        "properties": {"key": "properties", "type": "IndependentPipelineProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "IndependentpipelinesUnversionedEntityKind"],
        annotations: "IndependentPipelineAnnotations",
        properties: "IndependentPipelineProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.IndependentpipelinesUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.IndependentPipelineAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.IndependentPipelineProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(IndependentpipelinesUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class IndexAnnotations(msrest.serialization.Model):
    """IndexAnnotations.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar archived:
    :vartype archived: bool
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        additional_properties: Optional[Dict[str, Any]] = None,
        archived: Optional[bool] = None,
        tags: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        :keyword archived:
        :paramtype archived: bool
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(IndexAnnotations, self).__init__(**kwargs)
        self.additional_properties = additional_properties
        self.archived = archived
        self.tags = tags


class IndexColumn(msrest.serialization.Model):
    """IndexColumn.

    All required parameters must be populated in order to send to Azure.

    :ivar data_type: Required. Possible values include: "String", "Integer", "Long", "Float",
     "Double", "Binary", "Datetime", "Boolean".
    :vartype data_type: str or ~index_service_apis.models.IndexColumnDataType
    :ivar column_name: Required.
    :vartype column_name: str
    """

    _validation = {
        "data_type": {"required": True},
        "column_name": {"required": True},
    }

    _attribute_map = {
        "data_type": {"key": "dataType", "type": "str"},
        "column_name": {"key": "columnName", "type": "str"},
    }

    def __init__(self, *, data_type: Union[str, "IndexColumnDataType"], column_name: str, **kwargs):
        """
        :keyword data_type: Required. Possible values include: "String", "Integer", "Long", "Float",
         "Double", "Binary", "Datetime", "Boolean".
        :paramtype data_type: str or ~index_service_apis.models.IndexColumnDataType
        :keyword column_name: Required.
        :paramtype column_name: str
        """
        super(IndexColumn, self).__init__(**kwargs)
        self.data_type = data_type
        self.column_name = column_name


class IndexColumnDto(msrest.serialization.Model):
    """IndexColumnDto.

    All required parameters must be populated in order to send to Azure.

    :ivar type: Required. Possible values include: "string", "integer", "long", "float", "double",
     "binary", "datetime", "boolean".
    :vartype type: str or ~index_service_apis.models.IndexColumnDtoType
    :ivar name: Required.
    :vartype name: str
    """

    _validation = {
        "type": {"required": True},
        "name": {"required": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "name": {"key": "name", "type": "str"},
    }

    def __init__(self, *, type: Union[str, "IndexColumnDtoType"], name: str, **kwargs):
        """
        :keyword type: Required. Possible values include: "string", "integer", "long", "float",
         "double", "binary", "datetime", "boolean".
        :paramtype type: str or ~index_service_apis.models.IndexColumnDtoType
        :keyword name: Required.
        :paramtype name: str
        """
        super(IndexColumnDto, self).__init__(**kwargs)
        self.type = type
        self.name = name


class IndexedErrorResponse(msrest.serialization.Model):
    """IndexedErrorResponse.

    All required parameters must be populated in order to send to Azure.

    :ivar code: Required.
    :vartype code: str
    :ivar error_code_hierarchy: Required.
    :vartype error_code_hierarchy: str
    :ivar message: Required.
    :vartype message: str
    :ivar time: Required.
    :vartype time: ~datetime.datetime
    :ivar component_name: Required.
    :vartype component_name: str
    :ivar severity: Required.
    :vartype severity: int
    :ivar details_uri: Required.
    :vartype details_uri: str
    :ivar reference_code: Required.
    :vartype reference_code: str
    """

    _validation = {
        "code": {"required": True},
        "error_code_hierarchy": {"required": True},
        "message": {"required": True},
        "time": {"required": True},
        "component_name": {"required": True},
        "severity": {"required": True},
        "details_uri": {"required": True},
        "reference_code": {"required": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "error_code_hierarchy": {"key": "errorCodeHierarchy", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "time": {"key": "time", "type": "iso-8601"},
        "component_name": {"key": "componentName", "type": "str"},
        "severity": {"key": "severity", "type": "int"},
        "details_uri": {"key": "detailsUri", "type": "str"},
        "reference_code": {"key": "referenceCode", "type": "str"},
    }

    def __init__(
        self,
        *,
        code: str,
        error_code_hierarchy: str,
        message: str,
        time: datetime.datetime,
        component_name: str,
        severity: int,
        details_uri: str,
        reference_code: str,
        **kwargs
    ):
        """
        :keyword code: Required.
        :paramtype code: str
        :keyword error_code_hierarchy: Required.
        :paramtype error_code_hierarchy: str
        :keyword message: Required.
        :paramtype message: str
        :keyword time: Required.
        :paramtype time: ~datetime.datetime
        :keyword component_name: Required.
        :paramtype component_name: str
        :keyword severity: Required.
        :paramtype severity: int
        :keyword details_uri: Required.
        :paramtype details_uri: str
        :keyword reference_code: Required.
        :paramtype reference_code: str
        """
        super(IndexedErrorResponse, self).__init__(**kwargs)
        self.code = code
        self.error_code_hierarchy = error_code_hierarchy
        self.message = message
        self.time = time
        self.component_name = component_name
        self.severity = severity
        self.details_uri = details_uri
        self.reference_code = reference_code


class IndexEntitiesLocalOperationRequest(msrest.serialization.Model):
    """IndexEntitiesLocalOperationRequest.

    :ivar get_all_entities:
    :vartype get_all_entities: bool
    :ivar group:
    :vartype group: list[~index_service_apis.models.IndexEntitiesRequestGroup]
    :ivar highlight_options:
    :vartype highlight_options: ~index_service_apis.models.HighlightOptions
    :ivar search_builder:
    :vartype search_builder: str
    :ivar search_mode:
    :vartype search_mode: str
    :ivar free_text_search:
    :vartype free_text_search: str
    :ivar free_text_search_columns:
    :vartype free_text_search_columns: list[~index_service_apis.models.FreeTextSearchColumn]
    :ivar filters:
    :vartype filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
    :ivar asset_resource_filters:
    :vartype asset_resource_filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
    :ivar order:
    :vartype order: list[~index_service_apis.models.IndexEntitiesRequestOrder]
    :ivar page_size:
    :vartype page_size: int
    :ivar skip:
    :vartype skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar include_total_result_count:
    :vartype include_total_result_count: bool
    :ivar resources:
    :vartype resources: list[~index_service_apis.models.ResourceInformation]
    :ivar cmk_fanout:
    :vartype cmk_fanout: bool
    :ivar include_fanout_data:
    :vartype include_fanout_data: bool
    :ivar shard_id_to_shard_skips: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype shard_id_to_shard_skips: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    """

    _attribute_map = {
        "get_all_entities": {"key": "getAllEntities", "type": "bool"},
        "group": {"key": "group", "type": "[IndexEntitiesRequestGroup]"},
        "highlight_options": {"key": "highlightOptions", "type": "HighlightOptions"},
        "search_builder": {"key": "searchBuilder", "type": "str"},
        "search_mode": {"key": "searchMode", "type": "str"},
        "free_text_search": {"key": "freeTextSearch", "type": "str"},
        "free_text_search_columns": {"key": "freeTextSearchColumns", "type": "[FreeTextSearchColumn]"},
        "filters": {"key": "filters", "type": "[IndexEntitiesRequestFilter]"},
        "asset_resource_filters": {"key": "assetResourceFilters", "type": "[IndexEntitiesRequestFilter]"},
        "order": {"key": "order", "type": "[IndexEntitiesRequestOrder]"},
        "page_size": {"key": "pageSize", "type": "int"},
        "skip": {"key": "skip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "include_total_result_count": {"key": "includeTotalResultCount", "type": "bool"},
        "resources": {"key": "resources", "type": "[ResourceInformation]"},
        "cmk_fanout": {"key": "cmkFanout", "type": "bool"},
        "include_fanout_data": {"key": "includeFanoutData", "type": "bool"},
        "shard_id_to_shard_skips": {"key": "shardIdToShardSkips", "type": "{SingleShardFanoutData}"},
    }

    def __init__(
        self,
        *,
        get_all_entities: Optional[bool] = None,
        group: Optional[List["IndexEntitiesRequestGroup"]] = None,
        highlight_options: Optional["HighlightOptions"] = None,
        search_builder: Optional[str] = None,
        search_mode: Optional[str] = None,
        free_text_search: Optional[str] = None,
        free_text_search_columns: Optional[List["FreeTextSearchColumn"]] = None,
        filters: Optional[List["IndexEntitiesRequestFilter"]] = None,
        asset_resource_filters: Optional[List["IndexEntitiesRequestFilter"]] = None,
        order: Optional[List["IndexEntitiesRequestOrder"]] = None,
        page_size: Optional[int] = None,
        skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        include_total_result_count: Optional[bool] = None,
        resources: Optional[List["ResourceInformation"]] = None,
        cmk_fanout: Optional[bool] = None,
        include_fanout_data: Optional[bool] = None,
        shard_id_to_shard_skips: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        **kwargs
    ):
        """
        :keyword get_all_entities:
        :paramtype get_all_entities: bool
        :keyword group:
        :paramtype group: list[~index_service_apis.models.IndexEntitiesRequestGroup]
        :keyword highlight_options:
        :paramtype highlight_options: ~index_service_apis.models.HighlightOptions
        :keyword search_builder:
        :paramtype search_builder: str
        :keyword search_mode:
        :paramtype search_mode: str
        :keyword free_text_search:
        :paramtype free_text_search: str
        :keyword free_text_search_columns:
        :paramtype free_text_search_columns: list[~index_service_apis.models.FreeTextSearchColumn]
        :keyword filters:
        :paramtype filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
        :keyword asset_resource_filters:
        :paramtype asset_resource_filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
        :keyword order:
        :paramtype order: list[~index_service_apis.models.IndexEntitiesRequestOrder]
        :keyword page_size:
        :paramtype page_size: int
        :keyword skip:
        :paramtype skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword include_total_result_count:
        :paramtype include_total_result_count: bool
        :keyword resources:
        :paramtype resources: list[~index_service_apis.models.ResourceInformation]
        :keyword cmk_fanout:
        :paramtype cmk_fanout: bool
        :keyword include_fanout_data:
        :paramtype include_fanout_data: bool
        :keyword shard_id_to_shard_skips: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype shard_id_to_shard_skips: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        """
        super(IndexEntitiesLocalOperationRequest, self).__init__(**kwargs)
        self.get_all_entities = get_all_entities
        self.group = group
        self.highlight_options = highlight_options
        self.search_builder = search_builder
        self.search_mode = search_mode
        self.free_text_search = free_text_search
        self.free_text_search_columns = free_text_search_columns
        self.filters = filters
        self.asset_resource_filters = asset_resource_filters
        self.order = order
        self.page_size = page_size
        self.skip = skip
        self.continuation_token = continuation_token
        self.include_total_result_count = include_total_result_count
        self.resources = resources
        self.cmk_fanout = cmk_fanout
        self.include_fanout_data = include_fanout_data
        self.shard_id_to_shard_skips = shard_id_to_shard_skips


class IndexEntitiesRequest(msrest.serialization.Model):
    """IndexEntitiesRequest.

    :ivar highlight_options:
    :vartype highlight_options: ~index_service_apis.models.HighlightOptions
    :ivar search_builder:
    :vartype search_builder: str
    :ivar search_mode:
    :vartype search_mode: str
    :ivar free_text_search:
    :vartype free_text_search: str
    :ivar free_text_search_columns:
    :vartype free_text_search_columns: list[~index_service_apis.models.FreeTextSearchColumn]
    :ivar filters:
    :vartype filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
    :ivar asset_resource_filters:
    :vartype asset_resource_filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
    :ivar order:
    :vartype order: list[~index_service_apis.models.IndexEntitiesRequestOrder]
    :ivar page_size:
    :vartype page_size: int
    :ivar skip:
    :vartype skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar include_total_result_count:
    :vartype include_total_result_count: bool
    :ivar resources:
    :vartype resources: list[~index_service_apis.models.ResourceInformation]
    :ivar cmk_fanout:
    :vartype cmk_fanout: bool
    :ivar include_fanout_data:
    :vartype include_fanout_data: bool
    :ivar shard_id_to_shard_skips: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype shard_id_to_shard_skips: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    """

    _attribute_map = {
        "highlight_options": {"key": "highlightOptions", "type": "HighlightOptions"},
        "search_builder": {"key": "searchBuilder", "type": "str"},
        "search_mode": {"key": "searchMode", "type": "str"},
        "free_text_search": {"key": "freeTextSearch", "type": "str"},
        "free_text_search_columns": {"key": "freeTextSearchColumns", "type": "[FreeTextSearchColumn]"},
        "filters": {"key": "filters", "type": "[IndexEntitiesRequestFilter]"},
        "asset_resource_filters": {"key": "assetResourceFilters", "type": "[IndexEntitiesRequestFilter]"},
        "order": {"key": "order", "type": "[IndexEntitiesRequestOrder]"},
        "page_size": {"key": "pageSize", "type": "int"},
        "skip": {"key": "skip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "include_total_result_count": {"key": "includeTotalResultCount", "type": "bool"},
        "resources": {"key": "resources", "type": "[ResourceInformation]"},
        "cmk_fanout": {"key": "cmkFanout", "type": "bool"},
        "include_fanout_data": {"key": "includeFanoutData", "type": "bool"},
        "shard_id_to_shard_skips": {"key": "shardIdToShardSkips", "type": "{SingleShardFanoutData}"},
    }

    def __init__(
        self,
        *,
        highlight_options: Optional["HighlightOptions"] = None,
        search_builder: Optional[str] = None,
        search_mode: Optional[str] = None,
        free_text_search: Optional[str] = None,
        free_text_search_columns: Optional[List["FreeTextSearchColumn"]] = None,
        filters: Optional[List["IndexEntitiesRequestFilter"]] = None,
        asset_resource_filters: Optional[List["IndexEntitiesRequestFilter"]] = None,
        order: Optional[List["IndexEntitiesRequestOrder"]] = None,
        page_size: Optional[int] = None,
        skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        include_total_result_count: Optional[bool] = None,
        resources: Optional[List["ResourceInformation"]] = None,
        cmk_fanout: Optional[bool] = None,
        include_fanout_data: Optional[bool] = None,
        shard_id_to_shard_skips: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        **kwargs
    ):
        """
        :keyword highlight_options:
        :paramtype highlight_options: ~index_service_apis.models.HighlightOptions
        :keyword search_builder:
        :paramtype search_builder: str
        :keyword search_mode:
        :paramtype search_mode: str
        :keyword free_text_search:
        :paramtype free_text_search: str
        :keyword free_text_search_columns:
        :paramtype free_text_search_columns: list[~index_service_apis.models.FreeTextSearchColumn]
        :keyword filters:
        :paramtype filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
        :keyword asset_resource_filters:
        :paramtype asset_resource_filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
        :keyword order:
        :paramtype order: list[~index_service_apis.models.IndexEntitiesRequestOrder]
        :keyword page_size:
        :paramtype page_size: int
        :keyword skip:
        :paramtype skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword include_total_result_count:
        :paramtype include_total_result_count: bool
        :keyword resources:
        :paramtype resources: list[~index_service_apis.models.ResourceInformation]
        :keyword cmk_fanout:
        :paramtype cmk_fanout: bool
        :keyword include_fanout_data:
        :paramtype include_fanout_data: bool
        :keyword shard_id_to_shard_skips: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype shard_id_to_shard_skips: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        """
        super(IndexEntitiesRequest, self).__init__(**kwargs)
        self.highlight_options = highlight_options
        self.search_builder = search_builder
        self.search_mode = search_mode
        self.free_text_search = free_text_search
        self.free_text_search_columns = free_text_search_columns
        self.filters = filters
        self.asset_resource_filters = asset_resource_filters
        self.order = order
        self.page_size = page_size
        self.skip = skip
        self.continuation_token = continuation_token
        self.include_total_result_count = include_total_result_count
        self.resources = resources
        self.cmk_fanout = cmk_fanout
        self.include_fanout_data = include_fanout_data
        self.shard_id_to_shard_skips = shard_id_to_shard_skips


class IndexEntitiesRequestFilter(msrest.serialization.Model):
    """IndexEntitiesRequestFilter.

    :ivar field:
    :vartype field: str
    :ivar operator:
    :vartype operator: str
    :ivar values:
    :vartype values: list[str]
    """

    _attribute_map = {
        "field": {"key": "field", "type": "str"},
        "operator": {"key": "operator", "type": "str"},
        "values": {"key": "values", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        field: Optional[str] = None,
        operator: Optional[str] = None,
        values: Optional[List[str]] = None,
        **kwargs
    ):
        """
        :keyword field:
        :paramtype field: str
        :keyword operator:
        :paramtype operator: str
        :keyword values:
        :paramtype values: list[str]
        """
        super(IndexEntitiesRequestFilter, self).__init__(**kwargs)
        self.field = field
        self.operator = operator
        self.values = values


class IndexEntitiesRequestGroup(msrest.serialization.Model):
    """IndexEntitiesRequestGroup.

    :ivar field:
    :vartype field: str
    """

    _attribute_map = {
        "field": {"key": "field", "type": "str"},
    }

    def __init__(self, *, field: Optional[str] = None, **kwargs):
        """
        :keyword field:
        :paramtype field: str
        """
        super(IndexEntitiesRequestGroup, self).__init__(**kwargs)
        self.field = field


class IndexEntitiesRequestOrder(msrest.serialization.Model):
    """IndexEntitiesRequestOrder.

    :ivar field:
    :vartype field: str
    :ivar direction: Possible values include: "Asc", "Desc".
    :vartype direction: str or ~index_service_apis.models.IndexEntitiesRequestOrderDirection
    """

    _attribute_map = {
        "field": {"key": "field", "type": "str"},
        "direction": {"key": "direction", "type": "str"},
    }

    def __init__(
        self,
        *,
        field: Optional[str] = None,
        direction: Optional[Union[str, "IndexEntitiesRequestOrderDirection"]] = None,
        **kwargs
    ):
        """
        :keyword field:
        :paramtype field: str
        :keyword direction: Possible values include: "Asc", "Desc".
        :paramtype direction: str or ~index_service_apis.models.IndexEntitiesRequestOrderDirection
        """
        super(IndexEntitiesRequestOrder, self).__init__(**kwargs)
        self.field = field
        self.direction = direction


class IndexEntitiesResponse(msrest.serialization.Model):
    """IndexEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.IndexEntityResponse]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[IndexEntityResponse]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["IndexEntityResponse"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.IndexEntityResponse]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(IndexEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class IndexEntityContainerMetadata(msrest.serialization.Model):
    """IndexEntityContainerMetadata.

    :ivar resource_id:
    :vartype resource_id: str
    :ivar subscription_id:
    :vartype subscription_id: str
    :ivar resource_group:
    :vartype resource_group: str
    :ivar resource_name:
    :vartype resource_name: str
    :ivar entity_container_type:
    :vartype entity_container_type: str
    :ivar regions:
    :vartype regions: list[~index_service_apis.models.ResourceRegion]
    :ivar tenant_id:
    :vartype tenant_id: str
    :ivar immutable_resource_id:
    :vartype immutable_resource_id: str
    :ivar is_public_resource:
    :vartype is_public_resource: bool
    """

    _attribute_map = {
        "resource_id": {"key": "resourceId", "type": "str"},
        "subscription_id": {"key": "subscriptionId", "type": "str"},
        "resource_group": {"key": "resourceGroup", "type": "str"},
        "resource_name": {"key": "resourceName", "type": "str"},
        "entity_container_type": {"key": "entityContainerType", "type": "str"},
        "regions": {"key": "regions", "type": "[ResourceRegion]"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "immutable_resource_id": {"key": "immutableResourceId", "type": "str"},
        "is_public_resource": {"key": "isPublicResource", "type": "bool"},
    }

    def __init__(
        self,
        *,
        resource_id: Optional[str] = None,
        subscription_id: Optional[str] = None,
        resource_group: Optional[str] = None,
        resource_name: Optional[str] = None,
        entity_container_type: Optional[str] = None,
        regions: Optional[List["ResourceRegion"]] = None,
        tenant_id: Optional[str] = None,
        immutable_resource_id: Optional[str] = None,
        is_public_resource: Optional[bool] = None,
        **kwargs
    ):
        """
        :keyword resource_id:
        :paramtype resource_id: str
        :keyword subscription_id:
        :paramtype subscription_id: str
        :keyword resource_group:
        :paramtype resource_group: str
        :keyword resource_name:
        :paramtype resource_name: str
        :keyword entity_container_type:
        :paramtype entity_container_type: str
        :keyword regions:
        :paramtype regions: list[~index_service_apis.models.ResourceRegion]
        :keyword tenant_id:
        :paramtype tenant_id: str
        :keyword immutable_resource_id:
        :paramtype immutable_resource_id: str
        :keyword is_public_resource:
        :paramtype is_public_resource: bool
        """
        super(IndexEntityContainerMetadata, self).__init__(**kwargs)
        self.resource_id = resource_id
        self.subscription_id = subscription_id
        self.resource_group = resource_group
        self.resource_name = resource_name
        self.entity_container_type = entity_container_type
        self.regions = regions
        self.tenant_id = tenant_id
        self.immutable_resource_id = immutable_resource_id
        self.is_public_resource = is_public_resource


class IndexEntityResponse(msrest.serialization.Model):
    """IndexEntityResponse.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar relevancy_score:
    :vartype relevancy_score: float
    :ivar entity_resource_name:
    :vartype entity_resource_name: str
    :ivar highlights: Dictionary of
     <components·jiilq·schemas·indexentityresponse·properties·highlights·additionalproperties>.
    :vartype highlights: dict[str, list[str]]
    :ivar usage:
    :vartype usage: ~index_service_apis.models.Usage
    :ivar schema_id:
    :vartype schema_id: str
    :ivar entity_id:
    :vartype entity_id: str
    :ivar kind: Possible values include: "Invalid", "LineageRoot", "Versioned", "Unversioned".
    :vartype kind: str or ~index_service_apis.models.EntityKind
    :ivar annotations:
    :vartype annotations: ~index_service_apis.models.IndexAnnotations
    :ivar properties:
    :vartype properties: ~index_service_apis.models.IndexProperties
    :ivar internal: Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence:
    :vartype update_sequence: long
    :ivar type:
    :vartype type: str
    :ivar version:
    :vartype version: str
    :ivar entity_container_id:
    :vartype entity_container_id: str
    :ivar entity_object_id:
    :vartype entity_object_id: str
    :ivar resource_type:
    :vartype resource_type: str
    :ivar relationships:
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "version": {"readonly": True},
        "entity_container_id": {"readonly": True},
        "entity_object_id": {"readonly": True},
        "resource_type": {"readonly": True},
    }

    _attribute_map = {
        "relevancy_score": {"key": "relevancyScore", "type": "float"},
        "entity_resource_name": {"key": "entityResourceName", "type": "str"},
        "highlights": {"key": "highlights", "type": "{[str]}"},
        "usage": {"key": "usage", "type": "Usage"},
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "IndexAnnotations"},
        "properties": {"key": "properties", "type": "IndexProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "long"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        relevancy_score: Optional[float] = None,
        entity_resource_name: Optional[str] = None,
        highlights: Optional[Dict[str, List[str]]] = None,
        usage: Optional["Usage"] = None,
        schema_id: Optional[str] = None,
        entity_id: Optional[str] = None,
        kind: Optional[Union[str, "EntityKind"]] = None,
        annotations: Optional["IndexAnnotations"] = None,
        properties: Optional["IndexProperties"] = None,
        internal: Optional[Dict[str, Any]] = None,
        update_sequence: Optional[int] = None,
        type: Optional[str] = None,
        relationships: Optional[List["Relationship"]] = None,
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword relevancy_score:
        :paramtype relevancy_score: float
        :keyword entity_resource_name:
        :paramtype entity_resource_name: str
        :keyword highlights: Dictionary of
         <components·jiilq·schemas·indexentityresponse·properties·highlights·additionalproperties>.
        :paramtype highlights: dict[str, list[str]]
        :keyword usage:
        :paramtype usage: ~index_service_apis.models.Usage
        :keyword schema_id:
        :paramtype schema_id: str
        :keyword entity_id:
        :paramtype entity_id: str
        :keyword kind: Possible values include: "Invalid", "LineageRoot", "Versioned", "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.EntityKind
        :keyword annotations:
        :paramtype annotations: ~index_service_apis.models.IndexAnnotations
        :keyword properties:
        :paramtype properties: ~index_service_apis.models.IndexProperties
        :keyword internal: Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence:
        :paramtype update_sequence: long
        :keyword type:
        :paramtype type: str
        :keyword relationships:
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(IndexEntityResponse, self).__init__(**kwargs)
        self.relevancy_score = relevancy_score
        self.entity_resource_name = entity_resource_name
        self.highlights = highlights
        self.usage = usage
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = None
        self.entity_container_id = None
        self.entity_object_id = None
        self.resource_type = None
        self.relationships = relationships
        self.asset_id = asset_id


class IndexProperties(msrest.serialization.Model):
    """IndexProperties.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar updated_time:
    :vartype updated_time: ~datetime.datetime
    :ivar creation_context:
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "updated_time": {"key": "updatedTime", "type": "iso-8601"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(
        self,
        *,
        additional_properties: Optional[Dict[str, Any]] = None,
        updated_time: Optional[datetime.datetime] = None,
        creation_context: Optional["CreationContext"] = None,
        **kwargs
    ):
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        :keyword updated_time:
        :paramtype updated_time: ~datetime.datetime
        :keyword creation_context:
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(IndexProperties, self).__init__(**kwargs)
        self.additional_properties = additional_properties
        self.updated_time = updated_time
        self.creation_context = creation_context


class IndexResourceDiscoveryResponseItem(msrest.serialization.Model):
    """IndexResourceDiscoveryResponseItem.

    :ivar name:
    :vartype name: str
    :ivar resource_id:
    :vartype resource_id: str
    :ivar resource_type:
    :vartype resource_type: str
    :ivar region:
    :vartype region: str
    :ivar regions:
    :vartype regions: list[~index_service_apis.models.ResourceRegion]
    :ivar subscription_id:
    :vartype subscription_id: str
    :ivar resource_group_name:
    :vartype resource_group_name: str
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar is_customer_managed:
    :vartype is_customer_managed: bool
    :ivar is_private_link_resource:
    :vartype is_private_link_resource: bool
    :ivar is_private_link_resource_behind_vnet:
    :vartype is_private_link_resource_behind_vnet: bool
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "region": {"key": "region", "type": "str"},
        "regions": {"key": "regions", "type": "[ResourceRegion]"},
        "subscription_id": {"key": "subscriptionId", "type": "str"},
        "resource_group_name": {"key": "resourceGroupName", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_customer_managed": {"key": "isCustomerManaged", "type": "bool"},
        "is_private_link_resource": {"key": "isPrivateLinkResource", "type": "bool"},
        "is_private_link_resource_behind_vnet": {"key": "isPrivateLinkResourceBehindVnet", "type": "bool"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        resource_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        region: Optional[str] = None,
        regions: Optional[List["ResourceRegion"]] = None,
        subscription_id: Optional[str] = None,
        resource_group_name: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        is_customer_managed: Optional[bool] = None,
        is_private_link_resource: Optional[bool] = None,
        is_private_link_resource_behind_vnet: Optional[bool] = None,
        **kwargs
    ):
        """
        :keyword name:
        :paramtype name: str
        :keyword resource_id:
        :paramtype resource_id: str
        :keyword resource_type:
        :paramtype resource_type: str
        :keyword region:
        :paramtype region: str
        :keyword regions:
        :paramtype regions: list[~index_service_apis.models.ResourceRegion]
        :keyword subscription_id:
        :paramtype subscription_id: str
        :keyword resource_group_name:
        :paramtype resource_group_name: str
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword is_customer_managed:
        :paramtype is_customer_managed: bool
        :keyword is_private_link_resource:
        :paramtype is_private_link_resource: bool
        :keyword is_private_link_resource_behind_vnet:
        :paramtype is_private_link_resource_behind_vnet: bool
        """
        super(IndexResourceDiscoveryResponseItem, self).__init__(**kwargs)
        self.name = name
        self.resource_id = resource_id
        self.resource_type = resource_type
        self.region = region
        self.regions = regions
        self.subscription_id = subscription_id
        self.resource_group_name = resource_group_name
        self.tags = tags
        self.is_customer_managed = is_customer_managed
        self.is_private_link_resource = is_private_link_resource
        self.is_private_link_resource_behind_vnet = is_private_link_resource_behind_vnet


class IndexResourceDiscoveryResponseItemPaginatedResult(msrest.serialization.Model):
    """IndexResourceDiscoveryResponseItemPaginatedResult.

    :ivar value:
    :vartype value: list[~index_service_apis.models.IndexResourceDiscoveryResponseItem]
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar next_link:
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[IndexResourceDiscoveryResponseItem]"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["IndexResourceDiscoveryResponseItem"]] = None,
        continuation_token: Optional[str] = None,
        next_link: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword value:
        :paramtype value: list[~index_service_apis.models.IndexResourceDiscoveryResponseItem]
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword next_link:
        :paramtype next_link: str
        """
        super(IndexResourceDiscoveryResponseItemPaginatedResult, self).__init__(**kwargs)
        self.value = value
        self.continuation_token = continuation_token
        self.next_link = next_link


class InnerErrorResponse(msrest.serialization.Model):
    """InnerErrorResponse.

    :ivar code:
    :vartype code: str
    :ivar inner_error:
    :vartype inner_error: ~index_service_apis.models.InnerErrorResponse
    """

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "inner_error": {"key": "innerError", "type": "InnerErrorResponse"},
    }

    def __init__(self, *, code: Optional[str] = None, inner_error: Optional["InnerErrorResponse"] = None, **kwargs):
        """
        :keyword code:
        :paramtype code: str
        :keyword inner_error:
        :paramtype inner_error: ~index_service_apis.models.InnerErrorResponse
        """
        super(InnerErrorResponse, self).__init__(**kwargs)
        self.code = code
        self.inner_error = inner_error


class InputNameAndDataTypeIdsList(msrest.serialization.Model):
    """InputNameAndDataTypeIdsList.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required.
    :vartype name: str
    :ivar data_type_ids_list: Required.
    :vartype data_type_ids_list: list[str]
    :ivar is_optional: Required.
    :vartype is_optional: bool
    :ivar description: Required.
    :vartype description: str
    :ivar label: Required.
    :vartype label: str
    """

    _validation = {
        "name": {"required": True},
        "data_type_ids_list": {"required": True},
        "is_optional": {"required": True},
        "description": {"required": True},
        "label": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "data_type_ids_list": {"key": "dataTypeIdsList", "type": "[str]"},
        "is_optional": {"key": "isOptional", "type": "bool"},
        "description": {"key": "description", "type": "str"},
        "label": {"key": "label", "type": "str"},
    }

    def __init__(
        self, *, name: str, data_type_ids_list: List[str], is_optional: bool, description: str, label: str, **kwargs
    ):
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword data_type_ids_list: Required.
        :paramtype data_type_ids_list: list[str]
        :keyword is_optional: Required.
        :paramtype is_optional: bool
        :keyword description: Required.
        :paramtype description: str
        :keyword label: Required.
        :paramtype label: str
        """
        super(InputNameAndDataTypeIdsList, self).__init__(**kwargs)
        self.name = name
        self.data_type_ids_list = data_type_ids_list
        self.is_optional = is_optional
        self.description = description
        self.label = label


class InternalUxPresenceWarmUpRequest(msrest.serialization.Model):
    """InternalUxPresenceWarmUpRequest.

    :ivar resources:
    :vartype resources: list[~index_service_apis.models.UxPresenceResource]
    :ivar source_ip:
    :vartype source_ip: str
    :ivar user_tenant_id:
    :vartype user_tenant_id: str
    :ivar user_object_id:
    :vartype user_object_id: str
    """

    _attribute_map = {
        "resources": {"key": "resources", "type": "[UxPresenceResource]"},
        "source_ip": {"key": "sourceIp", "type": "str"},
        "user_tenant_id": {"key": "userTenantId", "type": "str"},
        "user_object_id": {"key": "userObjectId", "type": "str"},
    }

    def __init__(
        self,
        *,
        resources: Optional[List["UxPresenceResource"]] = None,
        source_ip: Optional[str] = None,
        user_tenant_id: Optional[str] = None,
        user_object_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword resources:
        :paramtype resources: list[~index_service_apis.models.UxPresenceResource]
        :keyword source_ip:
        :paramtype source_ip: str
        :keyword user_tenant_id:
        :paramtype user_tenant_id: str
        :keyword user_object_id:
        :paramtype user_object_id: str
        """
        super(InternalUxPresenceWarmUpRequest, self).__init__(**kwargs)
        self.resources = resources
        self.source_ip = source_ip
        self.user_tenant_id = user_tenant_id
        self.user_object_id = user_object_id


class JobCost(msrest.serialization.Model):
    """JobCost.

    All required parameters must be populated in order to send to Azure.

    :ivar charged_cpu_core_seconds: Required.
    :vartype charged_cpu_core_seconds: float
    :ivar charged_cpu_memory_megabyte_seconds: Required.
    :vartype charged_cpu_memory_megabyte_seconds: float
    :ivar charged_gpu_seconds: Required.
    :vartype charged_gpu_seconds: float
    :ivar charged_node_utilization_seconds: Required.
    :vartype charged_node_utilization_seconds: float
    """

    _validation = {
        "charged_cpu_core_seconds": {"required": True},
        "charged_cpu_memory_megabyte_seconds": {"required": True},
        "charged_gpu_seconds": {"required": True},
        "charged_node_utilization_seconds": {"required": True},
    }

    _attribute_map = {
        "charged_cpu_core_seconds": {"key": "chargedCpuCoreSeconds", "type": "float"},
        "charged_cpu_memory_megabyte_seconds": {"key": "chargedCpuMemoryMegabyteSeconds", "type": "float"},
        "charged_gpu_seconds": {"key": "chargedGpuSeconds", "type": "float"},
        "charged_node_utilization_seconds": {"key": "chargedNodeUtilizationSeconds", "type": "float"},
    }

    def __init__(
        self,
        *,
        charged_cpu_core_seconds: float,
        charged_cpu_memory_megabyte_seconds: float,
        charged_gpu_seconds: float,
        charged_node_utilization_seconds: float,
        **kwargs
    ):
        """
        :keyword charged_cpu_core_seconds: Required.
        :paramtype charged_cpu_core_seconds: float
        :keyword charged_cpu_memory_megabyte_seconds: Required.
        :paramtype charged_cpu_memory_megabyte_seconds: float
        :keyword charged_gpu_seconds: Required.
        :paramtype charged_gpu_seconds: float
        :keyword charged_node_utilization_seconds: Required.
        :paramtype charged_node_utilization_seconds: float
        """
        super(JobCost, self).__init__(**kwargs)
        self.charged_cpu_core_seconds = charged_cpu_core_seconds
        self.charged_cpu_memory_megabyte_seconds = charged_cpu_memory_megabyte_seconds
        self.charged_gpu_seconds = charged_gpu_seconds
        self.charged_node_utilization_seconds = charged_node_utilization_seconds


class MaterializationComputeResource(msrest.serialization.Model):
    """MaterializationComputeResource.

    All required parameters must be populated in order to send to Azure.

    :ivar instance_type: Required.
    :vartype instance_type: str
    """

    _validation = {
        "instance_type": {"required": True},
    }

    _attribute_map = {
        "instance_type": {"key": "instanceType", "type": "str"},
    }

    def __init__(self, *, instance_type: str, **kwargs):
        """
        :keyword instance_type: Required.
        :paramtype instance_type: str
        """
        super(MaterializationComputeResource, self).__init__(**kwargs)
        self.instance_type = instance_type


class MaterializationSettings(msrest.serialization.Model):
    """MaterializationSettings.

    All required parameters must be populated in order to send to Azure.

    :ivar materialization_store_type: Required. Possible values include: "None", "Online",
     "Offline", "OnlineAndOffline".
    :vartype materialization_store_type: str or
     ~index_service_apis.models.MaterializationSettingsMaterializationStoreType
    :ivar schedule: Required.
    :vartype schedule: ~index_service_apis.models.Recurrence
    :ivar notification:
    :vartype notification: ~index_service_apis.models.NotificationSetting
    :ivar resource: Required.
    :vartype resource: ~index_service_apis.models.MaterializationComputeResource
    :ivar spark_configuration: Required. Dictionary of :code:`<string>`.
    :vartype spark_configuration: dict[str, str]
    """

    _validation = {
        "materialization_store_type": {"required": True},
        "schedule": {"required": True},
        "resource": {"required": True},
        "spark_configuration": {"required": True},
    }

    _attribute_map = {
        "materialization_store_type": {"key": "materializationStoreType", "type": "str"},
        "schedule": {"key": "schedule", "type": "Recurrence"},
        "notification": {"key": "notification", "type": "NotificationSetting"},
        "resource": {"key": "resource", "type": "MaterializationComputeResource"},
        "spark_configuration": {"key": "sparkConfiguration", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        materialization_store_type: Union[str, "MaterializationSettingsMaterializationStoreType"],
        schedule: "Recurrence",
        resource: "MaterializationComputeResource",
        spark_configuration: Dict[str, str],
        notification: Optional["NotificationSetting"] = None,
        **kwargs
    ):
        """
        :keyword materialization_store_type: Required. Possible values include: "None", "Online",
         "Offline", "OnlineAndOffline".
        :paramtype materialization_store_type: str or
         ~index_service_apis.models.MaterializationSettingsMaterializationStoreType
        :keyword schedule: Required.
        :paramtype schedule: ~index_service_apis.models.Recurrence
        :keyword notification:
        :paramtype notification: ~index_service_apis.models.NotificationSetting
        :keyword resource: Required.
        :paramtype resource: ~index_service_apis.models.MaterializationComputeResource
        :keyword spark_configuration: Required. Dictionary of :code:`<string>`.
        :paramtype spark_configuration: dict[str, str]
        """
        super(MaterializationSettings, self).__init__(**kwargs)
        self.materialization_store_type = materialization_store_type
        self.schedule = schedule
        self.notification = notification
        self.resource = resource
        self.spark_configuration = spark_configuration


class ModelAnnotations(msrest.serialization.Model):
    """ModelAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar datasets: Required.
    :vartype datasets: list[~index_service_apis.models.DatasetReference]
    :ivar sample_input_data: Required.
    :vartype sample_input_data: str
    :ivar sample_output_data: Required.
    :vartype sample_output_data: str
    :ivar resource_requirements: Required.
    :vartype resource_requirements: ~index_service_apis.models.ContainerResourceRequirements
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "datasets": {"required": True},
        "sample_input_data": {"required": True},
        "sample_output_data": {"required": True},
        "resource_requirements": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "datasets": {"key": "datasets", "type": "[DatasetReference]"},
        "sample_input_data": {"key": "sampleInputData", "type": "str"},
        "sample_output_data": {"key": "sampleOutputData", "type": "str"},
        "resource_requirements": {"key": "resourceRequirements", "type": "ContainerResourceRequirements"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        datasets: List["DatasetReference"],
        sample_input_data: str,
        sample_output_data: str,
        resource_requirements: "ContainerResourceRequirements",
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword datasets: Required.
        :paramtype datasets: list[~index_service_apis.models.DatasetReference]
        :keyword sample_input_data: Required.
        :paramtype sample_input_data: str
        :keyword sample_output_data: Required.
        :paramtype sample_output_data: str
        :keyword resource_requirements: Required.
        :paramtype resource_requirements: ~index_service_apis.models.ContainerResourceRequirements
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(ModelAnnotations, self).__init__(**kwargs)
        self.datasets = datasets
        self.sample_input_data = sample_input_data
        self.sample_output_data = sample_output_data
        self.resource_requirements = resource_requirements
        self.description = description
        self.archived = archived
        self.tags = tags


class ModelProperties(msrest.serialization.Model):
    """ModelProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar name: Required.
    :vartype name: str
    :ivar model_framework: Required.
    :vartype model_framework: str
    :ivar model_framework_version: Required.
    :vartype model_framework_version: str
    :ivar model_format: Required.
    :vartype model_format: str
    :ivar version: Required.
    :vartype version: int
    :ivar alphanumeric_version: Required.
    :vartype alphanumeric_version: str
    :ivar url: Required.
    :vartype url: str
    :ivar mime_type: Required.
    :vartype mime_type: str
    :ivar modified_time: Required.
    :vartype modified_time: ~datetime.datetime
    :ivar unpack: Required.
    :vartype unpack: bool
    :ivar parent_model_id: Required.
    :vartype parent_model_id: str
    :ivar run_id: Required.
    :vartype run_id: str
    :ivar experiment_name: Required.
    :vartype experiment_name: str
    :ivar experiment_id: Required.
    :vartype experiment_id: str
    :ivar derived_model_ids: Required.
    :vartype derived_model_ids: list[str]
    :ivar user_properties: Required. Dictionary of :code:`<string>`.
    :vartype user_properties: dict[str, str]
    :ivar is_anonymous: Required.
    :vartype is_anonymous: bool
    :ivar orgin_asset_id: Required.
    :vartype orgin_asset_id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "name": {"required": True},
        "model_framework": {"required": True},
        "model_framework_version": {"required": True},
        "model_format": {"required": True},
        "version": {"required": True},
        "alphanumeric_version": {"required": True},
        "url": {"required": True},
        "mime_type": {"required": True},
        "modified_time": {"required": True},
        "unpack": {"required": True},
        "parent_model_id": {"required": True},
        "run_id": {"required": True},
        "experiment_name": {"required": True},
        "experiment_id": {"required": True},
        "derived_model_ids": {"required": True},
        "user_properties": {"required": True},
        "is_anonymous": {"required": True},
        "orgin_asset_id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "model_framework": {"key": "modelFramework", "type": "str"},
        "model_framework_version": {"key": "modelFrameworkVersion", "type": "str"},
        "model_format": {"key": "modelFormat", "type": "str"},
        "version": {"key": "version", "type": "int"},
        "alphanumeric_version": {"key": "alphanumericVersion", "type": "str"},
        "url": {"key": "url", "type": "str"},
        "mime_type": {"key": "mimeType", "type": "str"},
        "modified_time": {"key": "modifiedTime", "type": "iso-8601"},
        "unpack": {"key": "unpack", "type": "bool"},
        "parent_model_id": {"key": "parentModelId", "type": "str"},
        "run_id": {"key": "runId", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "experiment_id": {"key": "experimentId", "type": "str"},
        "derived_model_ids": {"key": "derivedModelIds", "type": "[str]"},
        "user_properties": {"key": "userProperties", "type": "{str}"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "orgin_asset_id": {"key": "orginAssetId", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(
        self,
        *,
        id: str,
        name: str,
        model_framework: str,
        model_framework_version: str,
        model_format: str,
        version: int,
        alphanumeric_version: str,
        url: str,
        mime_type: str,
        modified_time: datetime.datetime,
        unpack: bool,
        parent_model_id: str,
        run_id: str,
        experiment_name: str,
        experiment_id: str,
        derived_model_ids: List[str],
        user_properties: Dict[str, str],
        is_anonymous: bool,
        orgin_asset_id: str,
        creation_context: "CreationContext",
        **kwargs
    ):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword name: Required.
        :paramtype name: str
        :keyword model_framework: Required.
        :paramtype model_framework: str
        :keyword model_framework_version: Required.
        :paramtype model_framework_version: str
        :keyword model_format: Required.
        :paramtype model_format: str
        :keyword version: Required.
        :paramtype version: int
        :keyword alphanumeric_version: Required.
        :paramtype alphanumeric_version: str
        :keyword url: Required.
        :paramtype url: str
        :keyword mime_type: Required.
        :paramtype mime_type: str
        :keyword modified_time: Required.
        :paramtype modified_time: ~datetime.datetime
        :keyword unpack: Required.
        :paramtype unpack: bool
        :keyword parent_model_id: Required.
        :paramtype parent_model_id: str
        :keyword run_id: Required.
        :paramtype run_id: str
        :keyword experiment_name: Required.
        :paramtype experiment_name: str
        :keyword experiment_id: Required.
        :paramtype experiment_id: str
        :keyword derived_model_ids: Required.
        :paramtype derived_model_ids: list[str]
        :keyword user_properties: Required. Dictionary of :code:`<string>`.
        :paramtype user_properties: dict[str, str]
        :keyword is_anonymous: Required.
        :paramtype is_anonymous: bool
        :keyword orgin_asset_id: Required.
        :paramtype orgin_asset_id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(ModelProperties, self).__init__(**kwargs)
        self.id = id
        self.name = name
        self.model_framework = model_framework
        self.model_framework_version = model_framework_version
        self.model_format = model_format
        self.version = version
        self.alphanumeric_version = alphanumeric_version
        self.url = url
        self.mime_type = mime_type
        self.modified_time = modified_time
        self.unpack = unpack
        self.parent_model_id = parent_model_id
        self.run_id = run_id
        self.experiment_name = experiment_name
        self.experiment_id = experiment_id
        self.derived_model_ids = derived_model_ids
        self.user_properties = user_properties
        self.is_anonymous = is_anonymous
        self.orgin_asset_id = orgin_asset_id
        self.creation_context = creation_context


class ModelsVersionedEntitiesResponse(msrest.serialization.Model):
    """ModelsVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.ModelsVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[ModelsVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["ModelsVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.ModelsVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(ModelsVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class ModelsVersionedEntity(msrest.serialization.Model):
    """ModelsVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.ModelsVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.ModelAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.ModelProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "ModelAnnotations"},
        "properties": {"key": "properties", "type": "ModelProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "ModelsVersionedEntityKind"],
        annotations: "ModelAnnotations",
        properties: "ModelProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.ModelsVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.ModelAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.ModelProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(ModelsVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class ModuleAnnotations(msrest.serialization.Model):
    """ModuleAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar module_name: Required.
    :vartype module_name: str
    :ivar display_name: Required.
    :vartype display_name: str
    :ivar lower_name: Required.
    :vartype lower_name: str
    :ivar module_version: Required.
    :vartype module_version: str
    :ivar inputs: Required.
    :vartype inputs: list[~index_service_apis.models.InputNameAndDataTypeIdsList]
    :ivar outputs: Required.
    :vartype outputs: list[~index_service_apis.models.OutputNameAndDataTypeId]
    :ivar category: Required.
    :vartype category: str
    :ivar step_type: Required.
    :vartype step_type: str
    :ivar entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
    :vartype entity_status: str or ~index_service_apis.models.ModuleAnnotationsEntityStatus
    :ivar is_deterministic: Required.
    :vartype is_deterministic: bool
    :ivar kv_tags: Required. Dictionary of :code:`<string>`.
    :vartype kv_tags: dict[str, str]
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar last_modified_date: Required.
    :vartype last_modified_date: ~datetime.datetime
    :ivar last_updated_by: Required.
    :vartype last_updated_by: ~index_service_apis.models.CreatedBy
    :ivar component_type_version: Required.
    :vartype component_type_version: str
    :ivar snapshot_id: Required.
    :vartype snapshot_id: str
    :ivar runconfig: Required.
    :vartype runconfig: str
    :ivar control_outputs: Required.
    :vartype control_outputs: list[~index_service_apis.models.ControlOutput]
    :ivar parameters: Required.
    :vartype parameters: list[~index_service_apis.models.StructuredInterfaceParameter]
    :ivar metadata_parameters: Required.
    :vartype metadata_parameters: list[~index_service_apis.models.StructuredInterfaceParameter]
    :ivar aml_module_name: Required.
    :vartype aml_module_name: str
    :ivar aml_module_entity_status: Required. Possible values include: "Active", "Deprecated",
     "Disabled".
    :vartype aml_module_entity_status: str or
     ~index_service_apis.models.ModuleAnnotationsAmlModuleEntityStatus
    :ivar module_type: Required. Possible values include: "User", "Builtin".
    :vartype module_type: str or ~index_service_apis.models.ModuleAnnotationsModuleType
    :ivar module_execution_type: Required.
    :vartype module_execution_type: str
    :ivar arguments_serialized: Required.
    :vartype arguments_serialized: str
    :ivar cloud_settings_serialized: Required.
    :vartype cloud_settings_serialized: str
    :ivar stage: Required.
    :vartype stage: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "module_name": {"required": True},
        "display_name": {"required": True},
        "lower_name": {"required": True},
        "module_version": {"required": True},
        "inputs": {"required": True},
        "outputs": {"required": True},
        "category": {"required": True},
        "step_type": {"required": True},
        "entity_status": {"required": True},
        "is_deterministic": {"required": True},
        "kv_tags": {"required": True},
        "properties": {"required": True},
        "last_modified_date": {"required": True},
        "last_updated_by": {"required": True},
        "component_type_version": {"required": True},
        "snapshot_id": {"required": True},
        "runconfig": {"required": True},
        "control_outputs": {"required": True},
        "parameters": {"required": True},
        "metadata_parameters": {"required": True},
        "aml_module_name": {"required": True},
        "aml_module_entity_status": {"required": True},
        "module_type": {"required": True},
        "module_execution_type": {"required": True},
        "arguments_serialized": {"required": True},
        "cloud_settings_serialized": {"required": True},
        "stage": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "module_name": {"key": "moduleName", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "lower_name": {"key": "lowerName", "type": "str"},
        "module_version": {"key": "moduleVersion", "type": "str"},
        "inputs": {"key": "inputs", "type": "[InputNameAndDataTypeIdsList]"},
        "outputs": {"key": "outputs", "type": "[OutputNameAndDataTypeId]"},
        "category": {"key": "category", "type": "str"},
        "step_type": {"key": "stepType", "type": "str"},
        "entity_status": {"key": "entityStatus", "type": "str"},
        "is_deterministic": {"key": "isDeterministic", "type": "bool"},
        "kv_tags": {"key": "kvTags", "type": "{str}"},
        "properties": {"key": "properties", "type": "{str}"},
        "last_modified_date": {"key": "lastModifiedDate", "type": "iso-8601"},
        "last_updated_by": {"key": "lastUpdatedBy", "type": "CreatedBy"},
        "component_type_version": {"key": "componentTypeVersion", "type": "str"},
        "snapshot_id": {"key": "snapshotId", "type": "str"},
        "runconfig": {"key": "runconfig", "type": "str"},
        "control_outputs": {"key": "controlOutputs", "type": "[ControlOutput]"},
        "parameters": {"key": "parameters", "type": "[StructuredInterfaceParameter]"},
        "metadata_parameters": {"key": "metadataParameters", "type": "[StructuredInterfaceParameter]"},
        "aml_module_name": {"key": "amlModuleName", "type": "str"},
        "aml_module_entity_status": {"key": "amlModuleEntityStatus", "type": "str"},
        "module_type": {"key": "moduleType", "type": "str"},
        "module_execution_type": {"key": "moduleExecutionType", "type": "str"},
        "arguments_serialized": {"key": "argumentsSerialized", "type": "str"},
        "cloud_settings_serialized": {"key": "cloudSettingsSerialized", "type": "str"},
        "stage": {"key": "stage", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        module_name: str,
        display_name: str,
        lower_name: str,
        module_version: str,
        inputs: List["InputNameAndDataTypeIdsList"],
        outputs: List["OutputNameAndDataTypeId"],
        category: str,
        step_type: str,
        entity_status: Union[str, "ModuleAnnotationsEntityStatus"],
        is_deterministic: bool,
        kv_tags: Dict[str, str],
        properties: Dict[str, str],
        last_modified_date: datetime.datetime,
        last_updated_by: "CreatedBy",
        component_type_version: str,
        snapshot_id: str,
        runconfig: str,
        control_outputs: List["ControlOutput"],
        parameters: List["StructuredInterfaceParameter"],
        metadata_parameters: List["StructuredInterfaceParameter"],
        aml_module_name: str,
        aml_module_entity_status: Union[str, "ModuleAnnotationsAmlModuleEntityStatus"],
        module_type: Union[str, "ModuleAnnotationsModuleType"],
        module_execution_type: str,
        arguments_serialized: str,
        cloud_settings_serialized: str,
        stage: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword module_name: Required.
        :paramtype module_name: str
        :keyword display_name: Required.
        :paramtype display_name: str
        :keyword lower_name: Required.
        :paramtype lower_name: str
        :keyword module_version: Required.
        :paramtype module_version: str
        :keyword inputs: Required.
        :paramtype inputs: list[~index_service_apis.models.InputNameAndDataTypeIdsList]
        :keyword outputs: Required.
        :paramtype outputs: list[~index_service_apis.models.OutputNameAndDataTypeId]
        :keyword category: Required.
        :paramtype category: str
        :keyword step_type: Required.
        :paramtype step_type: str
        :keyword entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
        :paramtype entity_status: str or ~index_service_apis.models.ModuleAnnotationsEntityStatus
        :keyword is_deterministic: Required.
        :paramtype is_deterministic: bool
        :keyword kv_tags: Required. Dictionary of :code:`<string>`.
        :paramtype kv_tags: dict[str, str]
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword last_modified_date: Required.
        :paramtype last_modified_date: ~datetime.datetime
        :keyword last_updated_by: Required.
        :paramtype last_updated_by: ~index_service_apis.models.CreatedBy
        :keyword component_type_version: Required.
        :paramtype component_type_version: str
        :keyword snapshot_id: Required.
        :paramtype snapshot_id: str
        :keyword runconfig: Required.
        :paramtype runconfig: str
        :keyword control_outputs: Required.
        :paramtype control_outputs: list[~index_service_apis.models.ControlOutput]
        :keyword parameters: Required.
        :paramtype parameters: list[~index_service_apis.models.StructuredInterfaceParameter]
        :keyword metadata_parameters: Required.
        :paramtype metadata_parameters: list[~index_service_apis.models.StructuredInterfaceParameter]
        :keyword aml_module_name: Required.
        :paramtype aml_module_name: str
        :keyword aml_module_entity_status: Required. Possible values include: "Active", "Deprecated",
         "Disabled".
        :paramtype aml_module_entity_status: str or
         ~index_service_apis.models.ModuleAnnotationsAmlModuleEntityStatus
        :keyword module_type: Required. Possible values include: "User", "Builtin".
        :paramtype module_type: str or ~index_service_apis.models.ModuleAnnotationsModuleType
        :keyword module_execution_type: Required.
        :paramtype module_execution_type: str
        :keyword arguments_serialized: Required.
        :paramtype arguments_serialized: str
        :keyword cloud_settings_serialized: Required.
        :paramtype cloud_settings_serialized: str
        :keyword stage: Required.
        :paramtype stage: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(ModuleAnnotations, self).__init__(**kwargs)
        self.module_name = module_name
        self.display_name = display_name
        self.lower_name = lower_name
        self.module_version = module_version
        self.inputs = inputs
        self.outputs = outputs
        self.category = category
        self.step_type = step_type
        self.entity_status = entity_status
        self.is_deterministic = is_deterministic
        self.kv_tags = kv_tags
        self.properties = properties
        self.last_modified_date = last_modified_date
        self.last_updated_by = last_updated_by
        self.component_type_version = component_type_version
        self.snapshot_id = snapshot_id
        self.runconfig = runconfig
        self.control_outputs = control_outputs
        self.parameters = parameters
        self.metadata_parameters = metadata_parameters
        self.aml_module_name = aml_module_name
        self.aml_module_entity_status = aml_module_entity_status
        self.module_type = module_type
        self.module_execution_type = module_execution_type
        self.arguments_serialized = arguments_serialized
        self.cloud_settings_serialized = cloud_settings_serialized
        self.stage = stage
        self.description = description
        self.archived = archived
        self.tags = tags


class ModuleProperties(msrest.serialization.Model):
    """ModuleProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar aml_module_id: Required.
    :vartype aml_module_id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "aml_module_id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "aml_module_id": {"key": "amlModuleId", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, id: str, aml_module_id: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword aml_module_id: Required.
        :paramtype aml_module_id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(ModuleProperties, self).__init__(**kwargs)
        self.id = id
        self.aml_module_id = aml_module_id
        self.creation_context = creation_context


class ModulesVersionedEntitiesResponse(msrest.serialization.Model):
    """ModulesVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.ModulesVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[ModulesVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["ModulesVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.ModulesVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(ModulesVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class ModulesVersionedEntity(msrest.serialization.Model):
    """ModulesVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.ModulesVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.ModuleAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.ModuleProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "ModuleAnnotations"},
        "properties": {"key": "properties", "type": "ModuleProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "ModulesVersionedEntityKind"],
        annotations: "ModuleAnnotations",
        properties: "ModuleProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.ModulesVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.ModuleAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.ModuleProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(ModulesVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class NotificationSetting(msrest.serialization.Model):
    """NotificationSetting.

    All required parameters must be populated in order to send to Azure.

    :ivar emails: Required.
    :vartype emails: list[str]
    :ivar email_on: Required.
    :vartype email_on: list[str or ~index_service_apis.models.NotificationSettingEmailOnItem]
    :ivar webhooks: Required. Dictionary of :code:`<Webhook>`.
    :vartype webhooks: dict[str, ~index_service_apis.models.Webhook]
    """

    _validation = {
        "emails": {"required": True},
        "email_on": {"required": True},
        "webhooks": {"required": True},
    }

    _attribute_map = {
        "emails": {"key": "emails", "type": "[str]"},
        "email_on": {"key": "emailOn", "type": "[str]"},
        "webhooks": {"key": "webhooks", "type": "{Webhook}"},
    }

    def __init__(
        self,
        *,
        emails: List[str],
        email_on: List[Union[str, "NotificationSettingEmailOnItem"]],
        webhooks: Dict[str, "Webhook"],
        **kwargs
    ):
        """
        :keyword emails: Required.
        :paramtype emails: list[str]
        :keyword email_on: Required.
        :paramtype email_on: list[str or ~index_service_apis.models.NotificationSettingEmailOnItem]
        :keyword webhooks: Required. Dictionary of :code:`<Webhook>`.
        :paramtype webhooks: dict[str, ~index_service_apis.models.Webhook]
        """
        super(NotificationSetting, self).__init__(**kwargs)
        self.emails = emails
        self.email_on = email_on
        self.webhooks = webhooks


class OnlineEndpointAnnotations(msrest.serialization.Model):
    """OnlineEndpointAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar compute_type: Required.
    :vartype compute_type: str
    :ivar compute_target: Required.
    :vartype compute_target: ~index_service_apis.models.EndpointComputeTarget
    :ivar scoring_uri: Required.
    :vartype scoring_uri: str
    :ivar location: Required.
    :vartype location: str
    :ivar last_modified_time: Required.
    :vartype last_modified_time: ~datetime.datetime
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "compute_type": {"required": True},
        "compute_target": {"required": True},
        "scoring_uri": {"required": True},
        "location": {"required": True},
        "last_modified_time": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "compute_type": {"key": "computeType", "type": "str"},
        "compute_target": {"key": "computeTarget", "type": "EndpointComputeTarget"},
        "scoring_uri": {"key": "scoringUri", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "last_modified_time": {"key": "lastModifiedTime", "type": "iso-8601"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        compute_type: str,
        compute_target: "EndpointComputeTarget",
        scoring_uri: str,
        location: str,
        last_modified_time: datetime.datetime,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword compute_type: Required.
        :paramtype compute_type: str
        :keyword compute_target: Required.
        :paramtype compute_target: ~index_service_apis.models.EndpointComputeTarget
        :keyword scoring_uri: Required.
        :paramtype scoring_uri: str
        :keyword location: Required.
        :paramtype location: str
        :keyword last_modified_time: Required.
        :paramtype last_modified_time: ~datetime.datetime
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(OnlineEndpointAnnotations, self).__init__(**kwargs)
        self.compute_type = compute_type
        self.compute_target = compute_target
        self.scoring_uri = scoring_uri
        self.location = location
        self.last_modified_time = last_modified_time
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class OnlineEndpointProperties(msrest.serialization.Model):
    """OnlineEndpointProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "properties": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "properties": {"key": "properties", "type": "{str}"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, properties: Dict[str, str], creation_context: "CreationContext", **kwargs):
        """
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(OnlineEndpointProperties, self).__init__(**kwargs)
        self.properties = properties
        self.creation_context = creation_context


class OnlineendpointsUnversionedEntitiesResponse(msrest.serialization.Model):
    """OnlineendpointsUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.OnlineendpointsUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[OnlineendpointsUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["OnlineendpointsUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.OnlineendpointsUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(OnlineendpointsUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class OnlineendpointsUnversionedEntity(msrest.serialization.Model):
    """OnlineendpointsUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.OnlineendpointsUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.OnlineEndpointAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.OnlineEndpointProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "OnlineEndpointAnnotations"},
        "properties": {"key": "properties", "type": "OnlineEndpointProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "OnlineendpointsUnversionedEntityKind"],
        annotations: "OnlineEndpointAnnotations",
        properties: "OnlineEndpointProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.OnlineendpointsUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.OnlineEndpointAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.OnlineEndpointProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(OnlineendpointsUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class OutputNameAndDataTypeId(msrest.serialization.Model):
    """OutputNameAndDataTypeId.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required.
    :vartype name: str
    :ivar data_type_id: Required.
    :vartype data_type_id: str
    :ivar description: Required.
    :vartype description: str
    :ivar label: Required.
    :vartype label: str
    """

    _validation = {
        "name": {"required": True},
        "data_type_id": {"required": True},
        "description": {"required": True},
        "label": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "data_type_id": {"key": "dataTypeId", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "label": {"key": "label", "type": "str"},
    }

    def __init__(self, *, name: str, data_type_id: str, description: str, label: str, **kwargs):
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword data_type_id: Required.
        :paramtype data_type_id: str
        :keyword description: Required.
        :paramtype description: str
        :keyword label: Required.
        :paramtype label: str
        """
        super(OutputNameAndDataTypeId, self).__init__(**kwargs)
        self.name = name
        self.data_type_id = data_type_id
        self.description = description
        self.label = label


class PipelineAnnotations(msrest.serialization.Model):
    """PipelineAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar pipeline_name: Required.
    :vartype pipeline_name: str
    :ivar version: Required.
    :vartype version: str
    :ivar url: Required.
    :vartype url: str
    :ivar pipeline_endpoint_ids: Required.
    :vartype pipeline_endpoint_ids: list[str]
    :ivar kv_tags: Required. Dictionary of :code:`<string>`.
    :vartype kv_tags: dict[str, str]
    :ivar step_tags: Required. Dictionary of :code:`<string>`.
    :vartype step_tags: dict[str, str]
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar step_properties: Required. Dictionary of :code:`<string>`.
    :vartype step_properties: dict[str, str]
    :ivar entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
    :vartype entity_status: str or ~index_service_apis.models.PipelineAnnotationsEntityStatus
    :ivar last_modified_date: Required.
    :vartype last_modified_date: ~datetime.datetime
    :ivar last_updated_by: Required.
    :vartype last_updated_by: ~index_service_apis.models.CreatedBy
    :ivar last_run_id: Required.
    :vartype last_run_id: str
    :ivar pipeline_type: Required.
    :vartype pipeline_type: str
    :ivar last_run_status_code: Required. Possible values include: "NotStarted", "Running",
     "Failed", "Finished", "Canceled", "Failing", "Queued", "CancelRequested".
    :vartype last_run_status_code: str or
     ~index_service_apis.models.PipelineAnnotationsLastRunStatusCode
    :ivar last_run_creation_time: Required.
    :vartype last_run_creation_time: ~datetime.datetime
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "pipeline_name": {"required": True},
        "version": {"required": True},
        "url": {"required": True},
        "pipeline_endpoint_ids": {"required": True},
        "kv_tags": {"required": True},
        "step_tags": {"required": True},
        "properties": {"required": True},
        "step_properties": {"required": True},
        "entity_status": {"required": True},
        "last_modified_date": {"required": True},
        "last_updated_by": {"required": True},
        "last_run_id": {"required": True},
        "pipeline_type": {"required": True},
        "last_run_status_code": {"required": True},
        "last_run_creation_time": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "pipeline_name": {"key": "pipelineName", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "url": {"key": "url", "type": "str"},
        "pipeline_endpoint_ids": {"key": "pipelineEndpointIds", "type": "[str]"},
        "kv_tags": {"key": "kvTags", "type": "{str}"},
        "step_tags": {"key": "stepTags", "type": "{str}"},
        "properties": {"key": "properties", "type": "{str}"},
        "step_properties": {"key": "stepProperties", "type": "{str}"},
        "entity_status": {"key": "entityStatus", "type": "str"},
        "last_modified_date": {"key": "lastModifiedDate", "type": "iso-8601"},
        "last_updated_by": {"key": "lastUpdatedBy", "type": "CreatedBy"},
        "last_run_id": {"key": "lastRunId", "type": "str"},
        "pipeline_type": {"key": "pipelineType", "type": "str"},
        "last_run_status_code": {"key": "lastRunStatusCode", "type": "str"},
        "last_run_creation_time": {"key": "lastRunCreationTime", "type": "iso-8601"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        pipeline_name: str,
        version: str,
        url: str,
        pipeline_endpoint_ids: List[str],
        kv_tags: Dict[str, str],
        step_tags: Dict[str, str],
        properties: Dict[str, str],
        step_properties: Dict[str, str],
        entity_status: Union[str, "PipelineAnnotationsEntityStatus"],
        last_modified_date: datetime.datetime,
        last_updated_by: "CreatedBy",
        last_run_id: str,
        pipeline_type: str,
        last_run_status_code: Union[str, "PipelineAnnotationsLastRunStatusCode"],
        last_run_creation_time: datetime.datetime,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword pipeline_name: Required.
        :paramtype pipeline_name: str
        :keyword version: Required.
        :paramtype version: str
        :keyword url: Required.
        :paramtype url: str
        :keyword pipeline_endpoint_ids: Required.
        :paramtype pipeline_endpoint_ids: list[str]
        :keyword kv_tags: Required. Dictionary of :code:`<string>`.
        :paramtype kv_tags: dict[str, str]
        :keyword step_tags: Required. Dictionary of :code:`<string>`.
        :paramtype step_tags: dict[str, str]
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword step_properties: Required. Dictionary of :code:`<string>`.
        :paramtype step_properties: dict[str, str]
        :keyword entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
        :paramtype entity_status: str or ~index_service_apis.models.PipelineAnnotationsEntityStatus
        :keyword last_modified_date: Required.
        :paramtype last_modified_date: ~datetime.datetime
        :keyword last_updated_by: Required.
        :paramtype last_updated_by: ~index_service_apis.models.CreatedBy
        :keyword last_run_id: Required.
        :paramtype last_run_id: str
        :keyword pipeline_type: Required.
        :paramtype pipeline_type: str
        :keyword last_run_status_code: Required. Possible values include: "NotStarted", "Running",
         "Failed", "Finished", "Canceled", "Failing", "Queued", "CancelRequested".
        :paramtype last_run_status_code: str or
         ~index_service_apis.models.PipelineAnnotationsLastRunStatusCode
        :keyword last_run_creation_time: Required.
        :paramtype last_run_creation_time: ~datetime.datetime
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(PipelineAnnotations, self).__init__(**kwargs)
        self.pipeline_name = pipeline_name
        self.version = version
        self.url = url
        self.pipeline_endpoint_ids = pipeline_endpoint_ids
        self.kv_tags = kv_tags
        self.step_tags = step_tags
        self.properties = properties
        self.step_properties = step_properties
        self.entity_status = entity_status
        self.last_modified_date = last_modified_date
        self.last_updated_by = last_updated_by
        self.last_run_id = last_run_id
        self.pipeline_type = pipeline_type
        self.last_run_status_code = last_run_status_code
        self.last_run_creation_time = last_run_creation_time
        self.description = description
        self.archived = archived
        self.tags = tags


class PipelineDraftAnnotations(msrest.serialization.Model):
    """PipelineDraftAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar kv_tags: Required. Dictionary of :code:`<string>`.
    :vartype kv_tags: dict[str, str]
    :ivar step_tags: Required. Dictionary of :code:`<string>`.
    :vartype step_tags: dict[str, str]
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar step_properties: Required. Dictionary of :code:`<string>`.
    :vartype step_properties: dict[str, str]
    :ivar entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
    :vartype entity_status: str or ~index_service_apis.models.PipelineDraftAnnotationsEntityStatus
    :ivar last_modified_date: Required.
    :vartype last_modified_date: ~datetime.datetime
    :ivar pipeline_type: Required.
    :vartype pipeline_type: str
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "kv_tags": {"required": True},
        "step_tags": {"required": True},
        "properties": {"required": True},
        "step_properties": {"required": True},
        "entity_status": {"required": True},
        "last_modified_date": {"required": True},
        "pipeline_type": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "kv_tags": {"key": "kvTags", "type": "{str}"},
        "step_tags": {"key": "stepTags", "type": "{str}"},
        "properties": {"key": "properties", "type": "{str}"},
        "step_properties": {"key": "stepProperties", "type": "{str}"},
        "entity_status": {"key": "entityStatus", "type": "str"},
        "last_modified_date": {"key": "lastModifiedDate", "type": "iso-8601"},
        "pipeline_type": {"key": "pipelineType", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        kv_tags: Dict[str, str],
        step_tags: Dict[str, str],
        properties: Dict[str, str],
        step_properties: Dict[str, str],
        entity_status: Union[str, "PipelineDraftAnnotationsEntityStatus"],
        last_modified_date: datetime.datetime,
        pipeline_type: str,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword kv_tags: Required. Dictionary of :code:`<string>`.
        :paramtype kv_tags: dict[str, str]
        :keyword step_tags: Required. Dictionary of :code:`<string>`.
        :paramtype step_tags: dict[str, str]
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword step_properties: Required. Dictionary of :code:`<string>`.
        :paramtype step_properties: dict[str, str]
        :keyword entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
        :paramtype entity_status: str or
         ~index_service_apis.models.PipelineDraftAnnotationsEntityStatus
        :keyword last_modified_date: Required.
        :paramtype last_modified_date: ~datetime.datetime
        :keyword pipeline_type: Required.
        :paramtype pipeline_type: str
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(PipelineDraftAnnotations, self).__init__(**kwargs)
        self.kv_tags = kv_tags
        self.step_tags = step_tags
        self.properties = properties
        self.step_properties = step_properties
        self.entity_status = entity_status
        self.last_modified_date = last_modified_date
        self.pipeline_type = pipeline_type
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class PipelineDraftProperties(msrest.serialization.Model):
    """PipelineDraftProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, id: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(PipelineDraftProperties, self).__init__(**kwargs)
        self.id = id
        self.creation_context = creation_context


class PipelinedraftsUnversionedEntitiesResponse(msrest.serialization.Model):
    """PipelinedraftsUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.PipelinedraftsUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[PipelinedraftsUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["PipelinedraftsUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.PipelinedraftsUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(PipelinedraftsUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class PipelinedraftsUnversionedEntity(msrest.serialization.Model):
    """PipelinedraftsUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.PipelinedraftsUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.PipelineDraftAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.PipelineDraftProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "PipelineDraftAnnotations"},
        "properties": {"key": "properties", "type": "PipelineDraftProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "PipelinedraftsUnversionedEntityKind"],
        annotations: "PipelineDraftAnnotations",
        properties: "PipelineDraftProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.PipelinedraftsUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.PipelineDraftAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.PipelineDraftProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(PipelinedraftsUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class PipelineProperties(msrest.serialization.Model):
    """PipelineProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, id: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(PipelineProperties, self).__init__(**kwargs)
        self.id = id
        self.creation_context = creation_context


class PipelineRunAnnotations(msrest.serialization.Model):
    """PipelineRunAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar kv_tags: Required. Dictionary of :code:`<string>`.
    :vartype kv_tags: dict[str, str]
    :ivar step_tags: Required. Dictionary of :code:`<string>`.
    :vartype step_tags: dict[str, str]
    :ivar properties: Required. Dictionary of :code:`<string>`.
    :vartype properties: dict[str, str]
    :ivar step_properties: Required. Dictionary of :code:`<string>`.
    :vartype step_properties: dict[str, str]
    :ivar entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
    :vartype entity_status: str or ~index_service_apis.models.PipelineRunAnnotationsEntityStatus
    :ivar status: Required.
    :vartype status: ~index_service_apis.models.ExperimentStatus
    :ivar run_history_experiment_name: Required.
    :vartype run_history_experiment_name: str
    :ivar has_errors: Required.
    :vartype has_errors: bool
    :ivar has_warnings: Required.
    :vartype has_warnings: bool
    :ivar last_modified_date: Required.
    :vartype last_modified_date: ~datetime.datetime
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "kv_tags": {"required": True},
        "step_tags": {"required": True},
        "properties": {"required": True},
        "step_properties": {"required": True},
        "entity_status": {"required": True},
        "status": {"required": True},
        "run_history_experiment_name": {"required": True},
        "has_errors": {"required": True},
        "has_warnings": {"required": True},
        "last_modified_date": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "kv_tags": {"key": "kvTags", "type": "{str}"},
        "step_tags": {"key": "stepTags", "type": "{str}"},
        "properties": {"key": "properties", "type": "{str}"},
        "step_properties": {"key": "stepProperties", "type": "{str}"},
        "entity_status": {"key": "entityStatus", "type": "str"},
        "status": {"key": "status", "type": "ExperimentStatus"},
        "run_history_experiment_name": {"key": "runHistoryExperimentName", "type": "str"},
        "has_errors": {"key": "hasErrors", "type": "bool"},
        "has_warnings": {"key": "hasWarnings", "type": "bool"},
        "last_modified_date": {"key": "lastModifiedDate", "type": "iso-8601"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        kv_tags: Dict[str, str],
        step_tags: Dict[str, str],
        properties: Dict[str, str],
        step_properties: Dict[str, str],
        entity_status: Union[str, "PipelineRunAnnotationsEntityStatus"],
        status: "ExperimentStatus",
        run_history_experiment_name: str,
        has_errors: bool,
        has_warnings: bool,
        last_modified_date: datetime.datetime,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword kv_tags: Required. Dictionary of :code:`<string>`.
        :paramtype kv_tags: dict[str, str]
        :keyword step_tags: Required. Dictionary of :code:`<string>`.
        :paramtype step_tags: dict[str, str]
        :keyword properties: Required. Dictionary of :code:`<string>`.
        :paramtype properties: dict[str, str]
        :keyword step_properties: Required. Dictionary of :code:`<string>`.
        :paramtype step_properties: dict[str, str]
        :keyword entity_status: Required. Possible values include: "Active", "Deprecated", "Disabled".
        :paramtype entity_status: str or ~index_service_apis.models.PipelineRunAnnotationsEntityStatus
        :keyword status: Required.
        :paramtype status: ~index_service_apis.models.ExperimentStatus
        :keyword run_history_experiment_name: Required.
        :paramtype run_history_experiment_name: str
        :keyword has_errors: Required.
        :paramtype has_errors: bool
        :keyword has_warnings: Required.
        :paramtype has_warnings: bool
        :keyword last_modified_date: Required.
        :paramtype last_modified_date: ~datetime.datetime
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(PipelineRunAnnotations, self).__init__(**kwargs)
        self.kv_tags = kv_tags
        self.step_tags = step_tags
        self.properties = properties
        self.step_properties = step_properties
        self.entity_status = entity_status
        self.status = status
        self.run_history_experiment_name = run_history_experiment_name
        self.has_errors = has_errors
        self.has_warnings = has_warnings
        self.last_modified_date = last_modified_date
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class PipelineRunProperties(msrest.serialization.Model):
    """PipelineRunProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Required.
    :vartype id: str
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "id": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(self, *, id: str, creation_context: "CreationContext", **kwargs):
        """
        :keyword id: Required.
        :paramtype id: str
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(PipelineRunProperties, self).__init__(**kwargs)
        self.id = id
        self.creation_context = creation_context


class PipelinerunsUnversionedEntitiesResponse(msrest.serialization.Model):
    """PipelinerunsUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.PipelinerunsUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[PipelinerunsUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["PipelinerunsUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.PipelinerunsUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(PipelinerunsUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class PipelinerunsUnversionedEntity(msrest.serialization.Model):
    """PipelinerunsUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.PipelinerunsUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.PipelineRunAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.PipelineRunProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "PipelineRunAnnotations"},
        "properties": {"key": "properties", "type": "PipelineRunProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "PipelinerunsUnversionedEntityKind"],
        annotations: "PipelineRunAnnotations",
        properties: "PipelineRunProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        relationships: List["Relationship"],
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.PipelinerunsUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.PipelineRunAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.PipelineRunProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        """
        super(PipelinerunsUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.relationships = relationships


class PipelinesVersionedEntitiesResponse(msrest.serialization.Model):
    """PipelinesVersionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.PipelinesVersionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[PipelinesVersionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["PipelinesVersionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.PipelinesVersionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(PipelinesVersionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class PipelinesVersionedEntity(msrest.serialization.Model):
    """PipelinesVersionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.PipelinesVersionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.PipelineAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.PipelineProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "PipelineAnnotations"},
        "properties": {"key": "properties", "type": "PipelineProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "PipelinesVersionedEntityKind"],
        annotations: "PipelineAnnotations",
        properties: "PipelineProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.PipelinesVersionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.PipelineAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.PipelineProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(PipelinesVersionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class Purpose(msrest.serialization.Model):
    """Purpose.

    All required parameters must be populated in order to send to Azure.

    :ivar has_inferencing_support: Required.
    :vartype has_inferencing_support: bool
    :ivar has_training_support: Required.
    :vartype has_training_support: bool
    """

    _validation = {
        "has_inferencing_support": {"required": True},
        "has_training_support": {"required": True},
    }

    _attribute_map = {
        "has_inferencing_support": {"key": "hasInferencingSupport", "type": "bool"},
        "has_training_support": {"key": "hasTrainingSupport", "type": "bool"},
    }

    def __init__(self, *, has_inferencing_support: bool, has_training_support: bool, **kwargs):
        """
        :keyword has_inferencing_support: Required.
        :paramtype has_inferencing_support: bool
        :keyword has_training_support: Required.
        :paramtype has_training_support: bool
        """
        super(Purpose, self).__init__(**kwargs)
        self.has_inferencing_support = has_inferencing_support
        self.has_training_support = has_training_support


class Recurrence(msrest.serialization.Model):
    """Recurrence.

    All required parameters must be populated in order to send to Azure.

    :ivar frequency: Required. Possible values include: "Month", "Week", "Day", "Hour", "Minute".
    :vartype frequency: str or ~index_service_apis.models.RecurrenceFrequency
    :ivar interval: Required.
    :vartype interval: int
    :ivar schedule: Required.
    :vartype schedule: ~index_service_apis.models.RecurrenceSchedule
    :ivar end_time: Required.
    :vartype end_time: str
    :ivar start_time: Required.
    :vartype start_time: str
    :ivar time_zone: Required.
    :vartype time_zone: str
    """

    _validation = {
        "frequency": {"required": True},
        "interval": {"required": True},
        "schedule": {"required": True},
        "end_time": {"required": True},
        "start_time": {"required": True},
        "time_zone": {"required": True},
    }

    _attribute_map = {
        "frequency": {"key": "frequency", "type": "str"},
        "interval": {"key": "interval", "type": "int"},
        "schedule": {"key": "schedule", "type": "RecurrenceSchedule"},
        "end_time": {"key": "endTime", "type": "str"},
        "start_time": {"key": "startTime", "type": "str"},
        "time_zone": {"key": "timeZone", "type": "str"},
    }

    def __init__(
        self,
        *,
        frequency: Union[str, "RecurrenceFrequency"],
        interval: int,
        schedule: "RecurrenceSchedule",
        end_time: str,
        start_time: str,
        time_zone: str,
        **kwargs
    ):
        """
        :keyword frequency: Required. Possible values include: "Month", "Week", "Day", "Hour",
         "Minute".
        :paramtype frequency: str or ~index_service_apis.models.RecurrenceFrequency
        :keyword interval: Required.
        :paramtype interval: int
        :keyword schedule: Required.
        :paramtype schedule: ~index_service_apis.models.RecurrenceSchedule
        :keyword end_time: Required.
        :paramtype end_time: str
        :keyword start_time: Required.
        :paramtype start_time: str
        :keyword time_zone: Required.
        :paramtype time_zone: str
        """
        super(Recurrence, self).__init__(**kwargs)
        self.frequency = frequency
        self.interval = interval
        self.schedule = schedule
        self.end_time = end_time
        self.start_time = start_time
        self.time_zone = time_zone


class RecurrenceSchedule(msrest.serialization.Model):
    """RecurrenceSchedule.

    :ivar hours:
    :vartype hours: list[int]
    :ivar minutes:
    :vartype minutes: list[int]
    :ivar week_days:
    :vartype week_days: list[str or ~index_service_apis.models.RecurrenceScheduleWeekDaysItem]
    :ivar month_days:
    :vartype month_days: list[int]
    """

    _attribute_map = {
        "hours": {"key": "hours", "type": "[int]"},
        "minutes": {"key": "minutes", "type": "[int]"},
        "week_days": {"key": "weekDays", "type": "[str]"},
        "month_days": {"key": "monthDays", "type": "[int]"},
    }

    def __init__(
        self,
        *,
        hours: Optional[List[int]] = None,
        minutes: Optional[List[int]] = None,
        week_days: Optional[List[Union[str, "RecurrenceScheduleWeekDaysItem"]]] = None,
        month_days: Optional[List[int]] = None,
        **kwargs
    ):
        """
        :keyword hours:
        :paramtype hours: list[int]
        :keyword minutes:
        :paramtype minutes: list[int]
        :keyword week_days:
        :paramtype week_days: list[str or ~index_service_apis.models.RecurrenceScheduleWeekDaysItem]
        :keyword month_days:
        :paramtype month_days: list[int]
        """
        super(RecurrenceSchedule, self).__init__(**kwargs)
        self.hours = hours
        self.minutes = minutes
        self.week_days = week_days
        self.month_days = month_days


class RecurrenceTrigger(msrest.serialization.Model):
    """RecurrenceTrigger.

    :ivar frequency: Possible values include: "Month", "Week", "Day", "Hour", "Minute".
    :vartype frequency: str or ~index_service_apis.models.RecurrenceTriggerFrequency
    :ivar interval:
    :vartype interval: int
    :ivar schedule:
    :vartype schedule: ~index_service_apis.models.RecurrenceSchedule
    """

    _attribute_map = {
        "frequency": {"key": "frequency", "type": "str"},
        "interval": {"key": "interval", "type": "int"},
        "schedule": {"key": "schedule", "type": "RecurrenceSchedule"},
    }

    def __init__(
        self,
        *,
        frequency: Optional[Union[str, "RecurrenceTriggerFrequency"]] = None,
        interval: Optional[int] = None,
        schedule: Optional["RecurrenceSchedule"] = None,
        **kwargs
    ):
        """
        :keyword frequency: Possible values include: "Month", "Week", "Day", "Hour", "Minute".
        :paramtype frequency: str or ~index_service_apis.models.RecurrenceTriggerFrequency
        :keyword interval:
        :paramtype interval: int
        :keyword schedule:
        :paramtype schedule: ~index_service_apis.models.RecurrenceSchedule
        """
        super(RecurrenceTrigger, self).__init__(**kwargs)
        self.frequency = frequency
        self.interval = interval
        self.schedule = schedule


class Relationship(msrest.serialization.Model):
    """Relationship.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar relation_type:
    :vartype relation_type: str
    :ivar target_entity_id:
    :vartype target_entity_id: str
    :ivar asset_id:
    :vartype asset_id: str
    :ivar entity_type:
    :vartype entity_type: str
    :ivar direction:
    :vartype direction: str
    :ivar entity_container_id:
    :vartype entity_container_id: str
    """

    _validation = {
        "entity_type": {"readonly": True},
        "entity_container_id": {"readonly": True},
    }

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "relation_type": {"key": "relationType", "type": "str"},
        "target_entity_id": {"key": "targetEntityId", "type": "str"},
        "asset_id": {"key": "assetId", "type": "str"},
        "entity_type": {"key": "entityType", "type": "str"},
        "direction": {"key": "direction", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
    }

    def __init__(
        self,
        *,
        additional_properties: Optional[Dict[str, Any]] = None,
        relation_type: Optional[str] = None,
        target_entity_id: Optional[str] = None,
        asset_id: Optional[str] = None,
        direction: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        :keyword relation_type:
        :paramtype relation_type: str
        :keyword target_entity_id:
        :paramtype target_entity_id: str
        :keyword asset_id:
        :paramtype asset_id: str
        :keyword direction:
        :paramtype direction: str
        """
        super(Relationship, self).__init__(**kwargs)
        self.additional_properties = additional_properties
        self.relation_type = relation_type
        self.target_entity_id = target_entity_id
        self.asset_id = asset_id
        self.entity_type = None
        self.direction = direction
        self.entity_container_id = None


class ResourceDiscoveryContainerMetadata(msrest.serialization.Model):
    """ResourceDiscoveryContainerMetadata.

    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar tenant_id:
    :vartype tenant_id: str
    :ivar resource_id:
    :vartype resource_id: str
    :ivar resource_arm_id:
    :vartype resource_arm_id: str
    :ivar subscription_id:
    :vartype subscription_id: str
    :ivar resource_group:
    :vartype resource_group: str
    :ivar resource_name:
    :vartype resource_name: str
    :ivar asset_container_type:
    :vartype asset_container_type: str
    :ivar immutable_resource_id:
    :vartype immutable_resource_id: str
    :ivar asset_resource_type:
    :vartype asset_resource_type: str
    :ivar is_customer_managed_resource:
    :vartype is_customer_managed_resource: bool
    :ivar is_private_link_behind_vnet:
    :vartype is_private_link_behind_vnet: bool
    :ivar is_public_container:
    :vartype is_public_container: bool
    :ivar is_public_resource:
    :vartype is_public_resource: bool
    :ivar regions:
    :vartype regions: list[~index_service_apis.models.ResourceRegion]
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
        "resource_arm_id": {"key": "resourceArmId", "type": "str"},
        "subscription_id": {"key": "subscriptionId", "type": "str"},
        "resource_group": {"key": "resourceGroup", "type": "str"},
        "resource_name": {"key": "resourceName", "type": "str"},
        "asset_container_type": {"key": "assetContainerType", "type": "str"},
        "immutable_resource_id": {"key": "immutableResourceId", "type": "str"},
        "asset_resource_type": {"key": "assetResourceType", "type": "str"},
        "is_customer_managed_resource": {"key": "isCustomerManagedResource", "type": "bool"},
        "is_private_link_behind_vnet": {"key": "isPrivateLinkBehindVnet", "type": "bool"},
        "is_public_container": {"key": "isPublicContainer", "type": "bool"},
        "is_public_resource": {"key": "isPublicResource", "type": "bool"},
        "regions": {"key": "regions", "type": "[ResourceRegion]"},
    }

    def __init__(
        self,
        *,
        tags: Optional[Dict[str, str]] = None,
        tenant_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        resource_arm_id: Optional[str] = None,
        subscription_id: Optional[str] = None,
        resource_group: Optional[str] = None,
        resource_name: Optional[str] = None,
        asset_container_type: Optional[str] = None,
        immutable_resource_id: Optional[str] = None,
        asset_resource_type: Optional[str] = None,
        is_customer_managed_resource: Optional[bool] = None,
        is_private_link_behind_vnet: Optional[bool] = None,
        is_public_container: Optional[bool] = None,
        is_public_resource: Optional[bool] = None,
        regions: Optional[List["ResourceRegion"]] = None,
        **kwargs
    ):
        """
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword tenant_id:
        :paramtype tenant_id: str
        :keyword resource_id:
        :paramtype resource_id: str
        :keyword resource_arm_id:
        :paramtype resource_arm_id: str
        :keyword subscription_id:
        :paramtype subscription_id: str
        :keyword resource_group:
        :paramtype resource_group: str
        :keyword resource_name:
        :paramtype resource_name: str
        :keyword asset_container_type:
        :paramtype asset_container_type: str
        :keyword immutable_resource_id:
        :paramtype immutable_resource_id: str
        :keyword asset_resource_type:
        :paramtype asset_resource_type: str
        :keyword is_customer_managed_resource:
        :paramtype is_customer_managed_resource: bool
        :keyword is_private_link_behind_vnet:
        :paramtype is_private_link_behind_vnet: bool
        :keyword is_public_container:
        :paramtype is_public_container: bool
        :keyword is_public_resource:
        :paramtype is_public_resource: bool
        :keyword regions:
        :paramtype regions: list[~index_service_apis.models.ResourceRegion]
        """
        super(ResourceDiscoveryContainerMetadata, self).__init__(**kwargs)
        self.tags = tags
        self.tenant_id = tenant_id
        self.resource_id = resource_id
        self.resource_arm_id = resource_arm_id
        self.subscription_id = subscription_id
        self.resource_group = resource_group
        self.resource_name = resource_name
        self.asset_container_type = asset_container_type
        self.immutable_resource_id = immutable_resource_id
        self.asset_resource_type = asset_resource_type
        self.is_customer_managed_resource = is_customer_managed_resource
        self.is_private_link_behind_vnet = is_private_link_behind_vnet
        self.is_public_container = is_public_container
        self.is_public_resource = is_public_resource
        self.regions = regions


class ResourceDiscoveryContainerMetadataPaginatedResult(msrest.serialization.Model):
    """ResourceDiscoveryContainerMetadataPaginatedResult.

    :ivar value:
    :vartype value: list[~index_service_apis.models.ResourceDiscoveryContainerMetadata]
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar next_link:
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[ResourceDiscoveryContainerMetadata]"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["ResourceDiscoveryContainerMetadata"]] = None,
        continuation_token: Optional[str] = None,
        next_link: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword value:
        :paramtype value: list[~index_service_apis.models.ResourceDiscoveryContainerMetadata]
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword next_link:
        :paramtype next_link: str
        """
        super(ResourceDiscoveryContainerMetadataPaginatedResult, self).__init__(**kwargs)
        self.value = value
        self.continuation_token = continuation_token
        self.next_link = next_link


class ResourceDiscoveryRequest(msrest.serialization.Model):
    """ResourceDiscoveryRequest.

    :ivar search_request:
    :vartype search_request: ~index_service_apis.models.StringContainsRequest
    :ivar order_by:
    :vartype order_by: ~index_service_apis.models.IndexEntitiesRequestOrder
    :ivar filters:
    :vartype filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
    :ivar page_size:
    :vartype page_size: int
    :ivar continuation_token:
    :vartype continuation_token: str
    """

    _attribute_map = {
        "search_request": {"key": "searchRequest", "type": "StringContainsRequest"},
        "order_by": {"key": "orderBy", "type": "IndexEntitiesRequestOrder"},
        "filters": {"key": "filters", "type": "[IndexEntitiesRequestFilter]"},
        "page_size": {"key": "pageSize", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
    }

    def __init__(
        self,
        *,
        search_request: Optional["StringContainsRequest"] = None,
        order_by: Optional["IndexEntitiesRequestOrder"] = None,
        filters: Optional[List["IndexEntitiesRequestFilter"]] = None,
        page_size: Optional[int] = None,
        continuation_token: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword search_request:
        :paramtype search_request: ~index_service_apis.models.StringContainsRequest
        :keyword order_by:
        :paramtype order_by: ~index_service_apis.models.IndexEntitiesRequestOrder
        :keyword filters:
        :paramtype filters: list[~index_service_apis.models.IndexEntitiesRequestFilter]
        :keyword page_size:
        :paramtype page_size: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        """
        super(ResourceDiscoveryRequest, self).__init__(**kwargs)
        self.search_request = search_request
        self.order_by = order_by
        self.filters = filters
        self.page_size = page_size
        self.continuation_token = continuation_token


class ResourceInformation(msrest.serialization.Model):
    """ResourceInformation.

    :ivar region:
    :vartype region: str
    :ivar entity_container_type: Possible values include: "Workspace", "Feed", "Registry".
    :vartype entity_container_type: str or ~index_service_apis.models.AssetContainerTypeFeedRename
    :ivar resource_id:
    :vartype resource_id: str
    """

    _attribute_map = {
        "region": {"key": "region", "type": "str"},
        "entity_container_type": {"key": "entityContainerType", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
    }

    def __init__(
        self,
        *,
        region: Optional[str] = None,
        entity_container_type: Optional[Union[str, "AssetContainerTypeFeedRename"]] = None,
        resource_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword region:
        :paramtype region: str
        :keyword entity_container_type: Possible values include: "Workspace", "Feed", "Registry".
        :paramtype entity_container_type: str or
         ~index_service_apis.models.AssetContainerTypeFeedRename
        :keyword resource_id:
        :paramtype resource_id: str
        """
        super(ResourceInformation, self).__init__(**kwargs)
        self.region = region
        self.entity_container_type = entity_container_type
        self.resource_id = resource_id


class ResourceRegion(msrest.serialization.Model):
    """ResourceRegion.

    :ivar region_name:
    :vartype region_name: str
    :ivar is_primary_region:
    :vartype is_primary_region: bool
    """

    _attribute_map = {
        "region_name": {"key": "regionName", "type": "str"},
        "is_primary_region": {"key": "isPrimaryRegion", "type": "bool"},
    }

    def __init__(self, *, region_name: Optional[str] = None, is_primary_region: Optional[bool] = None, **kwargs):
        """
        :keyword region_name:
        :paramtype region_name: str
        :keyword is_primary_region:
        :paramtype is_primary_region: bool
        """
        super(ResourceRegion, self).__init__(**kwargs)
        self.region_name = region_name
        self.is_primary_region = is_primary_region


class RootError(msrest.serialization.Model):
    """RootError.

    :ivar code:
    :vartype code: str
    :ivar severity:
    :vartype severity: int
    :ivar message:
    :vartype message: str
    :ivar message_format:
    :vartype message_format: str
    :ivar message_parameters: Dictionary of :code:`<string>`.
    :vartype message_parameters: dict[str, str]
    :ivar reference_code:
    :vartype reference_code: str
    :ivar details_uri:
    :vartype details_uri: str
    :ivar target:
    :vartype target: str
    :ivar details:
    :vartype details: list[~index_service_apis.models.RootError]
    :ivar inner_error:
    :vartype inner_error: ~index_service_apis.models.InnerErrorResponse
    :ivar debug_info:
    :vartype debug_info: ~index_service_apis.models.DebugInfoResponse
    :ivar additional_info:
    :vartype additional_info: list[~index_service_apis.models.ErrorAdditionalInfo]
    """

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "severity": {"key": "severity", "type": "int"},
        "message": {"key": "message", "type": "str"},
        "message_format": {"key": "messageFormat", "type": "str"},
        "message_parameters": {"key": "messageParameters", "type": "{str}"},
        "reference_code": {"key": "referenceCode", "type": "str"},
        "details_uri": {"key": "detailsUri", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[RootError]"},
        "inner_error": {"key": "innerError", "type": "InnerErrorResponse"},
        "debug_info": {"key": "debugInfo", "type": "DebugInfoResponse"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(
        self,
        *,
        code: Optional[str] = None,
        severity: Optional[int] = None,
        message: Optional[str] = None,
        message_format: Optional[str] = None,
        message_parameters: Optional[Dict[str, str]] = None,
        reference_code: Optional[str] = None,
        details_uri: Optional[str] = None,
        target: Optional[str] = None,
        details: Optional[List["RootError"]] = None,
        inner_error: Optional["InnerErrorResponse"] = None,
        debug_info: Optional["DebugInfoResponse"] = None,
        additional_info: Optional[List["ErrorAdditionalInfo"]] = None,
        **kwargs
    ):
        """
        :keyword code:
        :paramtype code: str
        :keyword severity:
        :paramtype severity: int
        :keyword message:
        :paramtype message: str
        :keyword message_format:
        :paramtype message_format: str
        :keyword message_parameters: Dictionary of :code:`<string>`.
        :paramtype message_parameters: dict[str, str]
        :keyword reference_code:
        :paramtype reference_code: str
        :keyword details_uri:
        :paramtype details_uri: str
        :keyword target:
        :paramtype target: str
        :keyword details:
        :paramtype details: list[~index_service_apis.models.RootError]
        :keyword inner_error:
        :paramtype inner_error: ~index_service_apis.models.InnerErrorResponse
        :keyword debug_info:
        :paramtype debug_info: ~index_service_apis.models.DebugInfoResponse
        :keyword additional_info:
        :paramtype additional_info: list[~index_service_apis.models.ErrorAdditionalInfo]
        """
        super(RootError, self).__init__(**kwargs)
        self.code = code
        self.severity = severity
        self.message = message
        self.message_format = message_format
        self.message_parameters = message_parameters
        self.reference_code = reference_code
        self.details_uri = details_uri
        self.target = target
        self.details = details
        self.inner_error = inner_error
        self.debug_info = debug_info
        self.additional_info = additional_info


class RunAnnotations(msrest.serialization.Model):
    """RunAnnotations.

    All required parameters must be populated in order to send to Azure.

    :ivar display_name: Required.
    :vartype display_name: str
    :ivar status: Required.
    :vartype status: str
    :ivar primary_metric_name: Required.
    :vartype primary_metric_name: str
    :ivar estimated_cost: Required.
    :vartype estimated_cost: float
    :ivar primary_metric_summary: Required.
    :vartype primary_metric_summary: ~index_service_apis.models.RunIndexMetricSummaryJValue
    :ivar metrics: Required. Dictionary of <RunIndexMetricSummary:code:`<Object>`>.
    :vartype metrics: dict[str, ~index_service_apis.models.RunIndexMetricSummaryObject]
    :ivar parameters: Required. Dictionary of :code:`<AnyObject>`.
    :vartype parameters: dict[str, any]
    :ivar settings: Required. Dictionary of :code:`<string>`.
    :vartype settings: dict[str, str]
    :ivar modified_time: Required.
    :vartype modified_time: ~datetime.datetime
    :ivar retain_for_lifetime_of_workspace: Required.
    :vartype retain_for_lifetime_of_workspace: bool
    :ivar error: Required.
    :vartype error: ~index_service_apis.models.IndexedErrorResponse
    :ivar resource_metric_summary: Required.
    :vartype resource_metric_summary: ~index_service_apis.models.RunIndexResourceMetricSummary
    :ivar job_cost: Required.
    :vartype job_cost: ~index_service_apis.models.JobCost
    :ivar compute_duration: Required.
    :vartype compute_duration: str
    :ivar compute_duration_milliseconds: Required.
    :vartype compute_duration_milliseconds: float
    :ivar effective_start_time_utc: Required.
    :vartype effective_start_time_utc: ~datetime.datetime
    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar archived: Required.
    :vartype archived: bool
    :ivar tags: Required. A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "display_name": {"required": True},
        "status": {"required": True},
        "primary_metric_name": {"required": True},
        "estimated_cost": {"required": True},
        "primary_metric_summary": {"required": True},
        "metrics": {"required": True},
        "parameters": {"required": True},
        "settings": {"required": True},
        "modified_time": {"required": True},
        "retain_for_lifetime_of_workspace": {"required": True},
        "error": {"required": True},
        "resource_metric_summary": {"required": True},
        "job_cost": {"required": True},
        "compute_duration": {"required": True},
        "compute_duration_milliseconds": {"required": True},
        "effective_start_time_utc": {"required": True},
        "name": {"required": True},
        "description": {"required": True},
        "archived": {"required": True},
        "tags": {"required": True},
    }

    _attribute_map = {
        "display_name": {"key": "displayName", "type": "str"},
        "status": {"key": "status", "type": "str"},
        "primary_metric_name": {"key": "primaryMetricName", "type": "str"},
        "estimated_cost": {"key": "estimatedCost", "type": "float"},
        "primary_metric_summary": {"key": "primaryMetricSummary", "type": "RunIndexMetricSummaryJValue"},
        "metrics": {"key": "metrics", "type": "{RunIndexMetricSummaryObject}"},
        "parameters": {"key": "parameters", "type": "{object}"},
        "settings": {"key": "settings", "type": "{str}"},
        "modified_time": {"key": "modifiedTime", "type": "iso-8601"},
        "retain_for_lifetime_of_workspace": {"key": "retainForLifetimeOfWorkspace", "type": "bool"},
        "error": {"key": "error", "type": "IndexedErrorResponse"},
        "resource_metric_summary": {"key": "resourceMetricSummary", "type": "RunIndexResourceMetricSummary"},
        "job_cost": {"key": "jobCost", "type": "JobCost"},
        "compute_duration": {"key": "computeDuration", "type": "str"},
        "compute_duration_milliseconds": {"key": "computeDurationMilliseconds", "type": "float"},
        "effective_start_time_utc": {"key": "effectiveStartTimeUtc", "type": "iso-8601"},
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "archived": {"key": "archived", "type": "bool"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        display_name: str,
        status: str,
        primary_metric_name: str,
        estimated_cost: float,
        primary_metric_summary: "RunIndexMetricSummaryJValue",
        metrics: Dict[str, "RunIndexMetricSummaryObject"],
        parameters: Dict[str, Any],
        settings: Dict[str, str],
        modified_time: datetime.datetime,
        retain_for_lifetime_of_workspace: bool,
        error: "IndexedErrorResponse",
        resource_metric_summary: "RunIndexResourceMetricSummary",
        job_cost: "JobCost",
        compute_duration: str,
        compute_duration_milliseconds: float,
        effective_start_time_utc: datetime.datetime,
        name: str,
        description: str,
        archived: bool,
        tags: Dict[str, str],
        **kwargs
    ):
        """
        :keyword display_name: Required.
        :paramtype display_name: str
        :keyword status: Required.
        :paramtype status: str
        :keyword primary_metric_name: Required.
        :paramtype primary_metric_name: str
        :keyword estimated_cost: Required.
        :paramtype estimated_cost: float
        :keyword primary_metric_summary: Required.
        :paramtype primary_metric_summary: ~index_service_apis.models.RunIndexMetricSummaryJValue
        :keyword metrics: Required. Dictionary of <RunIndexMetricSummary:code:`<Object>`>.
        :paramtype metrics: dict[str, ~index_service_apis.models.RunIndexMetricSummaryObject]
        :keyword parameters: Required. Dictionary of :code:`<AnyObject>`.
        :paramtype parameters: dict[str, any]
        :keyword settings: Required. Dictionary of :code:`<string>`.
        :paramtype settings: dict[str, str]
        :keyword modified_time: Required.
        :paramtype modified_time: ~datetime.datetime
        :keyword retain_for_lifetime_of_workspace: Required.
        :paramtype retain_for_lifetime_of_workspace: bool
        :keyword error: Required.
        :paramtype error: ~index_service_apis.models.IndexedErrorResponse
        :keyword resource_metric_summary: Required.
        :paramtype resource_metric_summary: ~index_service_apis.models.RunIndexResourceMetricSummary
        :keyword job_cost: Required.
        :paramtype job_cost: ~index_service_apis.models.JobCost
        :keyword compute_duration: Required.
        :paramtype compute_duration: str
        :keyword compute_duration_milliseconds: Required.
        :paramtype compute_duration_milliseconds: float
        :keyword effective_start_time_utc: Required.
        :paramtype effective_start_time_utc: ~datetime.datetime
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword archived: Required.
        :paramtype archived: bool
        :keyword tags: Required. A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        """
        super(RunAnnotations, self).__init__(**kwargs)
        self.display_name = display_name
        self.status = status
        self.primary_metric_name = primary_metric_name
        self.estimated_cost = estimated_cost
        self.primary_metric_summary = primary_metric_summary
        self.metrics = metrics
        self.parameters = parameters
        self.settings = settings
        self.modified_time = modified_time
        self.retain_for_lifetime_of_workspace = retain_for_lifetime_of_workspace
        self.error = error
        self.resource_metric_summary = resource_metric_summary
        self.job_cost = job_cost
        self.compute_duration = compute_duration
        self.compute_duration_milliseconds = compute_duration_milliseconds
        self.effective_start_time_utc = effective_start_time_utc
        self.name = name
        self.description = description
        self.archived = archived
        self.tags = tags


class RunIndexMetricSummaryJValue(msrest.serialization.Model):
    """RunIndexMetricSummaryJValue.

    All required parameters must be populated in order to send to Azure.

    :ivar count: Required.
    :vartype count: int
    :ivar last_value: Required. Any object.
    :vartype last_value: any
    :ivar minimum_value: Required. Any object.
    :vartype minimum_value: any
    :ivar maximum_value: Required. Any object.
    :vartype maximum_value: any
    :ivar metric_type: Required.
    :vartype metric_type: str
    """

    _validation = {
        "count": {"required": True},
        "last_value": {"required": True},
        "minimum_value": {"required": True},
        "maximum_value": {"required": True},
        "metric_type": {"required": True},
    }

    _attribute_map = {
        "count": {"key": "count", "type": "int"},
        "last_value": {"key": "lastValue", "type": "object"},
        "minimum_value": {"key": "minimumValue", "type": "object"},
        "maximum_value": {"key": "maximumValue", "type": "object"},
        "metric_type": {"key": "metricType", "type": "str"},
    }

    def __init__(
        self, *, count: int, last_value: Any, minimum_value: Any, maximum_value: Any, metric_type: str, **kwargs
    ):
        """
        :keyword count: Required.
        :paramtype count: int
        :keyword last_value: Required. Any object.
        :paramtype last_value: any
        :keyword minimum_value: Required. Any object.
        :paramtype minimum_value: any
        :keyword maximum_value: Required. Any object.
        :paramtype maximum_value: any
        :keyword metric_type: Required.
        :paramtype metric_type: str
        """
        super(RunIndexMetricSummaryJValue, self).__init__(**kwargs)
        self.count = count
        self.last_value = last_value
        self.minimum_value = minimum_value
        self.maximum_value = maximum_value
        self.metric_type = metric_type


class RunIndexMetricSummaryObject(msrest.serialization.Model):
    """RunIndexMetricSummaryObject.

    All required parameters must be populated in order to send to Azure.

    :ivar count: Required.
    :vartype count: int
    :ivar last_value: Required. Anything.
    :vartype last_value: any
    :ivar minimum_value: Required. Anything.
    :vartype minimum_value: any
    :ivar maximum_value: Required. Anything.
    :vartype maximum_value: any
    :ivar metric_type: Required.
    :vartype metric_type: str
    """

    _validation = {
        "count": {"required": True},
        "last_value": {"required": True},
        "minimum_value": {"required": True},
        "maximum_value": {"required": True},
        "metric_type": {"required": True},
    }

    _attribute_map = {
        "count": {"key": "count", "type": "int"},
        "last_value": {"key": "lastValue", "type": "object"},
        "minimum_value": {"key": "minimumValue", "type": "object"},
        "maximum_value": {"key": "maximumValue", "type": "object"},
        "metric_type": {"key": "metricType", "type": "str"},
    }

    def __init__(
        self, *, count: int, last_value: Any, minimum_value: Any, maximum_value: Any, metric_type: str, **kwargs
    ):
        """
        :keyword count: Required.
        :paramtype count: int
        :keyword last_value: Required. Anything.
        :paramtype last_value: any
        :keyword minimum_value: Required. Anything.
        :paramtype minimum_value: any
        :keyword maximum_value: Required. Anything.
        :paramtype maximum_value: any
        :keyword metric_type: Required.
        :paramtype metric_type: str
        """
        super(RunIndexMetricSummaryObject, self).__init__(**kwargs)
        self.count = count
        self.last_value = last_value
        self.minimum_value = minimum_value
        self.maximum_value = maximum_value
        self.metric_type = metric_type


class RunIndexResourceMetricSummary(msrest.serialization.Model):
    """RunIndexResourceMetricSummary.

    All required parameters must be populated in order to send to Azure.

    :ivar gpu_utilization_percent_last_hour: Required.
    :vartype gpu_utilization_percent_last_hour: float
    :ivar gpu_memory_utilization_percent_last_hour: Required.
    :vartype gpu_memory_utilization_percent_last_hour: float
    :ivar gpu_energy_joules: Required.
    :vartype gpu_energy_joules: float
    :ivar resource_metric_names: Required.
    :vartype resource_metric_names: list[str]
    """

    _validation = {
        "gpu_utilization_percent_last_hour": {"required": True},
        "gpu_memory_utilization_percent_last_hour": {"required": True},
        "gpu_energy_joules": {"required": True},
        "resource_metric_names": {"required": True},
    }

    _attribute_map = {
        "gpu_utilization_percent_last_hour": {"key": "gpuUtilizationPercentLastHour", "type": "float"},
        "gpu_memory_utilization_percent_last_hour": {"key": "gpuMemoryUtilizationPercentLastHour", "type": "float"},
        "gpu_energy_joules": {"key": "gpuEnergyJoules", "type": "float"},
        "resource_metric_names": {"key": "resourceMetricNames", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        gpu_utilization_percent_last_hour: float,
        gpu_memory_utilization_percent_last_hour: float,
        gpu_energy_joules: float,
        resource_metric_names: List[str],
        **kwargs
    ):
        """
        :keyword gpu_utilization_percent_last_hour: Required.
        :paramtype gpu_utilization_percent_last_hour: float
        :keyword gpu_memory_utilization_percent_last_hour: Required.
        :paramtype gpu_memory_utilization_percent_last_hour: float
        :keyword gpu_energy_joules: Required.
        :paramtype gpu_energy_joules: float
        :keyword resource_metric_names: Required.
        :paramtype resource_metric_names: list[str]
        """
        super(RunIndexResourceMetricSummary, self).__init__(**kwargs)
        self.gpu_utilization_percent_last_hour = gpu_utilization_percent_last_hour
        self.gpu_memory_utilization_percent_last_hour = gpu_memory_utilization_percent_last_hour
        self.gpu_energy_joules = gpu_energy_joules
        self.resource_metric_names = resource_metric_names


class RunProperties(msrest.serialization.Model):
    """RunProperties.

    All required parameters must be populated in order to send to Azure.

    :ivar data_container_id: Required.
    :vartype data_container_id: str
    :ivar target_name: Required.
    :vartype target_name: str
    :ivar run_name: Required.
    :vartype run_name: str
    :ivar experiment_name: Required.
    :vartype experiment_name: str
    :ivar run_id: Required.
    :vartype run_id: str
    :ivar parent_run_id: Required.
    :vartype parent_run_id: str
    :ivar root_run_id: Required.
    :vartype root_run_id: str
    :ivar run_type: Required.
    :vartype run_type: str
    :ivar run_type_v2: Required.
    :vartype run_type_v2: ~index_service_apis.models.RunTypeV2Index
    :ivar script_name: Required.
    :vartype script_name: str
    :ivar experiment_id: Required.
    :vartype experiment_id: str
    :ivar run_uuid: Required.
    :vartype run_uuid: str
    :ivar parent_run_uuid: Required.
    :vartype parent_run_uuid: str
    :ivar run_number: Required.
    :vartype run_number: int
    :ivar start_time: Required.
    :vartype start_time: ~datetime.datetime
    :ivar end_time: Required.
    :vartype end_time: ~datetime.datetime
    :ivar compute_request: Required.
    :vartype compute_request: ~index_service_apis.models.ComputeRequest
    :ivar compute: Required.
    :vartype compute: ~index_service_apis.models.Compute
    :ivar user_properties: Required. Dictionary of :code:`<string>`.
    :vartype user_properties: dict[str, str]
    :ivar action_uris: Required. Dictionary of :code:`<string>`.
    :vartype action_uris: dict[str, str]
    :ivar duration: Required.
    :vartype duration: str
    :ivar duration_milliseconds: Required.
    :vartype duration_milliseconds: float
    :ivar creation_context: Required.
    :vartype creation_context: ~index_service_apis.models.CreationContext
    """

    _validation = {
        "data_container_id": {"required": True},
        "target_name": {"required": True},
        "run_name": {"required": True},
        "experiment_name": {"required": True},
        "run_id": {"required": True},
        "parent_run_id": {"required": True},
        "root_run_id": {"required": True},
        "run_type": {"required": True},
        "run_type_v2": {"required": True},
        "script_name": {"required": True},
        "experiment_id": {"required": True},
        "run_uuid": {"required": True},
        "parent_run_uuid": {"required": True},
        "run_number": {"required": True},
        "start_time": {"required": True},
        "end_time": {"required": True},
        "compute_request": {"required": True},
        "compute": {"required": True},
        "user_properties": {"required": True},
        "action_uris": {"required": True},
        "duration": {"required": True},
        "duration_milliseconds": {"required": True},
        "creation_context": {"required": True},
    }

    _attribute_map = {
        "data_container_id": {"key": "dataContainerId", "type": "str"},
        "target_name": {"key": "targetName", "type": "str"},
        "run_name": {"key": "runName", "type": "str"},
        "experiment_name": {"key": "experimentName", "type": "str"},
        "run_id": {"key": "runId", "type": "str"},
        "parent_run_id": {"key": "parentRunId", "type": "str"},
        "root_run_id": {"key": "rootRunId", "type": "str"},
        "run_type": {"key": "runType", "type": "str"},
        "run_type_v2": {"key": "runTypeV2", "type": "RunTypeV2Index"},
        "script_name": {"key": "scriptName", "type": "str"},
        "experiment_id": {"key": "experimentId", "type": "str"},
        "run_uuid": {"key": "runUuid", "type": "str"},
        "parent_run_uuid": {"key": "parentRunUuid", "type": "str"},
        "run_number": {"key": "runNumber", "type": "int"},
        "start_time": {"key": "startTime", "type": "iso-8601"},
        "end_time": {"key": "endTime", "type": "iso-8601"},
        "compute_request": {"key": "computeRequest", "type": "ComputeRequest"},
        "compute": {"key": "compute", "type": "Compute"},
        "user_properties": {"key": "userProperties", "type": "{str}"},
        "action_uris": {"key": "actionUris", "type": "{str}"},
        "duration": {"key": "duration", "type": "str"},
        "duration_milliseconds": {"key": "durationMilliseconds", "type": "float"},
        "creation_context": {"key": "creationContext", "type": "CreationContext"},
    }

    def __init__(
        self,
        *,
        data_container_id: str,
        target_name: str,
        run_name: str,
        experiment_name: str,
        run_id: str,
        parent_run_id: str,
        root_run_id: str,
        run_type: str,
        run_type_v2: "RunTypeV2Index",
        script_name: str,
        experiment_id: str,
        run_uuid: str,
        parent_run_uuid: str,
        run_number: int,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        compute_request: "ComputeRequest",
        compute: "Compute",
        user_properties: Dict[str, str],
        action_uris: Dict[str, str],
        duration: str,
        duration_milliseconds: float,
        creation_context: "CreationContext",
        **kwargs
    ):
        """
        :keyword data_container_id: Required.
        :paramtype data_container_id: str
        :keyword target_name: Required.
        :paramtype target_name: str
        :keyword run_name: Required.
        :paramtype run_name: str
        :keyword experiment_name: Required.
        :paramtype experiment_name: str
        :keyword run_id: Required.
        :paramtype run_id: str
        :keyword parent_run_id: Required.
        :paramtype parent_run_id: str
        :keyword root_run_id: Required.
        :paramtype root_run_id: str
        :keyword run_type: Required.
        :paramtype run_type: str
        :keyword run_type_v2: Required.
        :paramtype run_type_v2: ~index_service_apis.models.RunTypeV2Index
        :keyword script_name: Required.
        :paramtype script_name: str
        :keyword experiment_id: Required.
        :paramtype experiment_id: str
        :keyword run_uuid: Required.
        :paramtype run_uuid: str
        :keyword parent_run_uuid: Required.
        :paramtype parent_run_uuid: str
        :keyword run_number: Required.
        :paramtype run_number: int
        :keyword start_time: Required.
        :paramtype start_time: ~datetime.datetime
        :keyword end_time: Required.
        :paramtype end_time: ~datetime.datetime
        :keyword compute_request: Required.
        :paramtype compute_request: ~index_service_apis.models.ComputeRequest
        :keyword compute: Required.
        :paramtype compute: ~index_service_apis.models.Compute
        :keyword user_properties: Required. Dictionary of :code:`<string>`.
        :paramtype user_properties: dict[str, str]
        :keyword action_uris: Required. Dictionary of :code:`<string>`.
        :paramtype action_uris: dict[str, str]
        :keyword duration: Required.
        :paramtype duration: str
        :keyword duration_milliseconds: Required.
        :paramtype duration_milliseconds: float
        :keyword creation_context: Required.
        :paramtype creation_context: ~index_service_apis.models.CreationContext
        """
        super(RunProperties, self).__init__(**kwargs)
        self.data_container_id = data_container_id
        self.target_name = target_name
        self.run_name = run_name
        self.experiment_name = experiment_name
        self.run_id = run_id
        self.parent_run_id = parent_run_id
        self.root_run_id = root_run_id
        self.run_type = run_type
        self.run_type_v2 = run_type_v2
        self.script_name = script_name
        self.experiment_id = experiment_id
        self.run_uuid = run_uuid
        self.parent_run_uuid = parent_run_uuid
        self.run_number = run_number
        self.start_time = start_time
        self.end_time = end_time
        self.compute_request = compute_request
        self.compute = compute
        self.user_properties = user_properties
        self.action_uris = action_uris
        self.duration = duration
        self.duration_milliseconds = duration_milliseconds
        self.creation_context = creation_context


class RunsUnversionedEntitiesResponse(msrest.serialization.Model):
    """RunsUnversionedEntitiesResponse.

    :ivar total_count:
    :vartype total_count: long
    :ivar value:
    :vartype value: list[~index_service_apis.models.RunsUnversionedEntity]
    :ivar next_skip:
    :vartype next_skip: int
    :ivar continuation_token:
    :vartype continuation_token: str
    :ivar entity_container_ids_to_entity_container_metadata: Dictionary of
     :code:`<IndexEntityContainerMetadata>`.
    :vartype entity_container_ids_to_entity_container_metadata: dict[str,
     ~index_service_apis.models.IndexEntityContainerMetadata]
    :ivar number_of_entity_containers_not_queried:
    :vartype number_of_entity_containers_not_queried: int
    :ivar fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
    :vartype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
    :ivar shard_errors: Dictionary of :code:`<ErrorResponse>`.
    :vartype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
    :ivar
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
    :vartype
     is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
     bool
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
        "value": {"key": "value", "type": "[RunsUnversionedEntity]"},
        "next_skip": {"key": "nextSkip", "type": "int"},
        "continuation_token": {"key": "continuationToken", "type": "str"},
        "entity_container_ids_to_entity_container_metadata": {
            "key": "entityContainerIdsToEntityContainerMetadata",
            "type": "{IndexEntityContainerMetadata}",
        },
        "number_of_entity_containers_not_queried": {"key": "numberOfEntityContainersNotQueried", "type": "int"},
        "fanout_data": {"key": "fanoutData", "type": "{SingleShardFanoutData}"},
        "shard_errors": {"key": "shardErrors", "type": "{ErrorResponse}"},
        "is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern": {
            "key": "isMultiWorkspaceQueryWithSkipSetWhichWillNoLongerBeASupportedCallingPattern",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        total_count: Optional[int] = None,
        value: Optional[List["RunsUnversionedEntity"]] = None,
        next_skip: Optional[int] = None,
        continuation_token: Optional[str] = None,
        entity_container_ids_to_entity_container_metadata: Optional[Dict[str, "IndexEntityContainerMetadata"]] = None,
        number_of_entity_containers_not_queried: Optional[int] = None,
        fanout_data: Optional[Dict[str, "SingleShardFanoutData"]] = None,
        shard_errors: Optional[Dict[str, "ErrorResponse"]] = None,
        is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern: Optional[
            bool
        ] = None,
        **kwargs
    ):
        """
        :keyword total_count:
        :paramtype total_count: long
        :keyword value:
        :paramtype value: list[~index_service_apis.models.RunsUnversionedEntity]
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword continuation_token:
        :paramtype continuation_token: str
        :keyword entity_container_ids_to_entity_container_metadata: Dictionary of
         :code:`<IndexEntityContainerMetadata>`.
        :paramtype entity_container_ids_to_entity_container_metadata: dict[str,
         ~index_service_apis.models.IndexEntityContainerMetadata]
        :keyword number_of_entity_containers_not_queried:
        :paramtype number_of_entity_containers_not_queried: int
        :keyword fanout_data: Dictionary of :code:`<SingleShardFanoutData>`.
        :paramtype fanout_data: dict[str, ~index_service_apis.models.SingleShardFanoutData]
        :keyword shard_errors: Dictionary of :code:`<ErrorResponse>`.
        :paramtype shard_errors: dict[str, ~index_service_apis.models.ErrorResponse]
        :keyword
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
        :paramtype
         is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern:
         bool
        """
        super(RunsUnversionedEntitiesResponse, self).__init__(**kwargs)
        self.total_count = total_count
        self.value = value
        self.next_skip = next_skip
        self.continuation_token = continuation_token
        self.entity_container_ids_to_entity_container_metadata = entity_container_ids_to_entity_container_metadata
        self.number_of_entity_containers_not_queried = number_of_entity_containers_not_queried
        self.fanout_data = fanout_data
        self.shard_errors = shard_errors
        self.is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern = (
            is_multi_workspace_query_with_skip_set_which_will_no_longer_be_a_supported_calling_pattern
        )


class RunsUnversionedEntity(msrest.serialization.Model):
    """RunsUnversionedEntity.

    All required parameters must be populated in order to send to Azure.

    :ivar schema_id: Required.
    :vartype schema_id: str
    :ivar entity_id: Required.
    :vartype entity_id: str
    :ivar kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
     "Unversioned".
    :vartype kind: str or ~index_service_apis.models.RunsUnversionedEntityKind
    :ivar annotations: Required.
    :vartype annotations: ~index_service_apis.models.RunAnnotations
    :ivar properties: Required.
    :vartype properties: ~index_service_apis.models.RunProperties
    :ivar internal: Required. Dictionary of :code:`<any>`.
    :vartype internal: dict[str, any]
    :ivar update_sequence: Required.
    :vartype update_sequence: int
    :ivar type: Required.
    :vartype type: str
    :ivar version: Required.
    :vartype version: str
    :ivar entity_container_id: Required.
    :vartype entity_container_id: str
    :ivar entity_object_id: Required.
    :vartype entity_object_id: str
    :ivar resource_type: Required.
    :vartype resource_type: str
    :ivar relationships: Required.
    :vartype relationships: list[~index_service_apis.models.Relationship]
    :ivar asset_id:
    :vartype asset_id: str
    """

    _validation = {
        "schema_id": {"required": True},
        "entity_id": {"required": True},
        "kind": {"required": True},
        "annotations": {"required": True},
        "properties": {"required": True},
        "internal": {"required": True},
        "update_sequence": {"required": True},
        "type": {"required": True},
        "version": {"required": True},
        "entity_container_id": {"required": True},
        "entity_object_id": {"required": True},
        "resource_type": {"required": True},
        "relationships": {"required": True},
    }

    _attribute_map = {
        "schema_id": {"key": "schemaId", "type": "str"},
        "entity_id": {"key": "entityId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
        "annotations": {"key": "annotations", "type": "RunAnnotations"},
        "properties": {"key": "properties", "type": "RunProperties"},
        "internal": {"key": "internal", "type": "{object}"},
        "update_sequence": {"key": "updateSequence", "type": "int"},
        "type": {"key": "type", "type": "str"},
        "version": {"key": "version", "type": "str"},
        "entity_container_id": {"key": "entityContainerId", "type": "str"},
        "entity_object_id": {"key": "entityObjectId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "relationships": {"key": "relationships", "type": "[Relationship]"},
        "asset_id": {"key": "assetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        schema_id: str,
        entity_id: str,
        kind: Union[str, "RunsUnversionedEntityKind"],
        annotations: "RunAnnotations",
        properties: "RunProperties",
        internal: Dict[str, Any],
        update_sequence: int,
        type: str,
        version: str,
        entity_container_id: str,
        entity_object_id: str,
        resource_type: str,
        relationships: List["Relationship"],
        asset_id: Optional[str] = None,
        **kwargs
    ):
        """
        :keyword schema_id: Required.
        :paramtype schema_id: str
        :keyword entity_id: Required.
        :paramtype entity_id: str
        :keyword kind: Required. Possible values include: "Invalid", "LineageRoot", "Versioned",
         "Unversioned".
        :paramtype kind: str or ~index_service_apis.models.RunsUnversionedEntityKind
        :keyword annotations: Required.
        :paramtype annotations: ~index_service_apis.models.RunAnnotations
        :keyword properties: Required.
        :paramtype properties: ~index_service_apis.models.RunProperties
        :keyword internal: Required. Dictionary of :code:`<any>`.
        :paramtype internal: dict[str, any]
        :keyword update_sequence: Required.
        :paramtype update_sequence: int
        :keyword type: Required.
        :paramtype type: str
        :keyword version: Required.
        :paramtype version: str
        :keyword entity_container_id: Required.
        :paramtype entity_container_id: str
        :keyword entity_object_id: Required.
        :paramtype entity_object_id: str
        :keyword resource_type: Required.
        :paramtype resource_type: str
        :keyword relationships: Required.
        :paramtype relationships: list[~index_service_apis.models.Relationship]
        :keyword asset_id:
        :paramtype asset_id: str
        """
        super(RunsUnversionedEntity, self).__init__(**kwargs)
        self.schema_id = schema_id
        self.entity_id = entity_id
        self.kind = kind
        self.annotations = annotations
        self.properties = properties
        self.internal = internal
        self.update_sequence = update_sequence
        self.type = type
        self.version = version
        self.entity_container_id = entity_container_id
        self.entity_object_id = entity_object_id
        self.resource_type = resource_type
        self.relationships = relationships
        self.asset_id = asset_id


class RunTypeV2Index(msrest.serialization.Model):
    """RunTypeV2Index.

    All required parameters must be populated in order to send to Azure.

    :ivar orchestrator: Required.
    :vartype orchestrator: str
    :ivar traits: Required. Dictionary of :code:`<string>`.
    :vartype traits: dict[str, str]
    :ivar attribution: Required.
    :vartype attribution: str
    :ivar compute_type: Required.
    :vartype compute_type: str
    """

    _validation = {
        "orchestrator": {"required": True},
        "traits": {"required": True},
        "attribution": {"required": True},
        "compute_type": {"required": True},
    }

    _attribute_map = {
        "orchestrator": {"key": "orchestrator", "type": "str"},
        "traits": {"key": "traits", "type": "{str}"},
        "attribution": {"key": "attribution", "type": "str"},
        "compute_type": {"key": "computeType", "type": "str"},
    }

    def __init__(self, *, orchestrator: str, traits: Dict[str, str], attribution: str, compute_type: str, **kwargs):
        """
        :keyword orchestrator: Required.
        :paramtype orchestrator: str
        :keyword traits: Required. Dictionary of :code:`<string>`.
        :paramtype traits: dict[str, str]
        :keyword attribution: Required.
        :paramtype attribution: str
        :keyword compute_type: Required.
        :paramtype compute_type: str
        """
        super(RunTypeV2Index, self).__init__(**kwargs)
        self.orchestrator = orchestrator
        self.traits = traits
        self.attribution = attribution
        self.compute_type = compute_type


class SingleShardFanoutData(msrest.serialization.Model):
    """SingleShardFanoutData.

    :ivar next_skip:
    :vartype next_skip: int
    :ivar is_shard_done:
    :vartype is_shard_done: bool
    :ivar did_shard_fail:
    :vartype did_shard_fail: bool
    :ivar total_count:
    :vartype total_count: long
    """

    _attribute_map = {
        "next_skip": {"key": "nextSkip", "type": "int"},
        "is_shard_done": {"key": "isShardDone", "type": "bool"},
        "did_shard_fail": {"key": "didShardFail", "type": "bool"},
        "total_count": {"key": "totalCount", "type": "long"},
    }

    def __init__(
        self,
        *,
        next_skip: Optional[int] = None,
        is_shard_done: Optional[bool] = None,
        did_shard_fail: Optional[bool] = None,
        total_count: Optional[int] = None,
        **kwargs
    ):
        """
        :keyword next_skip:
        :paramtype next_skip: int
        :keyword is_shard_done:
        :paramtype is_shard_done: bool
        :keyword did_shard_fail:
        :paramtype did_shard_fail: bool
        :keyword total_count:
        :paramtype total_count: long
        """
        super(SingleShardFanoutData, self).__init__(**kwargs)
        self.next_skip = next_skip
        self.is_shard_done = is_shard_done
        self.did_shard_fail = did_shard_fail
        self.total_count = total_count


class StringContainsRequest(msrest.serialization.Model):
    """StringContainsRequest.

    :ivar value_to_check:
    :vartype value_to_check: str
    :ivar columns_to_check:
    :vartype columns_to_check: list[str]
    """

    _attribute_map = {
        "value_to_check": {"key": "valueToCheck", "type": "str"},
        "columns_to_check": {"key": "columnsToCheck", "type": "[str]"},
    }

    def __init__(self, *, value_to_check: Optional[str] = None, columns_to_check: Optional[List[str]] = None, **kwargs):
        """
        :keyword value_to_check:
        :paramtype value_to_check: str
        :keyword columns_to_check:
        :paramtype columns_to_check: list[str]
        """
        super(StringContainsRequest, self).__init__(**kwargs)
        self.value_to_check = value_to_check
        self.columns_to_check = columns_to_check


class StructuredInterfaceParameter(msrest.serialization.Model):
    """StructuredInterfaceParameter.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required.
    :vartype name: str
    :ivar description: Required.
    :vartype description: str
    :ivar label: Required.
    :vartype label: str
    :ivar parameter_type: Required. Possible values include: "Int", "Double", "Bool", "String",
     "Undefined".
    :vartype parameter_type: str or ~index_service_apis.models.StructuredInterfaceParameterType
    :ivar is_optional: Required.
    :vartype is_optional: bool
    :ivar default_value: Required.
    :vartype default_value: str
    :ivar lower_bound: Required.
    :vartype lower_bound: str
    :ivar upper_bound: Required.
    :vartype upper_bound: str
    :ivar enum_values: Required.
    :vartype enum_values: list[str]
    """

    _validation = {
        "name": {"required": True},
        "description": {"required": True},
        "label": {"required": True},
        "parameter_type": {"required": True},
        "is_optional": {"required": True},
        "default_value": {"required": True},
        "lower_bound": {"required": True},
        "upper_bound": {"required": True},
        "enum_values": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "label": {"key": "label", "type": "str"},
        "parameter_type": {"key": "parameterType", "type": "str"},
        "is_optional": {"key": "isOptional", "type": "bool"},
        "default_value": {"key": "defaultValue", "type": "str"},
        "lower_bound": {"key": "lowerBound", "type": "str"},
        "upper_bound": {"key": "upperBound", "type": "str"},
        "enum_values": {"key": "enumValues", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        name: str,
        description: str,
        label: str,
        parameter_type: Union[str, "StructuredInterfaceParameterType"],
        is_optional: bool,
        default_value: str,
        lower_bound: str,
        upper_bound: str,
        enum_values: List[str],
        **kwargs
    ):
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword description: Required.
        :paramtype description: str
        :keyword label: Required.
        :paramtype label: str
        :keyword parameter_type: Required. Possible values include: "Int", "Double", "Bool", "String",
         "Undefined".
        :paramtype parameter_type: str or ~index_service_apis.models.StructuredInterfaceParameterType
        :keyword is_optional: Required.
        :paramtype is_optional: bool
        :keyword default_value: Required.
        :paramtype default_value: str
        :keyword lower_bound: Required.
        :paramtype lower_bound: str
        :keyword upper_bound: Required.
        :paramtype upper_bound: str
        :keyword enum_values: Required.
        :paramtype enum_values: list[str]
        """
        super(StructuredInterfaceParameter, self).__init__(**kwargs)
        self.name = name
        self.description = description
        self.label = label
        self.parameter_type = parameter_type
        self.is_optional = is_optional
        self.default_value = default_value
        self.lower_bound = lower_bound
        self.upper_bound = upper_bound
        self.enum_values = enum_values


class TimeDeltaDto(msrest.serialization.Model):
    """TimeDeltaDto.

    All required parameters must be populated in order to send to Azure.

    :ivar days: Required.
    :vartype days: int
    :ivar hours: Required.
    :vartype hours: int
    :ivar minutes: Required.
    :vartype minutes: int
    """

    _validation = {
        "days": {"required": True},
        "hours": {"required": True},
        "minutes": {"required": True},
    }

    _attribute_map = {
        "days": {"key": "days", "type": "int"},
        "hours": {"key": "hours", "type": "int"},
        "minutes": {"key": "minutes", "type": "int"},
    }

    def __init__(self, *, days: int, hours: int, minutes: int, **kwargs):
        """
        :keyword days: Required.
        :paramtype days: int
        :keyword hours: Required.
        :paramtype hours: int
        :keyword minutes: Required.
        :paramtype minutes: int
        """
        super(TimeDeltaDto, self).__init__(**kwargs)
        self.days = days
        self.hours = hours
        self.minutes = minutes


class TimestampColumnDto(msrest.serialization.Model):
    """TimestampColumnDto.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required.
    :vartype name: str
    :ivar format: Required.
    :vartype format: str
    """

    _validation = {
        "name": {"required": True},
        "format": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "format": {"key": "format", "type": "str"},
    }

    def __init__(self, *, name: str, format: str, **kwargs):
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword format: Required.
        :paramtype format: str
        """
        super(TimestampColumnDto, self).__init__(**kwargs)
        self.name = name
        self.format = format


class UnqueriableResourcesScope(msrest.serialization.Model):
    """UnqueriableResourcesScope.

    :ivar resource_set:
    :vartype resource_set: list[~index_service_apis.models.ResourceInformation]
    """

    _attribute_map = {
        "resource_set": {"key": "resourceSet", "type": "[ResourceInformation]"},
    }

    def __init__(self, *, resource_set: Optional[List["ResourceInformation"]] = None, **kwargs):
        """
        :keyword resource_set:
        :paramtype resource_set: list[~index_service_apis.models.ResourceInformation]
        """
        super(UnqueriableResourcesScope, self).__init__(**kwargs)
        self.resource_set = resource_set


class Usage(msrest.serialization.Model):
    """Usage.

    :ivar total_count:
    :vartype total_count: long
    """

    _attribute_map = {
        "total_count": {"key": "totalCount", "type": "long"},
    }

    def __init__(self, *, total_count: Optional[int] = None, **kwargs):
        """
        :keyword total_count:
        :paramtype total_count: long
        """
        super(Usage, self).__init__(**kwargs)
        self.total_count = total_count


class UserDto(msrest.serialization.Model):
    """UserDto.

    All required parameters must be populated in order to send to Azure.

    :ivar user_object_id: Required.
    :vartype user_object_id: str
    :ivar user_tenant_id: Required.
    :vartype user_tenant_id: str
    :ivar user_name: Required.
    :vartype user_name: str
    """

    _validation = {
        "user_object_id": {"required": True},
        "user_tenant_id": {"required": True},
        "user_name": {"required": True},
    }

    _attribute_map = {
        "user_object_id": {"key": "userObjectId", "type": "str"},
        "user_tenant_id": {"key": "userTenantId", "type": "str"},
        "user_name": {"key": "userName", "type": "str"},
    }

    def __init__(self, *, user_object_id: str, user_tenant_id: str, user_name: str, **kwargs):
        """
        :keyword user_object_id: Required.
        :paramtype user_object_id: str
        :keyword user_tenant_id: Required.
        :paramtype user_tenant_id: str
        :keyword user_name: Required.
        :paramtype user_name: str
        """
        super(UserDto, self).__init__(**kwargs)
        self.user_object_id = user_object_id
        self.user_tenant_id = user_tenant_id
        self.user_name = user_name


class UxPresenceResource(msrest.serialization.Model):
    """UxPresenceResource.

    :ivar name:
    :vartype name: str
    :ivar resource_id:
    :vartype resource_id: str
    :ivar resource_type:
    :vartype resource_type: str
    :ivar region:
    :vartype region: str
    :ivar regions:
    :vartype regions: list[~index_service_apis.models.ResourceRegion]
    :ivar subscription_id:
    :vartype subscription_id: str
    :ivar resource_group_name:
    :vartype resource_group_name: str
    :ivar tags: A set of tags. Dictionary of :code:`<string>`.
    :vartype tags: dict[str, str]
    :ivar is_customer_managed:
    :vartype is_customer_managed: bool
    :ivar is_private_link_resource:
    :vartype is_private_link_resource: bool
    :ivar is_private_link_resource_behind_vnet:
    :vartype is_private_link_resource_behind_vnet: bool
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "resource_id": {"key": "resourceId", "type": "str"},
        "resource_type": {"key": "resourceType", "type": "str"},
        "region": {"key": "region", "type": "str"},
        "regions": {"key": "regions", "type": "[ResourceRegion]"},
        "subscription_id": {"key": "subscriptionId", "type": "str"},
        "resource_group_name": {"key": "resourceGroupName", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "is_customer_managed": {"key": "isCustomerManaged", "type": "bool"},
        "is_private_link_resource": {"key": "isPrivateLinkResource", "type": "bool"},
        "is_private_link_resource_behind_vnet": {"key": "isPrivateLinkResourceBehindVnet", "type": "bool"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        resource_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        region: Optional[str] = None,
        regions: Optional[List["ResourceRegion"]] = None,
        subscription_id: Optional[str] = None,
        resource_group_name: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        is_customer_managed: Optional[bool] = None,
        is_private_link_resource: Optional[bool] = None,
        is_private_link_resource_behind_vnet: Optional[bool] = None,
        **kwargs
    ):
        """
        :keyword name:
        :paramtype name: str
        :keyword resource_id:
        :paramtype resource_id: str
        :keyword resource_type:
        :paramtype resource_type: str
        :keyword region:
        :paramtype region: str
        :keyword regions:
        :paramtype regions: list[~index_service_apis.models.ResourceRegion]
        :keyword subscription_id:
        :paramtype subscription_id: str
        :keyword resource_group_name:
        :paramtype resource_group_name: str
        :keyword tags: A set of tags. Dictionary of :code:`<string>`.
        :paramtype tags: dict[str, str]
        :keyword is_customer_managed:
        :paramtype is_customer_managed: bool
        :keyword is_private_link_resource:
        :paramtype is_private_link_resource: bool
        :keyword is_private_link_resource_behind_vnet:
        :paramtype is_private_link_resource_behind_vnet: bool
        """
        super(UxPresenceResource, self).__init__(**kwargs)
        self.name = name
        self.resource_id = resource_id
        self.resource_type = resource_type
        self.region = region
        self.regions = regions
        self.subscription_id = subscription_id
        self.resource_group_name = resource_group_name
        self.tags = tags
        self.is_customer_managed = is_customer_managed
        self.is_private_link_resource = is_private_link_resource
        self.is_private_link_resource_behind_vnet = is_private_link_resource_behind_vnet


class UxWarmUpRequest(msrest.serialization.Model):
    """UxWarmUpRequest.

    :ivar entity_types:
    :vartype entity_types: list[str]
    :ivar resource_ids:
    :vartype resource_ids: list[str]
    """

    _attribute_map = {
        "entity_types": {"key": "entityTypes", "type": "[str]"},
        "resource_ids": {"key": "resourceIds", "type": "[str]"},
    }

    def __init__(self, *, entity_types: Optional[List[str]] = None, resource_ids: Optional[List[str]] = None, **kwargs):
        """
        :keyword entity_types:
        :paramtype entity_types: list[str]
        :keyword resource_ids:
        :paramtype resource_ids: list[str]
        """
        super(UxWarmUpRequest, self).__init__(**kwargs)
        self.entity_types = entity_types
        self.resource_ids = resource_ids


class VersionedAttribute(msrest.serialization.Model):
    """VersionedAttribute.

    All required parameters must be populated in order to send to Azure.

    :ivar name: Required.
    :vartype name: str
    :ivar version: Required.
    :vartype version: str
    """

    _validation = {
        "name": {"required": True},
        "version": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "version": {"key": "version", "type": "str"},
    }

    def __init__(self, *, name: str, version: str, **kwargs):
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword version: Required.
        :paramtype version: str
        """
        super(VersionedAttribute, self).__init__(**kwargs)
        self.name = name
        self.version = version


class Webhook(msrest.serialization.Model):
    """Webhook.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar webhook_type:  Has constant value: "AzureDevOps".
    :vartype webhook_type: str
    :ivar event_type: Required.
    :vartype event_type: str
    """

    _validation = {
        "webhook_type": {"required": True, "constant": True},
        "event_type": {"required": True},
    }

    _attribute_map = {
        "webhook_type": {"key": "webhookType", "type": "str"},
        "event_type": {"key": "eventType", "type": "str"},
    }

    webhook_type = "AzureDevOps"

    def __init__(self, *, event_type: str, **kwargs):
        """
        :keyword event_type: Required.
        :paramtype event_type: str
        """
        super(Webhook, self).__init__(**kwargs)
        self.event_type = event_type


class WorkspaceContextWarmUpRequest(msrest.serialization.Model):
    """WorkspaceContextWarmUpRequest.

    :ivar discover_registries:
    :vartype discover_registries: bool
    """

    _attribute_map = {
        "discover_registries": {"key": "discoverRegistries", "type": "bool"},
    }

    def __init__(self, *, discover_registries: Optional[bool] = None, **kwargs):
        """
        :keyword discover_registries:
        :paramtype discover_registries: bool
        """
        super(WorkspaceContextWarmUpRequest, self).__init__(**kwargs)
        self.discover_registries = discover_registries
