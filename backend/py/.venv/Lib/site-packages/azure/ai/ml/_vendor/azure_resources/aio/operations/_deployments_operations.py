# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
from typing import Any, AsyncIterable, Callable, Dict, Optional, TypeVar, Union

from azure.core.async_paging import AsyncItemPaged, AsyncList
from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.pipeline.transport import As<PERSON><PERSON>ttp<PERSON><PERSON>ponse, HttpRequest
from azure.core.polling import Async<PERSON><PERSON><PERSON><PERSON>, AsyncNoPolling, AsyncPollingMethod
from azure.mgmt.core.exceptions import ARMErrorFormat
from azure.mgmt.core.polling.async_arm_polling import AsyncARMPolling

from ... import models

T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, AsyncHttpResponse], T, Dict[str, Any]], Any]]


class DeploymentsOperations:
    """DeploymentsOperations async operations.

    You should not instantiate this class directly. Instead, you should create a Client instance that
    instantiates it for you and attaches it as an attribute.

    :ivar models: Alias to model classes used in this operation group.
    :type models: ~azure.mgmt.resource.resources.v2020_06_01.models
    :param client: Client for service requests.
    :param config: Configuration of service client.
    :param serializer: An object model serializer.
    :param deserializer: An object model deserializer.
    """

    models = models

    def __init__(self, client, config, serializer, deserializer) -> None:
        self._client = client
        self._serialize = serializer
        self._deserialize = deserializer
        self._config = config

    async def _delete_at_scope_initial(self, scope: str, deployment_name: str, **kwargs) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self._delete_at_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_at_scope_initial.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_delete_at_scope(self, scope: str, deployment_name: str, **kwargs) -> AsyncLROPoller[None]:
        """Deletes a deployment from the deployment history.

        A template deployment that is currently running cannot be deleted. Deleting a template
        deployment removes the associated deployment operations. This is an asynchronous operation that
        returns a status of 202 until the template deployment is successfully deleted. The Location
        response header contains the URI that is used to obtain the status of the process. While the
        process is running, a call to the URI in the Location header returns a status of 202. When the
        process finishes, the URI in the Location header returns a status of 204 on success. If the
        asynchronous request failed, the URI in the Location header returns an error-level status code.

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_at_scope_initial(
                scope=scope, deployment_name=deployment_name, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def check_existence_at_scope(self, scope: str, deployment_name: str, **kwargs) -> bool:
        """Checks whether the deployment exists.

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.check_existence_at_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def _create_or_update_at_scope_initial(
        self, scope: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> "models.DeploymentExtended":
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_at_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "Deployment")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_at_scope_initial.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_create_or_update_at_scope(
        self, scope: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentExtended"]:
        """Deploys resources at a given scope.

        You can provide the template and parameters directly in the request or link to JSON files.

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Additional parameters supplied to the operation.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.Deployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentExtended or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_at_scope_initial(
                scope=scope, deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def get_at_scope(self, scope: str, deployment_name: str, **kwargs) -> "models.DeploymentExtended":
        """Gets a deployment.

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExtended, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.get_at_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def cancel_at_scope(self, scope: str, deployment_name: str, **kwargs) -> None:
        """Cancels a currently running template deployment.

        You can cancel a deployment only if the provisioningState is Accepted or Running. After the
        deployment is canceled, the provisioningState is set to Canceled. Canceling a template
        deployment stops the currently running template deployment and leaves the resources partially
        deployed.

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: None, or the result of cls(response)
        :rtype: None
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.cancel_at_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    cancel_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}/cancel"}  # type: ignore

    async def _validate_at_scope_initial(
        self, scope: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> Optional["models.DeploymentValidateResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.DeploymentValidateResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._validate_at_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "Deployment")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 400]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if response.status_code == 400:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _validate_at_scope_initial.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def begin_validate_at_scope(
        self, scope: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentValidateResult"]:
        """Validates whether the specified template is syntactically correct and will be accepted by Azure
        Resource Manager..

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.Deployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentValidateResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentValidateResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentValidateResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._validate_at_scope_initial(
                scope=scope, deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_validate_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def export_template_at_scope(
        self, scope: str, deployment_name: str, **kwargs
    ) -> "models.DeploymentExportResult":
        """Exports the template used for specified deployment.

        :param scope: The resource scope.
        :type scope: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExportResult, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExportResult
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExportResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.export_template_at_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExportResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    export_template_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/{deploymentName}/exportTemplate"}  # type: ignore

    def list_at_scope(
        self, scope: str, filter: Optional[str] = None, top: Optional[int] = None, **kwargs
    ) -> AsyncIterable["models.DeploymentListResult"]:
        """Get all the deployments at the given scope.

        :param scope: The resource scope.
        :type scope: str
        :param filter: The filter to apply on the operation. For example, you can use
         $filter=provisioningState eq '{state}'.
        :type filter: str
        :param top: The number of results to get. If null is passed, returns all deployments.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either DeploymentListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list_at_scope.metadata["url"]  # type: ignore
                path_format_arguments = {
                    "scope": self._serialize.url("scope", scope, "str", skip_quote=True),
                }
                url = self._client.format_url(url, **path_format_arguments)
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("DeploymentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_at_scope.metadata = {"url": "/{scope}/providers/Microsoft.Resources/deployments/"}  # type: ignore

    async def _delete_at_tenant_scope_initial(self, deployment_name: str, **kwargs) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self._delete_at_tenant_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_at_tenant_scope_initial.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_delete_at_tenant_scope(self, deployment_name: str, **kwargs) -> AsyncLROPoller[None]:
        """Deletes a deployment from the deployment history.

        A template deployment that is currently running cannot be deleted. Deleting a template
        deployment removes the associated deployment operations. This is an asynchronous operation that
        returns a status of 202 until the template deployment is successfully deleted. The Location
        response header contains the URI that is used to obtain the status of the process. While the
        process is running, a call to the URI in the Location header returns a status of 202. When the
        process finishes, the URI in the Location header returns a status of 204 on success. If the
        asynchronous request failed, the URI in the Location header returns an error-level status code.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_at_tenant_scope_initial(
                deployment_name=deployment_name, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def check_existence_at_tenant_scope(self, deployment_name: str, **kwargs) -> bool:
        """Checks whether the deployment exists.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.check_existence_at_tenant_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def _create_or_update_at_tenant_scope_initial(
        self, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> "models.DeploymentExtended":
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_at_tenant_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ScopedDeployment")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_at_tenant_scope_initial.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_create_or_update_at_tenant_scope(
        self, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentExtended"]:
        """Deploys resources at tenant scope.

        You can provide the template and parameters directly in the request or link to JSON files.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Additional parameters supplied to the operation.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ScopedDeployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentExtended or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_at_tenant_scope_initial(
                deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def get_at_tenant_scope(self, deployment_name: str, **kwargs) -> "models.DeploymentExtended":
        """Gets a deployment.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExtended, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.get_at_tenant_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def cancel_at_tenant_scope(self, deployment_name: str, **kwargs) -> None:
        """Cancels a currently running template deployment.

        You can cancel a deployment only if the provisioningState is Accepted or Running. After the
        deployment is canceled, the provisioningState is set to Canceled. Canceling a template
        deployment stops the currently running template deployment and leaves the resources partially
        deployed.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: None, or the result of cls(response)
        :rtype: None
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.cancel_at_tenant_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    cancel_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}/cancel"}  # type: ignore

    async def _validate_at_tenant_scope_initial(
        self, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> Optional["models.DeploymentValidateResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.DeploymentValidateResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._validate_at_tenant_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ScopedDeployment")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 400]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if response.status_code == 400:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _validate_at_tenant_scope_initial.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def begin_validate_at_tenant_scope(
        self, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentValidateResult"]:
        """Validates whether the specified template is syntactically correct and will be accepted by Azure
        Resource Manager..

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ScopedDeployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentValidateResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentValidateResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentValidateResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._validate_at_tenant_scope_initial(
                deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_validate_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def _what_if_at_tenant_scope_initial(
        self, deployment_name: str, parameters: "models.ScopedDeploymentWhatIf", **kwargs
    ) -> Optional["models.WhatIfOperationResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.WhatIfOperationResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._what_if_at_tenant_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ScopedDeploymentWhatIf")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        response_headers = {}
        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

        if response.status_code == 202:
            response_headers["Location"] = self._deserialize("str", response.headers.get("Location"))
            response_headers["Retry-After"] = self._deserialize("str", response.headers.get("Retry-After"))

        if cls:
            return cls(pipeline_response, deserialized, response_headers)

        return deserialized

    _what_if_at_tenant_scope_initial.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def begin_what_if_at_tenant_scope(
        self, deployment_name: str, parameters: "models.ScopedDeploymentWhatIf", **kwargs
    ) -> AsyncLROPoller["models.WhatIfOperationResult"]:
        """Returns changes that will be made by the deployment if executed at the scope of the tenant
        group.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ScopedDeploymentWhatIf
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either WhatIfOperationResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfOperationResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.WhatIfOperationResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._what_if_at_tenant_scope_initial(
                deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, lro_options={"final-state-via": "location"}, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_what_if_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def export_template_at_tenant_scope(self, deployment_name: str, **kwargs) -> "models.DeploymentExportResult":
        """Exports the template used for specified deployment.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExportResult, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExportResult
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExportResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.export_template_at_tenant_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExportResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    export_template_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/{deploymentName}/exportTemplate"}  # type: ignore

    def list_at_tenant_scope(
        self, filter: Optional[str] = None, top: Optional[int] = None, **kwargs
    ) -> AsyncIterable["models.DeploymentListResult"]:
        """Get all the deployments at the tenant scope.

        :param filter: The filter to apply on the operation. For example, you can use
         $filter=provisioningState eq '{state}'.
        :type filter: str
        :param top: The number of results to get. If null is passed, returns all deployments.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either DeploymentListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list_at_tenant_scope.metadata["url"]  # type: ignore
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("DeploymentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_at_tenant_scope.metadata = {"url": "/providers/Microsoft.Resources/deployments/"}  # type: ignore

    async def _delete_at_management_group_scope_initial(self, group_id: str, deployment_name: str, **kwargs) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self._delete_at_management_group_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_at_management_group_scope_initial.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_delete_at_management_group_scope(
        self, group_id: str, deployment_name: str, **kwargs
    ) -> AsyncLROPoller[None]:
        """Deletes a deployment from the deployment history.

        A template deployment that is currently running cannot be deleted. Deleting a template
        deployment removes the associated deployment operations. This is an asynchronous operation that
        returns a status of 202 until the template deployment is successfully deleted. The Location
        response header contains the URI that is used to obtain the status of the process. While the
        process is running, a call to the URI in the Location header returns a status of 202. When the
        process finishes, the URI in the Location header returns a status of 204 on success. If the
        asynchronous request failed, the URI in the Location header returns an error-level status code.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_at_management_group_scope_initial(
                group_id=group_id, deployment_name=deployment_name, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def check_existence_at_management_group_scope(self, group_id: str, deployment_name: str, **kwargs) -> bool:
        """Checks whether the deployment exists.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.check_existence_at_management_group_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def _create_or_update_at_management_group_scope_initial(
        self, group_id: str, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> "models.DeploymentExtended":
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_at_management_group_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ScopedDeployment")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_at_management_group_scope_initial.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_create_or_update_at_management_group_scope(
        self, group_id: str, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentExtended"]:
        """Deploys resources at management group scope.

        You can provide the template and parameters directly in the request or link to JSON files.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Additional parameters supplied to the operation.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ScopedDeployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentExtended or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_at_management_group_scope_initial(
                group_id=group_id,
                deployment_name=deployment_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def get_at_management_group_scope(
        self, group_id: str, deployment_name: str, **kwargs
    ) -> "models.DeploymentExtended":
        """Gets a deployment.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExtended, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.get_at_management_group_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def cancel_at_management_group_scope(self, group_id: str, deployment_name: str, **kwargs) -> None:
        """Cancels a currently running template deployment.

        You can cancel a deployment only if the provisioningState is Accepted or Running. After the
        deployment is canceled, the provisioningState is set to Canceled. Canceling a template
        deployment stops the currently running template deployment and leaves the resources partially
        deployed.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: None, or the result of cls(response)
        :rtype: None
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.cancel_at_management_group_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    cancel_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}/cancel"}  # type: ignore

    async def _validate_at_management_group_scope_initial(
        self, group_id: str, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> Optional["models.DeploymentValidateResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.DeploymentValidateResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._validate_at_management_group_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ScopedDeployment")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 400]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if response.status_code == 400:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _validate_at_management_group_scope_initial.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def begin_validate_at_management_group_scope(
        self, group_id: str, deployment_name: str, parameters: "models.ScopedDeployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentValidateResult"]:
        """Validates whether the specified template is syntactically correct and will be accepted by Azure
        Resource Manager..

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ScopedDeployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentValidateResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentValidateResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentValidateResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._validate_at_management_group_scope_initial(
                group_id=group_id,
                deployment_name=deployment_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_validate_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def _what_if_at_management_group_scope_initial(
        self, group_id: str, deployment_name: str, parameters: "models.ScopedDeploymentWhatIf", **kwargs
    ) -> Optional["models.WhatIfOperationResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.WhatIfOperationResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._what_if_at_management_group_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "ScopedDeploymentWhatIf")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        response_headers = {}
        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

        if response.status_code == 202:
            response_headers["Location"] = self._deserialize("str", response.headers.get("Location"))
            response_headers["Retry-After"] = self._deserialize("str", response.headers.get("Retry-After"))

        if cls:
            return cls(pipeline_response, deserialized, response_headers)

        return deserialized

    _what_if_at_management_group_scope_initial.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def begin_what_if_at_management_group_scope(
        self, group_id: str, deployment_name: str, parameters: "models.ScopedDeploymentWhatIf", **kwargs
    ) -> AsyncLROPoller["models.WhatIfOperationResult"]:
        """Returns changes that will be made by the deployment if executed at the scope of the management
        group.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.ScopedDeploymentWhatIf
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either WhatIfOperationResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfOperationResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.WhatIfOperationResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._what_if_at_management_group_scope_initial(
                group_id=group_id,
                deployment_name=deployment_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, lro_options={"final-state-via": "location"}, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_what_if_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def export_template_at_management_group_scope(
        self, group_id: str, deployment_name: str, **kwargs
    ) -> "models.DeploymentExportResult":
        """Exports the template used for specified deployment.

        :param group_id: The management group ID.
        :type group_id: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExportResult, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExportResult
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExportResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.export_template_at_management_group_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExportResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    export_template_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/{deploymentName}/exportTemplate"}  # type: ignore

    def list_at_management_group_scope(
        self, group_id: str, filter: Optional[str] = None, top: Optional[int] = None, **kwargs
    ) -> AsyncIterable["models.DeploymentListResult"]:
        """Get all the deployments for a management group.

        :param group_id: The management group ID.
        :type group_id: str
        :param filter: The filter to apply on the operation. For example, you can use
         $filter=provisioningState eq '{state}'.
        :type filter: str
        :param top: The number of results to get. If null is passed, returns all deployments.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either DeploymentListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list_at_management_group_scope.metadata["url"]  # type: ignore
                path_format_arguments = {
                    "groupId": self._serialize.url("group_id", group_id, "str", max_length=90, min_length=1),
                }
                url = self._client.format_url(url, **path_format_arguments)
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("DeploymentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_at_management_group_scope.metadata = {"url": "/providers/Microsoft.Management/managementGroups/{groupId}/providers/Microsoft.Resources/deployments/"}  # type: ignore

    async def _delete_at_subscription_scope_initial(self, deployment_name: str, **kwargs) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self._delete_at_subscription_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_at_subscription_scope_initial.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_delete_at_subscription_scope(self, deployment_name: str, **kwargs) -> AsyncLROPoller[None]:
        """Deletes a deployment from the deployment history.

        A template deployment that is currently running cannot be deleted. Deleting a template
        deployment removes the associated deployment operations. This is an asynchronous operation that
        returns a status of 202 until the template deployment is successfully deleted. The Location
        response header contains the URI that is used to obtain the status of the process. While the
        process is running, a call to the URI in the Location header returns a status of 202. When the
        process finishes, the URI in the Location header returns a status of 204 on success. If the
        asynchronous request failed, the URI in the Location header returns an error-level status code.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_at_subscription_scope_initial(
                deployment_name=deployment_name, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def check_existence_at_subscription_scope(self, deployment_name: str, **kwargs) -> bool:
        """Checks whether the deployment exists.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.check_existence_at_subscription_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def _create_or_update_at_subscription_scope_initial(
        self, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> "models.DeploymentExtended":
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_at_subscription_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "Deployment")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_at_subscription_scope_initial.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_create_or_update_at_subscription_scope(
        self, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentExtended"]:
        """Deploys resources at subscription scope.

        You can provide the template and parameters directly in the request or link to JSON files.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Additional parameters supplied to the operation.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.Deployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentExtended or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_at_subscription_scope_initial(
                deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def get_at_subscription_scope(self, deployment_name: str, **kwargs) -> "models.DeploymentExtended":
        """Gets a deployment.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExtended, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.get_at_subscription_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def cancel_at_subscription_scope(self, deployment_name: str, **kwargs) -> None:
        """Cancels a currently running template deployment.

        You can cancel a deployment only if the provisioningState is Accepted or Running. After the
        deployment is canceled, the provisioningState is set to Canceled. Canceling a template
        deployment stops the currently running template deployment and leaves the resources partially
        deployed.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: None, or the result of cls(response)
        :rtype: None
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.cancel_at_subscription_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    cancel_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}/cancel"}  # type: ignore

    async def _validate_at_subscription_scope_initial(
        self, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> Optional["models.DeploymentValidateResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.DeploymentValidateResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._validate_at_subscription_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "Deployment")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 400]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if response.status_code == 400:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _validate_at_subscription_scope_initial.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def begin_validate_at_subscription_scope(
        self, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentValidateResult"]:
        """Validates whether the specified template is syntactically correct and will be accepted by Azure
        Resource Manager..

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.Deployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentValidateResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentValidateResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentValidateResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._validate_at_subscription_scope_initial(
                deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_validate_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def _what_if_at_subscription_scope_initial(
        self, deployment_name: str, parameters: "models.DeploymentWhatIf", **kwargs
    ) -> Optional["models.WhatIfOperationResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.WhatIfOperationResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._what_if_at_subscription_scope_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "DeploymentWhatIf")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        response_headers = {}
        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

        if response.status_code == 202:
            response_headers["Location"] = self._deserialize("str", response.headers.get("Location"))
            response_headers["Retry-After"] = self._deserialize("str", response.headers.get("Retry-After"))

        if cls:
            return cls(pipeline_response, deserialized, response_headers)

        return deserialized

    _what_if_at_subscription_scope_initial.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def begin_what_if_at_subscription_scope(
        self, deployment_name: str, parameters: "models.DeploymentWhatIf", **kwargs
    ) -> AsyncLROPoller["models.WhatIfOperationResult"]:
        """Returns changes that will be made by the deployment if executed at the scope of the
        subscription.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to What If.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentWhatIf
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either WhatIfOperationResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfOperationResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.WhatIfOperationResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._what_if_at_subscription_scope_initial(
                deployment_name=deployment_name, parameters=parameters, cls=lambda x, y, z: x, **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, lro_options={"final-state-via": "location"}, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_what_if_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def export_template_at_subscription_scope(
        self, deployment_name: str, **kwargs
    ) -> "models.DeploymentExportResult":
        """Exports the template used for specified deployment.

        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExportResult, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExportResult
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExportResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.export_template_at_subscription_scope.metadata["url"]  # type: ignore
        path_format_arguments = {
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExportResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    export_template_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/{deploymentName}/exportTemplate"}  # type: ignore

    def list_at_subscription_scope(
        self, filter: Optional[str] = None, top: Optional[int] = None, **kwargs
    ) -> AsyncIterable["models.DeploymentListResult"]:
        """Get all the deployments for a subscription.

        :param filter: The filter to apply on the operation. For example, you can use
         $filter=provisioningState eq '{state}'.
        :type filter: str
        :param top: The number of results to get. If null is passed, returns all deployments.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either DeploymentListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list_at_subscription_scope.metadata["url"]  # type: ignore
                path_format_arguments = {
                    "subscriptionId": self._serialize.url(
                        "self._config.subscription_id", self._config.subscription_id, "str"
                    ),
                }
                url = self._client.format_url(url, **path_format_arguments)
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("DeploymentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_at_subscription_scope.metadata = {"url": "/subscriptions/{subscriptionId}/providers/Microsoft.Resources/deployments/"}  # type: ignore

    async def _delete_initial(self, resource_group_name: str, deployment_name: str, **kwargs) -> None:
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self._delete_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.delete(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [202, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    _delete_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_delete(self, resource_group_name: str, deployment_name: str, **kwargs) -> AsyncLROPoller[None]:
        """Deletes a deployment from the deployment history.

        A template deployment that is currently running cannot be deleted. Deleting a template
        deployment removes the associated deployment operations. Deleting a template deployment does
        not affect the state of the resource group. This is an asynchronous operation that returns a
        status of 202 until the template deployment is successfully deleted. The Location response
        header contains the URI that is used to obtain the status of the process. While the process is
        running, a call to the URI in the Location header returns a status of 202. When the process
        finishes, the URI in the Location header returns a status of 204 on success. If the
        asynchronous request failed, the URI in the Location header returns an error-level status code.

        :param resource_group_name: The name of the resource group with the deployment to delete. The
         name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either None or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[None]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._delete_initial(
                resource_group_name=resource_group_name,
                deployment_name=deployment_name,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            if cls:
                return cls(pipeline_response, None, {})

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_delete.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def check_existence(self, resource_group_name: str, deployment_name: str, **kwargs) -> bool:
        """Checks whether the deployment exists.

        :param resource_group_name: The name of the resource group with the deployment to check. The
         name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: bool, or the result of cls(response)
        :rtype: bool
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.check_existence.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.head(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204, 404]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

        return 200 <= response.status_code <= 299

    check_existence.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def _create_or_update_initial(
        self, resource_group_name: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> "models.DeploymentExtended":
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._create_or_update_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "Deployment")
        body_content_kwargs["content"] = body_content
        request = self._client.put(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _create_or_update_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def begin_create_or_update(
        self, resource_group_name: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentExtended"]:
        """Deploys resources to a resource group.

        You can provide the template and parameters directly in the request or link to JSON files.

        :param resource_group_name: The name of the resource group to deploy the resources to. The name
         is case insensitive. The resource group must already exist.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Additional parameters supplied to the operation.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.Deployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentExtended or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._create_or_update_initial(
                resource_group_name=resource_group_name,
                deployment_name=deployment_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentExtended", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_create_or_update.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def get(self, resource_group_name: str, deployment_name: str, **kwargs) -> "models.DeploymentExtended":
        """Gets a deployment.

        :param resource_group_name: The name of the resource group. The name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExtended, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExtended
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExtended"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.get.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.get(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExtended", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}"}  # type: ignore

    async def cancel(self, resource_group_name: str, deployment_name: str, **kwargs) -> None:
        """Cancels a currently running template deployment.

        You can cancel a deployment only if the provisioningState is Accepted or Running. After the
        deployment is canceled, the provisioningState is set to Canceled. Canceling a template
        deployment stops the currently running template deployment and leaves the resource group
        partially deployed.

        :param resource_group_name: The name of the resource group. The name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: None, or the result of cls(response)
        :rtype: None
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType[None]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.cancel.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    cancel.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}/cancel"}  # type: ignore

    async def _validate_initial(
        self, resource_group_name: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> Optional["models.DeploymentValidateResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.DeploymentValidateResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._validate_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "Deployment")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202, 400]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if response.status_code == 400:
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    _validate_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def begin_validate(
        self, resource_group_name: str, deployment_name: str, parameters: "models.Deployment", **kwargs
    ) -> AsyncLROPoller["models.DeploymentValidateResult"]:
        """Validates whether the specified template is syntactically correct and will be accepted by Azure
        Resource Manager..

        :param resource_group_name: The name of the resource group the template will be deployed to.
         The name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.Deployment
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either DeploymentValidateResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentValidateResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentValidateResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._validate_initial(
                resource_group_name=resource_group_name,
                deployment_name=deployment_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("DeploymentValidateResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_validate.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}/validate"}  # type: ignore

    async def _what_if_initial(
        self, resource_group_name: str, deployment_name: str, parameters: "models.DeploymentWhatIf", **kwargs
    ) -> Optional["models.WhatIfOperationResult"]:
        cls = kwargs.pop("cls", None)  # type: ClsType[Optional["models.WhatIfOperationResult"]]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self._what_if_initial.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(parameters, "DeploymentWhatIf")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200, 202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        response_headers = {}
        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

        if response.status_code == 202:
            response_headers["Location"] = self._deserialize("str", response.headers.get("Location"))
            response_headers["Retry-After"] = self._deserialize("str", response.headers.get("Retry-After"))

        if cls:
            return cls(pipeline_response, deserialized, response_headers)

        return deserialized

    _what_if_initial.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def begin_what_if(
        self, resource_group_name: str, deployment_name: str, parameters: "models.DeploymentWhatIf", **kwargs
    ) -> AsyncLROPoller["models.WhatIfOperationResult"]:
        """Returns changes that will be made by the deployment if executed at the scope of the resource
        group.

        :param resource_group_name: The name of the resource group the template will be deployed to.
         The name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :param parameters: Parameters to validate.
        :type parameters: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentWhatIf
        :keyword callable cls: A custom type or function that will be passed the direct response
        :keyword str continuation_token: A continuation token to restart a poller from a saved state.
        :keyword polling: True for ARMPolling, False for no polling, or a
         polling object for personal polling strategy
        :paramtype polling: bool or ~azure.core.polling.AsyncPollingMethod
        :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
        :return: An instance of AsyncLROPoller that returns either WhatIfOperationResult or the result of cls(response)
        :rtype: ~azure.core.polling.AsyncLROPoller[~azure.mgmt.resource.resources.v2020_06_01.models.WhatIfOperationResult]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        polling = kwargs.pop("polling", True)  # type: Union[bool, AsyncPollingMethod]
        cls = kwargs.pop("cls", None)  # type: ClsType["models.WhatIfOperationResult"]
        lro_delay = kwargs.pop("polling_interval", self._config.polling_interval)
        cont_token = kwargs.pop("continuation_token", None)  # type: Optional[str]
        if cont_token is None:
            raw_result = await self._what_if_initial(
                resource_group_name=resource_group_name,
                deployment_name=deployment_name,
                parameters=parameters,
                cls=lambda x, y, z: x,
                **kwargs
            )

        kwargs.pop("error_map", None)
        kwargs.pop("content_type", None)

        def get_long_running_output(pipeline_response):
            deserialized = self._deserialize("WhatIfOperationResult", pipeline_response)

            if cls:
                return cls(pipeline_response, deserialized, {})
            return deserialized

        if polling is True:
            polling_method = AsyncARMPolling(lro_delay, lro_options={"final-state-via": "location"}, **kwargs)
        elif polling is False:
            polling_method = AsyncNoPolling()
        else:
            polling_method = polling
        if cont_token:
            return AsyncLROPoller.from_continuation_token(
                polling_method=polling_method,
                continuation_token=cont_token,
                client=self._client,
                deserialization_callback=get_long_running_output,
            )
        else:
            return AsyncLROPoller(self._client, raw_result, get_long_running_output, polling_method)

    begin_what_if.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}/whatIf"}  # type: ignore

    async def export_template(
        self, resource_group_name: str, deployment_name: str, **kwargs
    ) -> "models.DeploymentExportResult":
        """Exports the template used for specified deployment.

        :param resource_group_name: The name of the resource group. The name is case insensitive.
        :type resource_group_name: str
        :param deployment_name: The name of the deployment.
        :type deployment_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: DeploymentExportResult, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentExportResult
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentExportResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        # Construct URL
        url = self.export_template.metadata["url"]  # type: ignore
        path_format_arguments = {
            "resourceGroupName": self._serialize.url(
                "resource_group_name",
                resource_group_name,
                "str",
                max_length=90,
                min_length=1,
                pattern=r"^[-\w\._\(\)]+$",
            ),
            "deploymentName": self._serialize.url(
                "deployment_name", deployment_name, "str", max_length=64, min_length=1, pattern=r"^[-\w\._\(\)]+$"
            ),
            "subscriptionId": self._serialize.url("self._config.subscription_id", self._config.subscription_id, "str"),
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        request = self._client.post(url, query_parameters, header_parameters)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("DeploymentExportResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    export_template.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/{deploymentName}/exportTemplate"}  # type: ignore

    def list_by_resource_group(
        self, resource_group_name: str, filter: Optional[str] = None, top: Optional[int] = None, **kwargs
    ) -> AsyncIterable["models.DeploymentListResult"]:
        """Get all the deployments for a resource group.

        :param resource_group_name: The name of the resource group with the deployments to get. The
         name is case insensitive.
        :type resource_group_name: str
        :param filter: The filter to apply on the operation. For example, you can use
         $filter=provisioningState eq '{state}'.
        :type filter: str
        :param top: The number of results to get. If null is passed, returns all deployments.
        :type top: int
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either DeploymentListResult or the result of cls(response)
        :rtype: ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.resource.resources.v2020_06_01.models.DeploymentListResult]
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.DeploymentListResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        accept = "application/json"

        def prepare_request(next_link=None):
            # Construct headers
            header_parameters = {}  # type: Dict[str, Any]
            header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

            if not next_link:
                # Construct URL
                url = self.list_by_resource_group.metadata["url"]  # type: ignore
                path_format_arguments = {
                    "resourceGroupName": self._serialize.url(
                        "resource_group_name",
                        resource_group_name,
                        "str",
                        max_length=90,
                        min_length=1,
                        pattern=r"^[-\w\._\(\)]+$",
                    ),
                    "subscriptionId": self._serialize.url(
                        "self._config.subscription_id", self._config.subscription_id, "str"
                    ),
                }
                url = self._client.format_url(url, **path_format_arguments)
                # Construct parameters
                query_parameters = {}  # type: Dict[str, Any]
                if filter is not None:
                    query_parameters["$filter"] = self._serialize.query("filter", filter, "str")
                if top is not None:
                    query_parameters["$top"] = self._serialize.query("top", top, "int")
                query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

                request = self._client.get(url, query_parameters, header_parameters)
            else:
                url = next_link
                query_parameters = {}  # type: Dict[str, Any]
                request = self._client.get(url, query_parameters, header_parameters)
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("DeploymentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_by_resource_group.metadata = {"url": "/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/deployments/"}  # type: ignore

    async def calculate_template_hash(self, template: object, **kwargs) -> "models.TemplateHashResult":
        """Calculate the hash of the given template.

        :param template: The template provided to calculate hash.
        :type template: object
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: TemplateHashResult, or the result of cls(response)
        :rtype: ~azure.mgmt.resource.resources.v2020_06_01.models.TemplateHashResult
        :raises: ~azure.core.exceptions.HttpResponseError
        """
        cls = kwargs.pop("cls", None)  # type: ClsType["models.TemplateHashResult"]
        error_map = {401: ClientAuthenticationError, 404: ResourceNotFoundError, 409: ResourceExistsError}
        error_map.update(kwargs.pop("error_map", {}))
        api_version = "2020-06-01"
        content_type = kwargs.pop("content_type", "application/json")
        accept = "application/json"

        # Construct URL
        url = self.calculate_template_hash.metadata["url"]  # type: ignore

        # Construct parameters
        query_parameters = {}  # type: Dict[str, Any]
        query_parameters["api-version"] = self._serialize.query("api_version", api_version, "str")

        # Construct headers
        header_parameters = {}  # type: Dict[str, Any]
        header_parameters["Content-Type"] = self._serialize.header("content_type", content_type, "str")
        header_parameters["Accept"] = self._serialize.header("accept", accept, "str")

        body_content_kwargs = {}  # type: Dict[str, Any]
        body_content = self._serialize.body(template, "object")
        body_content_kwargs["content"] = body_content
        request = self._client.post(url, query_parameters, header_parameters, **body_content_kwargs)
        pipeline_response = await self._client._pipeline.run(request, stream=False, **kwargs)
        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("TemplateHashResult", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    calculate_template_hash.metadata = {"url": "/providers/Microsoft.Resources/calculateTemplateHash"}  # type: ignore
