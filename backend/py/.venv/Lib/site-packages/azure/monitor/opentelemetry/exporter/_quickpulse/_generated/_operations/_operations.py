# pylint: disable=too-many-lines,too-many-statements
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
from io import IOBase
import sys
from typing import Any, Callable, Dict, IO, List, Optional, Type, TypeVar, Union, overload

from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.rest import HttpRequest, HttpResponse
from azure.core.tracing.decorator import distributed_trace
from azure.core.utils import case_insensitive_dict

from .. import models as _models
from .._serialization import Serializer
from .._vendor import QuickpulseClientMixinABC

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore  # pylint: disable=ungrouped-imports
T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, HttpResponse], T, Dict[str, Any]], Any]]

_SERIALIZER = Serializer()
_SERIALIZER.client_side_validation = False


def build_quickpulse_is_subscribed_request(
    *,
    ikey: str,
    transmission_time: Optional[int] = None,
    machine_name: Optional[str] = None,
    instance_name: Optional[str] = None,
    stream_id: Optional[str] = None,
    role_name: Optional[str] = None,
    invariant_version: Optional[str] = None,
    configuration_etag: Optional[str] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2024-04-01-preview"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/QuickPulseService.svc/ping"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")
    _params["ikey"] = _SERIALIZER.query("ikey", ikey, "str")

    # Construct headers
    if transmission_time is not None:
        _headers["x-ms-qps-transmission-time"] = _SERIALIZER.header("transmission_time", transmission_time, "int")
    if machine_name is not None:
        _headers["x-ms-qps-machine-name"] = _SERIALIZER.header("machine_name", machine_name, "str")
    if instance_name is not None:
        _headers["x-ms-qps-instance-name"] = _SERIALIZER.header("instance_name", instance_name, "str")
    if stream_id is not None:
        _headers["x-ms-qps-stream-id"] = _SERIALIZER.header("stream_id", stream_id, "str")
    if role_name is not None:
        _headers["x-ms-qps-role-name"] = _SERIALIZER.header("role_name", role_name, "str")
    if invariant_version is not None:
        _headers["x-ms-qps-invariant-version"] = _SERIALIZER.header("invariant_version", invariant_version, "str")
    if configuration_etag is not None:
        _headers["x-ms-qps-configuration-etag"] = _SERIALIZER.header("configuration_etag", configuration_etag, "str")
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_quickpulse_publish_request(
    *, ikey: str, configuration_etag: Optional[str] = None, transmission_time: Optional[int] = None, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2024-04-01-preview"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/QuickPulseService.svc/post"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")
    _params["ikey"] = _SERIALIZER.query("ikey", ikey, "str")

    # Construct headers
    if configuration_etag is not None:
        _headers["x-ms-qps-configuration-etag"] = _SERIALIZER.header("configuration_etag", configuration_etag, "str")
    if transmission_time is not None:
        _headers["x-ms-qps-transmission-time"] = _SERIALIZER.header("transmission_time", transmission_time, "int")
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


class QuickpulseClientOperationsMixin(QuickpulseClientMixinABC):
    @overload
    def is_subscribed(
        self,
        endpoint: str = "https://global.livediagnostics.monitor.azure.com",
        monitoring_data_point: Optional[_models.MonitoringDataPoint] = None,
        *,
        ikey: str,
        transmission_time: Optional[int] = None,
        machine_name: Optional[str] = None,
        instance_name: Optional[str] = None,
        stream_id: Optional[str] = None,
        role_name: Optional[str] = None,
        invariant_version: Optional[str] = None,
        configuration_etag: Optional[str] = None,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.CollectionConfigurationInfo:
        """Determine whether there is any subscription to the metrics and documents.

        :param endpoint: The endpoint of the Live Metrics service. Default value is
         "https://global.livediagnostics.monitor.azure.com".
        :type endpoint: str
        :param monitoring_data_point: Data contract between Application Insights client SDK and Live
         Metrics. /QuickPulseService.svc/ping uses this as a backup source of machine name, instance
         name and invariant version. Default value is None.
        :type monitoring_data_point: ~quickpulse_client.models.MonitoringDataPoint
        :keyword ikey: The instrumentation key of the target Application Insights component for which
         the client checks whether there's any subscription to it. Required.
        :paramtype ikey: str
        :keyword transmission_time: Timestamp when the client transmits the metrics and documents to
         Live Metrics. A 8-byte long type of ticks. Default value is None.
        :paramtype transmission_time: int
        :keyword machine_name: Computer name where Application Insights SDK lives. Live Metrics uses
         machine name with instance name as a backup. Default value is None.
        :paramtype machine_name: str
        :keyword instance_name: Service instance name where Application Insights SDK lives. Live
         Metrics uses machine name with instance name as a backup. Default value is None.
        :paramtype instance_name: str
        :keyword stream_id: Identifies an Application Insights SDK as trusted agent to report metrics
         and documents. Default value is None.
        :paramtype stream_id: str
        :keyword role_name: Cloud role name of the service. Default value is None.
        :paramtype role_name: str
        :keyword invariant_version: Version/generation of the data contract (MonitoringDataPoint)
         between the client and Live Metrics. Default value is None.
        :paramtype invariant_version: str
        :keyword configuration_etag: An encoded string that indicates whether the collection
         configuration is changed. Default value is None.
        :paramtype configuration_etag: str
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CollectionConfigurationInfo
        :rtype: ~quickpulse_client.models.CollectionConfigurationInfo
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def is_subscribed(
        self,
        endpoint: str = "https://global.livediagnostics.monitor.azure.com",
        monitoring_data_point: Optional[IO[bytes]] = None,
        *,
        ikey: str,
        transmission_time: Optional[int] = None,
        machine_name: Optional[str] = None,
        instance_name: Optional[str] = None,
        stream_id: Optional[str] = None,
        role_name: Optional[str] = None,
        invariant_version: Optional[str] = None,
        configuration_etag: Optional[str] = None,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.CollectionConfigurationInfo:
        """Determine whether there is any subscription to the metrics and documents.

        :param endpoint: The endpoint of the Live Metrics service. Default value is
         "https://global.livediagnostics.monitor.azure.com".
        :type endpoint: str
        :param monitoring_data_point: Data contract between Application Insights client SDK and Live
         Metrics. /QuickPulseService.svc/ping uses this as a backup source of machine name, instance
         name and invariant version. Default value is None.
        :type monitoring_data_point: IO[bytes]
        :keyword ikey: The instrumentation key of the target Application Insights component for which
         the client checks whether there's any subscription to it. Required.
        :paramtype ikey: str
        :keyword transmission_time: Timestamp when the client transmits the metrics and documents to
         Live Metrics. A 8-byte long type of ticks. Default value is None.
        :paramtype transmission_time: int
        :keyword machine_name: Computer name where Application Insights SDK lives. Live Metrics uses
         machine name with instance name as a backup. Default value is None.
        :paramtype machine_name: str
        :keyword instance_name: Service instance name where Application Insights SDK lives. Live
         Metrics uses machine name with instance name as a backup. Default value is None.
        :paramtype instance_name: str
        :keyword stream_id: Identifies an Application Insights SDK as trusted agent to report metrics
         and documents. Default value is None.
        :paramtype stream_id: str
        :keyword role_name: Cloud role name of the service. Default value is None.
        :paramtype role_name: str
        :keyword invariant_version: Version/generation of the data contract (MonitoringDataPoint)
         between the client and Live Metrics. Default value is None.
        :paramtype invariant_version: str
        :keyword configuration_etag: An encoded string that indicates whether the collection
         configuration is changed. Default value is None.
        :paramtype configuration_etag: str
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CollectionConfigurationInfo
        :rtype: ~quickpulse_client.models.CollectionConfigurationInfo
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    # @distributed_trace
    def is_subscribed(
        self,
        endpoint: str = "https://global.livediagnostics.monitor.azure.com",
        monitoring_data_point: Optional[Union[_models.MonitoringDataPoint, IO[bytes]]] = None,
        *,
        ikey: str,
        transmission_time: Optional[int] = None,
        machine_name: Optional[str] = None,
        instance_name: Optional[str] = None,
        stream_id: Optional[str] = None,
        role_name: Optional[str] = None,
        invariant_version: Optional[str] = None,
        configuration_etag: Optional[str] = None,
        **kwargs: Any
    ) -> _models.CollectionConfigurationInfo:
        """Determine whether there is any subscription to the metrics and documents.

        :param endpoint: The endpoint of the Live Metrics service. Default value is
         "https://global.livediagnostics.monitor.azure.com".
        :type endpoint: str
        :param monitoring_data_point: Data contract between Application Insights client SDK and Live
         Metrics. /QuickPulseService.svc/ping uses this as a backup source of machine name, instance
         name and invariant version. Is either a MonitoringDataPoint type or a IO[bytes] type. Default
         value is None.
        :type monitoring_data_point: ~quickpulse_client.models.MonitoringDataPoint or IO[bytes]
        :keyword ikey: The instrumentation key of the target Application Insights component for which
         the client checks whether there's any subscription to it. Required.
        :paramtype ikey: str
        :keyword transmission_time: Timestamp when the client transmits the metrics and documents to
         Live Metrics. A 8-byte long type of ticks. Default value is None.
        :paramtype transmission_time: int
        :keyword machine_name: Computer name where Application Insights SDK lives. Live Metrics uses
         machine name with instance name as a backup. Default value is None.
        :paramtype machine_name: str
        :keyword instance_name: Service instance name where Application Insights SDK lives. Live
         Metrics uses machine name with instance name as a backup. Default value is None.
        :paramtype instance_name: str
        :keyword stream_id: Identifies an Application Insights SDK as trusted agent to report metrics
         and documents. Default value is None.
        :paramtype stream_id: str
        :keyword role_name: Cloud role name of the service. Default value is None.
        :paramtype role_name: str
        :keyword invariant_version: Version/generation of the data contract (MonitoringDataPoint)
         between the client and Live Metrics. Default value is None.
        :paramtype invariant_version: str
        :keyword configuration_etag: An encoded string that indicates whether the collection
         configuration is changed. Default value is None.
        :paramtype configuration_etag: str
        :return: CollectionConfigurationInfo
        :rtype: ~quickpulse_client.models.CollectionConfigurationInfo
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.CollectionConfigurationInfo] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(monitoring_data_point, (IOBase, bytes)):
            _content = monitoring_data_point
        else:
            if monitoring_data_point is not None:
                _json = self._serialize.body(monitoring_data_point, "MonitoringDataPoint")
            else:
                _json = None

        _request = build_quickpulse_is_subscribed_request(
            ikey=ikey,
            transmission_time=transmission_time,
            machine_name=machine_name,
            instance_name=instance_name,
            stream_id=stream_id,
            role_name=role_name,
            invariant_version=invariant_version,
            configuration_etag=configuration_etag,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("endpoint", endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            if _stream:
                response.read()  # Load the body in memory and close the socket
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ServiceError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-qps-configuration-etag"] = self._deserialize(
            "str", response.headers.get("x-ms-qps-configuration-etag")
        )
        response_headers["x-ms-qps-service-endpoint-redirect-v2"] = self._deserialize(
            "str", response.headers.get("x-ms-qps-service-endpoint-redirect-v2")
        )
        response_headers["x-ms-qps-service-polling-interval-hint"] = self._deserialize(
            "str", response.headers.get("x-ms-qps-service-polling-interval-hint")
        )
        response_headers["x-ms-qps-subscribed"] = self._deserialize("str", response.headers.get("x-ms-qps-subscribed"))

        deserialized = self._deserialize("CollectionConfigurationInfo", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @overload
    def publish(
        self,
        endpoint: str = "https://global.livediagnostics.monitor.azure.com",
        monitoring_data_points: Optional[List[_models.MonitoringDataPoint]] = None,
        *,
        ikey: str,
        configuration_etag: Optional[str] = None,
        transmission_time: Optional[int] = None,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.CollectionConfigurationInfo:
        """Publish live metrics to the Live Metrics service when there is an active subscription to the
        metrics.

        :param endpoint: The endpoint of the Live Metrics service. Default value is
         "https://global.livediagnostics.monitor.azure.com".
        :type endpoint: str
        :param monitoring_data_points: Data contract between the client and Live Metrics.
         /QuickPulseService.svc/ping uses this as a backup source of machine name, instance name and
         invariant version. Default value is None.
        :type monitoring_data_points: list[~quickpulse_client.models.MonitoringDataPoint]
        :keyword ikey: The instrumentation key of the target Application Insights component for which
         the client checks whether there's any subscription to it. Required.
        :paramtype ikey: str
        :keyword configuration_etag: An encoded string that indicates whether the collection
         configuration is changed. Default value is None.
        :paramtype configuration_etag: str
        :keyword transmission_time: Timestamp when the client transmits the metrics and documents to
         Live Metrics. A 8-byte long type of ticks. Default value is None.
        :paramtype transmission_time: int
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CollectionConfigurationInfo
        :rtype: ~quickpulse_client.models.CollectionConfigurationInfo
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def publish(
        self,
        endpoint: str = "https://global.livediagnostics.monitor.azure.com",
        monitoring_data_points: Optional[IO[bytes]] = None,
        *,
        ikey: str,
        configuration_etag: Optional[str] = None,
        transmission_time: Optional[int] = None,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.CollectionConfigurationInfo:
        """Publish live metrics to the Live Metrics service when there is an active subscription to the
        metrics.

        :param endpoint: The endpoint of the Live Metrics service. Default value is
         "https://global.livediagnostics.monitor.azure.com".
        :type endpoint: str
        :param monitoring_data_points: Data contract between the client and Live Metrics.
         /QuickPulseService.svc/ping uses this as a backup source of machine name, instance name and
         invariant version. Default value is None.
        :type monitoring_data_points: IO[bytes]
        :keyword ikey: The instrumentation key of the target Application Insights component for which
         the client checks whether there's any subscription to it. Required.
        :paramtype ikey: str
        :keyword configuration_etag: An encoded string that indicates whether the collection
         configuration is changed. Default value is None.
        :paramtype configuration_etag: str
        :keyword transmission_time: Timestamp when the client transmits the metrics and documents to
         Live Metrics. A 8-byte long type of ticks. Default value is None.
        :paramtype transmission_time: int
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CollectionConfigurationInfo
        :rtype: ~quickpulse_client.models.CollectionConfigurationInfo
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    # @distributed_trace
    def publish(
        self,
        endpoint: str = "https://global.livediagnostics.monitor.azure.com",
        monitoring_data_points: Optional[Union[List[_models.MonitoringDataPoint], IO[bytes]]] = None,
        *,
        ikey: str,
        configuration_etag: Optional[str] = None,
        transmission_time: Optional[int] = None,
        **kwargs: Any
    ) -> _models.CollectionConfigurationInfo:
        """Publish live metrics to the Live Metrics service when there is an active subscription to the
        metrics.

        :param endpoint: The endpoint of the Live Metrics service. Default value is
         "https://global.livediagnostics.monitor.azure.com".
        :type endpoint: str
        :param monitoring_data_points: Data contract between the client and Live Metrics.
         /QuickPulseService.svc/ping uses this as a backup source of machine name, instance name and
         invariant version. Is either a [MonitoringDataPoint] type or a IO[bytes] type. Default value is
         None.
        :type monitoring_data_points: list[~quickpulse_client.models.MonitoringDataPoint] or IO[bytes]
        :keyword ikey: The instrumentation key of the target Application Insights component for which
         the client checks whether there's any subscription to it. Required.
        :paramtype ikey: str
        :keyword configuration_etag: An encoded string that indicates whether the collection
         configuration is changed. Default value is None.
        :paramtype configuration_etag: str
        :keyword transmission_time: Timestamp when the client transmits the metrics and documents to
         Live Metrics. A 8-byte long type of ticks. Default value is None.
        :paramtype transmission_time: int
        :return: CollectionConfigurationInfo
        :rtype: ~quickpulse_client.models.CollectionConfigurationInfo
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping[int, Type[HttpResponseError]] = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.CollectionConfigurationInfo] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(monitoring_data_points, (IOBase, bytes)):
            _content = monitoring_data_points
        else:
            if monitoring_data_points is not None:
                _json = self._serialize.body(monitoring_data_points, "[MonitoringDataPoint]")
            else:
                _json = None

        _request = build_quickpulse_publish_request(
            ikey=ikey,
            configuration_etag=configuration_etag,
            transmission_time=transmission_time,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("endpoint", endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            if _stream:
                response.read()  # Load the body in memory and close the socket
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ServiceError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-qps-configuration-etag"] = self._deserialize(
            "str", response.headers.get("x-ms-qps-configuration-etag")
        )
        response_headers["x-ms-qps-subscribed"] = self._deserialize("str", response.headers.get("x-ms-qps-subscribed"))

        deserialized = self._deserialize("CollectionConfigurationInfo", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore
