# pylint: disable=line-too-long,useless-suppression,too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
import sys
from typing import Any, Callable, Dict, IO, Iterator, Literal, Optional, TypeVar, Union

from azure.core import PipelineClient
from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    StreamClosed<PERSON>rror,
    StreamConsumedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.rest import HttpRequest, HttpResponse
from azure.core.tracing.decorator import distributed_trace
from azure.core.utils import case_insensitive_dict

from .. import models as _models
from .._configuration import AzureFileStorageConfiguration
from .._serialization import Deserializer, Serializer

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore
T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, HttpResponse], T, Dict[str, Any]], Any]]

_SERIALIZER = Serializer()
_SERIALIZER.client_side_validation = False


def build_create_request(
    url: str,
    *,
    file_content_length: int,
    timeout: Optional[int] = None,
    file_content_type: Optional[str] = None,
    file_content_encoding: Optional[str] = None,
    file_content_language: Optional[str] = None,
    file_cache_control: Optional[str] = None,
    file_content_md5: Optional[bytes] = None,
    file_content_disposition: Optional[str] = None,
    metadata: Optional[Dict[str, str]] = None,
    file_permission: str = "inherit",
    file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
    file_permission_key: Optional[str] = None,
    file_attributes: str = "none",
    file_creation_time: str = "now",
    file_last_write_time: str = "now",
    file_change_time: Optional[str] = None,
    lease_id: Optional[str] = None,
    owner: Optional[str] = None,
    group: Optional[str] = None,
    file_mode: Optional[str] = None,
    nfs_file_type: Optional[Union[str, _models.NfsFileType]] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    file_type_constant: Literal["file"] = kwargs.pop("file_type_constant", _headers.pop("x-ms-type", "file"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    _headers["x-ms-content-length"] = _SERIALIZER.header("file_content_length", file_content_length, "int")
    _headers["x-ms-type"] = _SERIALIZER.header("file_type_constant", file_type_constant, "str")
    if file_content_type is not None:
        _headers["x-ms-content-type"] = _SERIALIZER.header("file_content_type", file_content_type, "str")
    if file_content_encoding is not None:
        _headers["x-ms-content-encoding"] = _SERIALIZER.header("file_content_encoding", file_content_encoding, "str")
    if file_content_language is not None:
        _headers["x-ms-content-language"] = _SERIALIZER.header("file_content_language", file_content_language, "str")
    if file_cache_control is not None:
        _headers["x-ms-cache-control"] = _SERIALIZER.header("file_cache_control", file_cache_control, "str")
    if file_content_md5 is not None:
        _headers["x-ms-content-md5"] = _SERIALIZER.header("file_content_md5", file_content_md5, "bytearray")
    if file_content_disposition is not None:
        _headers["x-ms-content-disposition"] = _SERIALIZER.header(
            "file_content_disposition", file_content_disposition, "str"
        )
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if file_permission is not None:
        _headers["x-ms-file-permission"] = _SERIALIZER.header("file_permission", file_permission, "str")
    if file_permission_format is not None:
        _headers["x-ms-file-permission-format"] = _SERIALIZER.header(
            "file_permission_format", file_permission_format, "str"
        )
    if file_permission_key is not None:
        _headers["x-ms-file-permission-key"] = _SERIALIZER.header("file_permission_key", file_permission_key, "str")
    if file_attributes is not None:
        _headers["x-ms-file-attributes"] = _SERIALIZER.header("file_attributes", file_attributes, "str")
    if file_creation_time is not None:
        _headers["x-ms-file-creation-time"] = _SERIALIZER.header("file_creation_time", file_creation_time, "str")
    if file_last_write_time is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header("file_last_write_time", file_last_write_time, "str")
    if file_change_time is not None:
        _headers["x-ms-file-change-time"] = _SERIALIZER.header("file_change_time", file_change_time, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    if owner is not None:
        _headers["x-ms-owner"] = _SERIALIZER.header("owner", owner, "str")
    if group is not None:
        _headers["x-ms-group"] = _SERIALIZER.header("group", group, "str")
    if file_mode is not None:
        _headers["x-ms-mode"] = _SERIALIZER.header("file_mode", file_mode, "str")
    if nfs_file_type is not None:
        _headers["x-ms-file-file-type"] = _SERIALIZER.header("nfs_file_type", nfs_file_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_download_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    range: Optional[str] = None,
    range_get_content_md5: Optional[bool] = None,
    structured_body_type: Optional[str] = None,
    lease_id: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if range is not None:
        _headers["x-ms-range"] = _SERIALIZER.header("range", range, "str")
    if range_get_content_md5 is not None:
        _headers["x-ms-range-get-content-md5"] = _SERIALIZER.header(
            "range_get_content_md5", range_get_content_md5, "bool"
        )
    if structured_body_type is not None:
        _headers["x-ms-structured-body"] = _SERIALIZER.header("structured_body_type", structured_body_type, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_get_properties_request(
    url: str,
    *,
    sharesnapshot: Optional[str] = None,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if sharesnapshot is not None:
        _params["sharesnapshot"] = _SERIALIZER.query("sharesnapshot", sharesnapshot, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="HEAD", url=_url, params=_params, headers=_headers, **kwargs)


def build_delete_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="DELETE", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_http_headers_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    file_content_length: Optional[int] = None,
    file_content_type: Optional[str] = None,
    file_content_encoding: Optional[str] = None,
    file_content_language: Optional[str] = None,
    file_cache_control: Optional[str] = None,
    file_content_md5: Optional[bytes] = None,
    file_content_disposition: Optional[str] = None,
    file_permission: str = "inherit",
    file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
    file_permission_key: Optional[str] = None,
    file_attributes: str = "none",
    file_creation_time: str = "now",
    file_last_write_time: str = "now",
    file_change_time: Optional[str] = None,
    lease_id: Optional[str] = None,
    owner: Optional[str] = None,
    group: Optional[str] = None,
    file_mode: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if file_content_length is not None:
        _headers["x-ms-content-length"] = _SERIALIZER.header("file_content_length", file_content_length, "int")
    if file_content_type is not None:
        _headers["x-ms-content-type"] = _SERIALIZER.header("file_content_type", file_content_type, "str")
    if file_content_encoding is not None:
        _headers["x-ms-content-encoding"] = _SERIALIZER.header("file_content_encoding", file_content_encoding, "str")
    if file_content_language is not None:
        _headers["x-ms-content-language"] = _SERIALIZER.header("file_content_language", file_content_language, "str")
    if file_cache_control is not None:
        _headers["x-ms-cache-control"] = _SERIALIZER.header("file_cache_control", file_cache_control, "str")
    if file_content_md5 is not None:
        _headers["x-ms-content-md5"] = _SERIALIZER.header("file_content_md5", file_content_md5, "bytearray")
    if file_content_disposition is not None:
        _headers["x-ms-content-disposition"] = _SERIALIZER.header(
            "file_content_disposition", file_content_disposition, "str"
        )
    if file_permission is not None:
        _headers["x-ms-file-permission"] = _SERIALIZER.header("file_permission", file_permission, "str")
    if file_permission_format is not None:
        _headers["x-ms-file-permission-format"] = _SERIALIZER.header(
            "file_permission_format", file_permission_format, "str"
        )
    if file_permission_key is not None:
        _headers["x-ms-file-permission-key"] = _SERIALIZER.header("file_permission_key", file_permission_key, "str")
    if file_attributes is not None:
        _headers["x-ms-file-attributes"] = _SERIALIZER.header("file_attributes", file_attributes, "str")
    if file_creation_time is not None:
        _headers["x-ms-file-creation-time"] = _SERIALIZER.header("file_creation_time", file_creation_time, "str")
    if file_last_write_time is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header("file_last_write_time", file_last_write_time, "str")
    if file_change_time is not None:
        _headers["x-ms-file-change-time"] = _SERIALIZER.header("file_change_time", file_change_time, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    if owner is not None:
        _headers["x-ms-owner"] = _SERIALIZER.header("owner", owner, "str")
    if group is not None:
        _headers["x-ms-group"] = _SERIALIZER.header("group", group, "str")
    if file_mode is not None:
        _headers["x-ms-mode"] = _SERIALIZER.header("file_mode", file_mode, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_set_metadata_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    lease_id: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["metadata"] = kwargs.pop("comp", _params.pop("comp", "metadata"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_acquire_lease_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    duration: Optional[int] = None,
    proposed_lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["acquire"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "acquire"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    if duration is not None:
        _headers["x-ms-lease-duration"] = _SERIALIZER.header("duration", duration, "int")
    if proposed_lease_id is not None:
        _headers["x-ms-proposed-lease-id"] = _SERIALIZER.header("proposed_lease_id", proposed_lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_release_lease_request(
    url: str,
    *,
    lease_id: str,
    timeout: Optional[int] = None,
    request_id_parameter: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["release"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "release"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_change_lease_request(
    url: str,
    *,
    lease_id: str,
    timeout: Optional[int] = None,
    proposed_lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["change"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "change"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if proposed_lease_id is not None:
        _headers["x-ms-proposed-lease-id"] = _SERIALIZER.header("proposed_lease_id", proposed_lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_break_lease_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
    action: Literal["break"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "break"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-lease-action"] = _SERIALIZER.header("action", action, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_upload_range_request(
    url: str,
    *,
    range: str,
    content_length: int,
    timeout: Optional[int] = None,
    file_range_write: Union[str, _models.FileRangeWriteType] = "update",
    content_md5: Optional[bytes] = None,
    lease_id: Optional[str] = None,
    file_last_written_mode: Optional[Union[str, _models.FileLastWrittenMode]] = None,
    structured_body_type: Optional[str] = None,
    structured_content_length: Optional[int] = None,
    content: Optional[IO[bytes]] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["range"] = kwargs.pop("comp", _params.pop("comp", "range"))
    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-range"] = _SERIALIZER.header("range", range, "str")
    _headers["x-ms-write"] = _SERIALIZER.header("file_range_write", file_range_write, "str")
    _headers["Content-Length"] = _SERIALIZER.header("content_length", content_length, "int")
    if content_md5 is not None:
        _headers["Content-MD5"] = _SERIALIZER.header("content_md5", content_md5, "bytearray")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if file_last_written_mode is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header(
            "file_last_written_mode", file_last_written_mode, "str"
        )
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    if structured_body_type is not None:
        _headers["x-ms-structured-body"] = _SERIALIZER.header("structured_body_type", structured_body_type, "str")
    if structured_content_length is not None:
        _headers["x-ms-structured-content-length"] = _SERIALIZER.header(
            "structured_content_length", structured_content_length, "int"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, content=content, **kwargs)


def build_upload_range_from_url_request(
    url: str,
    *,
    range: str,
    copy_source: str,
    content_length: int,
    timeout: Optional[int] = None,
    source_range: Optional[str] = None,
    source_content_crc64: Optional[bytes] = None,
    source_if_match_crc64: Optional[bytes] = None,
    source_if_none_match_crc64: Optional[bytes] = None,
    lease_id: Optional[str] = None,
    copy_source_authorization: Optional[str] = None,
    file_last_written_mode: Optional[Union[str, _models.FileLastWrittenMode]] = None,
    allow_trailing_dot: Optional[bool] = None,
    allow_source_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["range"] = kwargs.pop("comp", _params.pop("comp", "range"))
    file_range_write_from_url: Literal["update"] = kwargs.pop(
        "file_range_write_from_url", _headers.pop("x-ms-write", "update")
    )
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-range"] = _SERIALIZER.header("range", range, "str")
    _headers["x-ms-copy-source"] = _SERIALIZER.header("copy_source", copy_source, "str")
    if source_range is not None:
        _headers["x-ms-source-range"] = _SERIALIZER.header("source_range", source_range, "str")
    _headers["x-ms-write"] = _SERIALIZER.header("file_range_write_from_url", file_range_write_from_url, "str")
    _headers["Content-Length"] = _SERIALIZER.header("content_length", content_length, "int")
    if source_content_crc64 is not None:
        _headers["x-ms-source-content-crc64"] = _SERIALIZER.header(
            "source_content_crc64", source_content_crc64, "bytearray"
        )
    if source_if_match_crc64 is not None:
        _headers["x-ms-source-if-match-crc64"] = _SERIALIZER.header(
            "source_if_match_crc64", source_if_match_crc64, "bytearray"
        )
    if source_if_none_match_crc64 is not None:
        _headers["x-ms-source-if-none-match-crc64"] = _SERIALIZER.header(
            "source_if_none_match_crc64", source_if_none_match_crc64, "bytearray"
        )
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if copy_source_authorization is not None:
        _headers["x-ms-copy-source-authorization"] = _SERIALIZER.header(
            "copy_source_authorization", copy_source_authorization, "str"
        )
    if file_last_written_mode is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header(
            "file_last_written_mode", file_last_written_mode, "str"
        )
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if allow_source_trailing_dot is not None:
        _headers["x-ms-source-allow-trailing-dot"] = _SERIALIZER.header(
            "allow_source_trailing_dot", allow_source_trailing_dot, "bool"
        )
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_get_range_list_request(
    url: str,
    *,
    sharesnapshot: Optional[str] = None,
    prevsharesnapshot: Optional[str] = None,
    timeout: Optional[int] = None,
    range: Optional[str] = None,
    lease_id: Optional[str] = None,
    support_rename: Optional[bool] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["rangelist"] = kwargs.pop("comp", _params.pop("comp", "rangelist"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if sharesnapshot is not None:
        _params["sharesnapshot"] = _SERIALIZER.query("sharesnapshot", sharesnapshot, "str")
    if prevsharesnapshot is not None:
        _params["prevsharesnapshot"] = _SERIALIZER.query("prevsharesnapshot", prevsharesnapshot, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if range is not None:
        _headers["x-ms-range"] = _SERIALIZER.header("range", range, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    if support_rename is not None:
        _headers["x-ms-file-support-rename"] = _SERIALIZER.header("support_rename", support_rename, "bool")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_start_copy_request(
    url: str,
    *,
    copy_source: str,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    file_permission: str = "inherit",
    file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
    file_permission_key: Optional[str] = None,
    file_permission_copy_mode: Optional[Union[str, _models.PermissionCopyModeType]] = None,
    ignore_read_only: Optional[bool] = None,
    file_attributes: Optional[str] = None,
    file_creation_time: Optional[str] = None,
    file_last_write_time: Optional[str] = None,
    file_change_time: Optional[str] = None,
    set_archive_attribute: Optional[bool] = None,
    lease_id: Optional[str] = None,
    owner: Optional[str] = None,
    group: Optional[str] = None,
    file_mode: Optional[str] = None,
    file_mode_copy_mode: Optional[Union[str, _models.ModeCopyMode]] = None,
    file_owner_copy_mode: Optional[Union[str, _models.OwnerCopyMode]] = None,
    allow_trailing_dot: Optional[bool] = None,
    allow_source_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    _headers["x-ms-copy-source"] = _SERIALIZER.header("copy_source", copy_source, "str")
    if file_permission is not None:
        _headers["x-ms-file-permission"] = _SERIALIZER.header("file_permission", file_permission, "str")
    if file_permission_format is not None:
        _headers["x-ms-file-permission-format"] = _SERIALIZER.header(
            "file_permission_format", file_permission_format, "str"
        )
    if file_permission_key is not None:
        _headers["x-ms-file-permission-key"] = _SERIALIZER.header("file_permission_key", file_permission_key, "str")
    if file_permission_copy_mode is not None:
        _headers["x-ms-file-permission-copy-mode"] = _SERIALIZER.header(
            "file_permission_copy_mode", file_permission_copy_mode, "str"
        )
    if ignore_read_only is not None:
        _headers["x-ms-file-copy-ignore-readonly"] = _SERIALIZER.header("ignore_read_only", ignore_read_only, "bool")
    if file_attributes is not None:
        _headers["x-ms-file-attributes"] = _SERIALIZER.header("file_attributes", file_attributes, "str")
    if file_creation_time is not None:
        _headers["x-ms-file-creation-time"] = _SERIALIZER.header("file_creation_time", file_creation_time, "str")
    if file_last_write_time is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header("file_last_write_time", file_last_write_time, "str")
    if file_change_time is not None:
        _headers["x-ms-file-change-time"] = _SERIALIZER.header("file_change_time", file_change_time, "str")
    if set_archive_attribute is not None:
        _headers["x-ms-file-copy-set-archive"] = _SERIALIZER.header(
            "set_archive_attribute", set_archive_attribute, "bool"
        )
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if allow_source_trailing_dot is not None:
        _headers["x-ms-source-allow-trailing-dot"] = _SERIALIZER.header(
            "allow_source_trailing_dot", allow_source_trailing_dot, "bool"
        )
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    if owner is not None:
        _headers["x-ms-owner"] = _SERIALIZER.header("owner", owner, "str")
    if group is not None:
        _headers["x-ms-group"] = _SERIALIZER.header("group", group, "str")
    if file_mode is not None:
        _headers["x-ms-mode"] = _SERIALIZER.header("file_mode", file_mode, "str")
    if file_mode_copy_mode is not None:
        _headers["x-ms-file-mode-copy-mode"] = _SERIALIZER.header("file_mode_copy_mode", file_mode_copy_mode, "str")
    if file_owner_copy_mode is not None:
        _headers["x-ms-file-owner-copy-mode"] = _SERIALIZER.header("file_owner_copy_mode", file_owner_copy_mode, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_abort_copy_request(
    url: str,
    *,
    copy_id: str,
    timeout: Optional[int] = None,
    lease_id: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["copy"] = kwargs.pop("comp", _params.pop("comp", "copy"))
    copy_action_abort_constant: Literal["abort"] = kwargs.pop(
        "copy_action_abort_constant", _headers.pop("x-ms-copy-action", "abort")
    )
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    _params["copyid"] = _SERIALIZER.query("copy_id", copy_id, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-copy-action"] = _SERIALIZER.header("copy_action_abort_constant", copy_action_abort_constant, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_list_handles_request(
    url: str,
    *,
    marker: Optional[str] = None,
    maxresults: Optional[int] = None,
    timeout: Optional[int] = None,
    sharesnapshot: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["listhandles"] = kwargs.pop("comp", _params.pop("comp", "listhandles"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if marker is not None:
        _params["marker"] = _SERIALIZER.query("marker", marker, "str")
    if maxresults is not None:
        _params["maxresults"] = _SERIALIZER.query("maxresults", maxresults, "int", minimum=1)
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)
    if sharesnapshot is not None:
        _params["sharesnapshot"] = _SERIALIZER.query("sharesnapshot", sharesnapshot, "str")

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_force_close_handles_request(
    url: str,
    *,
    handle_id: str,
    timeout: Optional[int] = None,
    marker: Optional[str] = None,
    sharesnapshot: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["forceclosehandles"] = kwargs.pop("comp", _params.pop("comp", "forceclosehandles"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)
    if marker is not None:
        _params["marker"] = _SERIALIZER.query("marker", marker, "str")
    if sharesnapshot is not None:
        _params["sharesnapshot"] = _SERIALIZER.query("sharesnapshot", sharesnapshot, "str")

    # Construct headers
    _headers["x-ms-handle-id"] = _SERIALIZER.header("handle_id", handle_id, "str")
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_rename_request(
    url: str,
    *,
    rename_source: str,
    timeout: Optional[int] = None,
    replace_if_exists: Optional[bool] = None,
    ignore_read_only: Optional[bool] = None,
    source_lease_id: Optional[str] = None,
    destination_lease_id: Optional[str] = None,
    file_attributes: Optional[str] = None,
    file_creation_time: Optional[str] = None,
    file_last_write_time: Optional[str] = None,
    file_change_time: Optional[str] = None,
    file_permission: str = "inherit",
    file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
    file_permission_key: Optional[str] = None,
    metadata: Optional[Dict[str, str]] = None,
    file_content_type: Optional[str] = None,
    allow_trailing_dot: Optional[bool] = None,
    allow_source_trailing_dot: Optional[bool] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    comp: Literal["rename"] = kwargs.pop("comp", _params.pop("comp", "rename"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["comp"] = _SERIALIZER.query("comp", comp, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    _headers["x-ms-file-rename-source"] = _SERIALIZER.header("rename_source", rename_source, "str")
    if replace_if_exists is not None:
        _headers["x-ms-file-rename-replace-if-exists"] = _SERIALIZER.header(
            "replace_if_exists", replace_if_exists, "bool"
        )
    if ignore_read_only is not None:
        _headers["x-ms-file-rename-ignore-readonly"] = _SERIALIZER.header("ignore_read_only", ignore_read_only, "bool")
    if source_lease_id is not None:
        _headers["x-ms-source-lease-id"] = _SERIALIZER.header("source_lease_id", source_lease_id, "str")
    if destination_lease_id is not None:
        _headers["x-ms-destination-lease-id"] = _SERIALIZER.header("destination_lease_id", destination_lease_id, "str")
    if file_attributes is not None:
        _headers["x-ms-file-attributes"] = _SERIALIZER.header("file_attributes", file_attributes, "str")
    if file_creation_time is not None:
        _headers["x-ms-file-creation-time"] = _SERIALIZER.header("file_creation_time", file_creation_time, "str")
    if file_last_write_time is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header("file_last_write_time", file_last_write_time, "str")
    if file_change_time is not None:
        _headers["x-ms-file-change-time"] = _SERIALIZER.header("file_change_time", file_change_time, "str")
    if file_permission is not None:
        _headers["x-ms-file-permission"] = _SERIALIZER.header("file_permission", file_permission, "str")
    if file_permission_format is not None:
        _headers["x-ms-file-permission-format"] = _SERIALIZER.header(
            "file_permission_format", file_permission_format, "str"
        )
    if file_permission_key is not None:
        _headers["x-ms-file-permission-key"] = _SERIALIZER.header("file_permission_key", file_permission_key, "str")
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if file_content_type is not None:
        _headers["x-ms-content-type"] = _SERIALIZER.header("file_content_type", file_content_type, "str")
    if allow_trailing_dot is not None:
        _headers["x-ms-allow-trailing-dot"] = _SERIALIZER.header("allow_trailing_dot", allow_trailing_dot, "bool")
    if allow_source_trailing_dot is not None:
        _headers["x-ms-source-allow-trailing-dot"] = _SERIALIZER.header(
            "allow_source_trailing_dot", allow_source_trailing_dot, "bool"
        )
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_create_symbolic_link_request(
    url: str,
    *,
    link_text: str,
    timeout: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
    file_creation_time: str = "now",
    file_last_write_time: str = "now",
    request_id_parameter: Optional[str] = None,
    lease_id: Optional[str] = None,
    owner: Optional[str] = None,
    group: Optional[str] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    restype: Literal["symboliclink"] = kwargs.pop("restype", _params.pop("restype", "symboliclink"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["restype"] = _SERIALIZER.query("restype", restype, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if metadata is not None:
        _headers["x-ms-meta"] = _SERIALIZER.header("metadata", metadata, "{str}")
    if file_creation_time is not None:
        _headers["x-ms-file-creation-time"] = _SERIALIZER.header("file_creation_time", file_creation_time, "str")
    if file_last_write_time is not None:
        _headers["x-ms-file-last-write-time"] = _SERIALIZER.header("file_last_write_time", file_last_write_time, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    if owner is not None:
        _headers["x-ms-owner"] = _SERIALIZER.header("owner", owner, "str")
    if group is not None:
        _headers["x-ms-group"] = _SERIALIZER.header("group", group, "str")
    _headers["x-ms-link-text"] = _SERIALIZER.header("link_text", link_text, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


def build_get_symbolic_link_request(
    url: str,
    *,
    timeout: Optional[int] = None,
    sharesnapshot: Optional[str] = None,
    request_id_parameter: Optional[str] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    restype: Literal["symboliclink"] = kwargs.pop("restype", _params.pop("restype", "symboliclink"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["restype"] = _SERIALIZER.query("restype", restype, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)
    if sharesnapshot is not None:
        _params["sharesnapshot"] = _SERIALIZER.query("sharesnapshot", sharesnapshot, "str")

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_create_hard_link_request(
    url: str,
    *,
    target_file: str,
    timeout: Optional[int] = None,
    request_id_parameter: Optional[str] = None,
    lease_id: Optional[str] = None,
    file_request_intent: Optional[Union[str, _models.ShareTokenIntent]] = None,
    **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    restype: Literal["hardlink"] = kwargs.pop("restype", _params.pop("restype", "hardlink"))
    file_type_constant: Literal["file"] = kwargs.pop("file_type_constant", _headers.pop("x-ms-type", "file"))
    version: Literal["2025-05-05"] = kwargs.pop("version", _headers.pop("x-ms-version", "2025-05-05"))
    accept = _headers.pop("Accept", "application/xml")

    # Construct URL
    _url = kwargs.pop("template_url", "{url}")
    path_format_arguments = {
        "url": _SERIALIZER.url("url", url, "str", skip_quote=True),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["restype"] = _SERIALIZER.query("restype", restype, "str")
    if timeout is not None:
        _params["timeout"] = _SERIALIZER.query("timeout", timeout, "int", minimum=0)

    # Construct headers
    _headers["x-ms-version"] = _SERIALIZER.header("version", version, "str")
    _headers["x-ms-type"] = _SERIALIZER.header("file_type_constant", file_type_constant, "str")
    if request_id_parameter is not None:
        _headers["x-ms-client-request-id"] = _SERIALIZER.header("request_id_parameter", request_id_parameter, "str")
    if lease_id is not None:
        _headers["x-ms-lease-id"] = _SERIALIZER.header("lease_id", lease_id, "str")
    _headers["x-ms-file-target-file"] = _SERIALIZER.header("target_file", target_file, "str")
    if file_request_intent is not None:
        _headers["x-ms-file-request-intent"] = _SERIALIZER.header("file_request_intent", file_request_intent, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="PUT", url=_url, params=_params, headers=_headers, **kwargs)


class FileOperations:  # pylint: disable=too-many-public-methods
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.storage.fileshare.AzureFileStorage`'s
        :attr:`file` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs):
        input_args = list(args)
        self._client: PipelineClient = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config: AzureFileStorageConfiguration = input_args.pop(0) if input_args else kwargs.pop("config")
        self._serialize: Serializer = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize: Deserializer = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @distributed_trace
    def create(  # pylint: disable=inconsistent-return-statements
        self,
        file_content_length: int,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        file_permission: str = "inherit",
        file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
        file_permission_key: Optional[str] = None,
        file_attributes: str = "none",
        file_creation_time: str = "now",
        file_last_write_time: str = "now",
        file_change_time: Optional[str] = None,
        owner: Optional[str] = None,
        group: Optional[str] = None,
        file_mode: Optional[str] = None,
        nfs_file_type: Optional[Union[str, _models.NfsFileType]] = None,
        file_http_headers: Optional[_models.FileHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Creates a new file or replaces a file. Note it only initializes the file with no content.

        :param file_content_length: Specifies the maximum size for the file, up to 4 TB. Required.
        :type file_content_length: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: A name-value pair to associate with a file storage object. Default value is
         None.
        :type metadata: dict[str, str]
        :param file_permission: If specified the permission (security descriptor) shall be set for the
         directory/file. This header can be used if Permission size is <= 8KB, else
         x-ms-file-permission-key header shall be used. Default value: Inherit. If SDDL is specified as
         input, it must have owner, group and dacl. Note: Only one of the x-ms-file-permission or
         x-ms-file-permission-key should be specified. Default value is "inherit".
        :type file_permission: str
        :param file_permission_format: Optional. Available for version 2023-06-01 and later. Specifies
         the format in which the permission is returned. Acceptable values are SDDL or binary. If
         x-ms-file-permission-format is unspecified or explicitly set to SDDL, the permission is
         returned in SDDL format. If x-ms-file-permission-format is explicitly set to binary, the
         permission is returned as a base64 string representing the binary encoding of the permission.
         Known values are: "Sddl" and "Binary". Default value is None.
        :type file_permission_format: str or ~azure.storage.fileshare.models.FilePermissionFormat
        :param file_permission_key: Key of the permission to be set for the directory/file. Note: Only
         one of the x-ms-file-permission or x-ms-file-permission-key should be specified. Default value
         is None.
        :type file_permission_key: str
        :param file_attributes: If specified, the provided file attributes shall be set. Default value:
         ‘Archive’ for file and ‘Directory’ for directory. ‘None’ can also be specified as default.
         Default value is "none".
        :type file_attributes: str
        :param file_creation_time: Creation time for the file/directory. Default value: Now. Default
         value is "now".
        :type file_creation_time: str
        :param file_last_write_time: Last write time for the file/directory. Default value: Now.
         Default value is "now".
        :type file_last_write_time: str
        :param file_change_time: Change time for the file/directory. Default value: Now. Default value
         is None.
        :type file_change_time: str
        :param owner: Optional, NFS only. The owner of the file or directory. Default value is None.
        :type owner: str
        :param group: Optional, NFS only. The owning group of the file or directory. Default value is
         None.
        :type group: str
        :param file_mode: Optional, NFS only. The file mode of the file or directory. Default value is
         None.
        :type file_mode: str
        :param nfs_file_type: Optional, NFS only. Type of the file or directory. Known values are:
         "Regular", "Directory", and "SymLink". Default value is None.
        :type nfs_file_type: str or ~azure.storage.fileshare.models.NfsFileType
        :param file_http_headers: Parameter group. Default value is None.
        :type file_http_headers: ~azure.storage.fileshare.models.FileHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        file_type_constant: Literal["file"] = kwargs.pop("file_type_constant", _headers.pop("x-ms-type", "file"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _file_content_type = None
        _file_content_encoding = None
        _file_content_language = None
        _file_cache_control = None
        _file_content_md5 = None
        _file_content_disposition = None
        _lease_id = None
        if file_http_headers is not None:
            _file_cache_control = file_http_headers.file_cache_control
            _file_content_disposition = file_http_headers.file_content_disposition
            _file_content_encoding = file_http_headers.file_content_encoding
            _file_content_language = file_http_headers.file_content_language
            _file_content_md5 = file_http_headers.file_content_md5
            _file_content_type = file_http_headers.file_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_create_request(
            url=self._config.url,
            file_content_length=file_content_length,
            timeout=timeout,
            file_content_type=_file_content_type,
            file_content_encoding=_file_content_encoding,
            file_content_language=_file_content_language,
            file_cache_control=_file_cache_control,
            file_content_md5=_file_content_md5,
            file_content_disposition=_file_content_disposition,
            metadata=metadata,
            file_permission=file_permission,
            file_permission_format=file_permission_format,
            file_permission_key=file_permission_key,
            file_attributes=file_attributes,
            file_creation_time=file_creation_time,
            file_last_write_time=file_last_write_time,
            file_change_time=file_change_time,
            lease_id=_lease_id,
            owner=owner,
            group=group,
            file_mode=file_mode,
            nfs_file_type=nfs_file_type,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            file_type_constant=file_type_constant,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-file-permission-key"] = self._deserialize(
            "str", response.headers.get("x-ms-file-permission-key")
        )
        response_headers["x-ms-file-attributes"] = self._deserialize(
            "str", response.headers.get("x-ms-file-attributes")
        )
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))
        response_headers["x-ms-mode"] = self._deserialize("str", response.headers.get("x-ms-mode"))
        response_headers["x-ms-owner"] = self._deserialize("str", response.headers.get("x-ms-owner"))
        response_headers["x-ms-group"] = self._deserialize("str", response.headers.get("x-ms-group"))
        response_headers["x-ms-file-file-type"] = self._deserialize("str", response.headers.get("x-ms-file-file-type"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def download(
        self,
        timeout: Optional[int] = None,
        range: Optional[str] = None,
        range_get_content_md5: Optional[bool] = None,
        structured_body_type: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> Iterator[bytes]:
        """Reads or downloads a file from the system, including its metadata and properties.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param range: Return file data only from the specified byte range. Default value is None.
        :type range: str
        :param range_get_content_md5: When this header is set to true and specified together with the
         Range header, the service returns the MD5 hash for the range, as long as the range is less than
         or equal to 4 MB in size. Default value is None.
        :type range_get_content_md5: bool
        :param structured_body_type: Specifies the response content should be returned as a structured
         message and specifies the message schema version and properties. Default value is None.
        :type structured_body_type: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: Iterator[bytes] or the result of cls(response)
        :rtype: Iterator[bytes]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[Iterator[bytes]] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_download_request(
            url=self._config.url,
            timeout=timeout,
            range=range,
            range_get_content_md5=range_get_content_md5,
            structured_body_type=structured_body_type,
            lease_id=_lease_id,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _decompress = kwargs.pop("decompress", True)
        _stream = True
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 206]:
            try:
                response.read()  # Load the body in memory and close the socket
            except (StreamConsumedError, StreamClosedError):
                pass
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
        response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
        response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
        response_headers["Content-Range"] = self._deserialize("str", response.headers.get("Content-Range"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
        response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
        response_headers["Content-Disposition"] = self._deserialize("str", response.headers.get("Content-Disposition"))
        response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Accept-Ranges"] = self._deserialize("str", response.headers.get("Accept-Ranges"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-copy-completion-time"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-copy-completion-time")
        )
        response_headers["x-ms-copy-status-description"] = self._deserialize(
            "str", response.headers.get("x-ms-copy-status-description")
        )
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-progress"] = self._deserialize("str", response.headers.get("x-ms-copy-progress"))
        response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
        response_headers["x-ms-content-md5"] = self._deserialize("bytearray", response.headers.get("x-ms-content-md5"))
        response_headers["x-ms-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-server-encrypted")
        )
        response_headers["x-ms-file-attributes"] = self._deserialize(
            "str", response.headers.get("x-ms-file-attributes")
        )
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-permission-key"] = self._deserialize(
            "str", response.headers.get("x-ms-file-permission-key")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))
        response_headers["x-ms-lease-duration"] = self._deserialize("str", response.headers.get("x-ms-lease-duration"))
        response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
        response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
        response_headers["x-ms-structured-body"] = self._deserialize(
            "str", response.headers.get("x-ms-structured-body")
        )
        response_headers["x-ms-structured-content-length"] = self._deserialize(
            "int", response.headers.get("x-ms-structured-content-length")
        )
        response_headers["x-ms-mode"] = self._deserialize("str", response.headers.get("x-ms-mode"))
        response_headers["x-ms-owner"] = self._deserialize("str", response.headers.get("x-ms-owner"))
        response_headers["x-ms-group"] = self._deserialize("str", response.headers.get("x-ms-group"))
        response_headers["x-ms-link-count"] = self._deserialize("int", response.headers.get("x-ms-link-count"))

        deserialized = response.stream_download(self._client._pipeline, decompress=_decompress)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def get_properties(  # pylint: disable=inconsistent-return-statements
        self,
        sharesnapshot: Optional[str] = None,
        timeout: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Returns all user-defined metadata, standard HTTP properties, and system properties for the
        file. It does not return the content of the file.

        :param sharesnapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the share snapshot to query. Default value is None.
        :type sharesnapshot: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_get_properties_request(
            url=self._config.url,
            sharesnapshot=sharesnapshot,
            timeout=timeout,
            lease_id=_lease_id,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-meta"] = self._deserialize("{str}", response.headers.get("x-ms-meta"))
        response_headers["x-ms-type"] = self._deserialize("str", response.headers.get("x-ms-type"))
        response_headers["Content-Length"] = self._deserialize("int", response.headers.get("Content-Length"))
        response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["Content-Encoding"] = self._deserialize("str", response.headers.get("Content-Encoding"))
        response_headers["Cache-Control"] = self._deserialize("str", response.headers.get("Cache-Control"))
        response_headers["Content-Disposition"] = self._deserialize("str", response.headers.get("Content-Disposition"))
        response_headers["Content-Language"] = self._deserialize("str", response.headers.get("Content-Language"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-copy-completion-time"] = self._deserialize(
            "rfc-1123", response.headers.get("x-ms-copy-completion-time")
        )
        response_headers["x-ms-copy-status-description"] = self._deserialize(
            "str", response.headers.get("x-ms-copy-status-description")
        )
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-progress"] = self._deserialize("str", response.headers.get("x-ms-copy-progress"))
        response_headers["x-ms-copy-source"] = self._deserialize("str", response.headers.get("x-ms-copy-source"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))
        response_headers["x-ms-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-server-encrypted")
        )
        response_headers["x-ms-file-attributes"] = self._deserialize(
            "str", response.headers.get("x-ms-file-attributes")
        )
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-permission-key"] = self._deserialize(
            "str", response.headers.get("x-ms-file-permission-key")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))
        response_headers["x-ms-lease-duration"] = self._deserialize("str", response.headers.get("x-ms-lease-duration"))
        response_headers["x-ms-lease-state"] = self._deserialize("str", response.headers.get("x-ms-lease-state"))
        response_headers["x-ms-lease-status"] = self._deserialize("str", response.headers.get("x-ms-lease-status"))
        response_headers["x-ms-mode"] = self._deserialize("str", response.headers.get("x-ms-mode"))
        response_headers["x-ms-owner"] = self._deserialize("str", response.headers.get("x-ms-owner"))
        response_headers["x-ms-group"] = self._deserialize("str", response.headers.get("x-ms-group"))
        response_headers["x-ms-link-count"] = self._deserialize("int", response.headers.get("x-ms-link-count"))
        response_headers["x-ms-file-file-type"] = self._deserialize("str", response.headers.get("x-ms-file-file-type"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def delete(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """removes the file from the storage account.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_delete_request(
            url=self._config.url,
            timeout=timeout,
            lease_id=_lease_id,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-link-count"] = self._deserialize("int", response.headers.get("x-ms-link-count"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_http_headers(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        file_content_length: Optional[int] = None,
        file_permission: str = "inherit",
        file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
        file_permission_key: Optional[str] = None,
        file_attributes: str = "none",
        file_creation_time: str = "now",
        file_last_write_time: str = "now",
        file_change_time: Optional[str] = None,
        owner: Optional[str] = None,
        group: Optional[str] = None,
        file_mode: Optional[str] = None,
        file_http_headers: Optional[_models.FileHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Sets HTTP headers on the file.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param file_content_length: Resizes a file to the specified size. If the specified byte value
         is less than the current size of the file, then all ranges above the specified byte value are
         cleared. Default value is None.
        :type file_content_length: int
        :param file_permission: If specified the permission (security descriptor) shall be set for the
         directory/file. This header can be used if Permission size is <= 8KB, else
         x-ms-file-permission-key header shall be used. Default value: Inherit. If SDDL is specified as
         input, it must have owner, group and dacl. Note: Only one of the x-ms-file-permission or
         x-ms-file-permission-key should be specified. Default value is "inherit".
        :type file_permission: str
        :param file_permission_format: Optional. Available for version 2023-06-01 and later. Specifies
         the format in which the permission is returned. Acceptable values are SDDL or binary. If
         x-ms-file-permission-format is unspecified or explicitly set to SDDL, the permission is
         returned in SDDL format. If x-ms-file-permission-format is explicitly set to binary, the
         permission is returned as a base64 string representing the binary encoding of the permission.
         Known values are: "Sddl" and "Binary". Default value is None.
        :type file_permission_format: str or ~azure.storage.fileshare.models.FilePermissionFormat
        :param file_permission_key: Key of the permission to be set for the directory/file. Note: Only
         one of the x-ms-file-permission or x-ms-file-permission-key should be specified. Default value
         is None.
        :type file_permission_key: str
        :param file_attributes: If specified, the provided file attributes shall be set. Default value:
         ‘Archive’ for file and ‘Directory’ for directory. ‘None’ can also be specified as default.
         Default value is "none".
        :type file_attributes: str
        :param file_creation_time: Creation time for the file/directory. Default value: Now. Default
         value is "now".
        :type file_creation_time: str
        :param file_last_write_time: Last write time for the file/directory. Default value: Now.
         Default value is "now".
        :type file_last_write_time: str
        :param file_change_time: Change time for the file/directory. Default value: Now. Default value
         is None.
        :type file_change_time: str
        :param owner: Optional, NFS only. The owner of the file or directory. Default value is None.
        :type owner: str
        :param group: Optional, NFS only. The owning group of the file or directory. Default value is
         None.
        :type group: str
        :param file_mode: Optional, NFS only. The file mode of the file or directory. Default value is
         None.
        :type file_mode: str
        :param file_http_headers: Parameter group. Default value is None.
        :type file_http_headers: ~azure.storage.fileshare.models.FileHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _file_content_type = None
        _file_content_encoding = None
        _file_content_language = None
        _file_cache_control = None
        _file_content_md5 = None
        _file_content_disposition = None
        _lease_id = None
        if file_http_headers is not None:
            _file_cache_control = file_http_headers.file_cache_control
            _file_content_disposition = file_http_headers.file_content_disposition
            _file_content_encoding = file_http_headers.file_content_encoding
            _file_content_language = file_http_headers.file_content_language
            _file_content_md5 = file_http_headers.file_content_md5
            _file_content_type = file_http_headers.file_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_set_http_headers_request(
            url=self._config.url,
            timeout=timeout,
            file_content_length=file_content_length,
            file_content_type=_file_content_type,
            file_content_encoding=_file_content_encoding,
            file_content_language=_file_content_language,
            file_cache_control=_file_cache_control,
            file_content_md5=_file_content_md5,
            file_content_disposition=_file_content_disposition,
            file_permission=file_permission,
            file_permission_format=file_permission_format,
            file_permission_key=file_permission_key,
            file_attributes=file_attributes,
            file_creation_time=file_creation_time,
            file_last_write_time=file_last_write_time,
            file_change_time=file_change_time,
            lease_id=_lease_id,
            owner=owner,
            group=group,
            file_mode=file_mode,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-file-permission-key"] = self._deserialize(
            "str", response.headers.get("x-ms-file-permission-key")
        )
        response_headers["x-ms-file-attributes"] = self._deserialize(
            "str", response.headers.get("x-ms-file-attributes")
        )
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))
        response_headers["x-ms-mode"] = self._deserialize("str", response.headers.get("x-ms-mode"))
        response_headers["x-ms-owner"] = self._deserialize("str", response.headers.get("x-ms-owner"))
        response_headers["x-ms-group"] = self._deserialize("str", response.headers.get("x-ms-group"))
        response_headers["x-ms-link-count"] = self._deserialize("int", response.headers.get("x-ms-link-count"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def set_metadata(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Updates user-defined metadata for the specified file.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: A name-value pair to associate with a file storage object. Default value is
         None.
        :type metadata: dict[str, str]
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["metadata"] = kwargs.pop("comp", _params.pop("comp", "metadata"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_set_metadata_request(
            url=self._config.url,
            timeout=timeout,
            metadata=metadata,
            lease_id=_lease_id,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def acquire_lease(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        duration: Optional[int] = None,
        proposed_lease_id: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease File operation establishes and manages a lock on a file for write and delete
        operations.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param duration: Specifies the duration of the lease, in seconds, or negative one (-1) for a
         lease that never expires. A non-infinite lease can be between 15 and 60 seconds. A lease
         duration cannot be changed using renew or change. Default value is None.
        :type duration: int
        :param proposed_lease_id: Proposed lease ID, in a GUID string format. The File service returns
         400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid
         Constructor (String) for a list of valid GUID string formats. Default value is None.
        :type proposed_lease_id: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["acquire"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "acquire"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_acquire_lease_request(
            url=self._config.url,
            timeout=timeout,
            duration=duration,
            proposed_lease_id=proposed_lease_id,
            request_id_parameter=request_id_parameter,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-lease-id"] = self._deserialize("str", response.headers.get("x-ms-lease-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def release_lease(  # pylint: disable=inconsistent-return-statements
        self, lease_id: str, timeout: Optional[int] = None, request_id_parameter: Optional[str] = None, **kwargs: Any
    ) -> None:
        """[Update] The Lease File operation establishes and manages a lock on a file for write and delete
        operations.

        :param lease_id: Specifies the current lease ID on the resource. Required.
        :type lease_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["release"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "release"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_release_lease_request(
            url=self._config.url,
            lease_id=lease_id,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def change_lease(  # pylint: disable=inconsistent-return-statements
        self,
        lease_id: str,
        timeout: Optional[int] = None,
        proposed_lease_id: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease File operation establishes and manages a lock on a file for write and delete
        operations.

        :param lease_id: Specifies the current lease ID on the resource. Required.
        :type lease_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param proposed_lease_id: Proposed lease ID, in a GUID string format. The File service returns
         400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid
         Constructor (String) for a list of valid GUID string formats. Default value is None.
        :type proposed_lease_id: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["change"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "change"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_change_lease_request(
            url=self._config.url,
            lease_id=lease_id,
            timeout=timeout,
            proposed_lease_id=proposed_lease_id,
            request_id_parameter=request_id_parameter,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-lease-id"] = self._deserialize("str", response.headers.get("x-ms-lease-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def break_lease(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """[Update] The Lease File operation establishes and manages a lock on a file for write and delete
        operations.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["lease"] = kwargs.pop("comp", _params.pop("comp", "lease"))
        action: Literal["break"] = kwargs.pop("action", _headers.pop("x-ms-lease-action", "break"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_break_lease_request(
            url=self._config.url,
            timeout=timeout,
            lease_id=_lease_id,
            request_id_parameter=request_id_parameter,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            action=action,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-lease-id"] = self._deserialize("str", response.headers.get("x-ms-lease-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def upload_range(  # pylint: disable=inconsistent-return-statements
        self,
        range: str,
        content_length: int,
        timeout: Optional[int] = None,
        file_range_write: Union[str, _models.FileRangeWriteType] = "update",
        content_md5: Optional[bytes] = None,
        file_last_written_mode: Optional[Union[str, _models.FileLastWrittenMode]] = None,
        structured_body_type: Optional[str] = None,
        structured_content_length: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        optionalbody: Optional[IO[bytes]] = None,
        **kwargs: Any
    ) -> None:
        """Upload a range of bytes to a file.

        :param range: Specifies the range of bytes to be written. Both the start and end of the range
         must be specified. For an update operation, the range can be up to 4 MB in size. For a clear
         operation, the range can be up to the value of the file's full size. The File service accepts
         only a single byte range for the Range and 'x-ms-range' headers, and the byte range must be
         specified in the following format: bytes=startByte-endByte. Required.
        :type range: str
        :param content_length: Specifies the number of bytes being transmitted in the request body.
         When the x-ms-write header is set to clear, the value of this header must be set to zero.
         Required.
        :type content_length: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param file_range_write: Specify one of the following options: - Update: Writes the bytes
         specified by the request body into the specified range. The Range and Content-Length headers
         must match to perform the update. - Clear: Clears the specified range and releases the space
         used in storage for that range. To clear a range, set the Content-Length header to zero, and
         set the Range header to a value that indicates the range to clear, up to maximum file size.
         Known values are: "update" and "clear". Default value is "update".
        :type file_range_write: str or ~azure.storage.fileshare.models.FileRangeWriteType
        :param content_md5: An MD5 hash of the content. This hash is used to verify the integrity of
         the data during transport. When the Content-MD5 header is specified, the File service compares
         the hash of the content that has arrived with the header value that was sent. If the two hashes
         do not match, the operation will fail with error code 400 (Bad Request). Default value is None.
        :type content_md5: bytes
        :param file_last_written_mode: If the file last write time should be preserved or overwritten.
         Known values are: "Now" and "Preserve". Default value is None.
        :type file_last_written_mode: str or ~azure.storage.fileshare.models.FileLastWrittenMode
        :param structured_body_type: Required if the request body is a structured message. Specifies
         the message schema version and properties. Default value is None.
        :type structured_body_type: str
        :param structured_content_length: Required if the request body is a structured message.
         Specifies the length of the blob/file content inside the message body. Will always be smaller
         than Content-Length. Default value is None.
        :type structured_content_length: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :param optionalbody: Initial data. Default value is None.
        :type optionalbody: IO[bytes]
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["range"] = kwargs.pop("comp", _params.pop("comp", "range"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/octet-stream"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        _content = optionalbody

        _request = build_upload_range_request(
            url=self._config.url,
            range=range,
            content_length=content_length,
            timeout=timeout,
            file_range_write=file_range_write,
            content_md5=content_md5,
            lease_id=_lease_id,
            file_last_written_mode=file_last_written_mode,
            structured_body_type=structured_body_type,
            structured_content_length=structured_content_length,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-structured-body"] = self._deserialize(
            "str", response.headers.get("x-ms-structured-body")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def upload_range_from_url(  # pylint: disable=inconsistent-return-statements
        self,
        range: str,
        copy_source: str,
        content_length: int,
        timeout: Optional[int] = None,
        source_range: Optional[str] = None,
        source_content_crc64: Optional[bytes] = None,
        copy_source_authorization: Optional[str] = None,
        file_last_written_mode: Optional[Union[str, _models.FileLastWrittenMode]] = None,
        source_modified_access_conditions: Optional[_models.SourceModifiedAccessConditions] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Upload a range of bytes to a file where the contents are read from a URL.

        :param range: Writes data to the specified byte range in the file. Required.
        :type range: str
        :param copy_source: Specifies the URL of the source file or blob, up to 2 KB in length. To copy
         a file to another file within the same storage account, you may use Shared Key to authenticate
         the source file. If you are copying a file from another storage account, or if you are copying
         a blob from the same storage account or another storage account, then you must authenticate the
         source file or blob using a shared access signature. If the source is a public blob, no
         authentication is required to perform the copy operation. A file in a share snapshot can also
         be specified as a copy source. Required.
        :type copy_source: str
        :param content_length: Specifies the number of bytes being transmitted in the request body.
         When the x-ms-write header is set to clear, the value of this header must be set to zero.
         Required.
        :type content_length: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param source_range: Bytes of source data in the specified range. Default value is None.
        :type source_range: str
        :param source_content_crc64: Specify the crc64 calculated for the range of bytes that must be
         read from the copy source. Default value is None.
        :type source_content_crc64: bytes
        :param copy_source_authorization: Only Bearer type is supported. Credentials should be a valid
         OAuth access token to copy source. Default value is None.
        :type copy_source_authorization: str
        :param file_last_written_mode: If the file last write time should be preserved or overwritten.
         Known values are: "Now" and "Preserve". Default value is None.
        :type file_last_written_mode: str or ~azure.storage.fileshare.models.FileLastWrittenMode
        :param source_modified_access_conditions: Parameter group. Default value is None.
        :type source_modified_access_conditions:
         ~azure.storage.fileshare.models.SourceModifiedAccessConditions
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["range"] = kwargs.pop("comp", _params.pop("comp", "range"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _source_if_match_crc64 = None
        _source_if_none_match_crc64 = None
        _lease_id = None
        if source_modified_access_conditions is not None:
            _source_if_match_crc64 = source_modified_access_conditions.source_if_match_crc64
            _source_if_none_match_crc64 = source_modified_access_conditions.source_if_none_match_crc64
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_upload_range_from_url_request(
            url=self._config.url,
            range=range,
            copy_source=copy_source,
            content_length=content_length,
            timeout=timeout,
            source_range=source_range,
            source_content_crc64=source_content_crc64,
            source_if_match_crc64=_source_if_match_crc64,
            source_if_none_match_crc64=_source_if_none_match_crc64,
            lease_id=_lease_id,
            copy_source_authorization=copy_source_authorization,
            file_last_written_mode=file_last_written_mode,
            allow_trailing_dot=self._config.allow_trailing_dot,
            allow_source_trailing_dot=self._config.allow_source_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            file_range_write_from_url=self._config.file_range_write_from_url,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def get_range_list(
        self,
        sharesnapshot: Optional[str] = None,
        prevsharesnapshot: Optional[str] = None,
        timeout: Optional[int] = None,
        range: Optional[str] = None,
        support_rename: Optional[bool] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> _models.ShareFileRangeList:
        """Returns the list of valid ranges for a file.

        :param sharesnapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the share snapshot to query. Default value is None.
        :type sharesnapshot: str
        :param prevsharesnapshot: The previous snapshot parameter is an opaque DateTime value that,
         when present, specifies the previous snapshot. Default value is None.
        :type prevsharesnapshot: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param range: Specifies the range of bytes over which to list ranges, inclusively. Default
         value is None.
        :type range: str
        :param support_rename: This header is allowed only when PrevShareSnapshot query parameter is
         set. Determines whether the changed ranges for a file that has been renamed or moved between
         the target snapshot (or the live file) and the previous snapshot should be listed. If the value
         is true, the valid changed ranges for the file will be returned. If the value is false, the
         operation will result in a failure with 409 (Conflict) response. The default value is false.
         Default value is None.
        :type support_rename: bool
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: ShareFileRangeList or the result of cls(response)
        :rtype: ~azure.storage.fileshare.models.ShareFileRangeList
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["rangelist"] = kwargs.pop("comp", _params.pop("comp", "rangelist"))
        cls: ClsType[_models.ShareFileRangeList] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_get_range_list_request(
            url=self._config.url,
            sharesnapshot=sharesnapshot,
            prevsharesnapshot=prevsharesnapshot,
            timeout=timeout,
            range=range,
            lease_id=_lease_id,
            support_rename=support_rename,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["x-ms-content-length"] = self._deserialize("int", response.headers.get("x-ms-content-length"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        deserialized = self._deserialize("ShareFileRangeList", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def start_copy(  # pylint: disable=inconsistent-return-statements
        self,
        copy_source: str,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        file_permission: str = "inherit",
        file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
        file_permission_key: Optional[str] = None,
        owner: Optional[str] = None,
        group: Optional[str] = None,
        file_mode: Optional[str] = None,
        file_mode_copy_mode: Optional[Union[str, _models.ModeCopyMode]] = None,
        file_owner_copy_mode: Optional[Union[str, _models.OwnerCopyMode]] = None,
        copy_file_smb_info: Optional[_models.CopyFileSmbInfo] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Copies a blob or file to a destination file within the storage account.

        :param copy_source: Specifies the URL of the source file or blob, up to 2 KB in length. To copy
         a file to another file within the same storage account, you may use Shared Key to authenticate
         the source file. If you are copying a file from another storage account, or if you are copying
         a blob from the same storage account or another storage account, then you must authenticate the
         source file or blob using a shared access signature. If the source is a public blob, no
         authentication is required to perform the copy operation. A file in a share snapshot can also
         be specified as a copy source. Required.
        :type copy_source: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: A name-value pair to associate with a file storage object. Default value is
         None.
        :type metadata: dict[str, str]
        :param file_permission: If specified the permission (security descriptor) shall be set for the
         directory/file. This header can be used if Permission size is <= 8KB, else
         x-ms-file-permission-key header shall be used. Default value: Inherit. If SDDL is specified as
         input, it must have owner, group and dacl. Note: Only one of the x-ms-file-permission or
         x-ms-file-permission-key should be specified. Default value is "inherit".
        :type file_permission: str
        :param file_permission_format: Optional. Available for version 2023-06-01 and later. Specifies
         the format in which the permission is returned. Acceptable values are SDDL or binary. If
         x-ms-file-permission-format is unspecified or explicitly set to SDDL, the permission is
         returned in SDDL format. If x-ms-file-permission-format is explicitly set to binary, the
         permission is returned as a base64 string representing the binary encoding of the permission.
         Known values are: "Sddl" and "Binary". Default value is None.
        :type file_permission_format: str or ~azure.storage.fileshare.models.FilePermissionFormat
        :param file_permission_key: Key of the permission to be set for the directory/file. Note: Only
         one of the x-ms-file-permission or x-ms-file-permission-key should be specified. Default value
         is None.
        :type file_permission_key: str
        :param owner: Optional, NFS only. The owner of the file or directory. Default value is None.
        :type owner: str
        :param group: Optional, NFS only. The owning group of the file or directory. Default value is
         None.
        :type group: str
        :param file_mode: Optional, NFS only. The file mode of the file or directory. Default value is
         None.
        :type file_mode: str
        :param file_mode_copy_mode: NFS only. Applicable only when the copy source is a File.
         Determines the copy behavior of the mode bits of the file. source: The mode on the destination
         file is copied from the source file. override: The mode on the destination file is determined
         via the x-ms-mode header. Known values are: "source" and "override". Default value is None.
        :type file_mode_copy_mode: str or ~azure.storage.fileshare.models.ModeCopyMode
        :param file_owner_copy_mode: NFS only. Determines the copy behavior of the owner user
         identifier (UID) and group identifier (GID) of the file. source: The owner user identifier
         (UID) and group identifier (GID) on the destination file is copied from the source file.
         override: The owner user identifier (UID) and group identifier (GID) on the destination file is
         determined via the x-ms-owner and x-ms-group  headers. Known values are: "source" and
         "override". Default value is None.
        :type file_owner_copy_mode: str or ~azure.storage.fileshare.models.OwnerCopyMode
        :param copy_file_smb_info: Parameter group. Default value is None.
        :type copy_file_smb_info: ~azure.storage.fileshare.models.CopyFileSmbInfo
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _file_permission_copy_mode = None
        _ignore_read_only = None
        _file_attributes = None
        _file_creation_time = None
        _file_last_write_time = None
        _file_change_time = None
        _set_archive_attribute = None
        _lease_id = None
        if copy_file_smb_info is not None:
            _file_attributes = copy_file_smb_info.file_attributes
            _file_change_time = copy_file_smb_info.file_change_time
            _file_creation_time = copy_file_smb_info.file_creation_time
            _file_last_write_time = copy_file_smb_info.file_last_write_time
            _file_permission_copy_mode = copy_file_smb_info.file_permission_copy_mode
            _ignore_read_only = copy_file_smb_info.ignore_read_only
            _set_archive_attribute = copy_file_smb_info.set_archive_attribute
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_start_copy_request(
            url=self._config.url,
            copy_source=copy_source,
            timeout=timeout,
            metadata=metadata,
            file_permission=file_permission,
            file_permission_format=file_permission_format,
            file_permission_key=file_permission_key,
            file_permission_copy_mode=_file_permission_copy_mode,
            ignore_read_only=_ignore_read_only,
            file_attributes=_file_attributes,
            file_creation_time=_file_creation_time,
            file_last_write_time=_file_last_write_time,
            file_change_time=_file_change_time,
            set_archive_attribute=_set_archive_attribute,
            lease_id=_lease_id,
            owner=owner,
            group=group,
            file_mode=file_mode,
            file_mode_copy_mode=file_mode_copy_mode,
            file_owner_copy_mode=file_owner_copy_mode,
            allow_trailing_dot=self._config.allow_trailing_dot,
            allow_source_trailing_dot=self._config.allow_source_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def abort_copy(  # pylint: disable=inconsistent-return-statements
        self,
        copy_id: str,
        timeout: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Aborts a pending Copy File operation, and leaves a destination file with zero length and full
        metadata.

        :param copy_id: The copy identifier provided in the x-ms-copy-id header of the original Copy
         File operation. Required.
        :type copy_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["copy"] = kwargs.pop("comp", _params.pop("comp", "copy"))
        copy_action_abort_constant: Literal["abort"] = kwargs.pop(
            "copy_action_abort_constant", _headers.pop("x-ms-copy-action", "abort")
        )
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_abort_copy_request(
            url=self._config.url,
            copy_id=copy_id,
            timeout=timeout,
            lease_id=_lease_id,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            copy_action_abort_constant=copy_action_abort_constant,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def list_handles(
        self,
        marker: Optional[str] = None,
        maxresults: Optional[int] = None,
        timeout: Optional[int] = None,
        sharesnapshot: Optional[str] = None,
        **kwargs: Any
    ) -> _models.ListHandlesResponse:
        """Lists handles for file.

        :param marker: A string value that identifies the portion of the list to be returned with the
         next list operation. The operation returns a marker value within the response body if the list
         returned was not complete. The marker value may then be used in a subsequent call to request
         the next set of list items. The marker value is opaque to the client. Default value is None.
        :type marker: str
        :param maxresults: Specifies the maximum number of entries to return. If the request does not
         specify maxresults, or specifies a value greater than 5,000, the server will return up to 5,000
         items. Default value is None.
        :type maxresults: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param sharesnapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the share snapshot to query. Default value is None.
        :type sharesnapshot: str
        :return: ListHandlesResponse or the result of cls(response)
        :rtype: ~azure.storage.fileshare.models.ListHandlesResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["listhandles"] = kwargs.pop("comp", _params.pop("comp", "listhandles"))
        cls: ClsType[_models.ListHandlesResponse] = kwargs.pop("cls", None)

        _request = build_list_handles_request(
            url=self._config.url,
            marker=marker,
            maxresults=maxresults,
            timeout=timeout,
            sharesnapshot=sharesnapshot,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Content-Type"] = self._deserialize("str", response.headers.get("Content-Type"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        deserialized = self._deserialize("ListHandlesResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def force_close_handles(  # pylint: disable=inconsistent-return-statements
        self,
        handle_id: str,
        timeout: Optional[int] = None,
        marker: Optional[str] = None,
        sharesnapshot: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """Closes all handles open for given file.

        :param handle_id: Specifies handle ID opened on the file or directory to be closed. Asterisk
         (‘*’) is a wildcard that specifies all handles. Required.
        :type handle_id: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param marker: A string value that identifies the portion of the list to be returned with the
         next list operation. The operation returns a marker value within the response body if the list
         returned was not complete. The marker value may then be used in a subsequent call to request
         the next set of list items. The marker value is opaque to the client. Default value is None.
        :type marker: str
        :param sharesnapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the share snapshot to query. Default value is None.
        :type sharesnapshot: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["forceclosehandles"] = kwargs.pop("comp", _params.pop("comp", "forceclosehandles"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_force_close_handles_request(
            url=self._config.url,
            handle_id=handle_id,
            timeout=timeout,
            marker=marker,
            sharesnapshot=sharesnapshot,
            allow_trailing_dot=self._config.allow_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-marker"] = self._deserialize("str", response.headers.get("x-ms-marker"))
        response_headers["x-ms-number-of-handles-closed"] = self._deserialize(
            "int", response.headers.get("x-ms-number-of-handles-closed")
        )
        response_headers["x-ms-number-of-handles-failed"] = self._deserialize(
            "int", response.headers.get("x-ms-number-of-handles-failed")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def rename(  # pylint: disable=inconsistent-return-statements
        self,
        rename_source: str,
        timeout: Optional[int] = None,
        replace_if_exists: Optional[bool] = None,
        ignore_read_only: Optional[bool] = None,
        file_permission: str = "inherit",
        file_permission_format: Optional[Union[str, _models.FilePermissionFormat]] = None,
        file_permission_key: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
        source_lease_access_conditions: Optional[_models.SourceLeaseAccessConditions] = None,
        destination_lease_access_conditions: Optional[_models.DestinationLeaseAccessConditions] = None,
        copy_file_smb_info: Optional[_models.CopyFileSmbInfo] = None,
        file_http_headers: Optional[_models.FileHTTPHeaders] = None,
        **kwargs: Any
    ) -> None:
        """Renames a file.

        :param rename_source: Required. Specifies the URI-style path of the source file, up to 2 KB in
         length. Required.
        :type rename_source: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param replace_if_exists: Optional. A boolean value for if the destination file already exists,
         whether this request will overwrite the file or not. If true, the rename will succeed and will
         overwrite the destination file. If not provided or if false and the destination file does
         exist, the request will not overwrite the destination file. If provided and the destination
         file doesn’t exist, the rename will succeed. Note: This value does not override the
         x-ms-file-copy-ignore-read-only header value. Default value is None.
        :type replace_if_exists: bool
        :param ignore_read_only: Optional. A boolean value that specifies whether the ReadOnly
         attribute on a preexisting destination file should be respected. If true, the rename will
         succeed, otherwise, a previous file at the destination with the ReadOnly attribute set will
         cause the rename to fail. Default value is None.
        :type ignore_read_only: bool
        :param file_permission: If specified the permission (security descriptor) shall be set for the
         directory/file. This header can be used if Permission size is <= 8KB, else
         x-ms-file-permission-key header shall be used. Default value: Inherit. If SDDL is specified as
         input, it must have owner, group and dacl. Note: Only one of the x-ms-file-permission or
         x-ms-file-permission-key should be specified. Default value is "inherit".
        :type file_permission: str
        :param file_permission_format: Optional. Available for version 2023-06-01 and later. Specifies
         the format in which the permission is returned. Acceptable values are SDDL or binary. If
         x-ms-file-permission-format is unspecified or explicitly set to SDDL, the permission is
         returned in SDDL format. If x-ms-file-permission-format is explicitly set to binary, the
         permission is returned as a base64 string representing the binary encoding of the permission.
         Known values are: "Sddl" and "Binary". Default value is None.
        :type file_permission_format: str or ~azure.storage.fileshare.models.FilePermissionFormat
        :param file_permission_key: Key of the permission to be set for the directory/file. Note: Only
         one of the x-ms-file-permission or x-ms-file-permission-key should be specified. Default value
         is None.
        :type file_permission_key: str
        :param metadata: A name-value pair to associate with a file storage object. Default value is
         None.
        :type metadata: dict[str, str]
        :param source_lease_access_conditions: Parameter group. Default value is None.
        :type source_lease_access_conditions:
         ~azure.storage.fileshare.models.SourceLeaseAccessConditions
        :param destination_lease_access_conditions: Parameter group. Default value is None.
        :type destination_lease_access_conditions:
         ~azure.storage.fileshare.models.DestinationLeaseAccessConditions
        :param copy_file_smb_info: Parameter group. Default value is None.
        :type copy_file_smb_info: ~azure.storage.fileshare.models.CopyFileSmbInfo
        :param file_http_headers: Parameter group. Default value is None.
        :type file_http_headers: ~azure.storage.fileshare.models.FileHTTPHeaders
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["rename"] = kwargs.pop("comp", _params.pop("comp", "rename"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _source_lease_id = None
        _destination_lease_id = None
        _file_attributes = None
        _file_creation_time = None
        _file_last_write_time = None
        _file_change_time = None
        _file_content_type = None
        if source_lease_access_conditions is not None:
            _source_lease_id = source_lease_access_conditions.source_lease_id
        if destination_lease_access_conditions is not None:
            _destination_lease_id = destination_lease_access_conditions.destination_lease_id
        if copy_file_smb_info is not None:
            _file_attributes = copy_file_smb_info.file_attributes
            _file_change_time = copy_file_smb_info.file_change_time
            _file_creation_time = copy_file_smb_info.file_creation_time
            _file_last_write_time = copy_file_smb_info.file_last_write_time
        if file_http_headers is not None:
            _file_content_type = file_http_headers.file_content_type

        _request = build_rename_request(
            url=self._config.url,
            rename_source=rename_source,
            timeout=timeout,
            replace_if_exists=replace_if_exists,
            ignore_read_only=ignore_read_only,
            source_lease_id=_source_lease_id,
            destination_lease_id=_destination_lease_id,
            file_attributes=_file_attributes,
            file_creation_time=_file_creation_time,
            file_last_write_time=_file_last_write_time,
            file_change_time=_file_change_time,
            file_permission=file_permission,
            file_permission_format=file_permission_format,
            file_permission_key=file_permission_key,
            metadata=metadata,
            file_content_type=_file_content_type,
            allow_trailing_dot=self._config.allow_trailing_dot,
            allow_source_trailing_dot=self._config.allow_source_trailing_dot,
            file_request_intent=self._config.file_request_intent,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-file-permission-key"] = self._deserialize(
            "str", response.headers.get("x-ms-file-permission-key")
        )
        response_headers["x-ms-file-attributes"] = self._deserialize(
            "str", response.headers.get("x-ms-file-attributes")
        )
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def create_symbolic_link(  # pylint: disable=inconsistent-return-statements
        self,
        link_text: str,
        timeout: Optional[int] = None,
        metadata: Optional[Dict[str, str]] = None,
        file_creation_time: str = "now",
        file_last_write_time: str = "now",
        request_id_parameter: Optional[str] = None,
        owner: Optional[str] = None,
        group: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Creates a symbolic link.

        :param link_text: NFS only. Required. The path to the original file, the symbolic link is
         pointing to. The path is of type string which is not resolved and is stored as is. The path can
         be absolute path or the relative path depending on the content stored in the symbolic link
         file. Required.
        :type link_text: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param metadata: A name-value pair to associate with a file storage object. Default value is
         None.
        :type metadata: dict[str, str]
        :param file_creation_time: Creation time for the file/directory. Default value: Now. Default
         value is "now".
        :type file_creation_time: str
        :param file_last_write_time: Last write time for the file/directory. Default value: Now.
         Default value is "now".
        :type file_last_write_time: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param owner: Optional, NFS only. The owner of the file or directory. Default value is None.
        :type owner: str
        :param group: Optional, NFS only. The owning group of the file or directory. Default value is
         None.
        :type group: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        restype: Literal["symboliclink"] = kwargs.pop("restype", _params.pop("restype", "symboliclink"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_create_symbolic_link_request(
            url=self._config.url,
            link_text=link_text,
            timeout=timeout,
            metadata=metadata,
            file_creation_time=file_creation_time,
            file_last_write_time=file_last_write_time,
            request_id_parameter=request_id_parameter,
            lease_id=_lease_id,
            owner=owner,
            group=group,
            file_request_intent=self._config.file_request_intent,
            restype=restype,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-mode"] = self._deserialize("str", response.headers.get("x-ms-mode"))
        response_headers["x-ms-owner"] = self._deserialize("str", response.headers.get("x-ms-owner"))
        response_headers["x-ms-group"] = self._deserialize("str", response.headers.get("x-ms-group"))
        response_headers["x-ms-file-file-type"] = self._deserialize("str", response.headers.get("x-ms-file-file-type"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def get_symbolic_link(  # pylint: disable=inconsistent-return-statements
        self,
        timeout: Optional[int] = None,
        sharesnapshot: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """get_symbolic_link.

        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param sharesnapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the share snapshot to query. Default value is None.
        :type sharesnapshot: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        restype: Literal["symboliclink"] = kwargs.pop("restype", _params.pop("restype", "symboliclink"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_get_symbolic_link_request(
            url=self._config.url,
            timeout=timeout,
            sharesnapshot=sharesnapshot,
            request_id_parameter=request_id_parameter,
            file_request_intent=self._config.file_request_intent,
            restype=restype,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-link-text"] = self._deserialize("str", response.headers.get("x-ms-link-text"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace
    def create_hard_link(  # pylint: disable=inconsistent-return-statements
        self,
        target_file: str,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        """Creates a hard link.

        :param target_file: NFS only. Required. Specifies the path of the target file to which the link
         will be created, up to 2 KiB in length. It should be full path of the target from the root.The
         target file must be in the same share and hence the same storage account. Required.
        :type target_file: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://learn.microsoft.com/rest/api/storageservices/Setting-Timeouts-for-File-Service-Operations">Setting
         Timeouts for File Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.fileshare.models.LeaseAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        restype: Literal["hardlink"] = kwargs.pop("restype", _params.pop("restype", "hardlink"))
        file_type_constant: Literal["file"] = kwargs.pop("file_type_constant", _headers.pop("x-ms-type", "file"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id

        _request = build_create_hard_link_request(
            url=self._config.url,
            target_file=target_file,
            timeout=timeout,
            request_id_parameter=request_id_parameter,
            lease_id=_lease_id,
            file_request_intent=self._config.file_request_intent,
            restype=restype,
            file_type_constant=file_type_constant,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-file-creation-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-creation-time")
        )
        response_headers["x-ms-file-last-write-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-last-write-time")
        )
        response_headers["x-ms-file-change-time"] = self._deserialize(
            "str", response.headers.get("x-ms-file-change-time")
        )
        response_headers["x-ms-file-id"] = self._deserialize("str", response.headers.get("x-ms-file-id"))
        response_headers["x-ms-file-parent-id"] = self._deserialize("str", response.headers.get("x-ms-file-parent-id"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-link-count"] = self._deserialize("int", response.headers.get("x-ms-link-count"))
        response_headers["x-ms-mode"] = self._deserialize("str", response.headers.get("x-ms-mode"))
        response_headers["x-ms-owner"] = self._deserialize("str", response.headers.get("x-ms-owner"))
        response_headers["x-ms-group"] = self._deserialize("str", response.headers.get("x-ms-group"))
        response_headers["x-ms-file-file-type"] = self._deserialize("str", response.headers.get("x-ms-file-file-type"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore
