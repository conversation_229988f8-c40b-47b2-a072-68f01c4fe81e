# AnythingChat 前端启动指南

## 项目概述

AnythingChat 是一个基于 React + Next.js 的智能对话平台前端应用，提供用户友好的界面来管理和交互AI对话系统。

## 技术栈

- **框架**: Next.js 14.2.5
- **语言**: TypeScript 5.5.2
- **样式**: Tailwind CSS 3.4.17 + SCSS
- **UI组件**: Radix UI + 自定义组件
- **状态管理**: React Context
- **数据可视化**: D3.js + Chart.js + Visx
- **包管理器**: PNPM (推荐) / NPM
- **监控**: Sentry
- **主题**: 支持暗色/亮色主题切换

## 系统要求

- Node.js >= 18.0.0
- PNPM >= 8.0.0 (推荐) 或 NPM >= 9.0.0
- Windows 10/11, macOS, 或 Linux

## 快速启动

### 1. 安装依赖

项目已经安装了所有必要的依赖包，如果需要重新安装：

```bash
# 使用 PNPM (推荐)
pnpm install

# 或使用 NPM
npm install
```

### 2. 启动开发服务器

```bash
# 进入前端目录
cd frontend

# 启动开发服务器
npx next dev
```

**启动成功标志:**
- 看到 "✓ Ready in XX.Xs" 消息
- 服务器运行在 http://localhost:3000 (如果3000端口被占用，会自动使用3001)
- 编译无错误

### 3. 访问应用

打开浏览器访问: http://localhost:3000

## 项目结构

```
frontend/
├── src/
│   ├── components/          # React组件
│   │   ├── ui/             # 基础UI组件
│   │   ├── Layout/         # 布局组件
│   │   ├── ChatDemo/       # 聊天演示组件
│   │   ├── ThemeProvider/  # 主题提供者
│   │   └── shared/         # 共享组件
│   ├── pages/              # Next.js页面
│   │   ├── _app.tsx        # 应用入口
│   │   ├── index.tsx       # 首页
│   │   ├── chat.tsx        # 聊天页面
│   │   ├── documents.tsx   # 文档管理
│   │   ├── analytics.tsx   # 数据分析
│   │   └── api/            # API路由
│   ├── styles/             # 样式文件
│   │   ├── globals.css     # 全局样式
│   │   └── modern-theme.css # 现代主题
│   ├── lib/                # 工具库
│   ├── hooks/              # 自定义Hooks
│   ├── context/            # React Context
│   ├── config/             # 配置文件
│   └── types.ts            # TypeScript类型定义
├── public/                 # 静态资源
├── package.json            # 项目配置
├── next.config.js          # Next.js配置
├── tailwind.config.js      # Tailwind配置
└── tsconfig.json           # TypeScript配置
```

## 核心功能

- **🗂️ 文档管理**: 上传、更新和删除文档及其元数据
- **🛝 对话体验**: 支持多种模型的流式对话响应和可配置设置
- **📊 数据分析**: 查看延迟和指标的聚合统计信息及详细直方图
- **📜 日志记录**: 跟踪用户查询、搜索结果和AI响应
- **🎨 主题切换**: 支持暗色/亮色主题
- **📱 响应式设计**: 适配各种屏幕尺寸

## 开发命令

```bash
# 开发模式启动
pnpm dev

# 生产构建
pnpm build

# 生产模式启动
pnpm start

# 代码格式化
pnpm format

# 代码检查
pnpm lint

# 类型检查
pnpm type-check
```

## 配置说明

### 1. TypeScript配置 (tsconfig.json)
- 已配置路径映射 `@/*` 指向 `./src/*`
- 支持 JSX 和现代 ES 特性

### 2. Next.js配置 (next.config.js)
- 启用 Sentry 监控
- 配置图片域名白名单
- Webpack 自定义配置

### 3. Tailwind配置 (tailwind.config.js)
- 自定义颜色主题
- 动画效果配置
- 响应式断点设置

## 常见问题解决

### 1. 端口占用
如果3000端口被占用，Next.js会自动尝试3001端口。

### 2. 模块找不到错误
确保 tsconfig.json 中的路径映射配置正确：
```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 3. 权限错误 (Windows)
如果遇到 EPERM 错误，尝试以管理员身份运行终端。

### 4. 依赖安装失败
```bash
# 清理缓存
pnpm store prune
# 或
npm cache clean --force

# 重新安装
pnpm install
```

## 环境变量

前端项目目前不需要额外的环境变量配置，所有配置都在代码中硬编码或通过配置文件管理。

## 部署说明

### 开发环境
- 运行在 http://localhost:3000
- 支持热重载
- 开启开发者工具

### 生产环境
```bash
# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start
```

## 监控和错误追踪

项目集成了 Sentry 进行错误监控和性能追踪：
- 自动捕获 JavaScript 错误
- 性能监控
- 用户会话重放

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 更新日志

- **v1.0.4**: 当前版本，包含完整的聊天、文档管理和分析功能
- 支持现代化UI设计
- 集成多种数据可视化组件

## 技术支持

如遇到问题，请检查：
1. Node.js 版本是否符合要求
2. 依赖是否正确安装
3. 端口是否被占用
4. TypeScript 配置是否正确

---

**启动成功标志**: 
- ✅ 终端显示 "✓ Ready in XX.Xs"
- ✅ 浏览器可以访问 http://localhost:3000
- ✅ 页面正常加载，无控制台错误

**当前状态**: 前端已成功启动并运行在 http://localhost:3000
